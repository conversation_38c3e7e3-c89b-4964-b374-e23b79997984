@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 9% 7%;
    --foreground: 210 40% 98%;

    --card: 212 12% 12%;
    --card-foreground: 210 40% 98%;

    --popover: 212 12% 12%;
    --popover-foreground: 210 40% 98%;

    --primary: 142 70% 42%;
    --primary-foreground: 0 0% 98%;

    --secondary: 217 19% 27%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 19% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 142 70% 42%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 212 12% 23%;
    --input: 212 12% 23%;
    --ring: 142 70% 42%;

    --radius: 0.5rem;
  }

  * {
    @apply border-border;
    box-sizing: border-box;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
    overflow-x: hidden;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #0d1117;
  }

  ::-webkit-scrollbar-thumb {
    background: #30363d;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #484f58;
  }

  /* Neon glow effects */
  .neon-glow {
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
  }

  .neon-border {
    box-shadow: 0 0 5px currentColor, inset 0 0 5px currentColor;
  }

  /* Matrix-style terminal effect */
  .terminal-text {
    font-family: 'Fira Code', monospace;
    color: #3fb950;
    text-shadow: 0 0 10px #3fb950;
  }

  /* GitHub-style contribution graph */
  .github-cell {
    transition: all 0.2s ease;
  }

  .github-cell:hover {
    transform: scale(1.1);
  }

  /* Smooth animations */
  .smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Custom button styles */
  .btn-neon {
    position: relative;
    background: transparent;
    border: 2px solid #3fb950;
    color: #3fb950;
    padding: 12px 24px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .btn-neon:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(63, 185, 80, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn-neon:hover:before {
    left: 100%;
  }

  .btn-neon:hover {
    color: #000;
    background: #3fb950;
    box-shadow: 0 0 20px #3fb950;
  }

  /* Section containers */
  .section-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  .section-title {
    font-size: 3rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    background: linear-gradient(135deg, #3fb950, #1f6feb);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
  }

  .section-title:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(135deg, #3fb950, #1f6feb);
    border-radius: 2px;
  }

  /* Card styles */
  .glass-card {
    background: rgba(22, 27, 34, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(48, 54, 61, 0.5);
    border-radius: 12px;
    transition: all 0.3s ease;
  }

  .glass-card:hover {
    background: rgba(22, 27, 34, 0.9);
    border-color: #3fb950;
    box-shadow: 0 8px 32px rgba(63, 185, 80, 0.1);
  }

  /* Loading animations */
  @keyframes pulse-neon {
    0%, 100% {
      opacity: 1;
      text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
    }
    50% {
      opacity: 0.8;
      text-shadow: 0 0 2px currentColor, 0 0 5px currentColor, 0 0 8px currentColor;
    }
  }

  .pulse-neon {
    animation: pulse-neon 2s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .float-animation {
    animation: float 3s ease-in-out infinite;
  }

  /* Responsive design helpers */
  @media (max-width: 768px) {
    .section-container {
      padding: 0 1rem;
    }

    .section-title {
      font-size: 2rem;
    }
  }

  /* Hide scrollbar for specific elements */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Custom focus styles */
  .focus-visible:focus-visible {
    outline: 2px solid #3fb950;
    outline-offset: 2px;
  }
}

@layer components {
  /* Component-specific styles can go here */
}

@layer utilities {
  /* Utility classes can go here */
}
