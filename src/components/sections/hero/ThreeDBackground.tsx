
'use client';

import React, { Suspense, useEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Canvas } from '@react-three/fiber';
import ThreeFallback from './ThreeFallback';
import dynamic from 'next/dynamic';

// Use Next.js dynamic import for the 3D scene
const InteractiveThreeScene = dynamic(
  () => import('../../3d/InteractiveThreeScene'),
  {
    ssr: false,
    loading: () => null
  }
);

interface ThreeDBackgroundProps {
  mounted: boolean;
}

const ThreeDBackground = ({ mounted }: ThreeDBackgroundProps) => {
  const [isClient, setIsClient] = useState(false);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Don't render anything on server side or if there's an error
  if (!isClient || !mounted || hasError) {
    return <ThreeFallback />;
  }

  return (
    <div className="absolute inset-0 z-0">
      <ErrorBoundary
        fallback={<ThreeFallback />}
        onError={(error, errorInfo) => {
          console.error('3D Background Error:', error, errorInfo);
          setHasError(true);
        }}
      >
        <Suspense fallback={<ThreeFallback />}>
          <Canvas
            camera={{ position: [0, 0, 6], fov: 50 }}
            dpr={[1, 2]}
            style={{ background: 'transparent' }}
            gl={{
              antialias: true,
              alpha: true,
              powerPreference: 'high-performance',
              preserveDrawingBuffer: false,
              failIfMajorPerformanceCaveat: false
            }}
            onCreated={({ gl }) => {
              try {
                gl.setClearColor(0x000000, 0);
                console.log('3D Canvas initialized successfully');
              } catch (error) {
                console.error('Canvas creation error:', error);
                setHasError(true);
              }
            }}
            frameloop="demand"
            legacy={false}
          >
            <InteractiveThreeScene />
          </Canvas>
        </Suspense>
      </ErrorBoundary>
    </div>
  );
};

export default ThreeDBackground;
