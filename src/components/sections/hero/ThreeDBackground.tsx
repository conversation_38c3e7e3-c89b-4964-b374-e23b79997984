
'use client';

import React, { useEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import ThreeFallback from './ThreeFallback';
import dynamic from 'next/dynamic';

// Dynamically import the entire 3D canvas component with no SSR
const ThreeCanvas = dynamic(
  () => import('./ThreeCanvas'),
  {
    ssr: false,
    loading: () => <ThreeFallback />
  }
);

interface ThreeDBackgroundProps {
  mounted: boolean;
}

const ThreeDBackground = ({ mounted }: ThreeDBackgroundProps) => {
  const [isClient, setIsClient] = useState(false);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Don't render anything on server side or if there's an error
  if (!isClient || !mounted || hasError) {
    return <ThreeFallback />;
  }

  return (
    <div className="absolute inset-0 z-0">
      <ErrorBoundary
        fallback={<ThreeFallback />}
        onError={(error, errorInfo) => {
          console.error('3D Background Error:', error, errorInfo);
          setHasError(true);
        }}
      >
        <ThreeCanvas />
      </ErrorBoundary>
    </div>
  );
};

export default ThreeDBackground;
