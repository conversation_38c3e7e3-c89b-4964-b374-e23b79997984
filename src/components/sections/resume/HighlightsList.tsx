
import { useState } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import HighlightItem from './HighlightItem';
import { generateResumeHighlight } from '../../../utils/geminiService';

interface GeminiResponse {
  highlight: string;
  category: string;
  icon: 'award' | 'book-open' | 'coffee';
}

const HighlightsList = () => {
  const [highlights, setHighlights] = useState<GeminiResponse[]>([
    {
      highlight: "Full Stack Development with React, Node.js, and TypeScript",
      category: "Development",
      icon: "award"
    },
    {
      highlight: "Machine Learning specialization with PyTorch and TensorFlow",
      category: "AI/ML",
      icon: "book-open"
    },
    {
      highlight: "5+ years experience working with distributed teams",
      category: "Experience",
      icon: "coffee"
    },
    {
      highlight: "Open Source contributor to various GitHub projects",
      category: "Community",
      icon: "award"
    },
    {
      highlight: "Conference speaker on AI and web technologies",
      category: "Speaking",
      icon: "book-open"
    }
  ]);
  const [selectedHighlight, setSelectedHighlight] = useState<number | null>(null);
  const [isGeneratingHighlight, setIsGeneratingHighlight] = useState(false);

  // Simulate Gemini AI generating a new highlight
  const generateNewHighlight = async () => {
    setIsGeneratingHighlight(true);
    
    try {
      // Use our updated geminiService function
      const newHighlight = await generateResumeHighlight();
      setHighlights(prev => [...prev, newHighlight]);
      toast.success("New highlight generated by Gemini AI");
    } catch (error) {
      toast.error("Failed to generate highlight");
      console.error(error);
    } finally {
      setIsGeneratingHighlight(false);
    }
  };

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }} 
      whileInView={{ opacity: 1, y: 0 }} 
      transition={{ duration: 0.5, delay: 0.2 }}
      viewport={{ once: true }}
      className="bg-github-light rounded-lg p-6 border border-github-border"
    >
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-white">Highlights</h3>
        <button 
          onClick={generateNewHighlight}
          disabled={isGeneratingHighlight}
          className="px-3 py-1 bg-neon-purple/20 text-neon-purple text-xs rounded-full hover:bg-neon-purple/30 transition-colors flex items-center gap-1"
        >
          {isGeneratingHighlight ? (
            <>
              <span className="h-2 w-2 bg-neon-purple rounded-full animate-pulse"></span>
              <span>Generating...</span>
            </>
          ) : (
            <>
              <span>+ Generate with Gemini</span>
            </>
          )}
        </button>
      </div>
      
      <ul className="space-y-4">
        {highlights.map((item, index) => (
          <HighlightItem 
            key={index}
            item={item}
            index={index}
            isSelected={selectedHighlight === index}
            onClick={() => setSelectedHighlight(index === selectedHighlight ? null : index)}
          />
        ))}
      </ul>
    </motion.div>
  );
};

export default HighlightsList;
