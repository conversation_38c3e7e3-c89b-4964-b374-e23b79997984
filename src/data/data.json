{"skills": {"categories": [{"name": "Programming Languages", "description": "Core programming languages I use for development", "skills": [{"name": "C++", "color": "bg-blue-600", "level": 85}, {"name": "DART", "color": "bg-blue-400", "level": 70}, {"name": "JAVASCRIPT", "color": "bg-yellow-500", "level": 95}, {"name": "PYTHON", "color": "bg-blue-500", "level": 90}, {"name": "TYPESCRIPT", "color": "bg-blue-700", "level": 92}, {"name": "RUST", "color": "bg-orange-700", "level": 60}, {"name": "POWERSHELL", "color": "bg-blue-300", "level": 75}, {"name": "BASH SCRIPT", "color": "bg-gray-700", "level": 80}]}, {"name": "Frontend Development", "description": "Technologies I use to build beautiful and interactive UIs", "skills": [{"name": "HTML5", "color": "bg-red-600", "level": 98}, {"name": "CSS3", "color": "bg-purple-600", "level": 95}, {"name": "REACT", "color": "bg-blue-400", "level": 92}, {"name": "REACT NATIVE", "color": "bg-blue-500", "level": 88}, {"name": "ANGULAR", "color": "bg-red-700", "level": 75}, {"name": "VUE.JS", "color": "bg-green-600", "level": 80}, {"name": "BOOTSTRAP", "color": "bg-purple-700", "level": 90}, {"name": "TAILWINDCSS", "color": "bg-teal-500", "level": 95}, {"name": "NEXT", "color": "bg-black", "level": 85}, {"name": "IONIC", "color": "bg-blue-600", "level": 70}]}, {"name": "Backend Development", "description": "Server-side technologies and frameworks", "skills": [{"name": "NODE.JS", "color": "bg-green-700", "level": 90}, {"name": "EXPRESS.JS", "color": "bg-gray-800", "level": 88}, {"name": "DJANGO", "color": "bg-green-900", "level": 82}, {"name": "FLASK", "color": "bg-gray-700", "level": 85}, {"name": "FASTAPI", "color": "bg-teal-700", "level": 80}, {"name": "SPRING", "color": "bg-green-600", "level": 65}]}, {"name": "Cloud & Deployment", "description": "Services and platforms I use for deployment", "skills": [{"name": "AWS", "color": "bg-yellow-600", "level": 82}, {"name": "AZURE", "color": "bg-blue-700", "level": 75}, {"name": "FIREBASE", "color": "bg-yellow-500", "level": 90}, {"name": "GOOGLECLOUD", "color": "bg-blue-500", "level": 78}, {"name": "NETLIFY", "color": "bg-teal-800", "level": 92}, {"name": "RENDER", "color": "bg-green-600", "level": 85}, {"name": "VERCEL", "color": "bg-black", "level": 95}]}, {"name": "Databases", "description": "Database technologies I work with", "skills": [{"name": "MYSQL", "color": "bg-blue-900", "level": 88}, {"name": "SQLITE", "color": "bg-blue-800", "level": 90}, {"name": "MONGODB", "color": "bg-green-700", "level": 92}, {"name": "SUPABASE", "color": "bg-green-600", "level": 85}]}, {"name": "DevOps & Tools", "description": "Development operations and tooling", "skills": [{"name": "GITHUB ACTIONS", "color": "bg-blue-600", "level": 85}, {"name": "GIT", "color": "bg-red-600", "level": 95}, {"name": "DOCKER", "color": "bg-blue-500", "level": 80}, {"name": "POSTMAN", "color": "bg-orange-500", "level": 92}, {"name": "KUBERNETES", "color": "bg-blue-700", "level": 70}, {"name": "GITHUB", "color": "bg-gray-800", "level": 96}]}, {"name": "Data Science & ML", "description": "Libraries and tools for data analysis and machine learning", "skills": [{"name": "MATPLOTLIB", "color": "bg-gray-700", "level": 85}, {"name": "NUMPY", "color": "bg-blue-800", "level": 90}, {"name": "PANDAS", "color": "bg-purple-800", "level": 92}, {"name": "TENSORFLOW", "color": "bg-orange-600", "level": 85}, {"name": "PYTORCH", "color": "bg-red-700", "level": 80}]}, {"name": "UI/UX & Design", "description": "Design tools and technologies", "skills": [{"name": "FIGMA", "color": "bg-red-600", "level": 85}, {"name": "CANVA", "color": "bg-teal-500", "level": 90}, {"name": "BLENDER", "color": "bg-orange-600", "level": 75}, {"name": "ADOBE CREATIVE CLOUD", "color": "bg-red-800", "level": 80}, {"name": "ADOBE PHOTOSHOP", "color": "bg-blue-900", "level": 85}]}], "topSkills": [{"name": "JAVASCRIPT", "level": 95}, {"name": "REACT", "level": 92}, {"name": "NODE.JS", "level": 90}, {"name": "PYTHON", "level": 90}, {"name": "TYPESCRIPT", "level": 92}, {"name": "MONGODB", "level": 92}]}, "projects": [{"title": "Portfolio", "description": "Personal portfolio website built with HTML and showcasing my projects and skills.", "tags": ["HTML", "CSS", "JavaScript"], "category": "web", "imageUrl": "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=800&ixid=MnwxfDB8MXxyYW5kb218MHx8dGVjaHx8fHx8fDE2MjM2MzYyODE&ixlib=rb-1.2.1&q=80&utm_campaign=api-credit&utm_medium=referral&utm_source=unsplash_source&w=1200"}, {"title": "SNW", "description": "A CSS-based interactive web application with modern design principles.", "tags": ["CSS", "React", "Tailwind"], "category": "web", "imageUrl": "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=800&ixid=MnwxfDB8MXxyYW5kb218MHx8dGVjaHx8fHx8fDE2MjM2MzYyODE&ixlib=rb-1.2.1&q=80&utm_campaign=api-credit&utm_medium=referral&utm_source=unsplash_source&w=1200"}, {"title": "<PERSON><PERSON><PERSON>", "description": "A CSS framework for creating responsive and accessible web interfaces.", "tags": ["CSS", "JavaScript", "Design"], "category": "design", "imageUrl": "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=800&ixid=MnwxfDB8MXxyYW5kb218MHx8cHJvZ3JhbW1pbmd8fHx8fHwxNjIzNjM2MzU4&ixlib=rb-1.2.1&q=80&utm_campaign=api-credit&utm_medium=referral&utm_source=unsplash_source&w=1200"}, {"title": "Storage-NextJs", "description": "A NextJS-based storage solution with TypeScript integration.", "tags": ["TypeScript", "Next.js", "Cloud"], "category": "app", "imageUrl": "https://images.unsplash.com/photo-1498050108023-c5249f4df085?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=800&ixid=MnwxfDB8MXxyYW5kb218MHx8Y29kZXx8fHx8fDE2MjM2MzYzNzg&ixlib=rb-1.2.1&q=80&utm_campaign=api-credit&utm_medium=referral&utm_source=unsplash_source&w=1200"}]}