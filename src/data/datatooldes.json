{"toolDescriptions": {"JavaScript": {"name": "JavaScript", "description": "Versatile scripting language that conforms to the ECMAScript specification.", "experience": 5, "level": 95, "projects": ["Portfolio", "SNW"], "strengths": ["DOM Manipulation", "Async Programming", "ES6+ Features"], "category": "Programming Languages"}, "TypeScript": {"name": "TypeScript", "description": "Strongly typed programming language that builds on JavaScript.", "experience": 3, "level": 92, "projects": ["Storage-NextJs"], "strengths": ["Type Safety", "Interface Design", "Generics"], "category": "Programming Languages"}, "Python": {"name": "Python", "description": "Interpreted high-level general-purpose programming language.", "experience": 4, "level": 90, "projects": ["Data Analysis", "Web Scraping"], "strengths": ["Data Processing", "Scripting", "Libraries"], "category": "Programming Languages"}, "HTML5": {"name": "HTML5", "description": "Latest version of the HTML standard with new elements, attributes, and behaviors.", "experience": 5, "level": 98, "strengths": ["<PERSON><PERSON><PERSON>", "Accessibility", "Forms"], "category": "Frontend Development"}, "CSS3": {"name": "CSS3", "description": "Latest evolution of the Cascading Style Sheets language with new capabilities.", "experience": 5, "level": 95, "strengths": ["Flexbox", "Grid", "Animations"], "category": "Frontend Development"}, "React": {"name": "React", "description": "JavaScript library for building user interfaces, particularly single-page applications.", "experience": 4, "level": 92, "projects": ["Portfolio", "SNW"], "strengths": ["Component Architecture", "<PERSON>s", "Context API"], "category": "Frontend Development"}, "Vue.js": {"name": "Vue.js", "description": "Progressive JavaScript framework for building user interfaces.", "experience": 3, "level": 80, "strengths": ["Reactivity System", "Single-File Components", "Directives"], "category": "Frontend Development"}, "Angular": {"name": "Angular", "description": "Platform and framework for building single-page client applications using HTML and TypeScript.", "experience": 2, "level": 75, "strengths": ["Dependency Injection", "RxJS Integration", "Forms"], "category": "Frontend Development"}, "Svelte": {"name": "Svelte", "description": "Compiler that takes your declarative components and converts them into efficient JavaScript.", "experience": 1, "level": 70, "strengths": ["No Virtual DOM", "Less Boilerplate", "Reactivity"], "category": "Frontend Development"}, "Next.js": {"name": "Next.js", "description": "React framework that enables server-side rendering and static site generation.", "experience": 3, "level": 85, "projects": ["Storage-NextJs"], "strengths": ["SSR/SSG", "API Routes", "Image Optimization"], "category": "Frontend Development"}, "React Native": {"name": "React Native", "description": "Framework for building native mobile applications using React.", "experience": 2, "level": 80, "strengths": ["Cross-Platform", "Native Components", "Hot Reloading"], "category": "Mobile Development"}, "Node.js": {"name": "Node.js", "description": "JavaScript runtime built on Chrome's V8 JavaScript engine.", "experience": 4, "level": 90, "projects": ["API Development", "Server Applications"], "strengths": ["Async I/O", "Event-Driven", "NPM Ecosystem"], "category": "Backend Development"}, "Express": {"name": "Express", "description": "Fast, unopinionated, minimalist web framework for Node.js.", "experience": 4, "level": 88, "strengths": ["Middleware", "Routing", "Template Engines"], "category": "Backend Development"}, "Deno": {"name": "<PERSON><PERSON>", "description": "Secure runtime for JavaScript and TypeScript built on V8.", "experience": 1, "level": 65, "strengths": ["Security", "TypeScript Support", "Standard Library"], "category": "Backend Development"}, "Flask": {"name": "Flask", "description": "Lightweight WSGI web application framework in Python.", "experience": 3, "level": 85, "strengths": ["Routing", "Templates", "Extensions"], "category": "Backend Development"}, "Django": {"name": "Django", "description": "High-level Python web framework that encourages rapid development.", "experience": 3, "level": 82, "strengths": ["ORM", "Admin Interface", "Security"], "category": "Backend Development"}, "Spring": {"name": "Spring", "description": "Application framework and inversion of control container for Java.", "experience": 2, "level": 65, "strengths": ["Dependency Injection", "AOP", "MVC"], "category": "Backend Development"}, "MongoDB": {"name": "MongoDB", "description": "Document-oriented NoSQL database used for high volume data storage.", "experience": 4, "level": 92, "strengths": ["Document Model", "Aggregation", "Indexing"], "category": "Databases"}, "PostgreSQL": {"name": "PostgreSQL", "description": "Powerful, open source object-relational database system.", "experience": 3, "level": 85, "strengths": ["ACID Compliance", "JSON Support", "Extensions"], "category": "Databases"}, "MySQL": {"name": "MySQL", "description": "Open-source relational database management system.", "experience": 4, "level": 88, "strengths": ["Indexing", "Stored Procedures", "Replication"], "category": "Databases"}, "Redis": {"name": "Redis", "description": "In-memory data structure store, used as a database, cache, and message broker.", "experience": 2, "level": 75, "strengths": ["Caching", "Pub/Sub", "Data Structures"], "category": "Databases"}, "Firebase": {"name": "Firebase", "description": "Platform developed by Google for creating mobile and web applications.", "experience": 3, "level": 90, "strengths": ["Realtime Database", "Authentication", "Hosting"], "category": "Cloud & Deployment"}, "GraphQL": {"name": "GraphQL", "description": "Query language for APIs and a runtime for executing those queries.", "experience": 2, "level": 80, "strengths": ["Typed Schema", "Efficient Data Fetching", "Introspection"], "category": "API Development"}, "GitHub": {"name": "GitHub", "description": "Web-based hosting service for version control using Git.", "experience": 5, "level": 95, "strengths": ["Collaboration", "CI/CD", "Project Management"], "category": "DevOps & Tools"}, "Docker": {"name": "<PERSON>er", "description": "Platform for developing, shipping, and running applications in containers.", "experience": 3, "level": 80, "strengths": ["Containerization", "<PERSON><PERSON>", "Image Management"], "category": "DevOps & Tools"}, "Kubernetes": {"name": "Kubernetes", "description": "Open-source system for automating deployment, scaling, and management of containerized applications.", "experience": 2, "level": 70, "strengths": ["Orchestration", "Sc<PERSON>", "Self-healing"], "category": "DevOps & Tools"}, "AWS": {"name": "AWS", "description": "Cloud computing platform provided by Amazon.", "experience": 3, "level": 82, "strengths": ["EC2", "S3", "Lambda"], "category": "Cloud & Deployment"}, "Azure": {"name": "Azure", "description": "Cloud computing service created by Microsoft.", "experience": 2, "level": 75, "strengths": ["App Service", "Azure Functions", "Cosmos DB"], "category": "Cloud & Deployment"}, "Google Cloud": {"name": "Google Cloud", "description": "Suite of cloud computing services offered by Google.", "experience": 2, "level": 78, "strengths": ["GCE", "GCS", "Cloud Functions"], "category": "Cloud & Deployment"}, "Sass": {"name": "Sass", "description": "CSS preprocessor scripting language that is interpreted or compiled into CSS.", "experience": 4, "level": 90, "strengths": ["Variables", "Nesting", "Mixins"], "category": "Frontend Development"}, "TailwindCSS": {"name": "TailwindCSS", "description": "Utility-first CSS framework for rapidly building custom user interfaces.", "experience": 3, "level": 95, "projects": ["Portfolio"], "strengths": ["Utility Classes", "Responsive Design", "Customization"], "category": "Frontend Development"}, "Bootstrap": {"name": "Bootstrap", "description": "Free and open-source CSS framework directed at responsive, mobile-first front-end web development.", "experience": 4, "level": 90, "strengths": ["Grid System", "Components", "Responsive Design"], "category": "Frontend Development"}, "Material UI": {"name": "Material UI", "description": "React components for faster and easier web development following Material Design.", "experience": 3, "level": 85, "strengths": ["Component Library", "Theming", "Customization"], "category": "Frontend Development"}, "Rust": {"name": "Rust", "description": "Multi-paradigm programming language designed for performance and safety.", "experience": 1, "level": 60, "strengths": ["Memory Safety", "Concurrency", "Performance"], "category": "Programming Languages"}, "C++": {"name": "C++", "description": "General-purpose programming language with a bias toward systems programming.", "experience": 3, "level": 85, "strengths": ["Performance", "Memory Management", "OOP"], "category": "Programming Languages"}, "Dart": {"name": "Dart", "description": "Client-optimized programming language for apps on multiple platforms.", "experience": 2, "level": 70, "strengths": ["Flutter Integration", "Async Support", "Strong Typing"], "category": "Programming Languages"}}}