{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "741d189cd3531fc0c81d9d1f047ee201", "previewModeSigningKey": "e1c5a8f151c8f9415c692731823057e43b4b28263d086d73f6683da782a6e306", "previewModeEncryptionKey": "99954c6fc2eb5ba4d2a067d2b0f3211411a8fcf76338bd1da3199e9b37ff0a9e"}}