{"kind": "FETCH", "data": {"headers": {"access-control-allow-origin": "*", "access-control-expose-headers": "ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OA<PERSON>-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset", "cache-control": "private, max-age=60, s-maxage=60", "content-encoding": "gzip", "content-security-policy": "default-src 'none'", "content-type": "application/json; charset=utf-8", "date": "Mon, 02 Jun 2025 11:44:05 GMT", "etag": "W/\"7123525889bddbd68b3c41f10fdcc4e6bc0ce58105ec3570c37c49cd99182576\"", "github-authentication-token-expiration": "2025-07-02 17:12:51 +0530", "last-modified": "Sat, 31 May 2025 05:04:03 GMT", "referrer-policy": "origin-when-cross-origin, strict-origin-when-cross-origin", "server": "github.com", "strict-transport-security": "max-age=31536000; includeSubdomains; preload", "transfer-encoding": "chunked", "vary": "Accept, Authorization, <PERSON><PERSON>, X-<PERSON>itHub-OTP,Accept-Encoding, Accept, X-Requested-With", "x-content-type-options": "nosniff", "x-frame-options": "deny", "x-github-api-version-selected": "2022-11-28", "x-github-media-type": "github.v3; format=json", "x-github-request-id": "098B:7561F:1A23EE:1DABE8:683D8E84", "x-ratelimit-limit": "5000", "x-ratelimit-remaining": "4983", "x-ratelimit-reset": "1748867487", "x-ratelimit-resource": "core", "x-ratelimit-used": "17", "x-xss-protection": "0"}, "body": "eyJsb2dpbiI6IkdyZWVuSGFja2VyNDIwIiwiaWQiOjEwNjUxMDkxMywibm9kZV9pZCI6IlVfa2dET0JsazZRUSIsImF2YXRhcl91cmwiOiJodHRwczovL2F2YXRhcnMuZ2l0aHVidXNlcmNvbnRlbnQuY29tL3UvMTA2NTEwOTEzP3Y9NCIsImdyYXZhdGFyX2lkIjoiIiwidXJsIjoiaHR0cHM6Ly9hcGkuZ2l0aHViLmNvbS91c2Vycy9HcmVlbkhhY2tlcjQyMCIsImh0bWxfdXJsIjoiaHR0cHM6Ly9naXRodWIuY29tL0dyZWVuSGFja2VyNDIwIiwiZm9sbG93ZXJzX3VybCI6Imh0dHBzOi8vYXBpLmdpdGh1Yi5jb20vdXNlcnMvR3JlZW5IYWNrZXI0MjAvZm9sbG93ZXJzIiwiZm9sbG93aW5nX3VybCI6Imh0dHBzOi8vYXBpLmdpdGh1Yi5jb20vdXNlcnMvR3JlZW5IYWNrZXI0MjAvZm9sbG93aW5ney9vdGhlcl91c2VyfSIsImdpc3RzX3VybCI6Imh0dHBzOi8vYXBpLmdpdGh1Yi5jb20vdXNlcnMvR3JlZW5IYWNrZXI0MjAvZ2lzdHN7L2dpc3RfaWR9Iiwic3RhcnJlZF91cmwiOiJodHRwczovL2FwaS5naXRodWIuY29tL3VzZXJzL0dyZWVuSGFja2VyNDIwL3N0YXJyZWR7L293bmVyfXsvcmVwb30iLCJzdWJzY3JpcHRpb25zX3VybCI6Imh0dHBzOi8vYXBpLmdpdGh1Yi5jb20vdXNlcnMvR3JlZW5IYWNrZXI0MjAvc3Vic2NyaXB0aW9ucyIsIm9yZ2FuaXphdGlvbnNfdXJsIjoiaHR0cHM6Ly9hcGkuZ2l0aHViLmNvbS91c2Vycy9HcmVlbkhhY2tlcjQyMC9vcmdzIiwicmVwb3NfdXJsIjoiaHR0cHM6Ly9hcGkuZ2l0aHViLmNvbS91c2Vycy9HcmVlbkhhY2tlcjQyMC9yZXBvcyIsImV2ZW50c191cmwiOiJodHRwczovL2FwaS5naXRodWIuY29tL3VzZXJzL0dyZWVuSGFja2VyNDIwL2V2ZW50c3svcHJpdmFjeX0iLCJyZWNlaXZlZF9ldmVudHNfdXJsIjoiaHR0cHM6Ly9hcGkuZ2l0aHViLmNvbS91c2Vycy9HcmVlbkhhY2tlcjQyMC9yZWNlaXZlZF9ldmVudHMiLCJ0eXBlIjoiVXNlciIsInVzZXJfdmlld190eXBlIjoicHVibGljIiwic2l0ZV9hZG1pbiI6ZmFsc2UsIm5hbWUiOiJHcmVlbiBIYWNrZXIiLCJjb21wYW55IjoiRXZlcmdyZWVuIENsYXNzaWMiLCJibG9nIjoiaHR0cHM6Ly9wb3J0Zm9saW8uZ3JlZW5oYWNrZXIudGVjaC8iLCJsb2NhdGlvbiI6IlB1bmUsIE1haGFyYXN0cmEiLCJlbWFpbCI6bnVsbCwiaGlyZWFibGUiOm51bGwsImJpbyI6bnVsbCwidHdpdHRlcl91c2VybmFtZSI6bnVsbCwicHVibGljX3JlcG9zIjo0MywicHVibGljX2dpc3RzIjowLCJmb2xsb3dlcnMiOjcsImZvbGxvd2luZyI6OCwiY3JlYXRlZF9hdCI6IjIwMjItMDUtMzBUMDY6MzU6MzVaIiwidXBkYXRlZF9hdCI6IjIwMjUtMDUtMzFUMDU6MDQ6MDNaIn0=", "status": 200, "url": "https://api.github.com/users/GreenHacker420"}, "revalidate": 3600, "tags": []}