(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[697],{10697:(e,t,n)=>{"use strict";let r,l,a;n.d(t,{A:()=>eS,B:()=>j,C:()=>ew,D:()=>ek,E:()=>D,F:()=>ex,G:()=>e_,H:()=>eC,a:()=>T,b:()=>ej,c:()=>eB,d:()=>eO,e:()=>z,f:()=>ea,g:()=>eo,h:()=>eU,i:()=>L,j:()=>eM,k:()=>eL,l:()=>q,m:()=>eN,n:()=>eI,o:()=>eh,p:()=>em,q:()=>eg,r:()=>eD,s:()=>eb,t:()=>P,u:()=>R,v:()=>eH,w:()=>H,x:()=>eQ,y:()=>B,z:()=>ez});var i,u,o=n(43264),s=n(97431),c=n(12115),f=n(61933);let d="undefined"==typeof window||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent)?c.useEffect:c.useLayoutEffect;function p(e){let t="function"==typeof e?function(e){let t,n=new Set,r=(e,r)=>{let l="function"==typeof e?e(t):e;if(l!==t){let e=t;t=r?l:Object.assign({},t,l),n.forEach(n=>n(t,e))}},l=()=>t,a=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Object.is;console.warn("[DEPRECATED] Please use `subscribeWithSelector` middleware");let i=r(t);function u(){let n=r(t);if(!a(i,n)){let t=i;e(i=n,t)}}return n.add(u),()=>n.delete(u)},i={setState:r,getState:l,subscribe:(e,t,r)=>t||r?a(e,t,r):(n.add(e),()=>n.delete(e)),destroy:()=>n.clear()};return t=e(r,l,i),i}(e):e,n=function(){let e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:t.getState,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object.is,[,l]=(0,c.useReducer)(e=>e+1,0),a=t.getState(),i=(0,c.useRef)(a),u=(0,c.useRef)(n),o=(0,c.useRef)(r),s=(0,c.useRef)(!1),f=(0,c.useRef)();void 0===f.current&&(f.current=n(a));let p=!1;(i.current!==a||u.current!==n||o.current!==r||s.current)&&(e=n(a),p=!r(f.current,e)),d(()=>{p&&(f.current=e),i.current=a,u.current=n,o.current=r,s.current=!1});let h=(0,c.useRef)(a);d(()=>{let e=()=>{try{let e=t.getState(),n=u.current(e);o.current(f.current,n)||(i.current=e,f.current=n,l())}catch(e){s.current=!0,l()}},n=t.subscribe(e);return t.getState()!==h.current&&e(),n},[]);let m=p?e:f.current;return(0,c.useDebugValue)(m),m};return Object.assign(n,t),n[Symbol.iterator]=function(){console.warn("[useStore, api] = create() is deprecated and will be removed in v4");let e=[n,t];return{next(){let t=e.length<=0;return{value:e.shift(),done:t}}}},n}let h=e=>"object"==typeof e&&"function"==typeof e.then,m=[];function g(e,t,n=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;let r=e.length;if(t.length!==r)return!1;for(let l=0;l<r;l++)if(!n(e[l],t[l]))return!1;return!0}function v(e,t=null,n=!1,r={}){for(let l of(null===t&&(t=[e]),m))if(g(t,l.keys,l.equal)){if(n)return;if(Object.prototype.hasOwnProperty.call(l,"error"))throw l.error;if(Object.prototype.hasOwnProperty.call(l,"response"))return r.lifespan&&r.lifespan>0&&(l.timeout&&clearTimeout(l.timeout),l.timeout=setTimeout(l.remove,r.lifespan)),l.response;if(!n)throw l.promise}let l={keys:t,equal:r.equal,remove:()=>{let e=m.indexOf(l);-1!==e&&m.splice(e,1)},promise:(h(e)?e:e(...t)).then(e=>{l.response=e,r.lifespan&&r.lifespan>0&&(l.timeout=setTimeout(l.remove,r.lifespan))}).catch(e=>l.error=e)};if(m.push(l),!n)throw l.promise}let b=(e,t,n)=>v(e,t,!1,n),y=(e,t,n)=>void v(e,t,!0,n),S=e=>{if(void 0===e||0===e.length)m.splice(0,m.length);else{let t=m.find(t=>g(e,t.keys,t.equal));t&&t.remove()}};var w=n(95155),k=n(45220),x=n.n(k),_=n(72407),E=n(49509),P=Object.freeze({__proto__:null});let C={},z=e=>void Object.assign(C,e),N=e=>"colorSpace"in e||"outputColorSpace"in e,I=()=>{var e;return null!=(e=C.ColorManagement)?e:null},M=e=>e&&e.isOrthographicCamera,L=e=>e&&e.hasOwnProperty("current"),T="undefined"!=typeof window&&(null!=(i=window.document)&&i.createElement||(null==(u=window.navigator)?void 0:u.product)==="ReactNative")?c.useLayoutEffect:c.useEffect;function R(e){let t=c.useRef(e);return T(()=>void(t.current=e),[e]),t}function j(e){let{set:t}=e;return T(()=>(t(new Promise(()=>null)),()=>t(!1)),[t]),null}class D extends c.Component{componentDidCatch(e){this.props.set(e)}render(){return this.state.error?null:this.props.children}constructor(...e){super(...e),this.state={error:!1}}}D.getDerivedStateFromError=()=>({error:!0});let F="__default",O=new Map,U=e=>e&&!!e.memoized&&!!e.changes;function A(e){var t;let n="undefined"!=typeof window?null!=(t=window.devicePixelRatio)?t:2:1;return Array.isArray(e)?Math.min(Math.max(e[0],n),e[1]):e}let H=e=>{var t;return null==(t=e.__r3f)?void 0:t.root.getState()};function Q(e){let t=e.__r3f.root;for(;t.getState().previousRoot;)t=t.getState().previousRoot;return t}let W={obj:e=>e===Object(e)&&!W.arr(e)&&"function"!=typeof e,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,boo:e=>"boolean"==typeof e,und:e=>void 0===e,arr:e=>Array.isArray(e),equ(e,t){let n,{arrays:r="shallow",objects:l="reference",strict:a=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(typeof e!=typeof t||!!e!=!!t)return!1;if(W.str(e)||W.num(e)||W.boo(e))return e===t;let i=W.obj(e);if(i&&"reference"===l)return e===t;let u=W.arr(e);if(u&&"reference"===r)return e===t;if((u||i)&&e===t)return!0;for(n in e)if(!(n in t))return!1;if(i&&"shallow"===r&&"shallow"===l){for(n in a?t:e)if(!W.equ(e[n],t[n],{strict:a,objects:"reference"}))return!1}else for(n in a?t:e)if(e[n]!==t[n])return!1;if(W.und(n)){if(u&&0===e.length&&0===t.length||i&&0===Object.keys(e).length&&0===Object.keys(t).length)return!0;if(e!==t)return!1}return!0}};function B(e){let t={nodes:{},materials:{}};return e&&e.traverse(e=>{e.name&&(t.nodes[e.name]=e),e.material&&!t.materials[e.material.name]&&(t.materials[e.material.name]=e.material)}),t}function q(e){for(let t in e.dispose&&"Scene"!==e.type&&e.dispose(),e)null==t.dispose||t.dispose(),delete e[t]}function $(e,t){return e.__r3f={type:"",root:null,previousAttach:null,memoizedProps:{},eventCount:0,handlers:{},objects:[],parent:null,...t},e}function V(e,t){let n=e;if(!t.includes("-"))return{target:n,key:t};{let r=t.split("-"),l=r.pop();return{target:n=r.reduce((e,t)=>e[t],e),key:l}}}let Y=/-\d+$/;function G(e,t,n){if(W.str(n)){if(Y.test(n)){let{target:t,key:r}=V(e,n.replace(Y,""));Array.isArray(t[r])||(t[r]=[])}let{target:r,key:l}=V(e,n);t.__r3f.previousAttach=r[l],r[l]=t}else t.__r3f.previousAttach=n(e,t)}function K(e,t,n){var r,l;if(W.str(n)){let{target:r,key:l}=V(e,n),a=t.__r3f.previousAttach;void 0===a?delete r[l]:r[l]=a}else null==(r=t.__r3f)||null==r.previousAttach||r.previousAttach(e,t);null==(l=t.__r3f)||delete l.previousAttach}function X(e,t){let{children:n,key:r,ref:l,...a}=t,{children:i,key:u,ref:o,...s}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},c=arguments.length>3&&void 0!==arguments[3]&&arguments[3],f=e.__r3f,d=Object.entries(a),p=[];if(c){let e=Object.keys(s);for(let t=0;t<e.length;t++)a.hasOwnProperty(e[t])||d.unshift([e[t],F+"remove"])}d.forEach(t=>{var n;let[r,l]=t;if(null!=(n=e.__r3f)&&n.primitive&&"object"===r||W.equ(l,s[r]))return;if(/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/.test(r))return p.push([r,l,!0,[]]);let i=[];for(let e in r.includes("-")&&(i=r.split("-")),p.push([r,l,!1,i]),a){let t=a[e];e.startsWith("".concat(r,"-"))&&p.push([e,t,!1,e.split("-")])}});let h={...a};return null!=f&&f.memoizedProps&&null!=f&&f.memoizedProps.args&&(h.args=f.memoizedProps.args),null!=f&&f.memoizedProps&&null!=f&&f.memoizedProps.attach&&(h.attach=f.memoizedProps.attach),{memoized:h,changes:p}}let Z=void 0!==E&&!1;function J(e,t){var n,r,l;let a=e.__r3f,i=null==a?void 0:a.root,u=null==i||null==i.getState?void 0:i.getState(),{memoized:s,changes:c}=U(t)?t:X(e,t),f=null==a?void 0:a.eventCount;e.__r3f&&(e.__r3f.memoizedProps=s);for(let t=0;t<c.length;t++){let[n,i,s,f]=c[t];if(N(e)){let e="srgb",t="srgb-linear";"encoding"===n?(n="colorSpace",i=3001===i?e:t):"outputEncoding"===n&&(n="outputColorSpace",i=3001===i?e:t)}let d=e,p=d[n];if(f.length&&!((p=f.reduce((e,t)=>e[t],e))&&p.set)){let[t,...r]=f.reverse();d=r.reverse().reduce((e,t)=>e[t],e),n=t}if(i===F+"remove")if(d.constructor){let e=O.get(d.constructor);e||(e=new d.constructor,O.set(d.constructor,e)),i=e[n]}else i=0;if(s&&a)i?a.handlers[n]=i:delete a.handlers[n],a.eventCount=Object.keys(a.handlers).length;else if(p&&p.set&&(p.copy||p instanceof o.zgK)){if(Array.isArray(i))p.fromArray?p.fromArray(i):p.set(...i);else if(p.copy&&i&&i.constructor&&(Z?p.constructor.name===i.constructor.name:p.constructor===i.constructor))p.copy(i);else if(void 0!==i){let e=null==(r=p)?void 0:r.isColor;!e&&p.setScalar?p.setScalar(i):p instanceof o.zgK&&i instanceof o.zgK?p.mask=i.mask:p.set(i),!I()&&u&&!u.linear&&e&&p.convertSRGBToLinear()}}else if(d[n]=i,null!=(l=d[n])&&l.isTexture&&d[n].format===o.GWd&&d[n].type===o.OUM&&u){let e=d[n];N(e)&&N(u.gl)?e.colorSpace=u.gl.outputColorSpace:e.encoding=u.gl.outputEncoding}ee(e)}if(a&&a.parent&&e.raycast&&f!==a.eventCount){let t=Q(e).getState().internal,n=t.interaction.indexOf(e);n>-1&&t.interaction.splice(n,1),a.eventCount&&t.interaction.push(e)}return(1!==c.length||"onUpdate"!==c[0][0])&&c.length&&null!=(n=e.__r3f)&&n.parent&&et(e),e}function ee(e){var t,n;let r=null==(t=e.__r3f)||null==(n=t.root)||null==n.getState?void 0:n.getState();r&&0===r.internal.frames&&r.invalidate()}function et(e){null==e.onUpdate||e.onUpdate(e)}function en(e,t){e.manual||(M(e)?(e.left=-(t.width/2),e.right=t.width/2,e.top=t.height/2,e.bottom=-(t.height/2)):e.aspect=t.width/t.height,e.updateProjectionMatrix(),e.updateMatrixWorld())}function er(e){return(e.eventObject||e.object).uuid+"/"+e.index+e.instanceId}function el(e,t,n,r){let l=n.get(t);l&&(n.delete(t),0===n.size&&(e.delete(r),l.target.releasePointerCapture(r)))}function ea(e){function t(e){return e.filter(e=>["Move","Over","Enter","Out","Leave"].some(t=>{var n;return null==(n=e.__r3f)?void 0:n.handlers["onPointer"+t]}))}function n(t){let{internal:n}=e.getState();for(let e of n.hovered.values())if(!t.length||!t.find(t=>t.object===e.object&&t.index===e.index&&t.instanceId===e.instanceId)){let r=e.eventObject.__r3f,l=null==r?void 0:r.handlers;if(n.hovered.delete(er(e)),null!=r&&r.eventCount){let n={...e,intersections:t};null==l.onPointerOut||l.onPointerOut(n),null==l.onPointerLeave||l.onPointerLeave(n)}}}function r(e,t){for(let n=0;n<t.length;n++){let r=t[n].__r3f;null==r||null==r.handlers.onPointerMissed||r.handlers.onPointerMissed(e)}}return{handlePointer:function(l){switch(l){case"onPointerLeave":case"onPointerCancel":return()=>n([]);case"onLostPointerCapture":return t=>{let{internal:r}=e.getState();"pointerId"in t&&r.capturedMap.has(t.pointerId)&&requestAnimationFrame(()=>{r.capturedMap.has(t.pointerId)&&(r.capturedMap.delete(t.pointerId),n([]))})}}return function(a){let{onPointerMissed:i,internal:u}=e.getState();u.lastEvent.current=a;let s="onPointerMove"===l,c="onClick"===l||"onContextMenu"===l||"onDoubleClick"===l,f=function(t,n){let r=e.getState(),l=new Set,a=[],i=n?n(r.internal.interaction):r.internal.interaction;for(let e=0;e<i.length;e++){let t=H(i[e]);t&&(t.raycaster.camera=void 0)}r.previousRoot||null==r.events.compute||r.events.compute(t,r);let u=i.flatMap(function(e){let n=H(e);if(!n||!n.events.enabled||null===n.raycaster.camera)return[];if(void 0===n.raycaster.camera){var r;null==n.events.compute||n.events.compute(t,n,null==(r=n.previousRoot)?void 0:r.getState()),void 0===n.raycaster.camera&&(n.raycaster.camera=null)}return n.raycaster.camera?n.raycaster.intersectObject(e,!0):[]}).sort((e,t)=>{let n=H(e.object),r=H(t.object);return n&&r&&r.events.priority-n.events.priority||e.distance-t.distance}).filter(e=>{let t=er(e);return!l.has(t)&&(l.add(t),!0)});for(let e of(r.events.filter&&(u=r.events.filter(u,r)),u)){let t=e.object;for(;t;){var o;null!=(o=t.__r3f)&&o.eventCount&&a.push({...e,eventObject:t}),t=t.parent}}if("pointerId"in t&&r.internal.capturedMap.has(t.pointerId))for(let e of r.internal.capturedMap.get(t.pointerId).values())l.has(er(e.intersection))||a.push(e.intersection);return a}(a,s?t:void 0),d=c?function(t){let{internal:n}=e.getState(),r=t.offsetX-n.initialClick[0],l=t.offsetY-n.initialClick[1];return Math.round(Math.sqrt(r*r+l*l))}(a):0;"onPointerDown"===l&&(u.initialClick=[a.offsetX,a.offsetY],u.initialHits=f.map(e=>e.eventObject)),c&&!f.length&&d<=2&&(r(a,u.interaction),i&&i(a)),s&&n(f),!function(t,r,l,a){let i=e.getState();if(t.length){let e={stopped:!1};for(let u of t){let{raycaster:s,pointer:c,camera:f,internal:d}=H(u.object)||i,p=new o.Pq0(c.x,c.y,0).unproject(f),h=e=>{var t,n;return null!=(t=null==(n=d.capturedMap.get(e))?void 0:n.has(u.eventObject))&&t},m=e=>{let t={intersection:u,target:r.target};d.capturedMap.has(e)?d.capturedMap.get(e).set(u.eventObject,t):d.capturedMap.set(e,new Map([[u.eventObject,t]])),r.target.setPointerCapture(e)},g=e=>{let t=d.capturedMap.get(e);t&&el(d.capturedMap,u.eventObject,t,e)},v={};for(let e in r){let t=r[e];"function"!=typeof t&&(v[e]=t)}let b={...u,...v,pointer:c,intersections:t,stopped:e.stopped,delta:l,unprojectedPoint:p,ray:s.ray,camera:f,stopPropagation(){let l="pointerId"in r&&d.capturedMap.get(r.pointerId);(!l||l.has(u.eventObject))&&(b.stopped=e.stopped=!0,d.hovered.size&&Array.from(d.hovered.values()).find(e=>e.eventObject===u.eventObject)&&n([...t.slice(0,t.indexOf(u)),u]))},target:{hasPointerCapture:h,setPointerCapture:m,releasePointerCapture:g},currentTarget:{hasPointerCapture:h,setPointerCapture:m,releasePointerCapture:g},nativeEvent:r};if(a(b),!0===e.stopped)break}}}(f,a,d,function(e){let t=e.eventObject,n=t.__r3f,i=null==n?void 0:n.handlers;if(null!=n&&n.eventCount)if(s){if(i.onPointerOver||i.onPointerEnter||i.onPointerOut||i.onPointerLeave){let t=er(e),n=u.hovered.get(t);n?n.stopped&&e.stopPropagation():(u.hovered.set(t,e),null==i.onPointerOver||i.onPointerOver(e),null==i.onPointerEnter||i.onPointerEnter(e))}null==i.onPointerMove||i.onPointerMove(e)}else{let n=i[l];n?(!c||u.initialHits.includes(t))&&(r(a,u.interaction.filter(e=>!u.initialHits.includes(e))),n(e)):c&&u.initialHits.includes(t)&&r(a,u.interaction.filter(e=>!u.initialHits.includes(e)))}})}}}}let ei=["set","get","setSize","setFrameloop","setDpr","events","invalidate","advance","size","viewport"],eu=e=>!!(null!=e&&e.render),eo=c.createContext(null),es=(e,t)=>{let n=p((n,r)=>{let l,a=new o.Pq0,i=new o.Pq0,u=new o.Pq0;function s(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r().camera,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r().size,{width:l,height:o,top:s,left:c}=n,f=l/o;t.isVector3?u.copy(t):u.set(...t);let d=e.getWorldPosition(a).distanceTo(u);if(M(e))return{width:l/e.zoom,height:o/e.zoom,top:s,left:c,factor:1,distance:d,aspect:f};{let t=2*Math.tan(e.fov*Math.PI/180/2)*d,n=l/o*t;return{width:n,height:t,top:s,left:c,factor:l/n,distance:d,aspect:f}}}let f=e=>n(t=>({performance:{...t.performance,current:e}})),d=new o.I9Y;return{set:n,get:r,gl:null,camera:null,raycaster:null,events:{priority:1,enabled:!0,connected:!1},xr:null,scene:null,invalidate:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return e(r(),t)},advance:(e,n)=>t(e,n,r()),legacy:!1,linear:!1,flat:!1,controls:null,clock:new o.zD7,pointer:d,mouse:d,frameloop:"always",onPointerMissed:void 0,performance:{current:1,min:.5,max:1,debounce:200,regress:()=>{let e=r();l&&clearTimeout(l),e.performance.current!==e.performance.min&&f(e.performance.min),l=setTimeout(()=>f(r().performance.max),e.performance.debounce)}},size:{width:0,height:0,top:0,left:0,updateStyle:!1},viewport:{initialDpr:0,dpr:0,width:0,height:0,top:0,left:0,aspect:0,distance:0,factor:0,getCurrentViewport:s},setEvents:e=>n(t=>({...t,events:{...t.events,...e}})),setSize:(e,t,l,a,u)=>{let o=r().camera,c={width:e,height:t,top:a||0,left:u||0,updateStyle:l};n(e=>({size:c,viewport:{...e.viewport,...s(o,i,c)}}))},setDpr:e=>n(t=>{let n=A(e);return{viewport:{...t.viewport,dpr:n,initialDpr:t.viewport.initialDpr||n}}}),setFrameloop:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"always",t=r().clock;t.stop(),t.elapsedTime=0,"never"!==e&&(t.start(),t.elapsedTime=0),n(()=>({frameloop:e}))},previousRoot:void 0,internal:{active:!1,priority:0,frames:0,lastEvent:c.createRef(),interaction:[],hovered:new Map,subscribers:[],initialClick:[0,0],initialHits:[],capturedMap:new Map,subscribe:(e,t,n)=>{let l=r().internal;return l.priority=l.priority+ +(t>0),l.subscribers.push({ref:e,priority:t,store:n}),l.subscribers=l.subscribers.sort((e,t)=>e.priority-t.priority),()=>{let n=r().internal;null!=n&&n.subscribers&&(n.priority=n.priority-(t>0),n.subscribers=n.subscribers.filter(t=>t.ref!==e))}}}}}),r=n.getState(),l=r.size,a=r.viewport.dpr,i=r.camera;return n.subscribe(()=>{let{camera:e,size:t,viewport:r,gl:u,set:o}=n.getState();if(t.width!==l.width||t.height!==l.height||r.dpr!==a){var s;l=t,a=r.dpr,en(e,t),u.setPixelRatio(r.dpr);let n=null!=(s=t.updateStyle)?s:"undefined"!=typeof HTMLCanvasElement&&u.domElement instanceof HTMLCanvasElement;u.setSize(t.width,t.height,n)}e!==i&&(i=e,o(t=>({viewport:{...t.viewport,...t.viewport.getCurrentViewport(e)}})))}),n.subscribe(t=>e(t)),n};function ec(e,t){let n={callback:e};return t.add(n),()=>void t.delete(n)}let ef=new Set,ed=new Set,ep=new Set,eh=e=>ec(e,ef),em=e=>ec(e,ed),eg=e=>ec(e,ep);function ev(e,t){if(e.size)for(let{callback:n}of e.values())n(t)}function eb(e,t){switch(e){case"before":return ev(ef,t);case"after":return ev(ed,t);case"tail":return ev(ep,t)}}function ey(e,t,n){let i=t.clock.getDelta();for("never"===t.frameloop&&"number"==typeof e&&(i=e-t.clock.elapsedTime,t.clock.oldTime=t.clock.elapsedTime,t.clock.elapsedTime=e),l=t.internal.subscribers,r=0;r<l.length;r++)(a=l[r]).ref.current(a.store.getState(),i,n);return!t.internal.priority&&t.gl.render&&t.gl.render(t.scene,t.camera),t.internal.frames=Math.max(0,t.internal.frames-1),"always"===t.frameloop?1:t.internal.frames}function eS(e){let t=c.useRef(null);return T(()=>void(t.current=e.current.__r3f),[e]),t}function ew(){let e=c.useContext(eo);if(!e)throw Error("R3F: Hooks can only be used within the Canvas component!");return e}function ek(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e=>e,t=arguments.length>1?arguments[1]:void 0;return ew()(e,t)}function ex(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=ew(),r=n.getState().internal.subscribe,l=R(e);return T(()=>r(l,t,n),[t,r,n]),null}function e_(e){return c.useMemo(()=>B(e),[e])}let eE=new WeakMap;function eP(e,t){return function(n){for(var r=arguments.length,l=Array(r>1?r-1:0),a=1;a<r;a++)l[a-1]=arguments[a];let i=eE.get(n);return i||(i=new n,eE.set(n,i)),e&&e(i),Promise.all(l.map(e=>new Promise((n,r)=>i.load(e,e=>{e.scene&&Object.assign(e,B(e.scene)),n(e)},t,t=>r(Error("Could not load ".concat(e,": ").concat(null==t?void 0:t.message)))))))}}function eC(e,t,n,r){let l=Array.isArray(t)?t:[t],a=b(eP(n,r),[e,...l],{equal:W.equ});return Array.isArray(t)?a:a[0]}eC.preload=function(e,t,n){let r=Array.isArray(t)?t:[t];return y(eP(n),[e,...r])},eC.clear=function(e,t){return S([e,...Array.isArray(t)?t:[t]])};let ez=new Map,{invalidate:eN,advance:eI}=function(e){let t,n,r,l=!1,a=!1;function i(u){for(let s of(n=requestAnimationFrame(i),l=!0,t=0,eb("before",u),a=!0,e.values())){var o;(r=s.store.getState()).internal.active&&("always"===r.frameloop||r.internal.frames>0)&&!(null!=(o=r.gl.xr)&&o.isPresenting)&&(t+=ey(u,r))}if(a=!1,eb("after",u),0===t)return eb("tail",u),l=!1,cancelAnimationFrame(n)}return{loop:i,invalidate:function t(n){var r;let u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;if(!n)return e.forEach(e=>t(e.store.getState(),u));(null==(r=n.gl.xr)||!r.isPresenting)&&n.internal.active&&"never"!==n.frameloop&&(u>1?n.internal.frames=Math.min(60,n.internal.frames+u):a?n.internal.frames=2:n.internal.frames=1,l||(l=!0,requestAnimationFrame(i)))},advance:function(t){let n=!(arguments.length>1)||void 0===arguments[1]||arguments[1],r=arguments.length>2?arguments[2]:void 0,l=arguments.length>3?arguments[3]:void 0;if(n&&eb("before",t),r)ey(t,r,l);else for(let n of e.values())ey(t,n.store.getState());n&&eb("after",t)}}}(ez),{reconciler:eM,applyProps:eL}=function(e,t){function n(e,t,n){let r,{args:l=[],attach:a,...i}=t,u="".concat(e[0].toUpperCase()).concat(e.slice(1));if("primitive"===e){if(void 0===i.object)throw Error("R3F: Primitives without 'object' are invalid!");r=$(i.object,{type:e,root:n,attach:a,primitive:!0})}else{let t=C[u];if(!t)throw Error("R3F: ".concat(u," is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively"));if(!Array.isArray(l))throw Error("R3F: The args prop must be an array!");r=$(new t(...l),{type:e,root:n,attach:a,memoizedProps:{args:l}})}return void 0===r.__r3f.attach&&(r.isBufferGeometry?r.__r3f.attach="geometry":r.isMaterial&&(r.__r3f.attach="material")),"inject"!==u&&J(r,i),r}function r(e,t){let n=!1;if(t){var r,l;null!=(r=t.__r3f)&&r.attach?G(e,t,t.__r3f.attach):t.isObject3D&&e.isObject3D&&(e.add(t),n=!0),n||null==(l=e.__r3f)||l.objects.push(t),t.__r3f||$(t,{}),t.__r3f.parent=e,et(t),ee(t)}}function l(e,t,n){let r=!1;if(t){var l,a;if(null!=(l=t.__r3f)&&l.attach)G(e,t,t.__r3f.attach);else if(t.isObject3D&&e.isObject3D){t.parent=e,t.dispatchEvent({type:"added"}),e.dispatchEvent({type:"childadded",child:t});let l=e.children.filter(e=>e!==t),a=l.indexOf(n);e.children=[...l.slice(0,a),t,...l.slice(a)],r=!0}r||null==(a=e.__r3f)||a.objects.push(t),t.__r3f||$(t,{}),t.__r3f.parent=e,et(t),ee(t)}}function a(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];e&&[...e].forEach(e=>i(t,e,n))}function i(e,t,n){if(t){var r,l,i,u,o;t.__r3f&&(t.__r3f.parent=null),null!=(r=e.__r3f)&&r.objects&&(e.__r3f.objects=e.__r3f.objects.filter(e=>e!==t)),null!=(l=t.__r3f)&&l.attach?K(e,t,t.__r3f.attach):t.isObject3D&&e.isObject3D&&(e.remove(t),null!=(u=t.__r3f)&&u.root&&function(e,t){let{internal:n}=e.getState();n.interaction=n.interaction.filter(e=>e!==t),n.initialHits=n.initialHits.filter(e=>e!==t),n.hovered.forEach((e,r)=>{(e.eventObject===t||e.object===t)&&n.hovered.delete(r)}),n.capturedMap.forEach((e,r)=>{el(n.capturedMap,t,e,r)})}(Q(t),t));let s=null==(i=t.__r3f)?void 0:i.primitive,c=!s&&(void 0===n?null!==t.dispose:n);if(s||(a(null==(o=t.__r3f)?void 0:o.objects,t,c),a(t.children,t,c)),delete t.__r3f,c&&t.dispose&&"Scene"!==t.type){let e=()=>{try{t.dispose()}catch(e){}};"undefined"==typeof IS_REACT_ACT_ENVIRONMENT?(0,_.unstable_scheduleCallback)(_.unstable_IdlePriority,e):e()}ee(e)}}let u=()=>{};return{reconciler:x()({createInstance:n,removeChild:i,appendChild:r,appendInitialChild:r,insertBefore:l,supportsMutation:!0,isPrimaryRenderer:!1,supportsPersistence:!1,supportsHydration:!1,noTimeout:-1,appendChildToContainer:(e,t)=>{if(!t)return;let n=e.getState().scene;n.__r3f&&(n.__r3f.root=e,r(n,t))},removeChildFromContainer:(e,t)=>{t&&i(e.getState().scene,t)},insertInContainerBefore:(e,t,n)=>{if(!t||!n)return;let r=e.getState().scene;r.__r3f&&l(r,t,n)},getRootHostContext:()=>null,getChildHostContext:e=>e,finalizeInitialChildren(e){var t;return!!(null!=(t=null==e?void 0:e.__r3f)?t:{}).handlers},prepareUpdate(e,t,n,r){var l;if((null!=(l=null==e?void 0:e.__r3f)?l:{}).primitive&&r.object&&r.object!==e)return[!0];{let{args:t=[],children:l,...a}=r,{args:i=[],children:u,...o}=n;if(!Array.isArray(t))throw Error("R3F: the args prop must be an array!");if(t.some((e,t)=>e!==i[t]))return[!0];let s=X(e,a,o,!0);return s.changes.length?[!1,s]:null}},commitUpdate(e,t,l,a,u,o){let[s,c]=t;s?function(e,t,l,a){var u;let o=null==(u=e.__r3f)?void 0:u.parent;if(!o)return;let s=n(t,l,e.__r3f.root);if(e.children){for(let t of e.children)t.__r3f&&r(s,t);e.children=e.children.filter(e=>!e.__r3f)}e.__r3f.objects.forEach(e=>r(s,e)),e.__r3f.objects=[],e.__r3f.autoRemovedBeforeAppend||i(o,e),s.parent&&(s.__r3f.autoRemovedBeforeAppend=!0),r(o,s),s.raycast&&s.__r3f.eventCount&&Q(s).getState().internal.interaction.push(s),[a,a.alternate].forEach(e=>{null!==e&&(e.stateNode=s,e.ref&&("function"==typeof e.ref?e.ref(s):e.ref.current=s))})}(e,l,u,o):J(e,c)},commitMount(e,t,n,r){var l;let a=null!=(l=e.__r3f)?l:{};e.raycast&&a.handlers&&a.eventCount&&Q(e).getState().internal.interaction.push(e)},getPublicInstance:e=>e,prepareForCommit:()=>null,preparePortalMount:e=>$(e.getState().scene),resetAfterCommit:()=>{},shouldSetTextContent:()=>!1,clearContainer:()=>!1,hideInstance(e){var t;let{attach:n,parent:r}=null!=(t=e.__r3f)?t:{};n&&r&&K(r,e,n),e.isObject3D&&(e.visible=!1),ee(e)},unhideInstance(e,t){var n;let{attach:r,parent:l}=null!=(n=e.__r3f)?n:{};r&&l&&G(l,e,r),(e.isObject3D&&null==t.visible||t.visible)&&(e.visible=!0),ee(e)},createTextInstance:u,hideTextInstance:u,unhideTextInstance:u,getCurrentEventPriority:()=>t?t():f.DefaultEventPriority,beforeActiveInstanceBlur:()=>{},afterActiveInstanceBlur:()=>{},detachDeletedInstance:()=>{},now:"undefined"!=typeof performance&&W.fun(performance.now)?performance.now:W.fun(Date.now)?Date.now:()=>0,scheduleTimeout:W.fun(setTimeout)?setTimeout:void 0,cancelTimeout:W.fun(clearTimeout)?clearTimeout:void 0}),applyProps:J}}(0,function(){var e;let t="undefined"!=typeof self&&self||"undefined"!=typeof window&&window;if(!t)return f.DefaultEventPriority;switch(null==(e=t.event)?void 0:e.type){case"click":case"contextmenu":case"dblclick":case"pointercancel":case"pointerdown":case"pointerup":return f.DiscreteEventPriority;case"pointermove":case"pointerout":case"pointerover":case"pointerenter":case"pointerleave":case"wheel":return f.ContinuousEventPriority;default:return f.DefaultEventPriority}}),eT={objects:"shallow",strict:!1},eR=(e,t)=>{let n="function"==typeof e?e(t):e;return eu(n)?n:new s.WebGLRenderer({powerPreference:"high-performance",canvas:t,antialias:!0,alpha:!0,...e})};function ej(e){let t,n,r=ez.get(e),l=null==r?void 0:r.fiber,a=null==r?void 0:r.store;r&&console.warn("R3F.createRoot should only be called once!");let i="function"==typeof reportError?reportError:console.error,u=a||es(eN,eI),s=l||eM.createContainer(u,f.ConcurrentRoot,null,!1,null,"",i,null);r||ez.set(e,{fiber:s,store:u});let c=!1;return{configure(){var r,l;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{gl:i,size:s,scene:f,events:d,onCreated:p,shadows:h=!1,linear:m=!1,flat:g=!1,legacy:v=!1,orthographic:b=!1,frameloop:y="always",dpr:S=[1,2],performance:w,raycaster:k,camera:x,onPointerMissed:_}=a,E=u.getState(),P=E.gl;E.gl||E.set({gl:P=eR(i,e)});let C=E.raycaster;C||E.set({raycaster:C=new o.tBo});let{params:z,...N}=k||{};if(W.equ(N,C,eT)||eL(C,{...N}),W.equ(z,C.params,eT)||eL(C,{params:{...C.params,...z}}),!E.camera||E.camera===n&&!W.equ(n,x,eT)){n=x;let e=x instanceof o.i7d,t=e?x:b?new o.qUd(0,0,0,0,.1,1e3):new o.ubm(75,0,.1,1e3);!e&&(t.position.z=5,x&&(eL(t,x),("aspect"in x||"left"in x||"right"in x||"bottom"in x||"top"in x)&&(t.manual=!0,t.updateProjectionMatrix())),E.camera||null!=x&&x.rotation||t.lookAt(0,0,0)),E.set({camera:t}),C.camera=t}if(!E.scene){let e;null!=f&&f.isScene?e=f:(e=new o.Z58,f&&eL(e,f)),E.set({scene:$(e)})}if(!E.xr){let e=(e,t)=>{let n=u.getState();"never"!==n.frameloop&&eI(e,!0,n,t)},t=()=>{let t=u.getState();t.gl.xr.enabled=t.gl.xr.isPresenting,t.gl.xr.setAnimationLoop(t.gl.xr.isPresenting?e:null),t.gl.xr.isPresenting||eN(t)},n={connect(){let e=u.getState().gl;e.xr.addEventListener("sessionstart",t),e.xr.addEventListener("sessionend",t)},disconnect(){let e=u.getState().gl;e.xr.removeEventListener("sessionstart",t),e.xr.removeEventListener("sessionend",t)}};"function"==typeof(null==(r=P.xr)?void 0:r.addEventListener)&&n.connect(),E.set({xr:n})}if(P.shadowMap){let e=P.shadowMap.enabled,t=P.shadowMap.type;if(P.shadowMap.enabled=!!h,W.boo(h))P.shadowMap.type=o.Wk7;else if(W.str(h)){let e={basic:o.bTm,percentage:o.QP0,soft:o.Wk7,variance:o.RyA};P.shadowMap.type=null!=(l=e[h])?l:o.Wk7}else W.obj(h)&&Object.assign(P.shadowMap,h);(e!==P.shadowMap.enabled||t!==P.shadowMap.type)&&(P.shadowMap.needsUpdate=!0)}let M=I();M&&("enabled"in M?M.enabled=!v:"legacyMode"in M&&(M.legacyMode=v)),c||eL(P,{outputEncoding:m?3e3:3001,toneMapping:g?o.y_p:o.FV}),E.legacy!==v&&E.set(()=>({legacy:v})),E.linear!==m&&E.set(()=>({linear:m})),E.flat!==g&&E.set(()=>({flat:g})),!i||W.fun(i)||eu(i)||W.equ(i,P,eT)||eL(P,i),d&&!E.events.handlers&&E.set({events:d(u)});let L=function(e,t){let n="undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement;if(t){let{width:e,height:r,top:l,left:a,updateStyle:i=n}=t;return{width:e,height:r,top:l,left:a,updateStyle:i}}if("undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&e.parentElement){let{width:t,height:r,top:l,left:a}=e.parentElement.getBoundingClientRect();return{width:t,height:r,top:l,left:a,updateStyle:n}}return"undefined"!=typeof OffscreenCanvas&&e instanceof OffscreenCanvas?{width:e.width,height:e.height,top:0,left:0,updateStyle:n}:{width:0,height:0,top:0,left:0}}(e,s);return W.equ(L,E.size,eT)||E.setSize(L.width,L.height,L.updateStyle,L.top,L.left),S&&E.viewport.dpr!==A(S)&&E.setDpr(S),E.frameloop!==y&&E.setFrameloop(y),E.onPointerMissed||E.set({onPointerMissed:_}),w&&!W.equ(w,E.performance,eT)&&E.set(e=>({performance:{...e.performance,...w}})),t=p,c=!0,this},render(n){return c||this.configure(),eM.updateContainer((0,w.jsx)(eF,{store:u,children:n,onCreated:t,rootElement:e}),s,null,()=>void 0),u},unmount(){eO(e)}}}function eD(e,t,n){console.warn("R3F.render is no longer supported in React 18. Use createRoot instead!");let r=ej(t);return r.configure(n),r.render(e)}function eF(e){let{store:t,children:n,onCreated:r,rootElement:l}=e;return T(()=>{let e=t.getState();e.set(e=>({internal:{...e.internal,active:!0}})),r&&r(e),t.getState().events.connected||null==e.events.connect||e.events.connect(l)},[]),(0,w.jsx)(eo.Provider,{value:t,children:n})}function eO(e,t){let n=ez.get(e),r=null==n?void 0:n.fiber;if(r){let l=null==n?void 0:n.store.getState();l&&(l.internal.active=!1),eM.updateContainer(null,r,null,()=>{l&&setTimeout(()=>{try{var n,r,a,i;null==l.events.disconnect||l.events.disconnect(),null==(n=l.gl)||null==(r=n.renderLists)||null==r.dispose||r.dispose(),null==(a=l.gl)||null==a.forceContextLoss||a.forceContextLoss(),null!=(i=l.gl)&&i.xr&&l.xr.disconnect(),q(l),ez.delete(e),t&&t(e)}catch(e){}},500)})}}function eU(e,t,n){return(0,w.jsx)(eA,{children:e,container:t,state:n},t.uuid)}function eA(e){let{state:t={},children:n,container:r}=e,{events:l,size:a,...i}=t,u=ew(),[s]=c.useState(()=>new o.tBo),[f]=c.useState(()=>new o.I9Y),d=c.useCallback((e,t)=>{let n,c={...e};if(Object.keys(e).forEach(n=>{(ei.includes(n)||e[n]!==t[n]&&t[n])&&delete c[n]}),t&&a){let r=t.camera;n=e.viewport.getCurrentViewport(r,new o.Pq0,a),r!==e.camera&&en(r,a)}return{...c,scene:r,raycaster:s,pointer:f,mouse:f,previousRoot:u,events:{...e.events,...null==t?void 0:t.events,...l},size:{...e.size,...a},viewport:{...e.viewport,...n},...i}},[t]),[h]=c.useState(()=>{let e=u.getState();return p((t,n)=>({...e,scene:r,raycaster:s,pointer:f,mouse:f,previousRoot:u,events:{...e.events,...l},size:{...e.size,...a},...i,set:t,get:n,setEvents:e=>t(t=>({...t,events:{...t.events,...e}}))}))});return c.useEffect(()=>{let e=u.subscribe(e=>h.setState(t=>d(e,t)));return()=>{e()}},[d]),c.useEffect(()=>{h.setState(e=>d(u.getState(),e))},[d]),c.useEffect(()=>()=>{h.destroy()},[]),(0,w.jsx)(w.Fragment,{children:eM.createPortal((0,w.jsx)(eo.Provider,{value:h,children:n}),h,null)})}function eH(e){return eM.flushSync(e,void 0)}eM.injectIntoDevTools({bundleType:0,rendererPackageName:"@react-three/fiber",version:c.version});let eQ=c.unstable_act,eW={onClick:["click",!1],onContextMenu:["contextmenu",!1],onDoubleClick:["dblclick",!1],onWheel:["wheel",!0],onPointerDown:["pointerdown",!0],onPointerUp:["pointerup",!0],onPointerLeave:["pointerleave",!0],onPointerMove:["pointermove",!0],onPointerCancel:["pointercancel",!0],onLostPointerCapture:["lostpointercapture",!0]};function eB(e){let{handlePointer:t}=ea(e);return{priority:1,enabled:!0,compute(e,t,n){t.pointer.set(e.offsetX/t.size.width*2-1,-(2*(e.offsetY/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)},connected:void 0,handlers:Object.keys(eW).reduce((e,n)=>({...e,[n]:t(n)}),{}),update:()=>{var t;let{events:n,internal:r}=e.getState();null!=(t=r.lastEvent)&&t.current&&n.handlers&&n.handlers.onPointerMove(r.lastEvent.current)},connect:t=>{var n;let{set:r,events:l}=e.getState();null==l.disconnect||l.disconnect(),r(e=>({events:{...e.events,connected:t}})),Object.entries(null!=(n=l.handlers)?n:[]).forEach(e=>{let[n,r]=e,[l,a]=eW[n];t.addEventListener(l,r,{passive:a})})},disconnect:()=>{let{set:t,events:n}=e.getState();if(n.connected){var r;Object.entries(null!=(r=n.handlers)?r:[]).forEach(e=>{let[t,r]=e;if(n&&n.connected instanceof HTMLElement){let[e]=eW[t];n.connected.removeEventListener(e,r)}}),t(e=>({events:{...e.events,connected:void 0}}))}}}}},19166:(e,t,n)=>{e.exports=function(e){"use strict";var t,r,l,a,i,u={},o=n(12115),s=n(72407),c=Object.assign;function f(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var d=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,p=Symbol.for("react.element"),h=Symbol.for("react.portal"),m=Symbol.for("react.fragment"),g=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),y=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),w=Symbol.for("react.suspense"),k=Symbol.for("react.suspense_list"),x=Symbol.for("react.memo"),_=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var E=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var P=Symbol.iterator;function C(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=P&&e[P]||e["@@iterator"])?e:null}function z(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case m:return"Fragment";case h:return"Portal";case v:return"Profiler";case g:return"StrictMode";case w:return"Suspense";case k:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case y:return(e.displayName||"Context")+".Consumer";case b:return(e._context.displayName||"Context")+".Provider";case S:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case x:return null!==(t=e.displayName||null)?t:z(e.type)||"Memo";case _:t=e._payload,e=e._init;try{return z(e(t))}catch(e){}}return null}function N(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do 0!=(4098&(t=e).flags)&&(n=t.return),e=t.return;while(e)}return 3===t.tag?n:null}function I(e){if(N(e)!==e)throw Error(f(188))}function M(e){var t=e.alternate;if(!t){if(null===(t=N(e)))throw Error(f(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(null===l)break;var a=l.alternate;if(null===a){if(null!==(r=l.return)){n=r;continue}break}if(l.child===a.child){for(a=l.child;a;){if(a===n)return I(l),e;if(a===r)return I(l),t;a=a.sibling}throw Error(f(188))}if(n.return!==r.return)n=l,r=a;else{for(var i=!1,u=l.child;u;){if(u===n){i=!0,n=l,r=a;break}if(u===r){i=!0,r=l,n=a;break}u=u.sibling}if(!i){for(u=a.child;u;){if(u===n){i=!0,n=a,r=l;break}if(u===r){i=!0,r=a,n=l;break}u=u.sibling}if(!i)throw Error(f(189))}}if(n.alternate!==r)throw Error(f(190))}if(3!==n.tag)throw Error(f(188));return n.stateNode.current===n?e:t}function L(e){return null!==(e=M(e))?function e(t){if(5===t.tag||6===t.tag)return t;for(t=t.child;null!==t;){var n=e(t);if(null!==n)return n;t=t.sibling}return null}(e):null}var T,R=Array.isArray,j=e.getPublicInstance,D=e.getRootHostContext,F=e.getChildHostContext,O=e.prepareForCommit,U=e.resetAfterCommit,A=e.createInstance,H=e.appendInitialChild,Q=e.finalizeInitialChildren,W=e.prepareUpdate,B=e.shouldSetTextContent,q=e.createTextInstance,$=e.scheduleTimeout,V=e.cancelTimeout,Y=e.noTimeout,G=e.isPrimaryRenderer,K=e.supportsMutation,X=e.supportsPersistence,Z=e.supportsHydration,J=e.getInstanceFromNode,ee=e.preparePortalMount,et=e.getCurrentEventPriority,en=e.detachDeletedInstance,er=e.supportsMicrotasks,el=e.scheduleMicrotask,ea=e.supportsTestSelectors,ei=e.findFiberRoot,eu=e.getBoundingRect,eo=e.getTextContent,es=e.isHiddenSubtree,ec=e.matchAccessibilityRole,ef=e.setFocusIfFocusable,ed=e.setupIntersectionObserver,ep=e.appendChild,eh=e.appendChildToContainer,em=e.commitTextUpdate,eg=e.commitMount,ev=e.commitUpdate,eb=e.insertBefore,ey=e.insertInContainerBefore,eS=e.removeChild,ew=e.removeChildFromContainer,ek=e.resetTextContent,ex=e.hideInstance,e_=e.hideTextInstance,eE=e.unhideInstance,eP=e.unhideTextInstance,eC=e.clearContainer,ez=e.cloneInstance,eN=e.createContainerChildSet,eI=e.appendChildToContainerChildSet,eM=e.finalizeContainerChildren,eL=e.replaceContainerChildren,eT=e.cloneHiddenInstance,eR=e.cloneHiddenTextInstance,ej=e.canHydrateInstance,eD=e.canHydrateTextInstance,eF=e.canHydrateSuspenseInstance,eO=e.isSuspenseInstancePending,eU=e.isSuspenseInstanceFallback,eA=e.registerSuspenseInstanceRetry,eH=e.getNextHydratableSibling,eQ=e.getFirstHydratableChild,eW=e.getFirstHydratableChildWithinContainer,eB=e.getFirstHydratableChildWithinSuspenseInstance,eq=e.hydrateInstance,e$=e.hydrateTextInstance,eV=e.hydrateSuspenseInstance,eY=e.getNextHydratableInstanceAfterSuspenseInstance,eG=e.commitHydratedContainer,eK=e.commitHydratedSuspenseInstance,eX=e.clearSuspenseBoundary,eZ=e.clearSuspenseBoundaryFromContainer,eJ=e.shouldDeleteUnhydratedTailInstances,e0=e.didNotMatchHydratedContainerTextInstance,e1=e.didNotMatchHydratedTextInstance;function e2(e){if(void 0===T)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);T=t&&t[1]||""}return"\n"+T+e}var e3=!1;function e4(e,t){if(!e||e3)return"";e3=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(e){var r=e}Reflect.construct(e,[],t)}else{try{t.call()}catch(e){r=e}e.call(t.prototype)}else{try{throw Error()}catch(e){r=e}e()}}catch(t){if(t&&r&&"string"==typeof t.stack){for(var l=t.stack.split("\n"),a=r.stack.split("\n"),i=l.length-1,u=a.length-1;1<=i&&0<=u&&l[i]!==a[u];)u--;for(;1<=i&&0<=u;i--,u--)if(l[i]!==a[u]){if(1!==i||1!==u)do if(i--,0>--u||l[i]!==a[u]){var o="\n"+l[i].replace(" at new "," at ");return e.displayName&&o.includes("<anonymous>")&&(o=o.replace("<anonymous>",e.displayName)),o}while(1<=i&&0<=u);break}}}finally{e3=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?e2(e):""}var e5=Object.prototype.hasOwnProperty,e6=[],e8=-1;function e9(e){return{current:e}}function e7(e){0>e8||(e.current=e6[e8],e6[e8]=null,e8--)}function te(e,t){e6[++e8]=e.current,e.current=t}var tt={},tn=e9(tt),tr=e9(!1),tl=tt;function ta(e,t){var n=e.type.contextTypes;if(!n)return tt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l,a={};for(l in n)a[l]=t[l];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function ti(e){return null!=(e=e.childContextTypes)}function tu(){e7(tr),e7(tn)}function to(e,t,n){if(tn.current!==tt)throw Error(f(168));te(tn,t),te(tr,n)}function ts(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var l in r=r.getChildContext())if(!(l in t))throw Error(f(108,function(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return z(t);case 8:return t===g?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}(e)||"Unknown",l));return c({},n,r)}function tc(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||tt,tl=tn.current,te(tn,e),te(tr,tr.current),!0}function tf(e,t,n){var r=e.stateNode;if(!r)throw Error(f(169));n?(r.__reactInternalMemoizedMergedChildContext=e=ts(e,t,tl),e7(tr),e7(tn),te(tn,e)):e7(tr),te(tr,n)}var td=Math.clz32?Math.clz32:function(e){return 0==(e>>>=0)?32:31-(tp(e)/th|0)|0},tp=Math.log,th=Math.LN2,tm=64,tg=4194304;function tv(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 0x1000000:case 0x2000000:case 0x4000000:return 0x7c00000&e;case 0x8000000:return 0x8000000;case 0x10000000:return 0x10000000;case 0x20000000:return 0x20000000;case 0x40000000:return 0x40000000;default:return e}}function tb(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,l=e.suspendedLanes,a=e.pingedLanes,i=0xfffffff&n;if(0!==i){var u=i&~l;0!==u?r=tv(u):0!=(a&=i)&&(r=tv(a))}else 0!=(i=n&~l)?r=tv(i):0!==a&&(r=tv(a));if(0===r)return 0;if(0!==t&&t!==r&&0==(t&l)&&((l=r&-r)>=(a=t&-t)||16===l&&0!=(4194240&a)))return t;if(0!=(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)l=1<<(n=31-td(t)),r|=e[n],t&=~l;return r}function ty(e){return 0!=(e=-0x40000001&e.pendingLanes)?e:0x40000000&e?0x40000000:0}function tS(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function tw(e,t,n){e.pendingLanes|=t,0x20000000!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-td(t)]=n}function tk(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-td(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var tx=0;function t_(e){return 1<(e&=-e)?4<e?0!=(0xfffffff&e)?16:0x20000000:4:1}var tE=s.unstable_scheduleCallback,tP=s.unstable_cancelCallback,tC=s.unstable_shouldYield,tz=s.unstable_requestPaint,tN=s.unstable_now,tI=s.unstable_ImmediatePriority,tM=s.unstable_UserBlockingPriority,tL=s.unstable_NormalPriority,tT=s.unstable_IdlePriority,tR=null,tj=null,tD="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},tF=null,tO=!1,tU=!1;function tA(e){null===tF?tF=[e]:tF.push(e)}function tH(){if(!tU&&null!==tF){tU=!0;var e=0,t=tx;try{var n=tF;for(tx=1;e<n.length;e++){var r=n[e];do r=r(!0);while(null!==r)}tF=null,tO=!1}catch(t){throw null!==tF&&(tF=tF.slice(e+1)),tE(tI,tH),t}finally{tx=t,tU=!1}}return null}var tQ=d.ReactCurrentBatchConfig;function tW(e,t){if(tD(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!e5.call(t,l)||!tD(e[l],t[l]))return!1}return!0}function tB(e,t){if(e&&e.defaultProps)for(var n in t=c({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}var tq=e9(null),t$=null,tV=null,tY=null;function tG(){tY=tV=t$=null}function tK(e,t,n){G?(te(tq,t._currentValue),t._currentValue=n):(te(tq,t._currentValue2),t._currentValue2=n)}function tX(e){var t=tq.current;e7(tq),G?e._currentValue=t:e._currentValue2=t}function tZ(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function tJ(e,t){t$=e,tY=tV=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!=(e.lanes&t)&&(r0=!0),e.firstContext=null)}function t0(e){var t=G?e._currentValue:e._currentValue2;if(tY!==e)if(e={context:e,memoizedValue:t,next:null},null===tV){if(null===t$)throw Error(f(308));tV=e,t$.dependencies={lanes:0,firstContext:e}}else tV=tV.next=e;return t}var t1=null,t2=!1;function t3(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function t4(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function t5(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function t6(e,t){var n=e.updateQueue;null!==n&&(n=n.shared,null!==lY&&0!=(1&e.mode)&&0==(2&lV)?(null===(e=n.interleaved)?(t.next=t,null===t1?t1=[n]:t1.push(n)):(t.next=e.next,e.next=t),n.interleaved=t):(null===(e=n.pending)?t.next=t:(t.next=e.next,e.next=t),n.pending=t))}function t8(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!=(4194240&n))){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,tk(e,n)}}function t9(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var l=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===a?l=a=i:a=a.next=i,n=n.next}while(null!==n);null===a?l=a=t:a=a.next=t}else l=a=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:a,shared:r.shared,effects:r.effects},e.updateQueue=n;return}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function t7(e,t,n,r){var l=e.updateQueue;t2=!1;var a=l.firstBaseUpdate,i=l.lastBaseUpdate,u=l.shared.pending;if(null!==u){l.shared.pending=null;var o=u,s=o.next;o.next=null,null===i?a=s:i.next=s,i=o;var f=e.alternate;null!==f&&(u=(f=f.updateQueue).lastBaseUpdate)!==i&&(null===u?f.firstBaseUpdate=s:u.next=s,f.lastBaseUpdate=o)}if(null!==a){var d=l.baseState;for(i=0,f=s=o=null,u=a;;){var p=u.lane,h=u.eventTime;if((r&p)===p){null!==f&&(f=f.next={eventTime:h,lane:0,tag:u.tag,payload:u.payload,callback:u.callback,next:null});e:{var m=e,g=u;switch(p=t,h=n,g.tag){case 1:if("function"==typeof(m=g.payload)){d=m.call(h,d,p);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null==(p="function"==typeof(m=g.payload)?m.call(h,d,p):m))break e;d=c({},d,p);break e;case 2:t2=!0}}null!==u.callback&&0!==u.lane&&(e.flags|=64,null===(p=l.effects)?l.effects=[u]:p.push(u))}else h={eventTime:h,lane:p,tag:u.tag,payload:u.payload,callback:u.callback,next:null},null===f?(s=f=h,o=d):f=f.next=h,i|=p;if(null===(u=u.next))if(null===(u=l.shared.pending))break;else u=(p=u).next,p.next=null,l.lastBaseUpdate=p,l.shared.pending=null}if(null===f&&(o=d),l.baseState=o,l.firstBaseUpdate=s,l.lastBaseUpdate=f,null!==(t=l.shared.interleaved)){l=t;do i|=l.lane,l=l.next;while(l!==t)}else null===a&&(l.shared.lanes=0);l1|=i,e.lanes=i,e.memoizedState=d}}function ne(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(null!==l){if(r.callback=null,r=n,"function"!=typeof l)throw Error(f(191,l));l.call(r)}}}var nt=(new o.Component).refs;function nn(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:c({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var nr={isMounted:function(e){return!!(e=e._reactInternals)&&N(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=as(),l=ac(e),a=t5(r,l);a.payload=t,null!=n&&(a.callback=n),t6(e,a),null!==(t=af(e,l,r))&&t8(t,e,l)},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=as(),l=ac(e),a=t5(r,l);a.tag=1,a.payload=t,null!=n&&(a.callback=n),t6(e,a),null!==(t=af(e,l,r))&&t8(t,e,l)},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=as(),r=ac(e),l=t5(n,r);l.tag=2,null!=t&&(l.callback=t),t6(e,l),null!==(t=af(e,r,n))&&t8(t,e,r)}};function nl(e,t,n,r,l,a,i){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,i):!t.prototype||!t.prototype.isPureReactComponent||!tW(n,r)||!tW(l,a)}function na(e,t,n){var r=!1,l=tt,a=t.contextType;return"object"==typeof a&&null!==a?a=t0(a):(l=ti(t)?tl:tn.current,a=(r=null!=(r=t.contextTypes))?ta(e,l):tt),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=nr,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=a),t}function ni(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&nr.enqueueReplaceState(t,t.state,null)}function nu(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs=nt,t3(e);var a=t.contextType;"object"==typeof a&&null!==a?l.context=t0(a):l.context=ta(e,a=ti(t)?tl:tn.current),l.state=e.memoizedState,"function"==typeof(a=t.getDerivedStateFromProps)&&(nn(e,t,a,n),l.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof l.getSnapshotBeforeUpdate||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||(t=l.state,"function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount(),t!==l.state&&nr.enqueueReplaceState(l,l.state,null),t7(e,n,l,r),l.state=e.memoizedState),"function"==typeof l.componentDidMount&&(e.flags|=4194308)}var no=[],ns=0,nc=null,nf=0,nd=[],np=0,nh=null,nm=1,ng="";function nv(e,t){no[ns++]=nf,no[ns++]=nc,nc=e,nf=t}function nb(e,t,n){nd[np++]=nm,nd[np++]=ng,nd[np++]=nh,nh=e;var r=nm;e=ng;var l=32-td(r)-1;r&=~(1<<l),n+=1;var a=32-td(t)+l;if(30<a){var i=l-l%5;a=(r&(1<<i)-1).toString(32),r>>=i,l-=i,nm=1<<32-td(t)+l|n<<l|r,ng=a+e}else nm=1<<a|n<<l|r,ng=e}function ny(e){null!==e.return&&(nv(e,1),nb(e,1,0))}function nS(e){for(;e===nc;)nc=no[--ns],no[ns]=null,nf=no[--ns],no[ns]=null;for(;e===nh;)nh=nd[--np],nd[np]=null,ng=nd[--np],nd[np]=null,nm=nd[--np],nd[np]=null}var nw=null,nk=null,nx=!1,n_=!1,nE=null;function nP(e,t){var n=aF(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function nC(e,t){switch(e.tag){case 5:return null!==(t=ej(t,e.type,e.pendingProps))&&(e.stateNode=t,nw=e,nk=eQ(t),!0);case 6:return null!==(t=eD(t,e.pendingProps))&&(e.stateNode=t,nw=e,nk=null,!0);case 13:if(null!==(t=eF(t))){var n=null!==nh?{id:nm,overflow:ng}:null;return e.memoizedState={dehydrated:t,treeContext:n,retryLane:0x40000000},(n=aF(18,null,null,0)).stateNode=t,n.return=e,e.child=n,nw=e,nk=null,!0}return!1;default:return!1}}function nz(e){return 0!=(1&e.mode)&&0==(128&e.flags)}function nN(e){if(nx){var t=nk;if(t){var n=t;if(!nC(e,t)){if(nz(e))throw Error(f(418));t=eH(n);var r=nw;t&&nC(e,t)?nP(r,n):(e.flags=-4097&e.flags|2,nx=!1,nw=e)}}else{if(nz(e))throw Error(f(418));e.flags=-4097&e.flags|2,nx=!1,nw=e}}}function nI(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;nw=e}function nM(e){if(!Z||e!==nw)return!1;if(!nx)return nI(e),nx=!0,!1;if(3!==e.tag&&(5!==e.tag||eJ(e.type)&&!B(e.type,e.memoizedProps))){var t=nk;if(t){if(nz(e)){for(e=nk;e;)e=eH(e);throw Error(f(418))}for(;t;)nP(e,t),t=eH(t)}}if(nI(e),13===e.tag){if(!Z)throw Error(f(316));if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(f(317));nk=eY(e)}else nk=nw?eH(e.stateNode):null;return!0}function nL(){Z&&(nk=nw=null,n_=nx=!1)}function nT(e){null===nE?nE=[e]:nE.push(e)}function nR(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(f(309));var r=n.stateNode}if(!r)throw Error(f(147,e));var l=r,a=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===a?t.ref:((t=function(e){var t=l.refs;t===nt&&(t=l.refs={}),null===e?delete t[a]:t[a]=e})._stringRef=a,t)}if("string"!=typeof e)throw Error(f(284));if(!n._owner)throw Error(f(290,e))}return e}function nj(e,t){throw Error(f(31,"[object Object]"===(e=Object.prototype.toString.call(t))?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function nD(e){return(0,e._init)(e._payload)}function nF(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function l(e,t){return(e=aU(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return(t.index=r,e)?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function u(e,t,n,r){return null===t||6!==t.tag?(t=aW(n,e.mode,r)).return=e:(t=l(t,n)).return=e,t}function o(e,t,n,r){var a=n.type;return a===m?c(e,t,n.props.children,r,n.key):(null!==t&&(t.elementType===a||"object"==typeof a&&null!==a&&a.$$typeof===_&&nD(a)===t.type)?(r=l(t,n.props)).ref=nR(e,t,n):(r=aA(n.type,n.key,n.props,null,e.mode,r)).ref=nR(e,t,n),r.return=e,r)}function s(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?(t=aB(n,e.mode,r)).return=e:(t=l(t,n.children||[])).return=e,t}function c(e,t,n,r,a){return null===t||7!==t.tag?(t=aH(n,e.mode,r,a)).return=e:(t=l(t,n)).return=e,t}function d(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=aW(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case p:return(n=aA(t.type,t.key,t.props,null,e.mode,n)).ref=nR(e,null,t),n.return=e,n;case h:return(t=aB(t,e.mode,n)).return=e,t;case _:return d(e,(0,t._init)(t._payload),n)}if(R(t)||C(t))return(t=aH(t,e.mode,n,null)).return=e,t;nj(e,t)}return null}function g(e,t,n,r){var l=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==l?null:u(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case p:return n.key===l?o(e,t,n,r):null;case h:return n.key===l?s(e,t,n,r):null;case _:return g(e,t,(l=n._init)(n._payload),r)}if(R(n)||C(n))return null!==l?null:c(e,t,n,r,null);nj(e,n)}return null}function v(e,t,n,r,l){if("string"==typeof r&&""!==r||"number"==typeof r)return u(t,e=e.get(n)||null,""+r,l);if("object"==typeof r&&null!==r){switch(r.$$typeof){case p:return o(t,e=e.get(null===r.key?n:r.key)||null,r,l);case h:return s(t,e=e.get(null===r.key?n:r.key)||null,r,l);case _:return v(e,t,n,(0,r._init)(r._payload),l)}if(R(r)||C(r))return c(t,e=e.get(n)||null,r,l,null);nj(t,r)}return null}return function u(o,s,c,b){if("object"==typeof c&&null!==c&&c.type===m&&null===c.key&&(c=c.props.children),"object"==typeof c&&null!==c){switch(c.$$typeof){case p:e:{for(var y=c.key,S=s;null!==S;){if(S.key===y){if((y=c.type)===m){if(7===S.tag){n(o,S.sibling),(s=l(S,c.props.children)).return=o,o=s;break e}}else if(S.elementType===y||"object"==typeof y&&null!==y&&y.$$typeof===_&&nD(y)===S.type){n(o,S.sibling),(s=l(S,c.props)).ref=nR(o,S,c),s.return=o,o=s;break e}n(o,S);break}t(o,S),S=S.sibling}c.type===m?((s=aH(c.props.children,o.mode,b,c.key)).return=o,o=s):((b=aA(c.type,c.key,c.props,null,o.mode,b)).ref=nR(o,s,c),b.return=o,o=b)}return i(o);case h:e:{for(S=c.key;null!==s;){if(s.key===S)if(4===s.tag&&s.stateNode.containerInfo===c.containerInfo&&s.stateNode.implementation===c.implementation){n(o,s.sibling),(s=l(s,c.children||[])).return=o,o=s;break e}else{n(o,s);break}t(o,s),s=s.sibling}(s=aB(c,o.mode,b)).return=o,o=s}return i(o);case _:return u(o,s,(S=c._init)(c._payload),b)}if(R(c))return function(l,i,u,o){for(var s=null,c=null,f=i,p=i=0,h=null;null!==f&&p<u.length;p++){f.index>p?(h=f,f=null):h=f.sibling;var m=g(l,f,u[p],o);if(null===m){null===f&&(f=h);break}e&&f&&null===m.alternate&&t(l,f),i=a(m,i,p),null===c?s=m:c.sibling=m,c=m,f=h}if(p===u.length)return n(l,f),nx&&nv(l,p),s;if(null===f){for(;p<u.length;p++)null!==(f=d(l,u[p],o))&&(i=a(f,i,p),null===c?s=f:c.sibling=f,c=f);return nx&&nv(l,p),s}for(f=r(l,f);p<u.length;p++)null!==(h=v(f,l,p,u[p],o))&&(e&&null!==h.alternate&&f.delete(null===h.key?p:h.key),i=a(h,i,p),null===c?s=h:c.sibling=h,c=h);return e&&f.forEach(function(e){return t(l,e)}),nx&&nv(l,p),s}(o,s,c,b);if(C(c))return function(l,i,u,o){var s=C(u);if("function"!=typeof s)throw Error(f(150));if(null==(u=s.call(u)))throw Error(f(151));for(var c=s=null,p=i,h=i=0,m=null,b=u.next();null!==p&&!b.done;h++,b=u.next()){p.index>h?(m=p,p=null):m=p.sibling;var y=g(l,p,b.value,o);if(null===y){null===p&&(p=m);break}e&&p&&null===y.alternate&&t(l,p),i=a(y,i,h),null===c?s=y:c.sibling=y,c=y,p=m}if(b.done)return n(l,p),nx&&nv(l,h),s;if(null===p){for(;!b.done;h++,b=u.next())null!==(b=d(l,b.value,o))&&(i=a(b,i,h),null===c?s=b:c.sibling=b,c=b);return nx&&nv(l,h),s}for(p=r(l,p);!b.done;h++,b=u.next())null!==(b=v(p,l,h,b.value,o))&&(e&&null!==b.alternate&&p.delete(null===b.key?h:b.key),i=a(b,i,h),null===c?s=b:c.sibling=b,c=b);return e&&p.forEach(function(e){return t(l,e)}),nx&&nv(l,h),s}(o,s,c,b);nj(o,c)}return"string"==typeof c&&""!==c||"number"==typeof c?(c=""+c,null!==s&&6===s.tag?(n(o,s.sibling),(s=l(s,c)).return=o):(n(o,s),(s=aW(c,o.mode,b)).return=o),i(o=s)):n(o,s)}}var nO=nF(!0),nU=nF(!1),nA={},nH=e9(nA),nQ=e9(nA),nW=e9(nA);function nB(e){if(e===nA)throw Error(f(174));return e}function nq(e,t){te(nW,t),te(nQ,e),te(nH,nA),e=D(t),e7(nH),te(nH,e)}function n$(){e7(nH),e7(nQ),e7(nW)}function nV(e){var t=nB(nW.current),n=nB(nH.current);t=F(n,e.type,t),n!==t&&(te(nQ,e),te(nH,t))}function nY(e){nQ.current===e&&(e7(nH),e7(nQ))}var nG=e9(0);function nK(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||eO(n)||eU(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!=(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var nX=[];function nZ(){for(var e=0;e<nX.length;e++){var t=nX[e];G?t._workInProgressVersionPrimary=null:t._workInProgressVersionSecondary=null}nX.length=0}var nJ=d.ReactCurrentDispatcher,n0=d.ReactCurrentBatchConfig,n1=0,n2=null,n3=null,n4=null,n5=!1,n6=!1,n8=0,n9=0;function n7(){throw Error(f(321))}function re(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!tD(e[n],t[n]))return!1;return!0}function rt(e,t,n,r,l,a){if(n1=a,n2=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,nJ.current=null===e||null===e.memoizedState?rF:rO,e=n(r,l),n6){a=0;do{if(n6=!1,n8=0,25<=a)throw Error(f(301));a+=1,n4=n3=null,t.updateQueue=null,nJ.current=rU,e=n(r,l)}while(n6)}if(nJ.current=rD,t=null!==n3&&null!==n3.next,n1=0,n4=n3=n2=null,n5=!1,t)throw Error(f(300));return e}function rn(){var e=0!==n8;return n8=0,e}function rr(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===n4?n2.memoizedState=n4=e:n4=n4.next=e,n4}function rl(){if(null===n3){var e=n2.alternate;e=null!==e?e.memoizedState:null}else e=n3.next;var t=null===n4?n2.memoizedState:n4.next;if(null!==t)n4=t,n3=e;else{if(null===e)throw Error(f(310));e={memoizedState:(n3=e).memoizedState,baseState:n3.baseState,baseQueue:n3.baseQueue,queue:n3.queue,next:null},null===n4?n2.memoizedState=n4=e:n4=n4.next=e}return n4}function ra(e,t){return"function"==typeof t?t(e):t}function ri(e){var t=rl(),n=t.queue;if(null===n)throw Error(f(311));n.lastRenderedReducer=e;var r=n3,l=r.baseQueue,a=n.pending;if(null!==a){if(null!==l){var i=l.next;l.next=a.next,a.next=i}r.baseQueue=l=a,n.pending=null}if(null!==l){a=l.next,r=r.baseState;var u=i=null,o=null,s=a;do{var c=s.lane;if((n1&c)===c)null!==o&&(o=o.next={lane:0,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null}),r=s.hasEagerState?s.eagerState:e(r,s.action);else{var d={lane:c,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null};null===o?(u=o=d,i=r):o=o.next=d,n2.lanes|=c,l1|=c}s=s.next}while(null!==s&&s!==a);null===o?i=r:o.next=u,tD(r,t.memoizedState)||(r0=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=o,n.lastRenderedState=r}if(null!==(e=n.interleaved)){l=e;do a=l.lane,n2.lanes|=a,l1|=a,l=l.next;while(l!==e)}else null===l&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ru(e){var t=rl(),n=t.queue;if(null===n)throw Error(f(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,a=t.memoizedState;if(null!==l){n.pending=null;var i=l=l.next;do a=e(a,i.action),i=i.next;while(i!==l);tD(a,t.memoizedState)||(r0=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function ro(){}function rs(e,t){var n=n2,r=rl(),l=t(),a=!tD(r.memoizedState,l);if(a&&(r.memoizedState=l,r0=!0),r=r.queue,rS(rd.bind(null,n,r,e),[e]),r.getSnapshot!==t||a||null!==n4&&1&n4.memoizedState.tag){if(n.flags|=2048,rm(9,rf.bind(null,n,r,l,t),void 0,null),null===lY)throw Error(f(349));0!=(30&n1)||rc(n,t,l)}return l}function rc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=n2.updateQueue)?(t={lastEffect:null,stores:null},n2.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function rf(e,t,n,r){t.value=n,t.getSnapshot=r,rp(t)&&af(e,1,-1)}function rd(e,t,n){return n(function(){rp(t)&&af(e,1,-1)})}function rp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!tD(e,n)}catch(e){return!0}}function rh(e){var t=rr();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,t.queue=e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ra,lastRenderedState:e},e=e.dispatch=rM.bind(null,n2,e),[t.memoizedState,e]}function rm(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=n2.updateQueue)?(t={lastEffect:null,stores:null},n2.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function rg(){return rl().memoizedState}function rv(e,t,n,r){var l=rr();n2.flags|=e,l.memoizedState=rm(1|t,n,void 0,void 0===r?null:r)}function rb(e,t,n,r){var l=rl();r=void 0===r?null:r;var a=void 0;if(null!==n3){var i=n3.memoizedState;if(a=i.destroy,null!==r&&re(r,i.deps)){l.memoizedState=rm(t,n,a,r);return}}n2.flags|=e,l.memoizedState=rm(1|t,n,a,r)}function ry(e,t){return rv(8390656,8,e,t)}function rS(e,t){return rb(2048,8,e,t)}function rw(e,t){return rb(4,2,e,t)}function rk(e,t){return rb(4,4,e,t)}function rx(e,t){return"function"==typeof t?(t(e=e()),function(){t(null)}):null!=t?(t.current=e=e(),function(){t.current=null}):void 0}function r_(e,t,n){return n=null!=n?n.concat([e]):null,rb(4,4,rx.bind(null,t,e),n)}function rE(){}function rP(e,t){var n=rl();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&re(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function rC(e,t){var n=rl();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&re(t,r[1])?r[0]:(n.memoizedState=[e=e(),t],e)}function rz(e,t){var n=tx;tx=0!==n&&4>n?n:4,e(!0);var r=n0.transition;n0.transition={};try{e(!1),t()}finally{tx=n,n0.transition=r}}function rN(){return rl().memoizedState}function rI(e,t,n){var r=ac(e);n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},rL(e)?rT(t,n):(rR(e,t,n),null!==(e=af(e,r,n=as()))&&rj(e,t,r))}function rM(e,t,n){var r=ac(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(rL(e))rT(t,l);else{rR(e,t,l);var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var i=t.lastRenderedState,u=a(i,n);if(l.hasEagerState=!0,l.eagerState=u,tD(u,i))return}catch(e){}finally{}null!==(e=af(e,r,n=as()))&&rj(e,t,r)}}function rL(e){var t=e.alternate;return e===n2||null!==t&&t===n2}function rT(e,t){n6=n5=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function rR(e,t,n){null!==lY&&0!=(1&e.mode)&&0==(2&lV)?(null===(e=t.interleaved)?(n.next=n,null===t1?t1=[t]:t1.push(t)):(n.next=e.next,e.next=n),t.interleaved=n):(null===(e=t.pending)?n.next=n:(n.next=e.next,e.next=n),t.pending=n)}function rj(e,t,n){if(0!=(4194240&n)){var r=t.lanes;r&=e.pendingLanes,t.lanes=n|=r,tk(e,n)}}var rD={readContext:t0,useCallback:n7,useContext:n7,useEffect:n7,useImperativeHandle:n7,useInsertionEffect:n7,useLayoutEffect:n7,useMemo:n7,useReducer:n7,useRef:n7,useState:n7,useDebugValue:n7,useDeferredValue:n7,useTransition:n7,useMutableSource:n7,useSyncExternalStore:n7,useId:n7,unstable_isNewReconciler:!1},rF={readContext:t0,useCallback:function(e,t){return rr().memoizedState=[e,void 0===t?null:t],e},useContext:t0,useEffect:ry,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,rv(4194308,4,rx.bind(null,t,e),n)},useLayoutEffect:function(e,t){return rv(4194308,4,e,t)},useInsertionEffect:function(e,t){return rv(4,2,e,t)},useMemo:function(e,t){return t=void 0===t?null:t,rr().memoizedState=[e=e(),t],e},useReducer:function(e,t,n){var r=rr();return r.memoizedState=r.baseState=t=void 0!==n?n(t):t,r.queue=e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},e=e.dispatch=rI.bind(null,n2,e),[r.memoizedState,e]},useRef:function(e){return rr().memoizedState=e={current:e}},useState:rh,useDebugValue:rE,useDeferredValue:function(e){var t=rh(e),n=t[0],r=t[1];return ry(function(){var t=n0.transition;n0.transition={};try{r(e)}finally{n0.transition=t}},[e]),n},useTransition:function(){var e=rh(!1),t=e[0];return e=rz.bind(null,e[1]),rr().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=n2,l=rr();if(nx){if(void 0===n)throw Error(f(407));n=n()}else{if(n=t(),null===lY)throw Error(f(349));0!=(30&n1)||rc(r,t,n)}l.memoizedState=n;var a={value:n,getSnapshot:t};return l.queue=a,ry(rd.bind(null,r,a,e),[e]),r.flags|=2048,rm(9,rf.bind(null,r,a,n,t),void 0,null),n},useId:function(){var e=rr(),t=lY.identifierPrefix;if(nx){var n=ng,r=nm;t=":"+t+"R"+(n=(r&~(1<<32-td(r)-1)).toString(32)+n),0<(n=n8++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=n9++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},rO={readContext:t0,useCallback:rP,useContext:t0,useEffect:rS,useImperativeHandle:r_,useInsertionEffect:rw,useLayoutEffect:rk,useMemo:rC,useReducer:ri,useRef:rg,useState:function(){return ri(ra)},useDebugValue:rE,useDeferredValue:function(e){var t=ri(ra),n=t[0],r=t[1];return rS(function(){var t=n0.transition;n0.transition={};try{r(e)}finally{n0.transition=t}},[e]),n},useTransition:function(){return[ri(ra)[0],rl().memoizedState]},useMutableSource:ro,useSyncExternalStore:rs,useId:rN,unstable_isNewReconciler:!1},rU={readContext:t0,useCallback:rP,useContext:t0,useEffect:rS,useImperativeHandle:r_,useInsertionEffect:rw,useLayoutEffect:rk,useMemo:rC,useReducer:ru,useRef:rg,useState:function(){return ru(ra)},useDebugValue:rE,useDeferredValue:function(e){var t=ru(ra),n=t[0],r=t[1];return rS(function(){var t=n0.transition;n0.transition={};try{r(e)}finally{n0.transition=t}},[e]),n},useTransition:function(){return[ru(ra)[0],rl().memoizedState]},useMutableSource:ro,useSyncExternalStore:rs,useId:rN,unstable_isNewReconciler:!1};function rA(e,t){try{var n="",r=t;do n+=function(e){switch(e.tag){case 5:return e2(e.type);case 16:return e2("Lazy");case 13:return e2("Suspense");case 19:return e2("SuspenseList");case 0:case 2:case 15:return e=e4(e.type,!1);case 11:return e=e4(e.type.render,!1);case 1:return e=e4(e.type,!0);default:return""}}(r),r=r.return;while(r);var l=n}catch(e){l="\nError generating stack: "+e.message+"\n"+e.stack}return{value:e,source:t,stack:l}}function rH(e,t){try{console.error(t.value)}catch(e){setTimeout(function(){throw e})}}var rQ="function"==typeof WeakMap?WeakMap:Map;function rW(e,t,n){(n=t5(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){l7||(l7=!0,ae=r),rH(e,t)},n}function rB(e,t,n){(n=t5(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){rH(e,t)}}var a=e.stateNode;return null!==a&&"function"==typeof a.componentDidCatch&&(n.callback=function(){rH(e,t),"function"!=typeof r&&(null===at?at=new Set([this]):at.add(this));var n=t.stack;this.componentDidCatch(t.value,{componentStack:null!==n?n:""})}),n}function rq(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new rQ;var l=new Set;r.set(t,l)}else void 0===(l=r.get(t))&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=aL.bind(null,e,t,n),t.then(e,e))}function r$(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function rV(e,t,n,r,l){return 0==(1&e.mode)?e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=t5(-1,1)).tag=2,t6(n,t))),n.lanes|=1):(e.flags|=65536,e.lanes=l),e}function rY(e){e.flags|=4}function rG(e,t){if(null!==e&&e.child===t.child)return!0;if(0!=(16&t.flags))return!1;for(e=t.child;null!==e;){if(0!=(12854&e.flags)||0!=(12854&e.subtreeFlags))return!1;e=e.sibling}return!0}if(K)t=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)H(e,n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},r=function(){},l=function(e,t,n,r,l){if((e=e.memoizedProps)!==r){var a=t.stateNode;(t.updateQueue=n=W(a,n,e,r,l,nB(nH.current)))&&rY(t)}},a=function(e,t,n,r){n!==r&&rY(t)};else if(X){t=function(e,n,r,l){for(var a=n.child;null!==a;){if(5===a.tag){var i=a.stateNode;r&&l&&(i=eT(i,a.type,a.memoizedProps,a)),H(e,i)}else if(6===a.tag)i=a.stateNode,r&&l&&(i=eR(i,a.memoizedProps,a)),H(e,i);else if(4!==a.tag){if(22===a.tag&&null!==a.memoizedState)null!==(i=a.child)&&(i.return=a),t(e,a,!0,!0);else if(null!==a.child){a.child.return=a,a=a.child;continue}}if(a===n)break;for(;null===a.sibling;){if(null===a.return||a.return===n)return;a=a.return}a.sibling.return=a.return,a=a.sibling}};var rK=function(e,t,n,r){for(var l=t.child;null!==l;){if(5===l.tag){var a=l.stateNode;n&&r&&(a=eT(a,l.type,l.memoizedProps,l)),eI(e,a)}else if(6===l.tag)a=l.stateNode,n&&r&&(a=eR(a,l.memoizedProps,l)),eI(e,a);else if(4!==l.tag){if(22===l.tag&&null!==l.memoizedState)null!==(a=l.child)&&(a.return=l),rK(e,l,!0,!0);else if(null!==l.child){l.child.return=l,l=l.child;continue}}if(l===t)break;for(;null===l.sibling;){if(null===l.return||l.return===t)return;l=l.return}l.sibling.return=l.return,l=l.sibling}};r=function(e,t){var n=t.stateNode;if(!rG(e,t)){var r=eN(e=n.containerInfo);rK(r,t,!1,!1),n.pendingChildren=r,rY(t),eM(e,r)}},l=function(e,n,r,l,a){var i=e.stateNode,u=e.memoizedProps;if((e=rG(e,n))&&u===l)n.stateNode=i;else{var o=n.stateNode,s=nB(nH.current),c=null;u!==l&&(c=W(o,r,u,l,a,s)),e&&null===c?n.stateNode=i:(Q(i=ez(i,c,r,u,l,n,e,o),r,l,a,s)&&rY(n),n.stateNode=i,e?rY(n):t(i,n,!1,!1))}},a=function(e,t,n,r){n!==r?(t.stateNode=q(r,e=nB(nW.current),n=nB(nH.current),t),rY(t)):t.stateNode=e.stateNode}}else r=function(){},l=function(){},a=function(){};function rX(e,t){if(!nx)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function rZ(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;null!==l;)n|=l.lanes|l.childLanes,r|=0xe00000&l.subtreeFlags,r|=0xe00000&l.flags,l.return=e,l=l.sibling;else for(l=e.child;null!==l;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}var rJ=d.ReactCurrentOwner,r0=!1;function r1(e,t,n,r){t.child=null===e?nU(t,null,n,r):nO(t,e.child,n,r)}function r2(e,t,n,r,l){n=n.render;var a=t.ref;return(tJ(t,l),r=rt(e,t,n,r,a,l),n=rn(),null===e||r0)?(nx&&n&&ny(t),t.flags|=1,r1(e,t,r,l),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,lc(e,t,l))}function r3(e,t,n,r,l){if(null===e){var a=n.type;return"function"!=typeof a||aO(a)||void 0!==a.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=aA(n.type,null,r,t,t.mode,l)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,r4(e,t,a,r,l))}if(a=e.child,0==(e.lanes&l)){var i=a.memoizedProps;if((n=null!==(n=n.compare)?n:tW)(i,r)&&e.ref===t.ref)return lc(e,t,l)}return t.flags|=1,(e=aU(a,r)).ref=t.ref,e.return=t,t.child=e}function r4(e,t,n,r,l){if(null!==e&&tW(e.memoizedProps,r)&&e.ref===t.ref)if(r0=!1,0==(e.lanes&l))return t.lanes=e.lanes,lc(e,t,l);else 0!=(131072&e.flags)&&(r0=!0);return r8(e,t,n,r,l)}function r5(e,t,n){var r=t.pendingProps,l=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0==(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null},te(lZ,lX),lX|=n;else{if(0==(0x40000000&n))return e=null!==a?a.baseLanes|n:n,t.lanes=t.childLanes=0x40000000,t.memoizedState={baseLanes:e,cachePool:null},t.updateQueue=null,te(lZ,lX),lX|=e,null;t.memoizedState={baseLanes:0,cachePool:null},r=null!==a?a.baseLanes:n,te(lZ,lX),lX|=r}else null!==a?(r=a.baseLanes|n,t.memoizedState=null):r=n,te(lZ,lX),lX|=r;return r1(e,t,l,n),t.child}function r6(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function r8(e,t,n,r,l){var a=ti(n)?tl:tn.current;return(a=ta(t,a),tJ(t,l),n=rt(e,t,n,r,a,l),r=rn(),null===e||r0)?(nx&&r&&ny(t),t.flags|=1,r1(e,t,n,l),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,lc(e,t,l))}function r9(e,t,n,r,l){if(ti(n)){var a=!0;tc(t)}else a=!1;if(tJ(t,l),null===t.stateNode)null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),na(t,n,r),nu(t,n,r,l),r=!0;else if(null===e){var i=t.stateNode,u=t.memoizedProps;i.props=u;var o=i.context,s=n.contextType;s="object"==typeof s&&null!==s?t0(s):ta(t,s=ti(n)?tl:tn.current);var c=n.getDerivedStateFromProps,f="function"==typeof c||"function"==typeof i.getSnapshotBeforeUpdate;f||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(u!==r||o!==s)&&ni(t,i,r,s),t2=!1;var d=t.memoizedState;i.state=d,t7(t,r,i,l),o=t.memoizedState,u!==r||d!==o||tr.current||t2?("function"==typeof c&&(nn(t,n,c,r),o=t.memoizedState),(u=t2||nl(t,n,u,r,d,o,s))?(f||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||("function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"==typeof i.componentDidMount&&(t.flags|=4194308)):("function"==typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=o),i.props=r,i.state=o,i.context=s,r=u):("function"==typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,t4(e,t),u=t.memoizedProps,s=t.type===t.elementType?u:tB(t.type,u),i.props=s,f=t.pendingProps,d=i.context,o="object"==typeof(o=n.contextType)&&null!==o?t0(o):ta(t,o=ti(n)?tl:tn.current);var p=n.getDerivedStateFromProps;(c="function"==typeof p||"function"==typeof i.getSnapshotBeforeUpdate)||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(u!==f||d!==o)&&ni(t,i,r,o),t2=!1,d=t.memoizedState,i.state=d,t7(t,r,i,l);var h=t.memoizedState;u!==f||d!==h||tr.current||t2?("function"==typeof p&&(nn(t,n,p,r),h=t.memoizedState),(s=t2||nl(t,n,s,r,d,h,o)||!1)?(c||"function"!=typeof i.UNSAFE_componentWillUpdate&&"function"!=typeof i.componentWillUpdate||("function"==typeof i.componentWillUpdate&&i.componentWillUpdate(r,h,o),"function"==typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,h,o)),"function"==typeof i.componentDidUpdate&&(t.flags|=4),"function"==typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof i.componentDidUpdate||u===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||u===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),i.props=r,i.state=h,i.context=o,r=s):("function"!=typeof i.componentDidUpdate||u===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||u===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return r7(e,t,n,r,a,l)}function r7(e,t,n,r,l,a){r6(e,t);var i=0!=(128&t.flags);if(!r&&!i)return l&&tf(t,n,!1),lc(e,t,a);r=t.stateNode,rJ.current=t;var u=i&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=nO(t,e.child,null,a),t.child=nO(t,null,u,a)):r1(e,t,u,a),t.memoizedState=r.state,l&&tf(t,n,!0),t.child}function le(e){var t=e.stateNode;t.pendingContext?to(e,t.pendingContext,t.pendingContext!==t.context):t.context&&to(e,t.context,!1),nq(e,t.containerInfo)}function lt(e,t,n,r,l){return nL(),nT(l),t.flags|=256,r1(e,t,n,r),t.child}var ln={dehydrated:null,treeContext:null,retryLane:0};function lr(e){return{baseLanes:e,cachePool:null}}function ll(e,t,n){var r,l=t.pendingProps,a=nG.current,i=!1,u=0!=(128&t.flags);if((r=u)||(r=(null===e||null!==e.memoizedState)&&0!=(2&a)),r?(i=!0,t.flags&=-129):(null===e||null!==e.memoizedState)&&(a|=1),te(nG,1&a),null===e)return(nN(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated))?(0==(1&t.mode)?t.lanes=1:eU(e)?t.lanes=8:t.lanes=0x40000000,null):(a=l.children,e=l.fallback,i?(l=t.mode,i=t.child,a={mode:"hidden",children:a},0==(1&l)&&null!==i?(i.childLanes=0,i.pendingProps=a):i=aQ(a,l,0,null),e=aH(e,l,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=lr(n),t.memoizedState=ln,e):la(t,a));if(null!==(a=e.memoizedState)&&null!==(r=a.dehydrated)){if(u)return 256&t.flags?(t.flags&=-257,li(e,t,n,Error(f(422)))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=l.fallback,a=t.mode,l=aQ({mode:"visible",children:l.children},a,0,null),i=aH(i,a,n,null),i.flags|=2,l.return=t,i.return=t,l.sibling=i,t.child=l,0!=(1&t.mode)&&nO(t,e.child,null,n),t.child.memoizedState=lr(n),t.memoizedState=ln,i);if(0==(1&t.mode))t=li(e,t,n,null);else if(eU(r))t=li(e,t,n,Error(f(419)));else if(l=0!=(n&e.childLanes),r0||l){if(null!==(l=lY)){switch(n&-n){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 0x1000000:case 0x2000000:case 0x4000000:i=32;break;case 0x20000000:i=0x10000000;break;default:i=0}0!==(l=0!=(i&(l.suspendedLanes|n))?0:i)&&l!==a.retryLane&&(a.retryLane=l,af(e,l,-1))}a_(),t=li(e,t,n,Error(f(421)))}else eO(r)?(t.flags|=128,t.child=e.child,eA(r,t=aR.bind(null,e)),t=null):(n=a.treeContext,Z&&(nk=eB(r),nw=t,nx=!0,nE=null,n_=!1,null!==n&&(nd[np++]=nm,nd[np++]=ng,nd[np++]=nh,nm=n.id,ng=n.overflow,nh=t)),t=la(t,t.pendingProps.children),t.flags|=4096);return t}return i?(l=function(e,t,n,r,l){var a=t.mode,i=(e=e.child).sibling,u={mode:"hidden",children:n};return 0==(1&a)&&t.child!==e?((n=t.child).childLanes=0,n.pendingProps=u,t.deletions=null):(n=aU(e,u)).subtreeFlags=0xe00000&e.subtreeFlags,null!==i?r=aU(i,r):(r=aH(r,a,l,null),r.flags|=2),r.return=t,n.return=t,n.sibling=r,t.child=n,r}(e,t,l.children,l.fallback,n),i=t.child,a=e.child.memoizedState,i.memoizedState=null===a?lr(n):{baseLanes:a.baseLanes|n,cachePool:null},i.childLanes=e.childLanes&~n,t.memoizedState=ln,l):(n=function(e,t,n,r){var l=e.child;return e=l.sibling,n=aU(l,{mode:"visible",children:n}),0==(1&t.mode)&&(n.lanes=r),n.return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n}(e,t,l.children,n),t.memoizedState=null,n)}function la(e,t){return(t=aQ({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function li(e,t,n,r){return null!==r&&nT(r),nO(t,e.child,null,n),e=la(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function lu(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),tZ(e.return,t,n)}function lo(e,t,n,r,l){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=l)}function ls(e,t,n){var r=t.pendingProps,l=r.revealOrder,a=r.tail;if(r1(e,t,r.children,n),0!=(2&(r=nG.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!=(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&lu(e,n,t);else if(19===e.tag)lu(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(te(nG,r),0==(1&t.mode))t.memoizedState=null;else switch(l){case"forwards":for(l=null,n=t.child;null!==n;)null!==(e=n.alternate)&&null===nK(e)&&(l=n),n=n.sibling;null===(n=l)?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),lo(t,!1,l,n,a);break;case"backwards":for(n=null,l=t.child,t.child=null;null!==l;){if(null!==(e=l.alternate)&&null===nK(e)){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}lo(t,!0,n,null,a);break;case"together":lo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function lc(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),l1|=t.lanes,0==(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(f(153));if(null!==t.child){for(n=aU(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=aU(e,e.pendingProps)).return=t;n.sibling=null}return t.child}var lf=!1,ld=!1,lp="function"==typeof WeakSet?WeakSet:Set,lh=null;function lm(e,t){var n=e.ref;if(null!==n)if("function"==typeof n)try{n(null)}catch(n){aM(e,t,n)}else n.current=null}function lg(e,t,n){try{n()}catch(n){aM(e,t,n)}}var lv=!1;function lb(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var l=r=r.next;do{if((l.tag&e)===e){var a=l.destroy;l.destroy=void 0,void 0!==a&&lg(t,n,a)}l=l.next}while(l!==r)}}function ly(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function lS(e){var t=e.ref;if(null!==t){var n=e.stateNode;e=5===e.tag?j(n):n,"function"==typeof t?t(e):t.current=e}}function lw(e,t,n){if(tj&&"function"==typeof tj.onCommitFiberUnmount)try{tj.onCommitFiberUnmount(tR,t)}catch(e){}switch(t.tag){case 0:case 11:case 14:case 15:if(null!==(e=t.updateQueue)&&null!==(e=e.lastEffect)){var r=e=e.next;do{var l=r,a=l.destroy;l=l.tag,void 0!==a&&(0!=(2&l)?lg(t,n,a):0!=(4&l)&&lg(t,n,a)),r=r.next}while(r!==e)}break;case 1:if(lm(t,n),"function"==typeof(e=t.stateNode).componentWillUnmount)try{e.props=t.memoizedProps,e.state=t.memoizedState,e.componentWillUnmount()}catch(e){aM(t,n,e)}break;case 5:lm(t,n);break;case 4:K?lP(e,t,n):X&&X&&(n=eN(t=t.stateNode.containerInfo),eL(t,n))}}function lk(e,t,n){for(var r=t;;)if(lw(e,r,n),null===r.child||K&&4===r.tag){if(r===t)break;for(;null===r.sibling;){if(null===r.return||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}else r.child.return=r,r=r.child}function lx(e){return 5===e.tag||3===e.tag||4===e.tag}function l_(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||lx(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags||null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function lE(e){if(K){e:{for(var t=e.return;null!==t;){if(lx(t))break e;t=t.return}throw Error(f(160))}var n=t;switch(n.tag){case 5:t=n.stateNode,32&n.flags&&(ek(t),n.flags&=-33),n=l_(e),function e(t,n,r){var l=t.tag;if(5===l||6===l)t=t.stateNode,n?eb(r,t,n):ep(r,t);else if(4!==l&&null!==(t=t.child))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t);break;case 3:case 4:t=n.stateNode.containerInfo,n=l_(e),function e(t,n,r){var l=t.tag;if(5===l||6===l)t=t.stateNode,n?ey(r,t,n):eh(r,t);else if(4!==l&&null!==(t=t.child))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t);break;default:throw Error(f(161))}}}function lP(e,t,n){for(var r,l,a=t,i=!1;;){if(!i){i=a.return;e:for(;;){if(null===i)throw Error(f(160));switch(r=i.stateNode,i.tag){case 5:l=!1;break e;case 3:case 4:r=r.containerInfo,l=!0;break e}i=i.return}i=!0}if(5===a.tag||6===a.tag)lk(e,a,n),l?ew(r,a.stateNode):eS(r,a.stateNode);else if(18===a.tag)l?eZ(r,a.stateNode):eX(r,a.stateNode);else if(4===a.tag){if(null!==a.child){r=a.stateNode.containerInfo,l=!0,a.child.return=a,a=a.child;continue}}else if(lw(e,a,n),null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break;for(;null===a.sibling;){if(null===a.return||a.return===t)return;4===(a=a.return).tag&&(i=!1)}a.sibling.return=a.return,a=a.sibling}}function lC(e,t){if(K){switch(t.tag){case 0:case 11:case 14:case 15:lb(3,t,t.return),ly(3,t),lb(5,t,t.return);return;case 1:case 12:case 17:return;case 5:var n=t.stateNode;if(null!=n){var r=t.memoizedProps;e=null!==e?e.memoizedProps:r;var l=t.type,a=t.updateQueue;t.updateQueue=null,null!==a&&ev(n,a,l,e,r,t)}return;case 6:if(null===t.stateNode)throw Error(f(162));n=t.memoizedProps,em(t.stateNode,null!==e?e.memoizedProps:n,n);return;case 3:Z&&null!==e&&e.memoizedState.isDehydrated&&eG(t.stateNode.containerInfo);return;case 13:case 19:lz(t);return}throw Error(f(163))}switch(t.tag){case 0:case 11:case 14:case 15:lb(3,t,t.return),ly(3,t),lb(5,t,t.return);return;case 12:case 22:case 23:return;case 13:case 19:lz(t);return;case 3:Z&&null!==e&&e.memoizedState.isDehydrated&&eG(t.stateNode.containerInfo)}e:if(X){switch(t.tag){case 1:case 5:case 6:break e;case 3:case 4:eL((t=t.stateNode).containerInfo,t.pendingChildren);break e}throw Error(f(163))}}function lz(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new lp),t.forEach(function(t){var r=aj.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function lN(e){for(;null!==lh;){var t=lh;if(0!=(8772&t.flags)){var n=t.alternate;try{if(0!=(8772&t.flags))switch(t.tag){case 0:case 11:case 15:ld||ly(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!ld)if(null===n)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:tB(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;null!==a&&ne(t,a,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:n=j(t.child.stateNode);break;case 1:n=t.child.stateNode}ne(t,i,n)}break;case 5:var u=t.stateNode;null===n&&4&t.flags&&eg(u,t.type,t.memoizedProps,t);break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:break;case 13:if(Z&&null===t.memoizedState){var o=t.alternate;if(null!==o){var s=o.memoizedState;if(null!==s){var c=s.dehydrated;null!==c&&eK(c)}}}break;default:throw Error(f(163))}ld||512&t.flags&&lS(t)}catch(e){aM(t,t.return,e)}}if(t===e){lh=null;break}if(null!==(n=t.sibling)){n.return=t.return,lh=n;break}lh=t.return}}function lI(e){for(;null!==lh;){var t=lh;if(t===e){lh=null;break}var n=t.sibling;if(null!==n){n.return=t.return,lh=n;break}lh=t.return}}function lM(e){for(;null!==lh;){var t=lh;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ly(4,t)}catch(e){aM(t,n,e)}break;case 1:var r=t.stateNode;if("function"==typeof r.componentDidMount){var l=t.return;try{r.componentDidMount()}catch(e){aM(t,l,e)}}var a=t.return;try{lS(t)}catch(e){aM(t,a,e)}break;case 5:var i=t.return;try{lS(t)}catch(e){aM(t,i,e)}}}catch(e){aM(t,t.return,e)}if(t===e){lh=null;break}var u=t.sibling;if(null!==u){u.return=t.return,lh=u;break}lh=t.return}}var lL=0,lT=1,lR=2,lj=3,lD=4;if("function"==typeof Symbol&&Symbol.for){var lF=Symbol.for;lL=lF("selector.component"),lT=lF("selector.has_pseudo_class"),lR=lF("selector.role"),lj=lF("selector.test_id"),lD=lF("selector.text")}function lO(e){var t=J(e);if(null!=t){if("string"!=typeof t.memoizedProps["data-testname"])throw Error(f(364));return t}if(null===(e=ei(e)))throw Error(f(362));return e.stateNode.current}function lU(e,t){switch(t.$$typeof){case lL:if(e.type===t.value)return!0;break;case lT:e:{t=t.value,e=[e,0];for(var n=0;n<e.length;){var r=e[n++],l=e[n++],a=t[l];if(5!==r.tag||!es(r)){for(;null!=a&&lU(r,a);)a=t[++l];if(l===t.length){t=!0;break e}for(r=r.child;null!==r;)e.push(r,l),r=r.sibling}}t=!1}return t;case lR:if(5===e.tag&&ec(e.stateNode,t.value))return!0;break;case lD:if((5===e.tag||6===e.tag)&&null!==(e=eo(e))&&0<=e.indexOf(t.value))return!0;break;case lj:if(5===e.tag&&"string"==typeof(e=e.memoizedProps["data-testname"])&&e.toLowerCase()===t.value.toLowerCase())return!0;break;default:throw Error(f(365))}return!1}function lA(e){switch(e.$$typeof){case lL:return"<"+(z(e.value)||"Unknown")+">";case lT:return":has("+(lA(e)||"")+")";case lR:return'[role="'+e.value+'"]';case lD:return'"'+e.value+'"';case lj:return'[data-testname="'+e.value+'"]';default:throw Error(f(365))}}function lH(e,t){var n=[];e=[e,0];for(var r=0;r<e.length;){var l=e[r++],a=e[r++],i=t[a];if(5!==l.tag||!es(l)){for(;null!=i&&lU(l,i);)i=t[++a];if(a===t.length)n.push(l);else for(l=l.child;null!==l;)e.push(l,a),l=l.sibling}}return n}function lQ(e,t){if(!ea)throw Error(f(363));e=lH(e=lO(e),t),t=[],e=Array.from(e);for(var n=0;n<e.length;){var r=e[n++];if(5===r.tag)es(r)||t.push(r.stateNode);else for(r=r.child;null!==r;)e.push(r),r=r.sibling}return t}var lW=Math.ceil,lB=d.ReactCurrentDispatcher,lq=d.ReactCurrentOwner,l$=d.ReactCurrentBatchConfig,lV=0,lY=null,lG=null,lK=0,lX=0,lZ=e9(0),lJ=0,l0=null,l1=0,l2=0,l3=0,l4=null,l5=null,l6=0,l8=1/0;function l9(){l8=tN()+500}var l7=!1,ae=null,at=null,an=!1,ar=null,al=0,aa=0,ai=null,au=-1,ao=0;function as(){return 0!=(6&lV)?tN():-1!==au?au:au=tN()}function ac(e){return 0==(1&e.mode)?1:0!=(2&lV)&&0!==lK?lK&-lK:null!==tQ.transition?(0===ao&&(e=tm,0==(4194240&(tm<<=1))&&(tm=64),ao=e),ao):0!==(e=tx)?e:et()}function af(e,t,n){if(50<aa)throw aa=0,ai=null,Error(f(185));var r=ad(e,t);return null===r?null:(tw(r,t,n),(0==(2&lV)||r!==lY)&&(r===lY&&(0==(2&lV)&&(l2|=t),4===lJ&&av(r,lK)),ap(r,n),1===t&&0===lV&&0==(1&e.mode)&&(l9(),tO&&tH())),r)}function ad(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}function ap(e,t){var n,r=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,a=e.pendingLanes;0<a;){var i=31-td(a),u=1<<i,o=l[i];-1===o?(0==(u&n)||0!=(u&r))&&(l[i]=function(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return -1}}(u,t)):o<=t&&(e.expiredLanes|=u),a&=~u}}(e,t);var l=tb(e,e===lY?lK:0);if(0===l)null!==r&&tP(r),e.callbackNode=null,e.callbackPriority=0;else if(t=l&-l,e.callbackPriority!==t){if(null!=r&&tP(r),1===t)0===e.tag?(n=ab.bind(null,e),tO=!0,tA(n)):tA(ab.bind(null,e)),er?el(function(){0===lV&&tH()}):tE(tI,tH),r=null;else{switch(t_(l)){case 1:r=tI;break;case 4:r=tM;break;case 16:default:r=tL;break;case 0x20000000:r=tT}r=tE(r,ah.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function ah(e,t){if(au=-1,ao=0,0!=(6&lV))throw Error(f(327));var n=e.callbackNode;if(aN()&&e.callbackNode!==n)return null;var r=tb(e,e===lY?lK:0);if(0===r)return null;if(0!=(30&r)||0!=(r&e.expiredLanes)||t)t=aE(e,r);else{t=r;var l=lV;lV|=2;var a=ax();for((lY!==e||lK!==t)&&(l9(),aw(e,t));;)try{for(;null!==lG&&!tC();)aP(lG);break}catch(t){ak(e,t)}tG(),lB.current=a,lV=l,null!==lG?t=0:(lY=null,lK=0,t=lJ)}if(0!==t){if(2===t&&0!==(l=ty(e))&&(r=l,t=am(e,l)),1===t)throw n=l0,aw(e,0),av(e,r),ap(e,tN()),n;if(6===t)av(e,r);else{if(l=e.current.alternate,0==(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var l=n[r],a=l.getSnapshot;l=l.value;try{if(!tD(a(),l))return!1}catch(e){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(l)&&(2===(t=aE(e,r))&&0!==(a=ty(e))&&(r=a,t=am(e,a)),1===t))throw n=l0,aw(e,0),av(e,r),ap(e,tN()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(f(345));case 2:case 5:az(e,l5);break;case 3:if(av(e,r),(0x7c00000&r)===r&&10<(t=l6+500-tN())){if(0!==tb(e,0))break;if(((l=e.suspendedLanes)&r)!==r){as(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=$(az.bind(null,e,l5),t);break}az(e,l5);break;case 4:if(av(e,r),(4194240&r)===r)break;for(l=-1,t=e.eventTimes;0<r;){var i=31-td(r);a=1<<i,(i=t[i])>l&&(l=i),r&=~a}if(r=l,10<(r=(120>(r=tN()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*lW(r/1960))-r)){e.timeoutHandle=$(az.bind(null,e,l5),r);break}az(e,l5);break;default:throw Error(f(329))}}}return ap(e,tN()),e.callbackNode===n?ah.bind(null,e):null}function am(e,t){var n=l4;return e.current.memoizedState.isDehydrated&&(aw(e,t).flags|=256),2!==(e=aE(e,t))&&(t=l5,l5=n,null!==t&&ag(t)),e}function ag(e){null===l5?l5=e:l5.push.apply(l5,e)}function av(e,t){for(t&=~l3,t&=~l2,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-td(t),r=1<<n;e[n]=-1,t&=~r}}function ab(e){if(0!=(6&lV))throw Error(f(327));aN();var t=tb(e,0);if(0==(1&t))return ap(e,tN()),null;var n=aE(e,t);if(0!==e.tag&&2===n){var r=ty(e);0!==r&&(t=r,n=am(e,r))}if(1===n)throw n=l0,aw(e,0),av(e,t),ap(e,tN()),n;if(6===n)throw Error(f(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,az(e,l5),ap(e,tN()),null}function ay(e){null!==ar&&0===ar.tag&&0==(6&lV)&&aN();var t=lV;lV|=1;var n=l$.transition,r=tx;try{if(l$.transition=null,tx=1,e)return e()}finally{tx=r,l$.transition=n,0==(6&(lV=t))&&tH()}}function aS(){lX=lZ.current,e7(lZ)}function aw(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==Y&&(e.timeoutHandle=Y,V(n)),null!==lG)for(n=lG.return;null!==n;){var r=n;switch(nS(r),r.tag){case 1:null!=(r=r.type.childContextTypes)&&tu();break;case 3:n$(),e7(tr),e7(tn),nZ();break;case 5:nY(r);break;case 4:n$();break;case 13:case 19:e7(nG);break;case 10:tX(r.type._context);break;case 22:case 23:aS()}n=n.return}if(lY=e,lG=e=aU(e.current,null),lK=lX=t,lJ=0,l0=null,l3=l2=l1=0,l5=l4=null,null!==t1){for(t=0;t<t1.length;t++)if(null!==(r=(n=t1[t]).interleaved)){n.interleaved=null;var l=r.next,a=n.pending;if(null!==a){var i=a.next;a.next=l,r.next=i}n.pending=r}t1=null}return e}function ak(e,t){for(;;){var n=lG;try{if(tG(),nJ.current=rD,n5){for(var r=n2.memoizedState;null!==r;){var l=r.queue;null!==l&&(l.pending=null),r=r.next}n5=!1}if(n1=0,n4=n3=n2=null,n6=!1,n8=0,lq.current=null,null===n||null===n.return){lJ=1,l0=t,lG=null;break}e:{var a=e,i=n.return,u=n,o=t;if(t=lK,u.flags|=32768,null!==o&&"object"==typeof o&&"function"==typeof o.then){var s=o,c=u,d=c.tag;if(0==(1&c.mode)&&(0===d||11===d||15===d)){var p=c.alternate;p?(c.updateQueue=p.updateQueue,c.memoizedState=p.memoizedState,c.lanes=p.lanes):(c.updateQueue=null,c.memoizedState=null)}var h=r$(i);if(null!==h){h.flags&=-257,rV(h,i,u,a,t),1&h.mode&&rq(a,s,t),t=h,o=s;var m=t.updateQueue;if(null===m){var g=new Set;g.add(o),t.updateQueue=g}else m.add(o);break e}if(0==(1&t)){rq(a,s,t),a_();break e}o=Error(f(426))}else if(nx&&1&u.mode){var v=r$(i);if(null!==v){0==(65536&v.flags)&&(v.flags|=256),rV(v,i,u,a,t),nT(o);break e}}a=o,4!==lJ&&(lJ=2),null===l4?l4=[a]:l4.push(a),o=rA(o,u),u=i;do{switch(u.tag){case 3:u.flags|=65536,t&=-t,u.lanes|=t;var b=rW(u,o,t);t9(u,b);break e;case 1:a=o;var y=u.type,S=u.stateNode;if(0==(128&u.flags)&&("function"==typeof y.getDerivedStateFromError||null!==S&&"function"==typeof S.componentDidCatch&&(null===at||!at.has(S)))){u.flags|=65536,t&=-t,u.lanes|=t;var w=rB(u,a,t);t9(u,w);break e}}u=u.return}while(null!==u)}aC(n)}catch(e){t=e,lG===n&&null!==n&&(lG=n=n.return);continue}break}}function ax(){var e=lB.current;return lB.current=rD,null===e?rD:e}function a_(){(0===lJ||3===lJ||2===lJ)&&(lJ=4),null===lY||0==(0xfffffff&l1)&&0==(0xfffffff&l2)||av(lY,lK)}function aE(e,t){var n=lV;lV|=2;var r=ax();for(lY===e&&lK===t||aw(e,t);;)try{for(;null!==lG;)aP(lG);break}catch(t){ak(e,t)}if(tG(),lV=n,lB.current=r,null!==lG)throw Error(f(261));return lY=null,lK=0,lJ}function aP(e){var t=i(e.alternate,e,lX);e.memoizedProps=e.pendingProps,null===t?aC(e):lG=t,lq.current=null}function aC(e){var n=e;do{var i=n.alternate;if(e=n.return,0==(32768&n.flags)){if(null!==(i=function(e,n,i){var u=n.pendingProps;switch(nS(n),n.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return rZ(n),null;case 1:case 17:return ti(n.type)&&tu(),rZ(n),null;case 3:return u=n.stateNode,n$(),e7(tr),e7(tn),nZ(),u.pendingContext&&(u.context=u.pendingContext,u.pendingContext=null),(null===e||null===e.child)&&(nM(n)?rY(n):null===e||e.memoizedState.isDehydrated&&0==(256&n.flags)||(n.flags|=1024,null!==nE&&(ag(nE),nE=null))),r(e,n),rZ(n),null;case 5:nY(n),i=nB(nW.current);var o=n.type;if(null!==e&&null!=n.stateNode)l(e,n,o,u,i),e.ref!==n.ref&&(n.flags|=512,n.flags|=2097152);else{if(!u){if(null===n.stateNode)throw Error(f(166));return rZ(n),null}if(e=nB(nH.current),nM(n)){if(!Z)throw Error(f(175));e=eq(n.stateNode,n.type,n.memoizedProps,i,e,n,!n_),n.updateQueue=e,null!==e&&rY(n)}else{var s=A(o,u,i,e,n);t(s,n,!1,!1),n.stateNode=s,Q(s,o,u,i,e)&&rY(n)}null!==n.ref&&(n.flags|=512,n.flags|=2097152)}return rZ(n),null;case 6:if(e&&null!=n.stateNode)a(e,n,e.memoizedProps,u);else{if("string"!=typeof u&&null===n.stateNode)throw Error(f(166));if(e=nB(nW.current),i=nB(nH.current),nM(n)){if(!Z)throw Error(f(176));if((i=e$(e=n.stateNode,u=n.memoizedProps,n,!n_))&&null!==(o=nw))switch(s=0!=(1&o.mode),o.tag){case 3:e0(o.stateNode.containerInfo,e,u,s);break;case 5:e1(o.type,o.memoizedProps,o.stateNode,e,u,s)}i&&rY(n)}else n.stateNode=q(u,e,i,n)}return rZ(n),null;case 13:if(e7(nG),u=n.memoizedState,nx&&null!==nk&&0!=(1&n.mode)&&0==(128&n.flags)){for(e=nk;e;)e=eH(e);return nL(),n.flags|=98560,n}if(null!==u&&null!==u.dehydrated){if(u=nM(n),null===e){if(!u)throw Error(f(318));if(!Z)throw Error(f(344));if(!(e=null!==(e=n.memoizedState)?e.dehydrated:null))throw Error(f(317));eV(e,n)}else nL(),0==(128&n.flags)&&(n.memoizedState=null),n.flags|=4;return rZ(n),null}if(null!==nE&&(ag(nE),nE=null),0!=(128&n.flags))return n.lanes=i,n;return u=null!==u,i=!1,null===e?nM(n):i=null!==e.memoizedState,u&&!i&&(n.child.flags|=8192,0!=(1&n.mode)&&(null===e||0!=(1&nG.current)?0===lJ&&(lJ=3):a_())),null!==n.updateQueue&&(n.flags|=4),rZ(n),null;case 4:return n$(),r(e,n),null===e&&ee(n.stateNode.containerInfo),rZ(n),null;case 10:return tX(n.type._context),rZ(n),null;case 19:if(e7(nG),null===(o=n.memoizedState))return rZ(n),null;if(u=0!=(128&n.flags),null===(s=o.rendering))if(u)rX(o,!1);else{if(0!==lJ||null!==e&&0!=(128&e.flags))for(e=n.child;null!==e;){if(null!==(s=nK(e))){for(n.flags|=128,rX(o,!1),null!==(e=s.updateQueue)&&(n.updateQueue=e,n.flags|=4),n.subtreeFlags=0,e=i,u=n.child;null!==u;)i=u,o=e,i.flags&=0xe00002,null===(s=i.alternate)?(i.childLanes=0,i.lanes=o,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,o=s.dependencies,i.dependencies=null===o?null:{lanes:o.lanes,firstContext:o.firstContext}),u=u.sibling;return te(nG,1&nG.current|2),n.child}e=e.sibling}null!==o.tail&&tN()>l8&&(n.flags|=128,u=!0,rX(o,!1),n.lanes=4194304)}else{if(!u)if(null!==(e=nK(s))){if(n.flags|=128,u=!0,null!==(e=e.updateQueue)&&(n.updateQueue=e,n.flags|=4),rX(o,!0),null===o.tail&&"hidden"===o.tailMode&&!s.alternate&&!nx)return rZ(n),null}else 2*tN()-o.renderingStartTime>l8&&0x40000000!==i&&(n.flags|=128,u=!0,rX(o,!1),n.lanes=4194304);o.isBackwards?(s.sibling=n.child,n.child=s):(null!==(e=o.last)?e.sibling=s:n.child=s,o.last=s)}if(null!==o.tail)return n=o.tail,o.rendering=n,o.tail=n.sibling,o.renderingStartTime=tN(),n.sibling=null,e=nG.current,te(nG,u?1&e|2:1&e),n;return rZ(n),null;case 22:case 23:return aS(),u=null!==n.memoizedState,null!==e&&null!==e.memoizedState!==u&&(n.flags|=8192),u&&0!=(1&n.mode)?0!=(0x40000000&lX)&&(rZ(n),K&&6&n.subtreeFlags&&(n.flags|=8192)):rZ(n),null;case 24:case 25:return null}throw Error(f(156,n.tag))}(i,n,lX))){lG=i;return}}else{if(null!==(i=function(e,t){switch(nS(t),t.tag){case 1:return ti(t.type)&&tu(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return n$(),e7(tr),e7(tn),nZ(),0!=(65536&(e=t.flags))&&0==(128&e)?(t.flags=-65537&e|128,t):null;case 5:return nY(t),null;case 13:if(e7(nG),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(f(340));nL()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return e7(nG),null;case 4:return n$(),null;case 10:return tX(t.type._context),null;case 22:case 23:return aS(),null;default:return null}}(i,n))){i.flags&=32767,lG=i;return}if(null!==e)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{lJ=6,lG=null;return}}if(null!==(n=n.sibling)){lG=n;return}lG=n=e}while(null!==n);0===lJ&&(lJ=5)}function az(e,t){var n=tx,r=l$.transition;try{l$.transition=null,tx=1,function(e,t,n){do aN();while(null!==ar);if(0!=(6&lV))throw Error(f(327));var r=e.finishedWork,l=e.finishedLanes;if(null!==r){if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(f(177));e.callbackNode=null,e.callbackPriority=0;var a=r.lanes|r.childLanes,i=e,u=a,o=i.pendingLanes&~u;i.pendingLanes=u,i.suspendedLanes=0,i.pingedLanes=0,i.expiredLanes&=u,i.mutableReadLanes&=u,i.entangledLanes&=u,u=i.entanglements;var s=i.eventTimes;for(i=i.expirationTimes;0<o;){var c=31-td(o),d=1<<c;u[c]=0,s[c]=-1,i[c]=-1,o&=~d}if(e===lY&&(lG=lY=null,lK=0),0==(2064&r.subtreeFlags)&&0==(2064&r.flags)||an||(an=!0,function(e,t){tE(e,t)}(tL,function(){return aN(),null})),a=0!=(15990&r.flags),0!=(15990&r.subtreeFlags)||a){a=l$.transition,l$.transition=null;var p,h,m=tx;tx=1;var g=lV;lV|=4,lq.current=null,function(e,t){for(O(e.containerInfo),lh=t;null!==lh;)if(t=(e=lh).child,0!=(1028&e.subtreeFlags)&&null!==t)t.return=e,lh=t;else for(;null!==lh;){e=lh;try{var n=e.alternate;if(0!=(1024&e.flags))switch(e.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==n){var r=n.memoizedProps,l=n.memoizedState,a=e.stateNode,i=a.getSnapshotBeforeUpdate(e.elementType===e.type?r:tB(e.type,r),l);a.__reactInternalSnapshotBeforeUpdate=i}break;case 3:K&&eC(e.stateNode.containerInfo);break;default:throw Error(f(163))}}catch(t){aM(e,e.return,t)}if(null!==(t=e.sibling)){t.return=e.return,lh=t;break}lh=e.return}n=lv,lv=!1}(e,r),function(e,t){for(lh=t;null!==lh;){var n=(t=lh).deletions;if(null!==n)for(var r=0;r<n.length;r++){var l=n[r];try{var a=e;K?lP(a,l,t):lk(a,l,t);var i=l.alternate;null!==i&&(i.return=null),l.return=null}catch(e){aM(l,t,e)}}if(n=t.child,0!=(12854&t.subtreeFlags)&&null!==n)n.return=t,lh=n;else for(;null!==lh;){t=lh;try{var u=t.flags;if(32&u&&K&&ek(t.stateNode),512&u){var o=t.alternate;if(null!==o){var s=o.ref;null!==s&&("function"==typeof s?s(null):s.current=null)}}if(8192&u)switch(t.tag){case 13:if(null!==t.memoizedState){var c=t.alternate;(null===c||null===c.memoizedState)&&(l6=tN())}break;case 22:var f=null!==t.memoizedState,d=t.alternate,p=null!==d&&null!==d.memoizedState;if(n=t,K){e:if(r=n,l=f,a=null,K)for(var h=r;;){if(5===h.tag){if(null===a){a=h;var m=h.stateNode;l?ex(m):eE(h.stateNode,h.memoizedProps)}}else if(6===h.tag){if(null===a){var g=h.stateNode;l?e_(g):eP(g,h.memoizedProps)}}else if((22!==h.tag&&23!==h.tag||null===h.memoizedState||h===r)&&null!==h.child){h.child.return=h,h=h.child;continue}if(h===r)break;for(;null===h.sibling;){if(null===h.return||h.return===r)break e;a===h&&(a=null),h=h.return}a===h&&(a=null),h.sibling.return=h.return,h=h.sibling}}if(f&&!p&&0!=(1&n.mode)){lh=n;for(var v=n.child;null!==v;){for(n=lh=v;null!==lh;){var b=(r=lh).child;switch(r.tag){case 0:case 11:case 14:case 15:lb(4,r,r.return);break;case 1:lm(r,r.return);var y=r.stateNode;if("function"==typeof y.componentWillUnmount){var S=r.return;try{y.props=r.memoizedProps,y.state=r.memoizedState,y.componentWillUnmount()}catch(e){aM(r,S,e)}}break;case 5:lm(r,r.return);break;case 22:if(null!==r.memoizedState){lI(n);continue}}null!==b?(b.return=r,lh=b):lI(n)}v=v.sibling}}}switch(4102&u){case 2:lE(t),t.flags&=-3;break;case 6:lE(t),t.flags&=-3,lC(t.alternate,t);break;case 4096:t.flags&=-4097;break;case 4100:t.flags&=-4097,lC(t.alternate,t);break;case 4:lC(t.alternate,t)}}catch(e){aM(t,t.return,e)}if(null!==(n=t.sibling)){n.return=t.return,lh=n;break}lh=t.return}}}(e,r,l),U(e.containerInfo),e.current=r,p=r,h=e,lh=p,function e(t,n,r){for(var l=0!=(1&t.mode);null!==lh;){var a=lh,i=a.child;if(22===a.tag&&l){var u=null!==a.memoizedState||lf;if(!u){var o=a.alternate,s=null!==o&&null!==o.memoizedState||ld;o=lf;var c=ld;if(lf=u,(ld=s)&&!c)for(lh=a;null!==lh;)s=(u=lh).child,22===u.tag&&null!==u.memoizedState?lM(a):null!==s?(s.return=u,lh=s):lM(a);for(;null!==i;)lh=i,e(i,n,r),i=i.sibling;lh=a,lf=o,ld=c}lN(t,n,r)}else 0!=(8772&a.subtreeFlags)&&null!==i?(i.return=a,lh=i):lN(t,n,r)}}(p,h,l),tz(),lV=g,tx=m,l$.transition=a}else e.current=r;an&&(an=!1,ar=e,al=l),0===(a=e.pendingLanes)&&(at=null);var v=r.stateNode;if(tj&&"function"==typeof tj.onCommitFiberRoot)try{tj.onCommitFiberRoot(tR,v,void 0,128==(128&v.current.flags))}catch(e){}if(ap(e,tN()),null!==t)for(n=e.onRecoverableError,r=0;r<t.length;r++)n(t[r]);if(l7)throw l7=!1,e=ae,ae=null,e;0!=(1&al)&&0!==e.tag&&aN(),0!=(1&(a=e.pendingLanes))?e===ai?aa++:(aa=0,ai=e):aa=0,tH()}}(e,t,n)}finally{l$.transition=r,tx=n}return null}function aN(){if(null!==ar){var e=t_(al),t=l$.transition,n=tx;try{if(l$.transition=null,tx=16>e?16:e,null===ar)var r=!1;else{if(e=ar,ar=null,al=0,0!=(6&lV))throw Error(f(331));var l=lV;for(lV|=4,lh=e.current;null!==lh;){var a=lh,i=a.child;if(0!=(16&lh.flags)){var u=a.deletions;if(null!==u){for(var o=0;o<u.length;o++){var s=u[o];for(lh=s;null!==lh;){var c=lh;switch(c.tag){case 0:case 11:case 15:lb(8,c,a)}var d=c.child;if(null!==d)d.return=c,lh=d;else for(;null!==lh;){var p=(c=lh).sibling,h=c.return;if(!function e(t){var n=t.alternate;null!==n&&(t.alternate=null,e(n)),t.child=null,t.deletions=null,t.sibling=null,5===t.tag&&null!==(n=t.stateNode)&&en(n),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}(c),c===s){lh=null;break}if(null!==p){p.return=h,lh=p;break}lh=h}}}var m=a.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}lh=a}}if(0!=(2064&a.subtreeFlags)&&null!==i)i.return=a,lh=i;else for(;null!==lh;){if(a=lh,0!=(2048&a.flags))switch(a.tag){case 0:case 11:case 15:lb(9,a,a.return)}var b=a.sibling;if(null!==b){b.return=a.return,lh=b;break}lh=a.return}}var y=e.current;for(lh=y;null!==lh;){var S=(i=lh).child;if(0!=(2064&i.subtreeFlags)&&null!==S)S.return=i,lh=S;else for(i=y;null!==lh;){if(u=lh,0!=(2048&u.flags))try{switch(u.tag){case 0:case 11:case 15:ly(9,u)}}catch(e){aM(u,u.return,e)}if(u===i){lh=null;break}var w=u.sibling;if(null!==w){w.return=u.return,lh=w;break}lh=u.return}}if(lV=l,tH(),tj&&"function"==typeof tj.onPostCommitFiberRoot)try{tj.onPostCommitFiberRoot(tR,e)}catch(e){}r=!0}return r}finally{tx=n,l$.transition=t}}return!1}function aI(e,t,n){t=rW(e,t=rA(n,t),1),t6(e,t),t=as(),null!==(e=ad(e,1))&&(tw(e,1,t),ap(e,t))}function aM(e,t,n){if(3===e.tag)aI(e,e,n);else for(;null!==t;){if(3===t.tag){aI(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===at||!at.has(r))){e=rB(t,e=rA(n,e),1),t6(t,e),e=as(),null!==(t=ad(t,1))&&(tw(t,1,e),ap(t,e));break}}t=t.return}}function aL(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=as(),e.pingedLanes|=e.suspendedLanes&n,lY===e&&(lK&n)===n&&(4===lJ||3===lJ&&(0x7c00000&lK)===lK&&500>tN()-l6?aw(e,0):l3|=n),ap(e,t)}function aT(e,t){0===t&&(0==(1&e.mode)?t=1:(t=tg,0==(0x7c00000&(tg<<=1))&&(tg=4194304)));var n=as();null!==(e=ad(e,t))&&(tw(e,t,n),ap(e,n))}function aR(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),aT(e,n)}function aj(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;null!==l&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(f(314))}null!==r&&r.delete(t),aT(e,n)}function aD(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function aF(e,t,n,r){return new aD(e,t,n,r)}function aO(e){return!(!(e=e.prototype)||!e.isReactComponent)}function aU(e,t){var n=e.alternate;return null===n?((n=aF(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=0xe00000&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function aA(e,t,n,r,l,a){var i=2;if(r=e,"function"==typeof e)aO(e)&&(i=1);else if("string"==typeof e)i=5;else e:switch(e){case m:return aH(n.children,l,a,t);case g:i=8,l|=8;break;case v:return(e=aF(12,n,t,2|l)).elementType=v,e.lanes=a,e;case w:return(e=aF(13,n,t,l)).elementType=w,e.lanes=a,e;case k:return(e=aF(19,n,t,l)).elementType=k,e.lanes=a,e;case E:return aQ(n,l,a,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case b:i=10;break e;case y:i=9;break e;case S:i=11;break e;case x:i=14;break e;case _:i=16,r=null;break e}throw Error(f(130,null==e?e:typeof e,""))}return(t=aF(i,n,t,l)).elementType=e,t.type=r,t.lanes=a,t}function aH(e,t,n,r){return(e=aF(7,e,r,t)).lanes=n,e}function aQ(e,t,n,r){return(e=aF(22,e,r,t)).elementType=E,e.lanes=n,e.stateNode={},e}function aW(e,t,n){return(e=aF(6,e,null,t)).lanes=n,e}function aB(e,t,n){return(t=aF(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function aq(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=Y,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=tS(0),this.expirationTimes=tS(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=tS(0),this.identifierPrefix=r,this.onRecoverableError=l,Z&&(this.mutableSourceEagerHydrationData=null)}function a$(e,t,n,r,l,a,i,u,o){return e=new aq(e,t,n,u,o),1===t?(t=1,!0===a&&(t|=8)):t=0,a=aF(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null},t3(a),e}function aV(e){if(!e)return tt;e=e._reactInternals;e:{if(N(e)!==e||1!==e.tag)throw Error(f(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ti(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(f(171))}if(1===e.tag){var n=e.type;if(ti(n))return ts(e,n,t)}return t}function aY(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(f(188));throw Error(f(268,e=Object.keys(e).join(",")))}return null===(e=L(t))?null:e.stateNode}function aG(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function aK(e,t){aG(e,t),(e=e.alternate)&&aG(e,t)}function aX(e){return null===(e=L(e))?null:e.stateNode}function aZ(){return null}return i=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||tr.current)r0=!0;else{if(0==(e.lanes&n)&&0==(128&t.flags))return r0=!1,function(e,t,n){switch(t.tag){case 3:le(t),nL();break;case 5:nV(t);break;case 1:ti(t.type)&&tc(t);break;case 4:nq(t,t.stateNode.containerInfo);break;case 10:tK(t,t.type._context,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r){if(null!==r.dehydrated)return te(nG,1&nG.current),t.flags|=128,null;if(0!=(n&t.child.childLanes))return ll(e,t,n);return te(nG,1&nG.current),null!==(e=lc(e,t,n))?e.sibling:null}te(nG,1&nG.current);break;case 19:if(r=0!=(n&t.childLanes),0!=(128&e.flags)){if(r)return ls(e,t,n);t.flags|=128}var l=t.memoizedState;if(null!==l&&(l.rendering=null,l.tail=null,l.lastEffect=null),te(nG,nG.current),!r)return null;break;case 22:case 23:return t.lanes=0,r5(e,t,n)}return lc(e,t,n)}(e,t,n);r0=0!=(131072&e.flags)}else r0=!1,nx&&0!=(1048576&t.flags)&&nb(t,nf,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),e=t.pendingProps;var l=ta(t,tn.current);tJ(t,n),l=rt(null,t,r,e,l,n);var a=rn();return t.flags|=1,"object"==typeof l&&null!==l&&"function"==typeof l.render&&void 0===l.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ti(r)?(a=!0,tc(t)):a=!1,t.memoizedState=null!==l.state&&void 0!==l.state?l.state:null,t3(t),l.updater=nr,t.stateNode=l,l._reactInternals=t,nu(t,r,e,n),t=r7(null,t,r,!0,a,n)):(t.tag=0,nx&&a&&ny(t),r1(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),e=t.pendingProps,r=(l=r._init)(r._payload),t.type=r,l=t.tag=function(e){if("function"==typeof e)return+!!aO(e);if(null!=e){if((e=e.$$typeof)===S)return 11;if(e===x)return 14}return 2}(r),e=tB(r,e),l){case 0:t=r8(null,t,r,e,n);break e;case 1:t=r9(null,t,r,e,n);break e;case 11:t=r2(null,t,r,e,n);break e;case 14:t=r3(null,t,r,tB(r.type,e),n);break e}throw Error(f(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:tB(r,l),r8(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:tB(r,l),r9(e,t,r,l,n);case 3:e:{if(le(t),null===e)throw Error(f(387));r=t.pendingProps,l=(a=t.memoizedState).element,t4(e,t),t7(t,r,null,n);var i=t.memoizedState;if(r=i.element,Z&&a.isDehydrated)if(a={element:r,isDehydrated:!1,cache:i.cache,transitions:i.transitions},t.updateQueue.baseState=a,t.memoizedState=a,256&t.flags){t=lt(e,t,r,n,l=Error(f(423)));break e}else if(r!==l){t=lt(e,t,r,n,l=Error(f(424)));break e}else for(Z&&(nk=eW(t.stateNode.containerInfo),nw=t,nx=!0,nE=null,n_=!1),n=nU(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling;else{if(nL(),r===l){t=lc(e,t,n);break e}r1(e,t,r,n)}t=t.child}return t;case 5:return nV(t),null===e&&nN(t),r=t.type,l=t.pendingProps,a=null!==e?e.memoizedProps:null,i=l.children,B(r,l)?i=null:null!==a&&B(r,a)&&(t.flags|=32),r6(e,t),r1(e,t,i,n),t.child;case 6:return null===e&&nN(t),null;case 13:return ll(e,t,n);case 4:return nq(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=nO(t,null,r,n):r1(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:tB(r,l),r2(e,t,r,l,n);case 7:return r1(e,t,t.pendingProps,n),t.child;case 8:case 12:return r1(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,a=t.memoizedProps,tK(t,r,i=l.value),null!==a)if(tD(a.value,i)){if(a.children===l.children&&!tr.current){t=lc(e,t,n);break e}}else for(null!==(a=t.child)&&(a.return=t);null!==a;){var u=a.dependencies;if(null!==u){i=a.child;for(var o=u.firstContext;null!==o;){if(o.context===r){if(1===a.tag){(o=t5(-1,n&-n)).tag=2;var s=a.updateQueue;if(null!==s){var c=(s=s.shared).pending;null===c?o.next=o:(o.next=c.next,c.next=o),s.pending=o}}a.lanes|=n,null!==(o=a.alternate)&&(o.lanes|=n),tZ(a.return,n,t),u.lanes|=n;break}o=o.next}}else if(10===a.tag)i=a.type===t.type?null:a.child;else if(18===a.tag){if(null===(i=a.return))throw Error(f(341));i.lanes|=n,null!==(u=i.alternate)&&(u.lanes|=n),tZ(i,n,t),i=a.sibling}else i=a.child;if(null!==i)i.return=a;else for(i=a;null!==i;){if(i===t){i=null;break}if(null!==(a=i.sibling)){a.return=i.return,i=a;break}i=i.return}a=i}r1(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,tJ(t,n),r=r(l=t0(l)),t.flags|=1,r1(e,t,r,n),t.child;case 14:return l=tB(r=t.type,t.pendingProps),l=tB(r.type,l),r3(e,t,r,l,n);case 15:return r4(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:tB(r,l),null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),t.tag=1,ti(r)?(e=!0,tc(t)):e=!1,tJ(t,n),na(t,r,l),nu(t,r,l,n),r7(null,t,r,!0,e,n);case 19:return ls(e,t,n);case 22:return r5(e,t,n)}throw Error(f(156,t.tag))},u.attemptContinuousHydration=function(e){13===e.tag&&(af(e,0x8000000,as()),aK(e,0x8000000))},u.attemptHydrationAtCurrentPriority=function(e){if(13===e.tag){var t=as(),n=ac(e);af(e,n,t),aK(e,n)}},u.attemptSynchronousHydration=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=tv(t.pendingLanes);0!==n&&(tk(t,1|n),ap(t,tN()),0==(6&lV)&&(l9(),tH()))}break;case 13:var r=as();ay(function(){return af(e,1,r)}),aK(e,1)}},u.batchedUpdates=function(e,t){var n=lV;lV|=1;try{return e(t)}finally{0===(lV=n)&&(l9(),tO&&tH())}},u.createComponentSelector=function(e){return{$$typeof:lL,value:e}},u.createContainer=function(e,t,n,r,l,a,i){return a$(e,t,!1,null,n,r,l,a,i)},u.createHasPseudoClassSelector=function(e){return{$$typeof:lT,value:e}},u.createHydrationContainer=function(e,t,n,r,l,a,i,u,o){return(e=a$(n,r,!0,e,l,a,i,u,o)).context=aV(null),n=e.current,(a=t5(r=as(),l=ac(n))).callback=null!=t?t:null,t6(n,a),e.current.lanes=l,tw(e,l,r),ap(e,r),e},u.createPortal=function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:h,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}},u.createRoleSelector=function(e){return{$$typeof:lR,value:e}},u.createTestNameSelector=function(e){return{$$typeof:lj,value:e}},u.createTextSelector=function(e){return{$$typeof:lD,value:e}},u.deferredUpdates=function(e){var t=tx,n=l$.transition;try{return l$.transition=null,tx=16,e()}finally{tx=t,l$.transition=n}},u.discreteUpdates=function(e,t,n,r,l){var a=tx,i=l$.transition;try{return l$.transition=null,tx=1,e(t,n,r,l)}finally{tx=a,l$.transition=i,0===lV&&l9()}},u.findAllNodes=lQ,u.findBoundingRects=function(e,t){if(!ea)throw Error(f(363));t=lQ(e,t),e=[];for(var n=0;n<t.length;n++)e.push(eu(t[n]));for(t=e.length-1;0<t;t--){n=e[t];for(var r=n.x,l=r+n.width,a=n.y,i=a+n.height,u=t-1;0<=u;u--)if(t!==u){var o=e[u],s=o.x,c=s+o.width,d=o.y,p=d+o.height;if(r>=s&&a>=d&&l<=c&&i<=p){e.splice(t,1);break}if(r!==s||n.width!==o.width||p<a||d>i){if(!(a!==d||n.height!==o.height||c<r||s>l)){s>r&&(o.width+=s-r,o.x=r),c<l&&(o.width=l-s),e.splice(t,1);break}}else{d>a&&(o.height+=d-a,o.y=a),p<i&&(o.height=i-d),e.splice(t,1);break}}}return e},u.findHostInstance=aY,u.findHostInstanceWithNoPortals=function(e){return null===(e=null!==(e=M(e))?function e(t){if(5===t.tag||6===t.tag)return t;for(t=t.child;null!==t;){if(4!==t.tag){var n=e(t);if(null!==n)return n}t=t.sibling}return null}(e):null)?null:e.stateNode},u.findHostInstanceWithWarning=function(e){return aY(e)},u.flushControlled=function(e){var t=lV;lV|=1;var n=l$.transition,r=tx;try{l$.transition=null,tx=1,e()}finally{tx=r,l$.transition=n,0===(lV=t)&&(l9(),tH())}},u.flushPassiveEffects=aN,u.flushSync=ay,u.focusWithin=function(e,t){if(!ea)throw Error(f(363));for(t=Array.from(t=lH(e=lO(e),t)),e=0;e<t.length;){var n=t[e++];if(!es(n)){if(5===n.tag&&ef(n.stateNode))return!0;for(n=n.child;null!==n;)t.push(n),n=n.sibling}}return!1},u.getCurrentUpdatePriority=function(){return tx},u.getFindAllNodesFailureDescription=function(e,t){if(!ea)throw Error(f(363));var n=0,r=[];e=[lO(e),0];for(var l=0;l<e.length;){var a=e[l++],i=e[l++],u=t[i];if((5!==a.tag||!es(a))&&(lU(a,u)&&(r.push(lA(u)),++i>n&&(n=i)),i<t.length))for(a=a.child;null!==a;)e.push(a,i),a=a.sibling}if(n<t.length){for(e=[];n<t.length;n++)e.push(lA(t[n]));return"findAllNodes was able to match part of the selector:\n  "+r.join(" > ")+"\n\nNo matching component was found for:\n  "+e.join(" > ")}return null},u.getPublicRootInstance=function(e){return(e=e.current).child?5===e.child.tag?j(e.child.stateNode):e.child.stateNode:null},u.injectIntoDevTools=function(e){if(e={bundleType:e.bundleType,version:e.version,rendererPackageName:e.rendererPackageName,rendererConfig:e.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:d.ReactCurrentDispatcher,findHostInstanceByFiber:aX,findFiberByHostInstance:e.findFiberByHostInstance||aZ,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.0.0-fc46dba67-20220329"},"undefined"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)e=!1;else{var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled||!t.supportsFiber)e=!0;else{try{tR=t.inject(e),tj=t}catch(e){}e=!!t.checkDCE}}return e},u.isAlreadyRendering=function(){return!1},u.observeVisibleRects=function(e,t,n,r){if(!ea)throw Error(f(363));var l=ed(e=lQ(e,t),n,r).disconnect;return{disconnect:function(){l()}}},u.registerMutableSourceForHydration=function(e,t){var n=t._getVersion;n=n(t._source),null==e.mutableSourceEagerHydrationData?e.mutableSourceEagerHydrationData=[t,n]:e.mutableSourceEagerHydrationData.push(t,n)},u.runWithPriority=function(e,t){var n=tx;try{return tx=e,t()}finally{tx=n}},u.shouldError=function(){return null},u.shouldSuspend=function(){return!1},u.updateContainer=function(e,t,n,r){var l=t.current,a=as(),i=ac(l);return n=aV(n),null===t.context?t.context=n:t.pendingContext=n,(t=t5(a,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),t6(l,t),null!==(e=af(l,i,a))&&t8(e,l,i),i},u}},45220:(e,t,n)=>{"use strict";e.exports=n(19166)},61933:(e,t,n)=>{"use strict";e.exports=n(70678)},67358:(e,t)=>{"use strict";function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,l=e[r];if(0<a(l,t))e[r]=t,e[n]=l,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function l(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,l=e.length,i=l>>>1;r<i;){var u=2*(r+1)-1,o=e[u],s=u+1,c=e[s];if(0>a(o,n))s<l&&0>a(c,o)?(e[r]=c,e[s]=n,r=s):(e[r]=o,e[u]=n,r=u);else if(s<l&&0>a(c,n))e[r]=c,e[s]=n,r=s;else break}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var i,u=performance;t.unstable_now=function(){return u.now()}}else{var o=Date,s=o.now();t.unstable_now=function(){return o.now()-s}}var c=[],f=[],d=1,p=null,h=3,m=!1,g=!1,v=!1,b="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,S="undefined"!=typeof setImmediate?setImmediate:null;function w(e){for(var t=r(f);null!==t;){if(null===t.callback)l(f);else if(t.startTime<=e)l(f),t.sortIndex=t.expirationTime,n(c,t);else break;t=r(f)}}function k(e){if(v=!1,w(e),!g)if(null!==r(c))g=!0,T(x);else{var t=r(f);null!==t&&R(k,t.startTime-e)}}function x(e,n){g=!1,v&&(v=!1,y(P),P=-1),m=!0;var a=h;try{for(w(n),p=r(c);null!==p&&(!(p.expirationTime>n)||e&&!N());){var i=p.callback;if("function"==typeof i){p.callback=null,h=p.priorityLevel;var u=i(p.expirationTime<=n);n=t.unstable_now(),"function"==typeof u?p.callback=u:p===r(c)&&l(c),w(n)}else l(c);p=r(c)}if(null!==p)var o=!0;else{var s=r(f);null!==s&&R(k,s.startTime-n),o=!1}return o}finally{p=null,h=a,m=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var _=!1,E=null,P=-1,C=5,z=-1;function N(){return!(t.unstable_now()-z<C)}function I(){if(null!==E){var e=t.unstable_now();z=e;var n=!0;try{n=E(!0,e)}finally{n?i():(_=!1,E=null)}}else _=!1}if("function"==typeof S)i=function(){S(I)};else if("undefined"!=typeof MessageChannel){var M=new MessageChannel,L=M.port2;M.port1.onmessage=I,i=function(){L.postMessage(null)}}else i=function(){b(I,0)};function T(e){E=e,_||(_=!0,i())}function R(e,n){P=b(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){g||m||(g=!0,T(x))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,l,a){var i=t.unstable_now();switch(a="object"==typeof a&&null!==a&&"number"==typeof(a=a.delay)&&0<a?i+a:i,e){case 1:var u=-1;break;case 2:u=250;break;case 5:u=0x3fffffff;break;case 4:u=1e4;break;default:u=5e3}return u=a+u,e={id:d++,callback:l,priorityLevel:e,startTime:a,expirationTime:u,sortIndex:-1},a>i?(e.sortIndex=a,n(f,e),null===r(c)&&e===r(f)&&(v?(y(P),P=-1):v=!0,R(k,a-i))):(e.sortIndex=u,n(c,e),g||m||(g=!0,T(x))),e},t.unstable_shouldYield=N,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},70678:(e,t)=>{"use strict";t.ConcurrentRoot=1,t.ContinuousEventPriority=4,t.DefaultEventPriority=16,t.DiscreteEventPriority=1},72407:(e,t,n)=>{"use strict";e.exports=n(67358)}}]);