(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/effects/AnimatedCursor.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const AnimatedCursor = ()=>{
    _s();
    const [position, setPosition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        x: 0,
        y: 0
    });
    const [clicked, setClicked] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [hovered, setHovered] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AnimatedCursor.useEffect": ()=>{
            const handleMouseMove = {
                "AnimatedCursor.useEffect.handleMouseMove": (e)=>{
                    setPosition({
                        x: e.clientX,
                        y: e.clientY
                    });
                }
            }["AnimatedCursor.useEffect.handleMouseMove"];
            const handleMouseDown = {
                "AnimatedCursor.useEffect.handleMouseDown": ()=>{
                    setClicked(true);
                    setTimeout({
                        "AnimatedCursor.useEffect.handleMouseDown": ()=>setClicked(false)
                    }["AnimatedCursor.useEffect.handleMouseDown"], 300);
                }
            }["AnimatedCursor.useEffect.handleMouseDown"];
            const handleMouseEnter = {
                "AnimatedCursor.useEffect.handleMouseEnter": ()=>{
                    document.body.style.cursor = 'none';
                }
            }["AnimatedCursor.useEffect.handleMouseEnter"];
            const handleMouseLeave = {
                "AnimatedCursor.useEffect.handleMouseLeave": ()=>{
                    document.body.style.cursor = 'auto';
                }
            }["AnimatedCursor.useEffect.handleMouseLeave"];
            // Add hover detection for interactive elements
            const handleMouseOver = {
                "AnimatedCursor.useEffect.handleMouseOver": (e)=>{
                    const target = e.target;
                    const isInteractive = target.tagName.toLowerCase() === 'button' || target.tagName.toLowerCase() === 'a' || target.closest('button') || target.closest('a');
                    setHovered(!!isInteractive);
                }
            }["AnimatedCursor.useEffect.handleMouseOver"];
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mousedown', handleMouseDown);
            document.addEventListener('mouseenter', handleMouseEnter);
            document.addEventListener('mouseleave', handleMouseLeave);
            document.addEventListener('mouseover', handleMouseOver);
            return ({
                "AnimatedCursor.useEffect": ()=>{
                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mousedown', handleMouseDown);
                    document.removeEventListener('mouseenter', handleMouseEnter);
                    document.removeEventListener('mouseleave', handleMouseLeave);
                    document.removeEventListener('mouseover', handleMouseOver);
                    document.body.style.cursor = 'auto';
                }
            })["AnimatedCursor.useEffect"];
        }
    }["AnimatedCursor.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "fixed top-0 left-0 w-8 h-8 rounded-full border border-neon-green z-[9999] pointer-events-none",
                animate: {
                    x: position.x - 16,
                    y: position.y - 16,
                    scale: clicked ? 0.8 : hovered ? 1.5 : 1
                },
                transition: {
                    type: "spring",
                    stiffness: 300,
                    damping: 20,
                    mass: 0.5
                }
            }, void 0, false, {
                fileName: "[project]/src/components/effects/AnimatedCursor.tsx",
                lineNumber: 61,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "fixed top-0 left-0 w-2 h-2 bg-neon-green rounded-full z-[10000] pointer-events-none",
                animate: {
                    x: position.x - 4,
                    y: position.y - 4,
                    opacity: clicked ? 0.5 : 1
                },
                transition: {
                    type: "spring",
                    stiffness: 400,
                    damping: 15
                }
            }, void 0, false, {
                fileName: "[project]/src/components/effects/AnimatedCursor.tsx",
                lineNumber: 77,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
_s(AnimatedCursor, "SIKiNpHBpIKGjm7Hu0Vh2gJQUGo=");
_c = AnimatedCursor;
const __TURBOPACK__default__export__ = AnimatedCursor;
var _c;
__turbopack_context__.k.register(_c, "AnimatedCursor");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/effects/AnimatedCursor.tsx [app-client] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/components/effects/AnimatedCursor.tsx [app-client] (ecmascript)"));
}}),
}]);

//# sourceMappingURL=src_components_effects_AnimatedCursor_tsx_1a540175._.js.map