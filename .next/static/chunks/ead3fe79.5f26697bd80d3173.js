"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[13],{24067:(r,e,t)=>{t.r(e),t.d(e,{default:()=>n});let n=(()=>{var r="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.ready=new Promise((r,e)=>{t=r,n=e});var t,n,o,a,i,s,u,l,d,c,f,h,m,p,v,y,g,w,E,_,b,k,F,A,D,P,T,S,M=Object.assign({},e),C="./this.program",x="";"undefined"!=typeof document&&document.currentScript&&(x=document.currentScript.src),r&&(x=r),x=0!==x.indexOf("blob:")?x.substr(0,x.replace(/[?#].*/,"").lastIndexOf("/")+1):"",o=r=>{var e=new XMLHttpRequest;return e.open("GET",r,!1),e.send(null),e.responseText},a=(r,e,t)=>{var n=new XMLHttpRequest;n.open("GET",r,!0),n.responseType="arraybuffer",n.onload=()=>{if(200==n.status||0==n.status&&n.response)return void e(n.response);t()},n.onerror=t,n.send(null)};var j=e.print||console.log.bind(console),R=e.printErr||console.error.bind(console);Object.assign(e,M),M=null,e.arguments&&e.arguments,e.thisProgram&&(C=e.thisProgram),e.quit&&e.quit,e.wasmBinary&&(s=e.wasmBinary),"object"!=typeof WebAssembly&&q("no native wasm support detected");var O=!1;function W(){var r=u.buffer;e.HEAP8=l=new Int8Array(r),e.HEAP16=c=new Int16Array(r),e.HEAPU8=d=new Uint8Array(r),e.HEAPU16=f=new Uint16Array(r),e.HEAP32=h=new Int32Array(r),e.HEAPU32=m=new Uint32Array(r),e.HEAPF32=p=new Float32Array(r),e.HEAPF64=v=new Float64Array(r)}var z=[],N=[],B=[],I=0,L=null,U=null;function H(r){I++,e.monitorRunDependencies&&e.monitorRunDependencies(I)}function Y(r){if(I--,e.monitorRunDependencies&&e.monitorRunDependencies(I),0==I&&(null!==L&&(clearInterval(L),L=null),U)){var t=U;U=null,t()}}function q(r){e.onAbort&&e.onAbort(r),R(r="Aborted("+r+")"),O=!0,r+=". Build with -sASSERTIONS for more info.";var t=new WebAssembly.RuntimeError(r);throw n(t),t}var V=r=>r.startsWith("data:application/octet-stream;base64,");function X(r){if(r==y&&s)return new Uint8Array(s);if(i)return i(r);throw"both async and sync fetching of the wasm failed"}function G(r,e,t){return(!s&&"function"==typeof fetch?fetch(r,{credentials:"same-origin"}).then(e=>{if(!e.ok)throw"failed to load wasm binary file at '"+r+"'";return e.arrayBuffer()}).catch(()=>X(r)):Promise.resolve().then(()=>X(r))).then(r=>WebAssembly.instantiate(r,e)).then(r=>r).then(t,r=>{R("failed to asynchronously prepare wasm: ".concat(r)),q(r)})}V(y="boolean.wasm")||(D=y,y=e.locateFile?e.locateFile(D,x):x+D);var J=r=>{for(;r.length>0;)r.shift()(e)};function K(r){this.excPtr=r,this.ptr=r-24,this.set_type=function(r){m[this.ptr+4>>2]=r},this.get_type=function(){return m[this.ptr+4>>2]},this.set_destructor=function(r){m[this.ptr+8>>2]=r},this.get_destructor=function(){return m[this.ptr+8>>2]},this.set_caught=function(r){r=+!!r,l[this.ptr+12|0]=r},this.get_caught=function(){return 0!=l[this.ptr+12|0]},this.set_rethrown=function(r){r=+!!r,l[this.ptr+13|0]=r},this.get_rethrown=function(){return 0!=l[this.ptr+13|0]},this.init=function(r,e){this.set_adjusted_ptr(0),this.set_type(r),this.set_destructor(e)},this.set_adjusted_ptr=function(r){m[this.ptr+16>>2]=r},this.get_adjusted_ptr=function(){return m[this.ptr+16>>2]},this.get_exception_ptr=function(){if(eM(this.get_type()))return m[this.excPtr>>2];var r=this.get_adjusted_ptr();return 0!==r?r:this.excPtr}}e.noExitRuntime;var $=0,Z=0,Q={},rr=r=>{for(;r.length;){var e=r.pop();r.pop()(e)}};function re(r){return this.fromWireType(h[r>>2])}var rt={},rn={},ro={},ra=r=>{throw new E(r)},ri=(r,e,t)=>{function n(e){var n=t(e);n.length!==r.length&&ra("Mismatched type converter count");for(var o=0;o<r.length;++o)rl(r[o],n[o])}r.forEach(function(r){ro[r]=e});var o=Array(e.length),a=[],i=0;e.forEach((r,e)=>{rn.hasOwnProperty(r)?o[e]=rn[r]:(a.push(r),rt.hasOwnProperty(r)||(rt[r]=[]),rt[r].push(()=>{o[e]=rn[r],++i===a.length&&n(o)}))}),0===a.length&&n(o)},rs=r=>{for(var e="",t=r;d[t];)e+=_[d[t++]];return e},ru=r=>{throw new b(r)};function rl(r,e){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!("argPackAdvance"in e))throw TypeError("registerType registeredInstance requires argPackAdvance");return function(r,e){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};var n=e.name;if(r||ru('type "'.concat(n,'" must have a positive integer typeid pointer')),rn.hasOwnProperty(r))if(t.ignoreDuplicateRegistrations)return;else ru("Cannot register type '".concat(n,"' twice"));if(rn[r]=e,delete ro[r],rt.hasOwnProperty(r)){var o=rt[r];delete rt[r],o.forEach(r=>r())}}(r,e,t)}function rd(){this.allocated=[void 0],this.freelist=[]}var rc=new rd,rf=r=>{r>=rc.reserved&&0==--rc.get(r).refcount&&rc.free(r)},rh=r=>(r||ru("Cannot use deleted val. handle = "+r),rc.get(r).value),rm=r=>{switch(r){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return rc.allocate({refcount:1,value:r})}},rp=(r,e,t)=>{if(void 0===r[e].overloadTable){var n=r[e];r[e]=function(){return r[e].overloadTable.hasOwnProperty(arguments.length)||ru("Function '".concat(t,"' called with an invalid number of arguments (").concat(arguments.length,") - expects one of (").concat(r[e].overloadTable,")!")),r[e].overloadTable[arguments.length].apply(this,arguments)},r[e].overloadTable=[],r[e].overloadTable[n.argCount]=n}},rv=(r,t,n)=>{e.hasOwnProperty(r)?((void 0===n||void 0!==e[r].overloadTable&&void 0!==e[r].overloadTable[n])&&ru("Cannot register public name '".concat(r,"' twice")),rp(e,r,r),e.hasOwnProperty(n)&&ru("Cannot register multiple overloads of a function with the same number of arguments (".concat(n,")!")),e[r].overloadTable[n]=t):(e[r]=t,void 0!==n&&(e[r].numArguments=n))},ry=(r,e,t)=>{switch(e){case 1:return t?function(r){return this.fromWireType(l[0|r])}:function(r){return this.fromWireType(d[0|r])};case 2:return t?function(r){return this.fromWireType(c[r>>1])}:function(r){return this.fromWireType(f[r>>1])};case 4:return t?function(r){return this.fromWireType(h[r>>2])}:function(r){return this.fromWireType(m[r>>2])};default:throw TypeError("invalid integer width (".concat(e,"): ").concat(r))}},rg=(r,e)=>Object.defineProperty(e,"name",{value:r}),rw=r=>{var e=eF(r),t=rs(e);return eD(e),t},rE=(r,e)=>{var t=rn[r];return void 0===t&&ru(e+" has unknown type "+rw(r)),t},r_=(r,e)=>{switch(e){case 4:return function(r){return this.fromWireType(p[r>>2])};case 8:return function(r){return this.fromWireType(v[r>>3])};default:throw TypeError("invalid float width (".concat(e,"): ").concat(r))}},rb=(r,e)=>{for(var t=[],n=0;n<r;n++)t.push(m[e+4*n>>2]);return t},rk=(r,t,n)=>{e.hasOwnProperty(r)||ra("Replacing nonexistant public symbol"),void 0!==e[r].overloadTable&&void 0!==n?e[r].overloadTable[n]=t:(e[r]=t,e[r].argCount=n)},rF=(r,t,n)=>{var o=e["dynCall_"+r];return n&&n.length?o.apply(null,[t].concat(n)):o.call(null,t)},rA=[],rD=r=>{var e=rA[r];return e||(r>=rA.length&&(rA.length=r+1),rA[r]=e=k.get(r)),e},rP=(r,e,t)=>r.includes("j")?rF(r,e,t):rD(e).apply(null,t),rT=(r,e)=>{var t=[];return function(){return t.length=0,Object.assign(t,arguments),rP(r,e,t)}},rS=(r,e)=>{var t=(r=rs(r)).includes("j")?rT(r,e):rD(e);return"function"!=typeof t&&ru("unknown function pointer with signature ".concat(r,": ").concat(e)),t},rM=(r,e)=>{var t=[],n={};throw e.forEach(function r(e){if(!n[e]&&!rn[e]){if(ro[e])return void ro[e].forEach(r);t.push(e),n[e]=!0}}),new F("".concat(r,": ")+t.map(rw).join([", "]))},rC=r=>{let e=(r=r.trim()).indexOf("(");return -1!==e?r.substr(0,e):r},rx=(r,e,t)=>{switch(e){case 1:return t?r=>l[0|r]:r=>d[0|r];case 2:return t?r=>c[r>>1]:r=>f[r>>1];case 4:return t?r=>h[r>>2]:r=>m[r>>2];default:throw TypeError("invalid integer width (".concat(e,"): ").concat(r))}};function rj(r){return this.fromWireType(m[r>>2])}var rR=(r,e,t,n)=>{if(!(n>0))return 0;for(var o=t,a=t+n-1,i=0;i<r.length;++i){var s=r.charCodeAt(i);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&r.charCodeAt(++i)),s<=127){if(t>=a)break;e[t++]=s}else if(s<=2047){if(t+1>=a)break;e[t++]=192|s>>6,e[t++]=128|63&s}else if(s<=65535){if(t+2>=a)break;e[t++]=224|s>>12,e[t++]=128|s>>6&63,e[t++]=128|63&s}else{if(t+3>=a)break;e[t++]=240|s>>18,e[t++]=128|s>>12&63,e[t++]=128|s>>6&63,e[t++]=128|63&s}}return e[t]=0,t-o},rO=(r,e,t)=>rR(r,d,e,t),rW=r=>{for(var e=0,t=0;t<r.length;++t){var n=r.charCodeAt(t);n<=127?e++:n<=2047?e+=2:n>=55296&&n<=57343?(e+=4,++t):e+=3}return e},rz="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,rN=(r,e,t)=>{for(var n=e+t,o=e;r[o]&&!(o>=n);)++o;if(o-e>16&&r.buffer&&rz)return rz.decode(r.subarray(e,o));for(var a="";e<o;){var i=r[e++];if(!(128&i)){a+=String.fromCharCode(i);continue}var s=63&r[e++];if((224&i)==192){a+=String.fromCharCode((31&i)<<6|s);continue}var u=63&r[e++];if((i=(240&i)==224?(15&i)<<12|s<<6|u:(7&i)<<18|s<<12|u<<6|63&r[e++])<65536)a+=String.fromCharCode(i);else{var l=i-65536;a+=String.fromCharCode(55296|l>>10,56320|1023&l)}}return a},rB=(r,e)=>r?rN(d,r,e):"",rI="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,rL=(r,e)=>{for(var t=r,n=t>>1,o=n+e/2;!(n>=o)&&f[n];)++n;if((t=n<<1)-r>32&&rI)return rI.decode(d.subarray(r,t));for(var a="",i=0;!(i>=e/2);++i){var s=c[r+2*i>>1];if(0==s)break;a+=String.fromCharCode(s)}return a},rU=(r,e,t)=>{if(void 0===t&&(t=0x7fffffff),t<2)return 0;for(var n=e,o=(t-=2)<2*r.length?t/2:r.length,a=0;a<o;++a){var i=r.charCodeAt(a);c[e>>1]=i,e+=2}return c[e>>1]=0,e-n},rH=r=>2*r.length,rY=(r,e)=>{for(var t=0,n="";!(t>=e/4);){var o=h[r+4*t>>2];if(0==o)break;if(++t,o>=65536){var a=o-65536;n+=String.fromCharCode(55296|a>>10,56320|1023&a)}else n+=String.fromCharCode(o)}return n},rq=(r,e,t)=>{if(void 0===t&&(t=0x7fffffff),t<4)return 0;for(var n=e,o=n+t-4,a=0;a<r.length;++a){var i=r.charCodeAt(a);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&r.charCodeAt(++a)),h[e>>2]=i,(e+=4)+4>o)break}return h[e>>2]=0,e-n},rV=r=>{for(var e=0,t=0;t<r.length;++t){var n=r.charCodeAt(t);n>=55296&&n<=57343&&++t,e+=4}return e},rX=()=>0x80000000,rG=r=>{var e=(r-u.buffer.byteLength+65535)/65536;try{return u.grow(e),W(),1}catch(r){}},rJ={},rK=()=>C||"./this.program",r$=()=>{if(!r$.strings){var r={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:rK()};for(var e in rJ)void 0===rJ[e]?delete r[e]:r[e]=rJ[e];var t=[];for(var e in r)t.push("".concat(e,"=").concat(r[e]));r$.strings=t}return r$.strings},rZ=(r,e)=>{for(var t=0;t<r.length;++t)l[0|e++]=r.charCodeAt(t);l[0|e]=0},rQ={isAbs:r=>"/"===r.charAt(0),splitPath:r=>/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(r).slice(1),normalizeArray:(r,e)=>{for(var t=0,n=r.length-1;n>=0;n--){var o=r[n];"."===o?r.splice(n,1):".."===o?(r.splice(n,1),t++):t&&(r.splice(n,1),t--)}if(e)for(;t;t--)r.unshift("..");return r},normalize:r=>{var e=rQ.isAbs(r),t="/"===r.substr(-1);return(r=rQ.normalizeArray(r.split("/").filter(r=>!!r),!e).join("/"))||e||(r="."),r&&t&&(r+="/"),(e?"/":"")+r},dirname:r=>{var e=rQ.splitPath(r),t=e[0],n=e[1];return t||n?(n&&(n=n.substr(0,n.length-1)),t+n):"."},basename:r=>{if("/"===r)return"/";var e=(r=(r=rQ.normalize(r)).replace(/\/$/,"")).lastIndexOf("/");return -1===e?r:r.substr(e+1)},join:function(){var r=Array.prototype.slice.call(arguments);return rQ.normalize(r.join("/"))},join2:(r,e)=>rQ.normalize(r+"/"+e)},r0=()=>{if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return r=>crypto.getRandomValues(r);q("initRandomDevice")},r1=r=>(r1=r0())(r),r2={resolve:function(){for(var r="",e=!1,t=arguments.length-1;t>=-1&&!e;t--){var n=t>=0?arguments[t]:ea.cwd();if("string"!=typeof n)throw TypeError("Arguments to path.resolve must be strings");if(!n)return"";r=n+"/"+r,e=rQ.isAbs(n)}return r=rQ.normalizeArray(r.split("/").filter(r=>!!r),!e).join("/"),(e?"/":"")+r||"."},relative:(r,e)=>{function t(r){for(var e=0;e<r.length&&""===r[e];e++);for(var t=r.length-1;t>=0&&""===r[t];t--);return e>t?[]:r.slice(e,t-e+1)}r=r2.resolve(r).substr(1),e=r2.resolve(e).substr(1);for(var n=t(r.split("/")),o=t(e.split("/")),a=Math.min(n.length,o.length),i=a,s=0;s<a;s++)if(n[s]!==o[s]){i=s;break}for(var u=[],s=i;s<n.length;s++)u.push("..");return(u=u.concat(o.slice(i))).join("/")}},r3=[];function r4(r,e,t){var n=Array(t>0?t:rW(r)+1),o=rR(r,n,0,n.length);return e&&(n.length=o),n}var r6=()=>{if(!r3.length){var r=null;if("undefined"!=typeof window&&"function"==typeof window.prompt?null!==(r=window.prompt("Input: "))&&(r+="\n"):"function"==typeof readline&&null!==(r=readline())&&(r+="\n"),!r)return null;r3=r4(r,!0)}return r3.shift()},r5={ttys:[],init(){},shutdown(){},register(r,e){r5.ttys[r]={input:[],output:[],ops:e},ea.registerDevice(r,r5.stream_ops)},stream_ops:{open(r){var e=r5.ttys[r.node.rdev];if(!e)throw new ea.ErrnoError(43);r.tty=e,r.seekable=!1},close(r){r.tty.ops.fsync(r.tty)},fsync(r){r.tty.ops.fsync(r.tty)},read(r,e,t,n,o){if(!r.tty||!r.tty.ops.get_char)throw new ea.ErrnoError(60);for(var a,i=0,s=0;s<n;s++){try{a=r.tty.ops.get_char(r.tty)}catch(r){throw new ea.ErrnoError(29)}if(void 0===a&&0===i)throw new ea.ErrnoError(6);if(null==a)break;i++,e[t+s]=a}return i&&(r.node.timestamp=Date.now()),i},write(r,e,t,n,o){if(!r.tty||!r.tty.ops.put_char)throw new ea.ErrnoError(60);try{for(var a=0;a<n;a++)r.tty.ops.put_char(r.tty,e[t+a])}catch(r){throw new ea.ErrnoError(29)}return n&&(r.node.timestamp=Date.now()),a}},default_tty_ops:{get_char:r=>r6(),put_char(r,e){null===e||10===e?(j(rN(r.output,0)),r.output=[]):0!=e&&r.output.push(e)},fsync(r){r.output&&r.output.length>0&&(j(rN(r.output,0)),r.output=[])},ioctl_tcgets:r=>({c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}),ioctl_tcsets:(r,e,t)=>0,ioctl_tiocgwinsz:r=>[24,80]},default_tty1_ops:{put_char(r,e){null===e||10===e?(R(rN(r.output,0)),r.output=[]):0!=e&&r.output.push(e)},fsync(r){r.output&&r.output.length>0&&(R(rN(r.output,0)),r.output=[])}}},r8=r=>{q()},r7={ops_table:null,mount:r=>r7.createNode(null,"/",16895,0),createNode(r,e,t,n){if(ea.isBlkdev(t)||ea.isFIFO(t))throw new ea.ErrnoError(63);r7.ops_table||(r7.ops_table={dir:{node:{getattr:r7.node_ops.getattr,setattr:r7.node_ops.setattr,lookup:r7.node_ops.lookup,mknod:r7.node_ops.mknod,rename:r7.node_ops.rename,unlink:r7.node_ops.unlink,rmdir:r7.node_ops.rmdir,readdir:r7.node_ops.readdir,symlink:r7.node_ops.symlink},stream:{llseek:r7.stream_ops.llseek}},file:{node:{getattr:r7.node_ops.getattr,setattr:r7.node_ops.setattr},stream:{llseek:r7.stream_ops.llseek,read:r7.stream_ops.read,write:r7.stream_ops.write,allocate:r7.stream_ops.allocate,mmap:r7.stream_ops.mmap,msync:r7.stream_ops.msync}},link:{node:{getattr:r7.node_ops.getattr,setattr:r7.node_ops.setattr,readlink:r7.node_ops.readlink},stream:{}},chrdev:{node:{getattr:r7.node_ops.getattr,setattr:r7.node_ops.setattr},stream:ea.chrdev_stream_ops}});var o=ea.createNode(r,e,t,n);return ea.isDir(o.mode)?(o.node_ops=r7.ops_table.dir.node,o.stream_ops=r7.ops_table.dir.stream,o.contents={}):ea.isFile(o.mode)?(o.node_ops=r7.ops_table.file.node,o.stream_ops=r7.ops_table.file.stream,o.usedBytes=0,o.contents=null):ea.isLink(o.mode)?(o.node_ops=r7.ops_table.link.node,o.stream_ops=r7.ops_table.link.stream):ea.isChrdev(o.mode)&&(o.node_ops=r7.ops_table.chrdev.node,o.stream_ops=r7.ops_table.chrdev.stream),o.timestamp=Date.now(),r&&(r.contents[e]=o,r.timestamp=o.timestamp),o},getFileDataAsTypedArray:r=>r.contents?r.contents.subarray?r.contents.subarray(0,r.usedBytes):new Uint8Array(r.contents):new Uint8Array(0),expandFileStorage(r,e){var t=r.contents?r.contents.length:0;if(!(t>=e)){e=Math.max(e,t*(t<1048576?2:1.125)>>>0),0!=t&&(e=Math.max(e,256));var n=r.contents;r.contents=new Uint8Array(e),r.usedBytes>0&&r.contents.set(n.subarray(0,r.usedBytes),0)}},resizeFileStorage(r,e){if(r.usedBytes!=e)if(0==e)r.contents=null,r.usedBytes=0;else{var t=r.contents;r.contents=new Uint8Array(e),t&&r.contents.set(t.subarray(0,Math.min(e,r.usedBytes))),r.usedBytes=e}},node_ops:{getattr(r){var e={};return e.dev=ea.isChrdev(r.mode)?r.id:1,e.ino=r.id,e.mode=r.mode,e.nlink=1,e.uid=0,e.gid=0,e.rdev=r.rdev,ea.isDir(r.mode)?e.size=4096:ea.isFile(r.mode)?e.size=r.usedBytes:ea.isLink(r.mode)?e.size=r.link.length:e.size=0,e.atime=new Date(r.timestamp),e.mtime=new Date(r.timestamp),e.ctime=new Date(r.timestamp),e.blksize=4096,e.blocks=Math.ceil(e.size/e.blksize),e},setattr(r,e){void 0!==e.mode&&(r.mode=e.mode),void 0!==e.timestamp&&(r.timestamp=e.timestamp),void 0!==e.size&&r7.resizeFileStorage(r,e.size)},lookup(r,e){throw ea.genericErrors[44]},mknod:(r,e,t,n)=>r7.createNode(r,e,t,n),rename(r,e,t){if(ea.isDir(r.mode)){var n;try{n=ea.lookupNode(e,t)}catch(r){}if(n)for(var o in n.contents)throw new ea.ErrnoError(55)}delete r.parent.contents[r.name],r.parent.timestamp=Date.now(),r.name=t,e.contents[t]=r,e.timestamp=r.parent.timestamp,r.parent=e},unlink(r,e){delete r.contents[e],r.timestamp=Date.now()},rmdir(r,e){var t=ea.lookupNode(r,e);for(var n in t.contents)throw new ea.ErrnoError(55);delete r.contents[e],r.timestamp=Date.now()},readdir(r){var e=[".",".."];for(var t in r.contents)r.contents.hasOwnProperty(t)&&e.push(t);return e},symlink(r,e,t){var n=r7.createNode(r,e,41471,0);return n.link=t,n},readlink(r){if(!ea.isLink(r.mode))throw new ea.ErrnoError(28);return r.link}},stream_ops:{read(r,e,t,n,o){var a=r.node.contents;if(o>=r.node.usedBytes)return 0;var i=Math.min(r.node.usedBytes-o,n);if(i>8&&a.subarray)e.set(a.subarray(o,o+i),t);else for(var s=0;s<i;s++)e[t+s]=a[o+s];return i},write(r,e,t,n,o,a){if(e.buffer===l.buffer&&(a=!1),!n)return 0;var i=r.node;if(i.timestamp=Date.now(),e.subarray&&(!i.contents||i.contents.subarray)){if(a)return i.contents=e.subarray(t,t+n),i.usedBytes=n,n;else if(0===i.usedBytes&&0===o)return i.contents=e.slice(t,t+n),i.usedBytes=n,n;else if(o+n<=i.usedBytes)return i.contents.set(e.subarray(t,t+n),o),n}if(r7.expandFileStorage(i,o+n),i.contents.subarray&&e.subarray)i.contents.set(e.subarray(t,t+n),o);else for(var s=0;s<n;s++)i.contents[o+s]=e[t+s];return i.usedBytes=Math.max(i.usedBytes,o+n),n},llseek(r,e,t){var n=e;if(1===t?n+=r.position:2===t&&ea.isFile(r.node.mode)&&(n+=r.node.usedBytes),n<0)throw new ea.ErrnoError(28);return n},allocate(r,e,t){r7.expandFileStorage(r.node,e+t),r.node.usedBytes=Math.max(r.node.usedBytes,e+t)},mmap(r,e,t,n,o){if(!ea.isFile(r.node.mode))throw new ea.ErrnoError(43);var a,i,s=r.node.contents;if(2&o||s.buffer!==l.buffer){if((t>0||t+e<s.length)&&(s=s.subarray?s.subarray(t,t+e):Array.prototype.slice.call(s,t,t+e)),i=!0,!(a=r8(e)))throw new ea.ErrnoError(48);l.set(s,a)}else i=!1,a=s.byteOffset;return{ptr:a,allocated:i}},msync:(r,e,t,n,o)=>(r7.stream_ops.write(r,e,0,n,t,!1),0)}},r9=(r,e,t,n)=>{var o=n?"":"al ".concat(r);a(r,t=>{var n;n='Loading data file "'.concat(r,'" failed (no arrayBuffer).'),t||q(n),e(new Uint8Array(t)),o&&Y(o)},e=>{if(t)t();else throw'Loading data file "'.concat(r,'" failed.')}),o&&H(o)},er=(r,e,t,n,o,a)=>{ea.createDataFile(r,e,t,n,o,a)},ee=e.preloadPlugins||[],et=(r,e,t,n)=>{"undefined"!=typeof Browser&&Browser.init();var o=!1;return ee.forEach(a=>{!o&&a.canHandle(e)&&(a.handle(r,e,t,n),o=!0)}),o},en=r=>{var e={r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090}[r];if(void 0===e)throw Error("Unknown file open mode: ".concat(r));return e},eo=(r,e)=>{var t=0;return r&&(t|=365),e&&(t|=146),t},ea={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath(r){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(r=r2.resolve(r)))return{path:"",node:null};if((e=Object.assign({follow_mount:!0,recurse_count:0},e)).recurse_count>8)throw new ea.ErrnoError(32);for(var t=r.split("/").filter(r=>!!r),n=ea.root,o="/",a=0;a<t.length;a++){var i=a===t.length-1;if(i&&e.parent)break;if(n=ea.lookupNode(n,t[a]),o=rQ.join2(o,t[a]),ea.isMountpoint(n)&&(!i||i&&e.follow_mount)&&(n=n.mounted.root),!i||e.follow)for(var s=0;ea.isLink(n.mode);){var u=ea.readlink(o);if(o=r2.resolve(rQ.dirname(o),u),n=ea.lookupPath(o,{recurse_count:e.recurse_count+1}).node,s++>40)throw new ea.ErrnoError(32)}}return{path:o,node:n}},getPath(r){for(var e;;){if(ea.isRoot(r)){var t=r.mount.mountpoint;if(!e)return t;return"/"!==t[t.length-1]?"".concat(t,"/").concat(e):t+e}e=e?"".concat(r.name,"/").concat(e):r.name,r=r.parent}},hashName(r,e){for(var t=0,n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n)|0;return(r+t>>>0)%ea.nameTable.length},hashAddNode(r){var e=ea.hashName(r.parent.id,r.name);r.name_next=ea.nameTable[e],ea.nameTable[e]=r},hashRemoveNode(r){var e=ea.hashName(r.parent.id,r.name);if(ea.nameTable[e]===r)ea.nameTable[e]=r.name_next;else for(var t=ea.nameTable[e];t;){if(t.name_next===r){t.name_next=r.name_next;break}t=t.name_next}},lookupNode(r,e){var t=ea.mayLookup(r);if(t)throw new ea.ErrnoError(t,r);for(var n=ea.hashName(r.id,e),o=ea.nameTable[n];o;o=o.name_next){var a=o.name;if(o.parent.id===r.id&&a===e)return o}return ea.lookup(r,e)},createNode(r,e,t,n){var o=new ea.FSNode(r,e,t,n);return ea.hashAddNode(o),o},destroyNode(r){ea.hashRemoveNode(r)},isRoot:r=>r===r.parent,isMountpoint:r=>!!r.mounted,isFile:r=>(61440&r)==32768,isDir:r=>(61440&r)==16384,isLink:r=>(61440&r)==40960,isChrdev:r=>(61440&r)==8192,isBlkdev:r=>(61440&r)==24576,isFIFO:r=>(61440&r)==4096,isSocket:r=>(49152&r)==49152,flagsToPermissionString(r){var e=["r","w","rw"][3&r];return 512&r&&(e+="w"),e},nodePermissions:(r,e)=>ea.ignorePermissions?0:e.includes("r")&&!(292&r.mode)||e.includes("w")&&!(146&r.mode)||e.includes("x")&&!(73&r.mode)?2:0,mayLookup(r){var e=ea.nodePermissions(r,"x");return e||2*!r.node_ops.lookup},mayCreate(r,e){try{return ea.lookupNode(r,e),20}catch(r){}return ea.nodePermissions(r,"wx")},mayDelete(r,e,t){try{n=ea.lookupNode(r,e)}catch(r){return r.errno}var n,o=ea.nodePermissions(r,"wx");if(o)return o;if(t){if(!ea.isDir(n.mode))return 54;if(ea.isRoot(n)||ea.getPath(n)===ea.cwd())return 10}else if(ea.isDir(n.mode))return 31;return 0},mayOpen:(r,e)=>r?ea.isLink(r.mode)?32:ea.isDir(r.mode)&&("r"!==ea.flagsToPermissionString(e)||512&e)?31:ea.nodePermissions(r,ea.flagsToPermissionString(e)):44,MAX_OPEN_FDS:4096,nextfd(){for(var r=0;r<=ea.MAX_OPEN_FDS;r++)if(!ea.streams[r])return r;throw new ea.ErrnoError(33)},getStreamChecked(r){var e=ea.getStream(r);if(!e)throw new ea.ErrnoError(8);return e},getStream:r=>ea.streams[r],createStream(r){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1;return ea.FSStream||(ea.FSStream=function(){this.shared={}},ea.FSStream.prototype={},Object.defineProperties(ea.FSStream.prototype,{object:{get(){return this.node},set(r){this.node=r}},isRead:{get(){return(2097155&this.flags)!=1}},isWrite:{get(){return(2097155&this.flags)!=0}},isAppend:{get(){return 1024&this.flags}},flags:{get(){return this.shared.flags},set(r){this.shared.flags=r}},position:{get(){return this.shared.position},set(r){this.shared.position=r}}})),r=Object.assign(new ea.FSStream,r),-1==e&&(e=ea.nextfd()),r.fd=e,ea.streams[e]=r,r},closeStream(r){ea.streams[r]=null},chrdev_stream_ops:{open(r){var e=ea.getDevice(r.node.rdev);r.stream_ops=e.stream_ops,r.stream_ops.open&&r.stream_ops.open(r)},llseek(){throw new ea.ErrnoError(70)}},major:r=>r>>8,minor:r=>255&r,makedev:(r,e)=>r<<8|e,registerDevice(r,e){ea.devices[r]={stream_ops:e}},getDevice:r=>ea.devices[r],getMounts(r){for(var e=[],t=[r];t.length;){var n=t.pop();e.push(n),t.push.apply(t,n.mounts)}return e},syncfs(r,e){"function"==typeof r&&(e=r,r=!1),ea.syncFSRequests++,ea.syncFSRequests>1&&R("warning: ".concat(ea.syncFSRequests," FS.syncfs operations in flight at once, probably just doing extra work"));var t=ea.getMounts(ea.root.mount),n=0;function o(r){return ea.syncFSRequests--,e(r)}function a(r){if(r)return a.errored?void 0:(a.errored=!0,o(r));++n>=t.length&&o(null)}t.forEach(e=>{if(!e.type.syncfs)return a(null);e.type.syncfs(e,r,a)})},mount(r,e,t){var n,o="/"===t,a=!t;if(o&&ea.root)throw new ea.ErrnoError(10);if(!o&&!a){var i=ea.lookupPath(t,{follow_mount:!1});if(t=i.path,n=i.node,ea.isMountpoint(n))throw new ea.ErrnoError(10);if(!ea.isDir(n.mode))throw new ea.ErrnoError(54)}var s={type:r,opts:e,mountpoint:t,mounts:[]},u=r.mount(s);return u.mount=s,s.root=u,o?ea.root=u:n&&(n.mounted=s,n.mount&&n.mount.mounts.push(s)),u},unmount(r){var e=ea.lookupPath(r,{follow_mount:!1});if(!ea.isMountpoint(e.node))throw new ea.ErrnoError(28);var t=e.node,n=t.mounted,o=ea.getMounts(n);Object.keys(ea.nameTable).forEach(r=>{for(var e=ea.nameTable[r];e;){var t=e.name_next;o.includes(e.mount)&&ea.destroyNode(e),e=t}}),t.mounted=null;var a=t.mount.mounts.indexOf(n);t.mount.mounts.splice(a,1)},lookup:(r,e)=>r.node_ops.lookup(r,e),mknod(r,e,t){var n=ea.lookupPath(r,{parent:!0}).node,o=rQ.basename(r);if(!o||"."===o||".."===o)throw new ea.ErrnoError(28);var a=ea.mayCreate(n,o);if(a)throw new ea.ErrnoError(a);if(!n.node_ops.mknod)throw new ea.ErrnoError(63);return n.node_ops.mknod(n,o,e,t)},create:(r,e)=>(e=(void 0!==e?e:438)&4095|32768,ea.mknod(r,e,0)),mkdir:(r,e)=>(e=(void 0!==e?e:511)&1023|16384,ea.mknod(r,e,0)),mkdirTree(r,e){for(var t=r.split("/"),n="",o=0;o<t.length;++o)if(t[o]){n+="/"+t[o];try{ea.mkdir(n,e)}catch(r){if(20!=r.errno)throw r}}},mkdev:(r,e,t)=>(void 0===t&&(t=e,e=438),e|=8192,ea.mknod(r,e,t)),symlink(r,e){if(!r2.resolve(r))throw new ea.ErrnoError(44);var t=ea.lookupPath(e,{parent:!0}).node;if(!t)throw new ea.ErrnoError(44);var n=rQ.basename(e),o=ea.mayCreate(t,n);if(o)throw new ea.ErrnoError(o);if(!t.node_ops.symlink)throw new ea.ErrnoError(63);return t.node_ops.symlink(t,n,r)},rename(r,e){var t,n,o,a,i=rQ.dirname(r),s=rQ.dirname(e),u=rQ.basename(r),l=rQ.basename(e);if(n=ea.lookupPath(r,{parent:!0}).node,o=ea.lookupPath(e,{parent:!0}).node,!n||!o)throw new ea.ErrnoError(44);if(n.mount!==o.mount)throw new ea.ErrnoError(75);var d=ea.lookupNode(n,u),c=r2.relative(r,s);if("."!==c.charAt(0))throw new ea.ErrnoError(28);if("."!==(c=r2.relative(e,i)).charAt(0))throw new ea.ErrnoError(55);try{a=ea.lookupNode(o,l)}catch(r){}if(d!==a){var f=ea.isDir(d.mode),h=ea.mayDelete(n,u,f);if(h||(h=a?ea.mayDelete(o,l,f):ea.mayCreate(o,l)))throw new ea.ErrnoError(h);if(!n.node_ops.rename)throw new ea.ErrnoError(63);if(ea.isMountpoint(d)||a&&ea.isMountpoint(a))throw new ea.ErrnoError(10);if(o!==n&&(h=ea.nodePermissions(n,"w")))throw new ea.ErrnoError(h);ea.hashRemoveNode(d);try{n.node_ops.rename(d,o,l)}catch(r){throw r}finally{ea.hashAddNode(d)}}},rmdir(r){var e=ea.lookupPath(r,{parent:!0}).node,t=rQ.basename(r),n=ea.lookupNode(e,t),o=ea.mayDelete(e,t,!0);if(o)throw new ea.ErrnoError(o);if(!e.node_ops.rmdir)throw new ea.ErrnoError(63);if(ea.isMountpoint(n))throw new ea.ErrnoError(10);e.node_ops.rmdir(e,t),ea.destroyNode(n)},readdir(r){var e=ea.lookupPath(r,{follow:!0}).node;if(!e.node_ops.readdir)throw new ea.ErrnoError(54);return e.node_ops.readdir(e)},unlink(r){var e=ea.lookupPath(r,{parent:!0}).node;if(!e)throw new ea.ErrnoError(44);var t=rQ.basename(r),n=ea.lookupNode(e,t),o=ea.mayDelete(e,t,!1);if(o)throw new ea.ErrnoError(o);if(!e.node_ops.unlink)throw new ea.ErrnoError(63);if(ea.isMountpoint(n))throw new ea.ErrnoError(10);e.node_ops.unlink(e,t),ea.destroyNode(n)},readlink(r){var e=ea.lookupPath(r).node;if(!e)throw new ea.ErrnoError(44);if(!e.node_ops.readlink)throw new ea.ErrnoError(28);return r2.resolve(ea.getPath(e.parent),e.node_ops.readlink(e))},stat(r,e){var t=ea.lookupPath(r,{follow:!e}).node;if(!t)throw new ea.ErrnoError(44);if(!t.node_ops.getattr)throw new ea.ErrnoError(63);return t.node_ops.getattr(t)},lstat:r=>ea.stat(r,!0),chmod(r,e,t){var n;if(!(n="string"==typeof r?ea.lookupPath(r,{follow:!t}).node:r).node_ops.setattr)throw new ea.ErrnoError(63);n.node_ops.setattr(n,{mode:4095&e|-4096&n.mode,timestamp:Date.now()})},lchmod(r,e){ea.chmod(r,e,!0)},fchmod(r,e){var t=ea.getStreamChecked(r);ea.chmod(t.node,e)},chown(r,e,t,n){var o;if(!(o="string"==typeof r?ea.lookupPath(r,{follow:!n}).node:r).node_ops.setattr)throw new ea.ErrnoError(63);o.node_ops.setattr(o,{timestamp:Date.now()})},lchown(r,e,t){ea.chown(r,e,t,!0)},fchown(r,e,t){var n=ea.getStreamChecked(r);ea.chown(n.node,e,t)},truncate(r,e){if(e<0)throw new ea.ErrnoError(28);if("string"==typeof r){var t;t=ea.lookupPath(r,{follow:!0}).node}else t=r;if(!t.node_ops.setattr)throw new ea.ErrnoError(63);if(ea.isDir(t.mode))throw new ea.ErrnoError(31);if(!ea.isFile(t.mode))throw new ea.ErrnoError(28);var n=ea.nodePermissions(t,"w");if(n)throw new ea.ErrnoError(n);t.node_ops.setattr(t,{size:e,timestamp:Date.now()})},ftruncate(r,e){var t=ea.getStreamChecked(r);if((2097155&t.flags)==0)throw new ea.ErrnoError(28);ea.truncate(t.node,e)},utime(r,e,t){var n=ea.lookupPath(r,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(e,t)})},open(r,t,n){if(""===r)throw new ea.ErrnoError(44);if(t="string"==typeof t?en(t):t,n=void 0===n?438:n,n=64&t?4095&n|32768:0,"object"==typeof r)o=r;else{r=rQ.normalize(r);try{var o;o=ea.lookupPath(r,{follow:!(131072&t)}).node}catch(r){}}var a=!1;if(64&t)if(o){if(128&t)throw new ea.ErrnoError(20)}else o=ea.mknod(r,n,0),a=!0;if(!o)throw new ea.ErrnoError(44);if(ea.isChrdev(o.mode)&&(t&=-513),65536&t&&!ea.isDir(o.mode))throw new ea.ErrnoError(54);if(!a){var i=ea.mayOpen(o,t);if(i)throw new ea.ErrnoError(i)}512&t&&!a&&ea.truncate(o,0),t&=-131713;var s=ea.createStream({node:o,path:ea.getPath(o),flags:t,seekable:!0,position:0,stream_ops:o.stream_ops,ungotten:[],error:!1});return s.stream_ops.open&&s.stream_ops.open(s),e.logReadFiles&&!(1&t)&&(ea.readFiles||(ea.readFiles={}),r in ea.readFiles||(ea.readFiles[r]=1)),s},close(r){if(ea.isClosed(r))throw new ea.ErrnoError(8);r.getdents&&(r.getdents=null);try{r.stream_ops.close&&r.stream_ops.close(r)}catch(r){throw r}finally{ea.closeStream(r.fd)}r.fd=null},isClosed:r=>null===r.fd,llseek(r,e,t){if(ea.isClosed(r))throw new ea.ErrnoError(8);if(!r.seekable||!r.stream_ops.llseek)throw new ea.ErrnoError(70);if(0!=t&&1!=t&&2!=t)throw new ea.ErrnoError(28);return r.position=r.stream_ops.llseek(r,e,t),r.ungotten=[],r.position},read(r,e,t,n,o){if(n<0||o<0)throw new ea.ErrnoError(28);if(ea.isClosed(r)||(2097155&r.flags)==1)throw new ea.ErrnoError(8);if(ea.isDir(r.node.mode))throw new ea.ErrnoError(31);if(!r.stream_ops.read)throw new ea.ErrnoError(28);var a=void 0!==o;if(a){if(!r.seekable)throw new ea.ErrnoError(70)}else o=r.position;var i=r.stream_ops.read(r,e,t,n,o);return a||(r.position+=i),i},write(r,e,t,n,o,a){if(n<0||o<0)throw new ea.ErrnoError(28);if(ea.isClosed(r)||(2097155&r.flags)==0)throw new ea.ErrnoError(8);if(ea.isDir(r.node.mode))throw new ea.ErrnoError(31);if(!r.stream_ops.write)throw new ea.ErrnoError(28);r.seekable&&1024&r.flags&&ea.llseek(r,0,2);var i=void 0!==o;if(i){if(!r.seekable)throw new ea.ErrnoError(70)}else o=r.position;var s=r.stream_ops.write(r,e,t,n,o,a);return i||(r.position+=s),s},allocate(r,e,t){if(ea.isClosed(r))throw new ea.ErrnoError(8);if(e<0||t<=0)throw new ea.ErrnoError(28);if((2097155&r.flags)==0)throw new ea.ErrnoError(8);if(!ea.isFile(r.node.mode)&&!ea.isDir(r.node.mode))throw new ea.ErrnoError(43);if(!r.stream_ops.allocate)throw new ea.ErrnoError(138);r.stream_ops.allocate(r,e,t)},mmap(r,e,t,n,o){if((2&n)!=0&&(2&o)==0&&(2097155&r.flags)!=2||(2097155&r.flags)==1)throw new ea.ErrnoError(2);if(!r.stream_ops.mmap)throw new ea.ErrnoError(43);return r.stream_ops.mmap(r,e,t,n,o)},msync:(r,e,t,n,o)=>r.stream_ops.msync?r.stream_ops.msync(r,e,t,n,o):0,munmap:r=>0,ioctl(r,e,t){if(!r.stream_ops.ioctl)throw new ea.ErrnoError(59);return r.stream_ops.ioctl(r,e,t)},readFile(r){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e.flags=e.flags||0,e.encoding=e.encoding||"binary","utf8"!==e.encoding&&"binary"!==e.encoding)throw Error('Invalid encoding type "'.concat(e.encoding,'"'));var t,n=ea.open(r,e.flags),o=ea.stat(r).size,a=new Uint8Array(o);return ea.read(n,a,0,o,0),"utf8"===e.encoding?t=rN(a,0):"binary"===e.encoding&&(t=a),ea.close(n),t},writeFile(r,e){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};t.flags=t.flags||577;var n=ea.open(r,t.flags,t.mode);if("string"==typeof e){var o=new Uint8Array(rW(e)+1),a=rR(e,o,0,o.length);ea.write(n,o,0,a,void 0,t.canOwn)}else if(ArrayBuffer.isView(e))ea.write(n,e,0,e.byteLength,void 0,t.canOwn);else throw Error("Unsupported data type");ea.close(n)},cwd:()=>ea.currentPath,chdir(r){var e=ea.lookupPath(r,{follow:!0});if(null===e.node)throw new ea.ErrnoError(44);if(!ea.isDir(e.node.mode))throw new ea.ErrnoError(54);var t=ea.nodePermissions(e.node,"x");if(t)throw new ea.ErrnoError(t);ea.currentPath=e.path},createDefaultDirectories(){ea.mkdir("/tmp"),ea.mkdir("/home"),ea.mkdir("/home/<USER>")},createDefaultDevices(){ea.mkdir("/dev"),ea.registerDevice(ea.makedev(1,3),{read:()=>0,write:(r,e,t,n,o)=>n}),ea.mkdev("/dev/null",ea.makedev(1,3)),r5.register(ea.makedev(5,0),r5.default_tty_ops),r5.register(ea.makedev(6,0),r5.default_tty1_ops),ea.mkdev("/dev/tty",ea.makedev(5,0)),ea.mkdev("/dev/tty1",ea.makedev(6,0));var r=new Uint8Array(1024),e=0,t=()=>(0===e&&(e=r1(r).byteLength),r[--e]);ea.createDevice("/dev","random",t),ea.createDevice("/dev","urandom",t),ea.mkdir("/dev/shm"),ea.mkdir("/dev/shm/tmp")},createSpecialDirectories(){ea.mkdir("/proc");var r=ea.mkdir("/proc/self");ea.mkdir("/proc/self/fd"),ea.mount({mount(){var e=ea.createNode(r,"fd",16895,73);return e.node_ops={lookup(r,e){var t=ea.getStreamChecked(+e),n={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>t.path}};return n.parent=n,n}},e}},{},"/proc/self/fd")},createStandardStreams(){e.stdin?ea.createDevice("/dev","stdin",e.stdin):ea.symlink("/dev/tty","/dev/stdin"),e.stdout?ea.createDevice("/dev","stdout",null,e.stdout):ea.symlink("/dev/tty","/dev/stdout"),e.stderr?ea.createDevice("/dev","stderr",null,e.stderr):ea.symlink("/dev/tty1","/dev/stderr"),ea.open("/dev/stdin",0),ea.open("/dev/stdout",1),ea.open("/dev/stderr",1)},ensureErrnoError(){ea.ErrnoError||(ea.ErrnoError=function(r,e){this.name="ErrnoError",this.node=e,this.setErrno=function(r){this.errno=r},this.setErrno(r),this.message="FS error"},ea.ErrnoError.prototype=Error(),ea.ErrnoError.prototype.constructor=ea.ErrnoError,[44].forEach(r=>{ea.genericErrors[r]=new ea.ErrnoError(r),ea.genericErrors[r].stack="<generic error, no stack>"}))},staticInit(){ea.ensureErrnoError(),ea.nameTable=Array(4096),ea.mount(r7,{},"/"),ea.createDefaultDirectories(),ea.createDefaultDevices(),ea.createSpecialDirectories(),ea.filesystems={MEMFS:r7}},init(r,t,n){ea.init.initialized=!0,ea.ensureErrnoError(),e.stdin=r||e.stdin,e.stdout=t||e.stdout,e.stderr=n||e.stderr,ea.createStandardStreams()},quit(){ea.init.initialized=!1;for(var r=0;r<ea.streams.length;r++){var e=ea.streams[r];e&&ea.close(e)}},findObject(r,e){var t=ea.analyzePath(r,e);return t.exists?t.object:null},analyzePath(r,e){try{var t=ea.lookupPath(r,{follow:!e});r=t.path}catch(r){}var n={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var t=ea.lookupPath(r,{parent:!0});n.parentExists=!0,n.parentPath=t.path,n.parentObject=t.node,n.name=rQ.basename(r),t=ea.lookupPath(r,{follow:!e}),n.exists=!0,n.path=t.path,n.object=t.node,n.name=t.node.name,n.isRoot="/"===t.path}catch(r){n.error=r.errno}return n},createPath(r,e,t,n){r="string"==typeof r?r:ea.getPath(r);for(var o=e.split("/").reverse();o.length;){var a=o.pop();if(a){var i=rQ.join2(r,a);try{ea.mkdir(i)}catch(r){}r=i}}return i},createFile(r,e,t,n,o){var a=rQ.join2("string"==typeof r?r:ea.getPath(r),e),i=eo(n,o);return ea.create(a,i)},createDataFile(r,e,t,n,o,a){var i=e;r&&(r="string"==typeof r?r:ea.getPath(r),i=e?rQ.join2(r,e):r);var s=eo(n,o),u=ea.create(i,s);if(t){if("string"==typeof t){for(var l=Array(t.length),d=0,c=t.length;d<c;++d)l[d]=t.charCodeAt(d);t=l}ea.chmod(u,146|s);var f=ea.open(u,577);ea.write(f,t,0,t.length,0,a),ea.close(f),ea.chmod(u,s)}},createDevice(r,e,t,n){var o=rQ.join2("string"==typeof r?r:ea.getPath(r),e),a=eo(!!t,!!n);ea.createDevice.major||(ea.createDevice.major=64);var i=ea.makedev(ea.createDevice.major++,0);return ea.registerDevice(i,{open(r){r.seekable=!1},close(r){n&&n.buffer&&n.buffer.length&&n(10)},read(r,e,n,o,a){for(var i,s=0,u=0;u<o;u++){try{i=t()}catch(r){throw new ea.ErrnoError(29)}if(void 0===i&&0===s)throw new ea.ErrnoError(6);if(null==i)break;s++,e[n+u]=i}return s&&(r.node.timestamp=Date.now()),s},write(r,e,t,o,a){for(var i=0;i<o;i++)try{n(e[t+i])}catch(r){throw new ea.ErrnoError(29)}return o&&(r.node.timestamp=Date.now()),i}}),ea.mkdev(o,a,i)},forceLoadFile(r){if(r.isDevice||r.isFolder||r.link||r.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(o)try{r.contents=r4(o(r.url),!0),r.usedBytes=r.contents.length}catch(r){throw new ea.ErrnoError(29)}else throw Error("Cannot load without read() or XMLHttpRequest.")},createLazyFile(r,e,t,n,o){function a(){this.lengthKnown=!1,this.chunks=[]}if(a.prototype.get=function(r){if(!(r>this.length-1)&&!(r<0)){var e=r%this.chunkSize,t=r/this.chunkSize|0;return this.getter(t)[e]}},a.prototype.setDataGetter=function(r){this.getter=r},a.prototype.cacheLength=function(){var r,e=new XMLHttpRequest;if(e.open("HEAD",t,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw Error("Couldn't load "+t+". Status: "+e.status);var n=Number(e.getResponseHeader("Content-length")),o=(r=e.getResponseHeader("Accept-Ranges"))&&"bytes"===r,a=(r=e.getResponseHeader("Content-Encoding"))&&"gzip"===r,i=1048576;o||(i=n);var s=(r,e)=>{if(r>e)throw Error("invalid range ("+r+", "+e+") or no bytes requested!");if(e>n-1)throw Error("only "+n+" bytes available! programmer error!");var o=new XMLHttpRequest;if(o.open("GET",t,!1),n!==i&&o.setRequestHeader("Range","bytes="+r+"-"+e),o.responseType="arraybuffer",o.overrideMimeType&&o.overrideMimeType("text/plain; charset=x-user-defined"),o.send(null),!(o.status>=200&&o.status<300||304===o.status))throw Error("Couldn't load "+t+". Status: "+o.status);return void 0!==o.response?new Uint8Array(o.response||[]):r4(o.responseText||"",!0)},u=this;u.setDataGetter(r=>{var e=r*i,t=(r+1)*i-1;if(t=Math.min(t,n-1),void 0===u.chunks[r]&&(u.chunks[r]=s(e,t)),void 0===u.chunks[r])throw Error("doXHR failed!");return u.chunks[r]}),(a||!n)&&(i=n=1,i=n=this.getter(0).length,j("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=i,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){var i;throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc"}var i={isDevice:!1,url:t},s=ea.createFile(r,e,i,n,o);i.contents?s.contents=i.contents:i.url&&(s.contents=null,s.url=i.url),Object.defineProperties(s,{usedBytes:{get:function(){return this.contents.length}}});var u={};function d(r,e,t,n,o){var a=r.node.contents;if(o>=a.length)return 0;var i=Math.min(a.length-o,n);if(a.slice)for(var s=0;s<i;s++)e[t+s]=a[o+s];else for(var s=0;s<i;s++)e[t+s]=a.get(o+s);return i}return Object.keys(s.stream_ops).forEach(r=>{var e=s.stream_ops[r];u[r]=function(){return ea.forceLoadFile(s),e.apply(null,arguments)}}),u.read=(r,e,t,n,o)=>(ea.forceLoadFile(s),d(r,e,t,n,o)),u.mmap=(r,e,t,n,o)=>{ea.forceLoadFile(s);var a=r8(e);if(!a)throw new ea.ErrnoError(48);return d(r,l,a,e,t),{ptr:a,allocated:!0}},s.stream_ops=u,s}},ei={DEFAULT_POLLMASK:5,calculateAt(r,e,t){if(rQ.isAbs(e))return e;if(-100===r)n=ea.cwd();else{var n;n=ei.getStreamFromFD(r).path}if(0==e.length){if(!t)throw new ea.ErrnoError(44);return n}return rQ.join2(n,e)},doStat(r,e,t){try{var n=r(e)}catch(r){if(r&&r.node&&rQ.normalize(e)!==rQ.normalize(ea.getPath(r.node)))return -54;throw r}h[t>>2]=n.dev,h[t+4>>2]=n.mode,m[t+8>>2]=n.nlink,h[t+12>>2]=n.uid,h[t+16>>2]=n.gid,h[t+20>>2]=n.rdev,w=[n.size>>>0,+Math.abs(g=n.size)>=1?g>0?Math.floor(g/0x100000000)>>>0:~~Math.ceil((g-(~~g>>>0))/0x100000000)>>>0:0],h[t+24>>2]=w[0],h[t+28>>2]=w[1],h[t+32>>2]=4096,h[t+36>>2]=n.blocks;var o=n.atime.getTime(),a=n.mtime.getTime(),i=n.ctime.getTime();return w=[Math.floor(o/1e3)>>>0,+Math.abs(g=Math.floor(o/1e3))>=1?g>0?Math.floor(g/0x100000000)>>>0:~~Math.ceil((g-(~~g>>>0))/0x100000000)>>>0:0],h[t+40>>2]=w[0],h[t+44>>2]=w[1],m[t+48>>2]=o%1e3*1e3,w=[Math.floor(a/1e3)>>>0,+Math.abs(g=Math.floor(a/1e3))>=1?g>0?Math.floor(g/0x100000000)>>>0:~~Math.ceil((g-(~~g>>>0))/0x100000000)>>>0:0],h[t+56>>2]=w[0],h[t+60>>2]=w[1],m[t+64>>2]=a%1e3*1e3,w=[Math.floor(i/1e3)>>>0,+Math.abs(g=Math.floor(i/1e3))>=1?g>0?Math.floor(g/0x100000000)>>>0:~~Math.ceil((g-(~~g>>>0))/0x100000000)>>>0:0],h[t+72>>2]=w[0],h[t+76>>2]=w[1],m[t+80>>2]=i%1e3*1e3,w=[n.ino>>>0,+Math.abs(g=n.ino)>=1?g>0?Math.floor(g/0x100000000)>>>0:~~Math.ceil((g-(~~g>>>0))/0x100000000)>>>0:0],h[t+88>>2]=w[0],h[t+92>>2]=w[1],0},doMsync(r,e,t,n,o){if(!ea.isFile(e.node.mode))throw new ea.ErrnoError(43);if(2&n)return 0;var a=d.slice(r,r+t);ea.msync(e,a,o,t,n)},varargs:void 0,get(){var r=h[ei.varargs>>2];return ei.varargs+=4,r},getp:()=>ei.get(),getStr:r=>rB(r),getStreamFromFD:r=>ea.getStreamChecked(r)},es=(r,e,t,n)=>{for(var o=0,a=0;a<t;a++){var i=m[e>>2],s=m[e+4>>2];e+=8;var u=ea.read(r,l,i,s,n);if(u<0)return -1;if(o+=u,u<s)break;void 0!==n&&(n+=u)}return o},eu=(r,e)=>e+2097152>>>0<4194305-!!r?(r>>>0)+0x100000000*e:NaN,el=(r,e,t,n)=>{for(var o=0,a=0;a<t;a++){var i=m[e>>2],s=m[e+4>>2];e+=8;var u=ea.write(r,l,i,s,n);if(u<0)return -1;o+=u,void 0!==n&&(n+=u)}return o},ed=r=>r%4==0&&(r%100!=0||r%400==0),ec=(r,e)=>{for(var t=0,n=0;n<=e;t+=r[n++]);return t},ef=[31,29,31,30,31,30,31,31,30,31,30,31],eh=[31,28,31,30,31,30,31,31,30,31,30,31],em=(r,e)=>{for(var t=new Date(r.getTime());e>0;){var n=ed(t.getFullYear()),o=t.getMonth(),a=(n?ef:eh)[o];if(e>a-t.getDate())e-=a-t.getDate()+1,t.setDate(1),o<11?t.setMonth(o+1):(t.setMonth(0),t.setFullYear(t.getFullYear()+1));else{t.setDate(t.getDate()+e);break}}return t},ep=(r,e)=>{l.set(r,e)},ev=(r,e,t,n)=>{var o=m[n+40>>2],a={tm_sec:h[n>>2],tm_min:h[n+4>>2],tm_hour:h[n+8>>2],tm_mday:h[n+12>>2],tm_mon:h[n+16>>2],tm_year:h[n+20>>2],tm_wday:h[n+24>>2],tm_yday:h[n+28>>2],tm_isdst:h[n+32>>2],tm_gmtoff:h[n+36>>2],tm_zone:o?rB(o):""},i=rB(t),s={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var u in s)i=i.replace(RegExp(u,"g"),s[u]);var l=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],d=["January","February","March","April","May","June","July","August","September","October","November","December"];function c(r,e,t){for(var n="number"==typeof r?r.toString():r||"";n.length<e;)n=t[0]+n;return n}function f(r,e){return c(r,e,"0")}function p(r,e){var t;function n(r){return r<0?-1:+(r>0)}return 0===(t=n(r.getFullYear()-e.getFullYear()))&&0===(t=n(r.getMonth()-e.getMonth()))&&(t=n(r.getDate()-e.getDate())),t}function v(r){switch(r.getDay()){case 0:return new Date(r.getFullYear()-1,11,29);case 1:return r;case 2:return new Date(r.getFullYear(),0,3);case 3:return new Date(r.getFullYear(),0,2);case 4:return new Date(r.getFullYear(),0,1);case 5:return new Date(r.getFullYear()-1,11,31);case 6:return new Date(r.getFullYear()-1,11,30)}}function y(r){var e=em(new Date(r.tm_year+1900,0,1),r.tm_yday),t=new Date(e.getFullYear(),0,4),n=new Date(e.getFullYear()+1,0,4),o=v(t),a=v(n);return 0>=p(o,e)?0>=p(a,e)?e.getFullYear()+1:e.getFullYear():e.getFullYear()-1}var g={"%a":r=>l[r.tm_wday].substring(0,3),"%A":r=>l[r.tm_wday],"%b":r=>d[r.tm_mon].substring(0,3),"%B":r=>d[r.tm_mon],"%C":r=>f((r.tm_year+1900)/100|0,2),"%d":r=>f(r.tm_mday,2),"%e":r=>c(r.tm_mday,2," "),"%g":r=>y(r).toString().substring(2),"%G":r=>y(r),"%H":r=>f(r.tm_hour,2),"%I":r=>{var e=r.tm_hour;return 0==e?e=12:e>12&&(e-=12),f(e,2)},"%j":r=>f(r.tm_mday+ec(ed(r.tm_year+1900)?ef:eh,r.tm_mon-1),3),"%m":r=>f(r.tm_mon+1,2),"%M":r=>f(r.tm_min,2),"%n":()=>"\n","%p":r=>r.tm_hour>=0&&r.tm_hour<12?"AM":"PM","%S":r=>f(r.tm_sec,2),"%t":()=>"	","%u":r=>r.tm_wday||7,"%U":r=>f(Math.floor((r.tm_yday+7-r.tm_wday)/7),2),"%V":r=>{var e=Math.floor((r.tm_yday+7-(r.tm_wday+6)%7)/7);if((r.tm_wday+371-r.tm_yday-2)%7<=2&&e++,e){if(53==e){var t=(r.tm_wday+371-r.tm_yday)%7;4==t||3==t&&ed(r.tm_year)||(e=1)}}else{e=52;var n=(r.tm_wday+7-r.tm_yday-1)%7;(4==n||5==n&&ed(r.tm_year%400-1))&&e++}return f(e,2)},"%w":r=>r.tm_wday,"%W":r=>f(Math.floor((r.tm_yday+7-(r.tm_wday+6)%7)/7),2),"%y":r=>(r.tm_year+1900).toString().substring(2),"%Y":r=>r.tm_year+1900,"%z":r=>{var e=r.tm_gmtoff;return(e>=0?"+":"-")+String("0000"+(e=(e=Math.abs(e)/60)/60*100+e%60)).slice(-4)},"%Z":r=>r.tm_zone,"%%":()=>"%"};for(var u in i=i.replace(/%%/g,"\0\0"),g)i.includes(u)&&(i=i.replace(RegExp(u,"g"),g[u](a)));var w=r4(i=i.replace(/\0\0/g,"%"),!1);return w.length>e?0:(ep(w,r),w.length-1)},ey=r=>e["_"+r],eg=r=>{var e=rW(r)+1,t=eS(e);return rO(r,t,e),t};E=e.InternalError=class extends Error{constructor(r){super(r),this.name="InternalError"}};for(var ew=Array(256),eE=0;eE<256;++eE)ew[eE]=String.fromCharCode(eE);_=ew,b=e.BindingError=class extends Error{constructor(r){super(r),this.name="BindingError"}},Object.assign(rd.prototype,{get(r){return this.allocated[r]},has(r){return void 0!==this.allocated[r]},allocate(r){var e=this.freelist.pop()||this.allocated.length;return this.allocated[e]=r,e},free(r){this.allocated[r]=void 0,this.freelist.push(r)}}),rc.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),rc.reserved=rc.allocated.length,e.count_emval_handles=()=>{for(var r=0,e=rc.reserved;e<rc.allocated.length;++e)void 0!==rc.allocated[e]&&++r;return r},P=Error,(S=rg(T="UnboundTypeError",function(r){this.name=T,this.message=r;var e=Error(r).stack;void 0!==e&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))})).prototype=Object.create(P.prototype),S.prototype.constructor=S,S.prototype.toString=function(){return void 0===this.message?this.name:"".concat(this.name,": ").concat(this.message)},F=e.UnboundTypeError=S;var e_=function(r,e,t,n){r||(r=this),this.parent=r,this.mount=r.mount,this.mounted=null,this.id=ea.nextInode++,this.name=e,this.mode=t,this.node_ops={},this.stream_ops={},this.rdev=n};Object.defineProperties(e_.prototype,{read:{get:function(){return(365&this.mode)==365},set:function(r){r?this.mode|=365:this.mode&=-366}},write:{get:function(){return(146&this.mode)==146},set:function(r){r?this.mode|=146:this.mode&=-147}},isFolder:{get:function(){return ea.isDir(this.mode)}},isDevice:{get:function(){return ea.isChrdev(this.mode)}}}),ea.FSNode=e_,ea.createPreloadedFile=(r,e,t,n,o,a,i,s,u,l)=>{var d=e?r2.resolve(rQ.join2(r,e)):r,c="cp ".concat(d);function f(t){function f(t){l&&l(),s||er(r,e,t,n,o,u),a&&a(),Y(c)}et(t,d,f,()=>{i&&i(),Y(c)})||f(t)}H(c),"string"==typeof t?r9(t,r=>f(r),i):f(t)},ea.staticInit();var eb={a:(r,e,t)=>{throw new K(r).init(e,t),$=r,Z++,$},m:r=>{var e=Q[r];delete Q[r];var t=e.elements,n=t.length,o=t.map(r=>r.getterReturnType).concat(t.map(r=>r.setterArgumentType)),a=e.rawConstructor,i=e.rawDestructor;ri([r],o,function(r){return t.forEach((e,t)=>{var o=r[t],a=e.getter,i=e.getterContext,s=r[t+n],u=e.setter,l=e.setterContext;e.read=r=>o.fromWireType(a(i,r)),e.write=(r,e)=>{var t=[];u(l,r,s.toWireType(t,e)),rr(t)}}),[{name:e.name,fromWireType:r=>{for(var e=Array(n),o=0;o<n;++o)e[o]=t[o].read(r);return i(r),e},toWireType:(r,o)=>{if(n!==o.length)throw TypeError("Incorrect number of tuple elements for ".concat(e.name,": expected=").concat(n,", actual=").concat(o.length));for(var s=a(),u=0;u<n;++u)t[u].write(s,o[u]);return null!==r&&r.push(i,s),s},argPackAdvance:8,readValueFromPointer:re,destructorFunction:i}]})},p:(r,e,t,n,o)=>{},k:(r,e,t,n)=>{rl(r,{name:e=rs(e),fromWireType:function(r){return!!r},toWireType:function(r,e){return e?t:n},argPackAdvance:8,readValueFromPointer:function(r){return this.fromWireType(d[r])},destructorFunction:null})},y:(r,e)=>{rl(r,{name:e=rs(e),fromWireType:r=>{var e=rh(r);return rf(r),e},toWireType:(r,e)=>rm(e),argPackAdvance:8,readValueFromPointer:re,destructorFunction:null})},z:(r,e,t,n)=>{function o(){}e=rs(e),o.values={},rl(r,{name:e,constructor:o,fromWireType:function(r){return this.constructor.values[r]},toWireType:(r,e)=>e.value,argPackAdvance:8,readValueFromPointer:ry(e,t,n),destructorFunction:null}),rv(e,o)},f:(r,e,t)=>{var n=rE(r,"enum");e=rs(e);var o=n.constructor,a=Object.create(n.constructor.prototype,{value:{value:t},constructor:{value:rg("".concat(n.name,"_").concat(e),function(){})}});o.values[t]=a,o[e]=a},j:(r,e,t)=>{rl(r,{name:e=rs(e),fromWireType:r=>r,toWireType:(r,e)=>e,argPackAdvance:8,readValueFromPointer:r_(e,t),destructorFunction:null})},d:(r,e,t,n,o,a,i)=>{var s=rb(e,t);r=rC(r=rs(r)),o=rS(n,o),rv(r,function(){rM("Cannot call ".concat(r," due to unbound types"),s)},e-1),ri([],s,function(t){var n=[t[0],null].concat(t.slice(1));return rk(r,function(r,e,t,n,o,a){var i=e.length;i<2&&ru("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var s=null!==e[1]&&!1,u=!1,l=1;l<e.length;++l)if(null!==e[l]&&void 0===e[l].destructorFunction){u=!0;break}for(var d="void"!==e[0].name,c="",f="",l=0;l<i-2;++l)c+=(0!==l?", ":"")+"arg"+l,f+=(0!==l?", ":"")+"arg"+l+"Wired";var h="\n        return function (".concat(c,") {\n        if (arguments.length !== ").concat(i-2,") {\n          throwBindingError('function ").concat(r," called with ' + arguments.length + ' arguments, expected ").concat(i-2,"');\n        }");u&&(h+="var destructors = [];\n");var m=u?"destructors":"null",p=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[ru,n,o,rr,e[0],e[1]];s&&(h+="var thisWired = classParam.toWireType("+m+", this);\n");for(var l=0;l<i-2;++l)h+="var arg"+l+"Wired = argType"+l+".toWireType("+m+", arg"+l+"); // "+e[l+2].name+"\n",p.push("argType"+l),v.push(e[l+2]);if(s&&(f="thisWired"+(f.length>0?", ":"")+f),h+=(d||a?"var rv = ":"")+"invoker(fn"+(f.length>0?", ":"")+f+");\n",u)h+="runDestructors(destructors);\n";else for(var l=s?1:2;l<e.length;++l){var y=1===l?"thisWired":"arg"+(l-2)+"Wired";null!==e[l].destructorFunction&&(h+=y+"_dtor("+y+"); // "+e[l].name+"\n",p.push(y+"_dtor"),v.push(e[l].destructorFunction))}d&&(h+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),h+="}\n",p.push(h);var g=(function(r,e){if(!(r instanceof Function))throw TypeError("new_ called with constructor type ".concat(typeof r," which is not a function"));var t=rg(r.name||"unknownFunctionName",function(){});t.prototype=r.prototype;var n=new t,o=r.apply(n,e);return o instanceof Object?o:n})(Function,p).apply(null,v);return rg(r,g)}(r,n,0,o,a,i),e-1),[]})},e:(r,e,t,n,o)=>{e=rs(e),-1===o&&(o=0xffffffff);var a,i=r=>r;if(0===n){var s=32-8*t;i=r=>r<<s>>>s}var u=e.includes("unsigned"),l=(r,e)=>{};rl(r,{name:e,fromWireType:i,toWireType:u?function(r,e){return l(e,this.name),e>>>0}:function(r,e){return l(e,this.name),e},argPackAdvance:8,readValueFromPointer:rx(e,t,0!==n),destructorFunction:null})},b:(r,e,t)=>{var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][e];function o(r){var e=m[r>>2],t=m[r+4>>2];return new n(l.buffer,t,e)}rl(r,{name:t=rs(t),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},h:(r,e)=>{var t="std::string"===(e=rs(e));rl(r,{name:e,fromWireType(r){var e,n=m[r>>2],o=r+4;if(t)for(var a=o,i=0;i<=n;++i){var s=o+i;if(i==n||0==d[s]){var u=s-a,l=rB(a,u);void 0===e?e=l:(e+="\0",e+=l),a=s+1}}else{for(var c=Array(n),i=0;i<n;++i)c[i]=String.fromCharCode(d[o+i]);e=c.join("")}return eD(r),e},toWireType(r,e){e instanceof ArrayBuffer&&(e=new Uint8Array(e));var n,o="string"==typeof e;o||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int8Array||ru("Cannot pass non-string to std::string"),n=t&&o?rW(e):e.length;var a=eA(4+n+1),i=a+4;if(m[a>>2]=n,t&&o)rO(e,i,n+1);else if(o)for(var s=0;s<n;++s){var u=e.charCodeAt(s);u>255&&(eD(i),ru("String has UTF-16 code units that do not fit in 8 bits")),d[i+s]=u}else for(var s=0;s<n;++s)d[i+s]=e[s];return null!==r&&r.push(eD,a),a},argPackAdvance:8,readValueFromPointer:rj,destructorFunction(r){eD(r)}})},g:(r,e,t)=>{var n,o,a,i,s;t=rs(t),2===e?(n=rL,o=rU,i=rH,a=()=>f,s=1):4===e&&(n=rY,o=rq,i=rV,a=()=>m,s=2),rl(r,{name:t,fromWireType:r=>{for(var t,o=m[r>>2],i=a(),u=r+4,l=0;l<=o;++l){var d=r+4+l*e;if(l==o||0==i[d>>s]){var c=d-u,f=n(u,c);void 0===t?t=f:(t+="\0",t+=f),u=d+e}}return eD(r),t},toWireType:(r,n)=>{"string"!=typeof n&&ru("Cannot pass non-string to C++ string type ".concat(t));var a=i(n),u=eA(4+a+e);return m[u>>2]=a>>s,o(n,u+4,a+e),null!==r&&r.push(eD,u),u},argPackAdvance:8,readValueFromPointer:re,destructorFunction(r){eD(r)}})},n:(r,e,t,n,o,a)=>{Q[r]={name:rs(e),rawConstructor:rS(t,n),rawDestructor:rS(o,a),elements:[]}},c:(r,e,t,n,o,a,i,s,u)=>{Q[r].elements.push({getterReturnType:e,getter:rS(t,n),getterContext:o,setterArgumentType:a,setter:rS(i,s),setterContext:u})},l:(r,e)=>{rl(r,{isVoid:!0,name:e=rs(e),argPackAdvance:0,fromWireType:()=>void 0,toWireType:(r,e)=>void 0})},i:()=>{q("")},x:(r,e,t)=>d.copyWithin(r,e,e+t),u:r=>{var e=d.length;r>>>=0;var t=rX();if(r>t)return!1;for(var n=(r,e)=>r+(e-r%e)%e,o=1;o<=4;o*=2){var a=e*(1+.2/o);if(a=Math.min(a,r+0x6000000),rG(Math.min(t,n(Math.max(r,a),65536))))return!0}return!1},r:(r,e)=>{var t=0;return r$().forEach((n,o)=>{var a=e+t;m[r+4*o>>2]=a,rZ(n,a),t+=n.length+1}),0},s:(r,e)=>{var t=r$();m[r>>2]=t.length;var n=0;return t.forEach(r=>n+=r.length+1),m[e>>2]=n,0},w:function(r){try{var e=ei.getStreamFromFD(r);return ea.close(e),0}catch(r){if(void 0===ea||"ErrnoError"!==r.name)throw r;return r.errno}},t:function(r,e,t,n){try{var o=ei.getStreamFromFD(r),a=es(o,e,t);return m[n>>2]=a,0}catch(r){if(void 0===ea||"ErrnoError"!==r.name)throw r;return r.errno}},o:function(r,e,t,n,o){var a=eu(e,t);try{if(isNaN(a))return 61;var i=ei.getStreamFromFD(r);return ea.llseek(i,a,n),w=[i.position>>>0,(g=i.position,+Math.abs(g)>=1?g>0?Math.floor(g/0x100000000)>>>0:~~Math.ceil((g-(~~g>>>0))/0x100000000)>>>0:0)],h[o>>2]=w[0],h[o+4>>2]=w[1],i.getdents&&0===a&&0===n&&(i.getdents=null),0}catch(r){if(void 0===ea||"ErrnoError"!==r.name)throw r;return r.errno}},v:function(r,e,t,n){try{var o=ei.getStreamFromFD(r),a=el(o,e,t);return m[n>>2]=a,0}catch(r){if(void 0===ea||"ErrnoError"!==r.name)throw r;return r.errno}},q:(r,e,t,n,o)=>ev(r,e,t,n)},ek=function(){var r,t,o,a={a:eb};function i(r,e){var t;return u=(ek=r.exports).A,W(),k=ek.C,t=ek.B,N.unshift(t),Y("wasm-instantiate"),ek}if(H("wasm-instantiate"),e.instantiateWasm)try{return e.instantiateWasm(a,i)}catch(r){R("Module.instantiateWasm callback failed with error: ".concat(r)),n(r)}return(r=s,t=y,o=function(r){i(r.instance)},!r&&"function"==typeof WebAssembly.instantiateStreaming&&!V(t)&&"function"==typeof fetch?fetch(t,{credentials:"same-origin"}).then(r=>WebAssembly.instantiateStreaming(r,a).then(o,function(r){return R("wasm streaming compile failed: ".concat(r)),R("falling back to ArrayBuffer instantiation"),G(t,a,o)})):G(t,a,o)).catch(n),{}}(),eF=r=>(eF=ek.D)(r),eA=e._malloc=r=>(eA=e._malloc=ek.E)(r),eD=e._free=r=>(eD=e._free=ek.F)(r),eP=()=>(eP=ek.G)(),eT=r=>(eT=ek.H)(r),eS=r=>(eS=ek.I)(r),eM=r=>(eM=ek.J)(r);function eC(){if(!(I>0)){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;){var r;r=e.preRun.shift(),z.unshift(r)}J(z),I>0||(e.setStatus?(e.setStatus("Running..."),setTimeout(function(){setTimeout(function(){e.setStatus("")},1),n()},1)):n())}function n(){if(!A&&(A=!0,e.calledRun=!0,!O)){if(e.noFSInit||ea.init.initialized||ea.init(),ea.ignorePermissions=!1,r5.init(),J(N),t(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;){var r;r=e.postRun.shift(),B.unshift(r)}J(B)}}}if(e.dynCall_jiji=(r,t,n,o,a)=>(e.dynCall_jiji=ek.K)(r,t,n,o,a),e.dynCall_viijii=(r,t,n,o,a,i,s)=>(e.dynCall_viijii=ek.L)(r,t,n,o,a,i,s),e.dynCall_iiiiij=(r,t,n,o,a,i,s)=>(e.dynCall_iiiiij=ek.M)(r,t,n,o,a,i,s),e.dynCall_iiiiijj=(r,t,n,o,a,i,s,u,l)=>(e.dynCall_iiiiijj=ek.N)(r,t,n,o,a,i,s,u,l),e.dynCall_iiiiiijj=(r,t,n,o,a,i,s,u,l,d)=>(e.dynCall_iiiiiijj=ek.O)(r,t,n,o,a,i,s,u,l,d),e.ccall=(r,e,t,n,o)=>{var a,i={string:r=>{var e=0;return null!=r&&0!==r&&(e=eg(r)),e},array:r=>{var e=eS(r.length);return ep(r,e),e}},s=ey(r),u=[],l=0;if(n)for(var d=0;d<n.length;d++){var c=i[t[d]];c?(0===l&&(l=eP()),u[d]=c(n[d])):u[d]=n[d]}var f=s.apply(null,u);return a=f,0!==l&&eT(l),f="string"===e?rB(a):"boolean"===e?!!a:a},U=function r(){A||eC(),A||(U=r)},e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();return eC(),e.ready}})()}}]);