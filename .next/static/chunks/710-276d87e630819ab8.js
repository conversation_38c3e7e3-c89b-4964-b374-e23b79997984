"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[710],{1335:(t,e,i)=>{i.d(e,{u:()=>n});var r=i(9064);let n={test:(0,i(55920).$)("#"),parse:function(t){let e="",i="",r="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:r.B.transform}},4272:(t,e,i)=>{i.d(e,{y:()=>o});var r=i(1335),n=i(18476),s=i(9064);let o={test:t=>s.B.test(t)||r.u.test(t)||n.V.test(t),parse:t=>s.B.test(t)?s.B.parse(t):n.V.test(t)?n.V.parse(t):r.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?s.B.transform(t):n.V.transform(t)}},6101:(t,e,i)=>{i.d(e,{s:()=>o,t:()=>s});var r=i(12115);function n(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}function s(...t){return e=>{let i=!1,r=t.map(t=>{let r=n(t,e);return i||"function"!=typeof r||(i=!0),r});if(i)return()=>{for(let e=0;e<r.length;e++){let i=r[e];"function"==typeof i?i():n(t[e],null)}}}}function o(...t){return r.useCallback(s(...t),t)}},6775:(t,e,i)=>{i.d(e,{G:()=>h});var r=i(23387),n=i(19827),s=i(53191),o=i(54542),a=i(45818),l=i(53678),u=i(26087);function h(t,e,{clamp:i=!0,ease:d,mixer:c}={}){let p=t.length;if((0,o.V)(p===e.length,"Both input and output ranges must be the same length"),1===p)return()=>e[0];if(2===p&&e[0]===e[1])return()=>e[1];let m=t[0]===t[1];t[0]>t[p-1]&&(t=[...t].reverse(),e=[...e].reverse());let f=function(t,e,i){let o=[],a=i||r.W.mix||u.j,l=t.length-1;for(let i=0;i<l;i++){let r=a(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||n.l:e;r=(0,s.F)(t,r)}o.push(r)}return o}(e,d,c),g=f.length,v=i=>{if(m&&i<t[0])return e[0];let r=0;if(g>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let n=(0,a.q)(t[r],t[r+1],i);return f[r](n)};return i?e=>v((0,l.q)(t[0],t[p-1],e)):v}},6983:(t,e,i)=>{i.d(e,{G:()=>r});function r(t){return"object"==typeof t&&null!==t}},7712:(t,e,i)=>{i.d(e,{po:()=>s,tn:()=>a,yT:()=>o});var r=i(91765),n=i(54180);let s=t=>1-Math.sin(Math.acos(t)),o=(0,n.G)(s),a=(0,r.V)(s)},9064:(t,e,i)=>{i.d(e,{B:()=>u});var r=i(53678),n=i(57887),s=i(11557),o=i(55920);let a=t=>(0,r.q)(0,255,t),l={...n.ai,transform:t=>Math.round(a(t))},u={test:(0,o.$)("rgb","red"),parse:(0,o.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+l.transform(t)+", "+l.transform(e)+", "+l.transform(i)+", "+(0,s.a)(n.X4.transform(r))+")"}},11557:(t,e,i)=>{i.d(e,{a:()=>r});let r=t=>Math.round(1e5*t)/1e5},18476:(t,e,i)=>{i.d(e,{V:()=>a});var r=i(57887),n=i(34158),s=i(11557),o=i(55920);let a={test:(0,o.$)("hsl","hue"),parse:(0,o.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:o=1})=>"hsla("+Math.round(t)+", "+n.KN.transform((0,s.a)(e))+", "+n.KN.transform((0,s.a)(i))+", "+(0,s.a)(r.X4.transform(o))+")"}},19827:(t,e,i)=>{i.d(e,{l:()=>r});let r=t=>t},23387:(t,e,i)=>{i.d(e,{W:()=>r});let r={}},24744:(t,e,i)=>{i.d(e,{Q:()=>r});let r={value:null,addProjectionMetrics:null}},26087:(t,e,i)=>{i.d(e,{j:()=>A});var r=i(53191),n=i(54542),s=i(78606),o=i(4272),a=i(60010),l=i(1335),u=i(18476);function h(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var d=i(9064);function c(t,e){return i=>i>0?e:t}var p=i(33210);let m=(t,e,i)=>{let r=t*t,n=i*(e*e-r)+r;return n<0?0:Math.sqrt(n)},f=[l.u,d.B,u.V],g=t=>f.find(e=>e.test(t));function v(t){let e=g(t);if((0,n.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===u.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let n=0,s=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,a=2*i-r;n=h(a,r,t+1/3),s=h(a,r,t),o=h(a,r,t-1/3)}else n=s=o=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:r}}(i)),i}let y=(t,e)=>{let i=v(t),r=v(e);if(!i||!r)return c(t,e);let n={...i};return t=>(n.red=m(i.red,r.red,t),n.green=m(i.green,r.green,t),n.blue=m(i.blue,r.blue,t),n.alpha=(0,p.k)(i.alpha,r.alpha,t),d.B.transform(n))},x=new Set(["none","hidden"]);function b(t,e){return i=>(0,p.k)(t,e,i)}function w(t){return"number"==typeof t?b:"string"==typeof t?(0,s.p)(t)?c:o.y.test(t)?y:P:Array.isArray(t)?T:"object"==typeof t?o.y.test(t)?y:S:c}function T(t,e){let i=[...t],r=i.length,n=t.map((t,i)=>w(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=n[e](t);return i}}function S(t,e){let i={...t,...e},r={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(r[n]=w(t[n])(t[n],e[n]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let P=(t,e)=>{let i=a.f.createTransformer(e),s=(0,a.V)(t),o=(0,a.V)(e);return s.indexes.var.length===o.indexes.var.length&&s.indexes.color.length===o.indexes.color.length&&s.indexes.number.length>=o.indexes.number.length?x.has(t)&&!o.values.length||x.has(e)&&!s.values.length?function(t,e){return x.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,r.F)(T(function(t,e){let i=[],r={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let s=e.types[n],o=t.indexes[s][r[s]],a=t.values[o]??0;i[n]=a,r[s]++}return i}(s,o),o.values),i):((0,n.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),c(t,e))};function A(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,p.k)(t,e,i):w(t)(t,e)}},27351:(t,e,i)=>{i.d(e,{s:()=>n});var r=i(6983);function n(t){return(0,r.G)(t)&&"offsetHeight"in t}},30532:(t,e,i)=>{i.d(e,{s:()=>y});var r=i(53191),n=i(53678),s=i(47215),o=i(74261),a=i(63704),l=i(26087),u=i(69515);let h=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>u.Gt.update(e,t),stop:()=>(0,u.WG)(e),now:()=>u.uv.isProcessing?u.uv.timestamp:o.k.now()}};var d=i(56330),c=i(44188),p=i(52458),m=i(76778),f=i(70144),g=i(63894);let v=t=>t/100;class y extends g.q{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{if(t){let{motionValue:t}=this.options;t&&t.updatedAt!==o.k.now()&&this.tick(o.k.now())}this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},a.q.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;(0,f.E)(t);let{type:e=c.i,repeat:i=0,repeatDelay:n=0,repeatType:s,velocity:o=0}=t,{keyframes:a}=t,u=e||c.i;u!==c.i&&"number"!=typeof a[0]&&(this.mixKeyframes=(0,r.F)(v,(0,l.j)(a[0],a[1])),a=[0,100]);let h=u({...t,keyframes:a});"mirror"===s&&(this.mirroredGenerator=u({...t,keyframes:[...a].reverse(),velocity:-o})),null===h.calculatedDuration&&(h.calculatedDuration=(0,p.t)(h));let{calculatedDuration:d}=h;this.calculatedDuration=d,this.resolvedDuration=d+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=h}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:r,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:l}=this;if(null===this.startTime)return i.next(0);let{delay:u=0,keyframes:h,repeat:c,repeatType:p,repeatDelay:f,type:g,onUpdate:v,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let x=this.currentTime-u*(this.playbackSpeed>=0?1:-1),b=this.playbackSpeed>=0?x<0:x>r;this.currentTime=Math.max(x,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let w=this.currentTime,T=i;if(c){let t=Math.min(this.currentTime,r)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,f&&(i-=f/a)):"mirror"===p&&(T=o)),w=(0,n.q)(0,1,i)*a}let S=b?{done:!1,value:h[0]}:T.next(w);s&&(S.value=s(S.value));let{done:P}=S;b||null===l||(P=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let A=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return A&&g!==d.B&&(S.value=(0,m.X)(h,this.options,y,this.speed)),v&&v(S.value),A&&this.finish(),S}then(t,e){return this.finished.then(t,e)}get duration(){return(0,s.X)(this.calculatedDuration)}get time(){return(0,s.X)(this.currentTime)}set time(t){t=(0,s.f)(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(o.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,s.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=h,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(o.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,a.q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}},30614:(t,e,i)=>{i.d(e,{S:()=>r});let r=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},32082:(t,e,i)=>{i.d(e,{xQ:()=>s});var r=i(12115),n=i(80845);function s(t=!0){let e=(0,r.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,r.useId)();(0,r.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,r.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},33210:(t,e,i)=>{i.d(e,{k:()=>r});let r=(t,e,i)=>t+(e-t)*i},33972:(t,e,i)=>{i.d(e,{Sz:()=>o,ZZ:()=>l,dg:()=>a});var r=i(62483),n=i(91765),s=i(54180);let o=(0,r.A)(.33,1.53,.69,.99),a=(0,s.G)(o),l=(0,n.V)(a)},34158:(t,e,i)=>{i.d(e,{KN:()=>s,gQ:()=>u,px:()=>o,uj:()=>n,vh:()=>a,vw:()=>l});let r=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),n=r("deg"),s=r("%"),o=r("px"),a=r("vh"),l=r("vw"),u={...s,parse:t=>s.parse(t)/100,transform:t=>s.transform(100*t)}},39688:(t,e,i)=>{i.d(e,{QP:()=>H});let r=t=>{let e=a(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:r}=t;return{getClassGroupId:t=>{let i=t.split("-");return""===i[0]&&1!==i.length&&i.shift(),n(i,e)||o(t)},getConflictingClassGroupIds:(t,e)=>{let n=i[t]||[];return e&&r[t]?[...n,...r[t]]:n}}},n=(t,e)=>{if(0===t.length)return e.classGroupId;let i=t[0],r=e.nextPart.get(i),s=r?n(t.slice(1),r):void 0;if(s)return s;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},s=/^\[(.+)\]$/,o=t=>{if(s.test(t)){let e=s.exec(t)[1],i=e?.substring(0,e.indexOf(":"));if(i)return"arbitrary.."+i}},a=t=>{let{theme:e,prefix:i}=t,r={nextPart:new Map,validators:[]};return d(Object.entries(t.classGroups),i).forEach(([t,i])=>{l(i,r,t,e)}),r},l=(t,e,i,r)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=i;return}if("function"==typeof t)return h(t)?void l(t(r),e,i,r):void e.validators.push({validator:t,classGroupId:i});Object.entries(t).forEach(([t,n])=>{l(n,u(e,t),i,r)})})},u=(t,e)=>{let i=t;return e.split("-").forEach(t=>{i.nextPart.has(t)||i.nextPart.set(t,{nextPart:new Map,validators:[]}),i=i.nextPart.get(t)}),i},h=t=>t.isThemeGetter,d=(t,e)=>e?t.map(([t,i])=>[t,i.map(t=>"string"==typeof t?e+t:"object"==typeof t?Object.fromEntries(Object.entries(t).map(([t,i])=>[e+t,i])):t)]):t,c=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,i=new Map,r=new Map,n=(n,s)=>{i.set(n,s),++e>t&&(e=0,r=i,i=new Map)};return{get(t){let e=i.get(t);return void 0!==e?e:void 0!==(e=r.get(t))?(n(t,e),e):void 0},set(t,e){i.has(t)?i.set(t,e):n(t,e)}}},p=t=>{let{separator:e,experimentalParseClassName:i}=t,r=1===e.length,n=e[0],s=e.length,o=t=>{let i,o=[],a=0,l=0;for(let u=0;u<t.length;u++){let h=t[u];if(0===a){if(h===n&&(r||t.slice(u,u+s)===e)){o.push(t.slice(l,u)),l=u+s;continue}if("/"===h){i=u;continue}}"["===h?a++:"]"===h&&a--}let u=0===o.length?t:t.substring(l),h=u.startsWith("!"),d=h?u.substring(1):u;return{modifiers:o,hasImportantModifier:h,baseClassName:d,maybePostfixModifierPosition:i&&i>l?i-l:void 0}};return i?t=>i({className:t,parseClassName:o}):o},m=t=>{if(t.length<=1)return t;let e=[],i=[];return t.forEach(t=>{"["===t[0]?(e.push(...i.sort(),t),i=[]):i.push(t)}),e.push(...i.sort()),e},f=t=>({cache:c(t.cacheSize),parseClassName:p(t),...r(t)}),g=/\s+/,v=(t,e)=>{let{parseClassName:i,getClassGroupId:r,getConflictingClassGroupIds:n}=e,s=[],o=t.trim().split(g),a="";for(let t=o.length-1;t>=0;t-=1){let e=o[t],{modifiers:l,hasImportantModifier:u,baseClassName:h,maybePostfixModifierPosition:d}=i(e),c=!!d,p=r(c?h.substring(0,d):h);if(!p){if(!c||!(p=r(h))){a=e+(a.length>0?" "+a:a);continue}c=!1}let f=m(l).join(":"),g=u?f+"!":f,v=g+p;if(s.includes(v))continue;s.push(v);let y=n(p,c);for(let t=0;t<y.length;++t){let e=y[t];s.push(g+e)}a=e+(a.length>0?" "+a:a)}return a};function y(){let t,e,i=0,r="";for(;i<arguments.length;)(t=arguments[i++])&&(e=x(t))&&(r&&(r+=" "),r+=e);return r}let x=t=>{let e;if("string"==typeof t)return t;let i="";for(let r=0;r<t.length;r++)t[r]&&(e=x(t[r]))&&(i&&(i+=" "),i+=e);return i},b=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:([a-z-]+):)?(.+)\]$/i,T=/^\d+\/\d+$/,S=new Set(["px","full","screen"]),P=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,A=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,k=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,V=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,M=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,E=t=>D(t)||S.has(t)||T.test(t),C=t=>G(t,"length",q),D=t=>!!t&&!Number.isNaN(Number(t)),j=t=>G(t,"number",D),R=t=>!!t&&Number.isInteger(Number(t)),L=t=>t.endsWith("%")&&D(t.slice(0,-1)),F=t=>w.test(t),B=t=>P.test(t),O=new Set(["length","size","percentage"]),I=t=>G(t,O,X),N=t=>G(t,"position",X),z=new Set(["image","url"]),W=t=>G(t,z,K),$=t=>G(t,"",Y),U=()=>!0,G=(t,e,i)=>{let r=w.exec(t);return!!r&&(r[1]?"string"==typeof e?r[1]===e:e.has(r[1]):i(r[2]))},q=t=>A.test(t)&&!k.test(t),X=()=>!1,Y=t=>V.test(t),K=t=>M.test(t);Symbol.toStringTag;let H=function(t,...e){let i,r,n,s=function(a){return r=(i=f(e.reduce((t,e)=>e(t),t()))).cache.get,n=i.cache.set,s=o,o(a)};function o(t){let e=r(t);if(e)return e;let s=v(t,i);return n(t,s),s}return function(){return s(y.apply(null,arguments))}}(()=>{let t=b("colors"),e=b("spacing"),i=b("blur"),r=b("brightness"),n=b("borderColor"),s=b("borderRadius"),o=b("borderSpacing"),a=b("borderWidth"),l=b("contrast"),u=b("grayscale"),h=b("hueRotate"),d=b("invert"),c=b("gap"),p=b("gradientColorStops"),m=b("gradientColorStopPositions"),f=b("inset"),g=b("margin"),v=b("opacity"),y=b("padding"),x=b("saturate"),w=b("scale"),T=b("sepia"),S=b("skew"),P=b("space"),A=b("translate"),k=()=>["auto","contain","none"],V=()=>["auto","hidden","clip","visible","scroll"],M=()=>["auto",F,e],O=()=>[F,e],z=()=>["",E,C],G=()=>["auto",D,F],q=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],X=()=>["solid","dashed","dotted","double","none"],Y=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],K=()=>["start","end","center","between","around","evenly","stretch"],H=()=>["","0",F],Q=()=>["auto","avoid","all","avoid-page","page","left","right","column"],_=()=>[D,F];return{cacheSize:500,separator:":",theme:{colors:[U],spacing:[E,C],blur:["none","",B,F],brightness:_(),borderColor:[t],borderRadius:["none","","full",B,F],borderSpacing:O(),borderWidth:z(),contrast:_(),grayscale:H(),hueRotate:_(),invert:H(),gap:O(),gradientColorStops:[t],gradientColorStopPositions:[L,C],inset:M(),margin:M(),opacity:_(),padding:O(),saturate:_(),scale:_(),sepia:H(),skew:_(),space:O(),translate:O()},classGroups:{aspect:[{aspect:["auto","square","video",F]}],container:["container"],columns:[{columns:[B]}],"break-after":[{"break-after":Q()}],"break-before":[{"break-before":Q()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...q(),F]}],overflow:[{overflow:V()}],"overflow-x":[{"overflow-x":V()}],"overflow-y":[{"overflow-y":V()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",R,F]}],basis:[{basis:M()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",F]}],grow:[{grow:H()}],shrink:[{shrink:H()}],order:[{order:["first","last","none",R,F]}],"grid-cols":[{"grid-cols":[U]}],"col-start-end":[{col:["auto",{span:["full",R,F]},F]}],"col-start":[{"col-start":G()}],"col-end":[{"col-end":G()}],"grid-rows":[{"grid-rows":[U]}],"row-start-end":[{row:["auto",{span:[R,F]},F]}],"row-start":[{"row-start":G()}],"row-end":[{"row-end":G()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",F]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",F]}],gap:[{gap:[c]}],"gap-x":[{"gap-x":[c]}],"gap-y":[{"gap-y":[c]}],"justify-content":[{justify:["normal",...K()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...K(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...K(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[P]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[P]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",F,e]}],"min-w":[{"min-w":[F,e,"min","max","fit"]}],"max-w":[{"max-w":[F,e,"none","full","min","max","fit","prose",{screen:[B]},B]}],h:[{h:[F,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[F,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[F,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[F,e,"auto","min","max","fit"]}],"font-size":[{text:["base",B,C]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",j]}],"font-family":[{font:[U]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",F]}],"line-clamp":[{"line-clamp":["none",D,j]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",E,F]}],"list-image":[{"list-image":["none",F]}],"list-style-type":[{list:["none","disc","decimal",F]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...X(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",E,C]}],"underline-offset":[{"underline-offset":["auto",E,F]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:O()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...q(),N]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",I]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},W]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...X(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:X()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...X()]}],"outline-offset":[{"outline-offset":[E,F]}],"outline-w":[{outline:[E,C]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[E,C]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",B,$]}],"shadow-color":[{shadow:[U]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...Y(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Y()}],filter:[{filter:["","none"]}],blur:[{blur:[i]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",B,F]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[h]}],invert:[{invert:[d]}],saturate:[{saturate:[x]}],sepia:[{sepia:[T]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[i]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[h]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[x]}],"backdrop-sepia":[{"backdrop-sepia":[T]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",F]}],duration:[{duration:_()}],ease:[{ease:["linear","in","out","in-out",F]}],delay:[{delay:_()}],animate:[{animate:["none","spin","ping","pulse","bounce",F]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[R,F]}],"translate-x":[{"translate-x":[A]}],"translate-y":[{"translate-y":[A]}],"skew-x":[{"skew-x":[S]}],"skew-y":[{"skew-y":[S]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",F]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":O()}],"scroll-mx":[{"scroll-mx":O()}],"scroll-my":[{"scroll-my":O()}],"scroll-ms":[{"scroll-ms":O()}],"scroll-me":[{"scroll-me":O()}],"scroll-mt":[{"scroll-mt":O()}],"scroll-mr":[{"scroll-mr":O()}],"scroll-mb":[{"scroll-mb":O()}],"scroll-ml":[{"scroll-ml":O()}],"scroll-p":[{"scroll-p":O()}],"scroll-px":[{"scroll-px":O()}],"scroll-py":[{"scroll-py":O()}],"scroll-ps":[{"scroll-ps":O()}],"scroll-pe":[{"scroll-pe":O()}],"scroll-pt":[{"scroll-pt":O()}],"scroll-pr":[{"scroll-pr":O()}],"scroll-pb":[{"scroll-pb":O()}],"scroll-pl":[{"scroll-pl":O()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[E,C,j]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},44188:(t,e,i)=>{i.d(e,{i:()=>b});var r=i(62483);let n=(0,r.A)(.42,0,1,1),s=(0,r.A)(0,0,.58,1),o=(0,r.A)(.42,0,.58,1),a=t=>Array.isArray(t)&&"number"!=typeof t[0];var l=i(54542),u=i(19827),h=i(46009),d=i(33972),c=i(7712),p=i(68589);let m={linear:u.l,easeIn:n,easeInOut:o,easeOut:s,circIn:c.po,circInOut:c.tn,circOut:c.yT,backIn:d.dg,backInOut:d.ZZ,backOut:d.Sz,anticipate:h.b},f=t=>"string"==typeof t,g=t=>{if((0,p.D)(t)){(0,l.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,s]=t;return(0,r.A)(e,i,n,s)}return f(t)?((0,l.V)(void 0!==m[t],`Invalid easing type '${t}'`),m[t]):t};var v=i(6775),y=i(45818),x=i(33210);function b({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var n;let s=a(r)?r.map(g):g(r),l={done:!1,value:e[0]},u=(n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let n=(0,y.q)(0,e,r);t.push((0,x.k)(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),h=(0,v.G)(u,e,{ease:Array.isArray(s)?s:e.map(()=>s||o).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(l.value=h(e),l.done=e>=t,l)}}},45818:(t,e,i)=>{i.d(e,{q:()=>r});let r=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r}},46009:(t,e,i)=>{i.d(e,{b:()=>n});var r=i(33972);let n=t=>(t*=2)<1?.5*(0,r.dg)(t):.5*(2-Math.pow(2,-10*(t-1)))},47147:(t,e,i)=>{i.d(e,{A:()=>o});var r=i(12115),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},s=i(66977);let o=(0,r.forwardRef)((t,e)=>{let{color:i="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:h,iconNode:d,...c}=t;return(0,r.createElement)("svg",{ref:e,...n,width:o,height:o,stroke:i,strokeWidth:l?24*Number(a)/Number(o):a,className:(0,s.z)("lucide",u),...c},[...d.map(t=>{let[e,i]=t;return(0,r.createElement)(e,i)}),...Array.isArray(h)?h:[h]])})},47215:(t,e,i)=>{i.d(e,{X:()=>n,f:()=>r});let r=t=>1e3*t,n=t=>t/1e3},47705:(t,e,i)=>{i.d(e,{K:()=>r});let r=(t,e,i=10)=>{let r="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)r+=t(e/(n-1))+", ";return`linear(${r.substring(0,r.length-2)})`}},51508:(t,e,i)=>{i.d(e,{Q:()=>r});let r=(0,i(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},52458:(t,e,i)=>{i.d(e,{Y:()=>r,t:()=>n});let r=2e4;function n(t){let e=0,i=t.next(e);for(;!i.done&&e<r;)e+=50,i=t.next(e);return e>=r?1/0:e}},52596:(t,e,i)=>{function r(){for(var t,e,i=0,r="",n=arguments.length;i<n;i++)(t=arguments[i])&&(e=function t(e){var i,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(i=0;i<s;i++)e[i]&&(r=t(e[i]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}(t))&&(r&&(r+=" "),r+=e);return r}i.d(e,{$:()=>r,A:()=>n});let n=r},53191:(t,e,i)=>{i.d(e,{F:()=>n});let r=(t,e)=>i=>e(t(i)),n=(...t)=>t.reduce(r)},53678:(t,e,i)=>{i.d(e,{q:()=>r});let r=(t,e,i)=>i>e?e:i<t?t:i},54180:(t,e,i)=>{i.d(e,{G:()=>r});let r=t=>e=>1-t(1-e)},54542:(t,e,i)=>{i.d(e,{$:()=>r,V:()=>n});let r=()=>{},n=()=>{}},55920:(t,e,i)=>{i.d(e,{$:()=>s,q:()=>o});var r=i(30614);let n=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,s=(t,e)=>i=>!!("string"==typeof i&&n.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),o=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[s,o,a,l]=n.match(r.S);return{[t]:parseFloat(s),[e]:parseFloat(o),[i]:parseFloat(a),alpha:void 0!==l?parseFloat(l):1}}},56330:(t,e,i)=>{i.d(e,{B:()=>s});var r=i(82886),n=i(73945);function s({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:o=10,bounceStiffness:a=500,modifyTarget:l,min:u,max:h,restDelta:d=.5,restSpeed:c}){let p,m,f=t[0],g={done:!1,value:f},v=t=>void 0!==u&&t<u||void 0!==h&&t>h,y=t=>void 0===u?h:void 0===h||Math.abs(u-t)<Math.abs(h-t)?u:h,x=i*e,b=f+x,w=void 0===l?b:l(b);w!==b&&(x=w-f);let T=t=>-x*Math.exp(-t/s),S=t=>w+T(t),P=t=>{let e=T(t),i=S(t);g.done=Math.abs(e)<=d,g.value=g.done?w:i},A=t=>{v(g.value)&&(p=t,m=(0,r.o)({keyframes:[g.value,y(g.value)],velocity:(0,n.Y)(S,t,g.value),damping:o,stiffness:a,restDelta:d,restSpeed:c}))};return A(0),{calculatedDuration:null,next:t=>{let e=!1;return(m||void 0!==p||(e=!0,P(t),A(t)),void 0!==p&&t>=p)?m.next(t-p):(e||P(t),g)}}}},56668:(t,e,i)=>{function r(t,e){-1===t.indexOf(e)&&t.push(e)}function n(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>n,Kq:()=>r})},57887:(t,e,i)=>{i.d(e,{X4:()=>s,ai:()=>n,hs:()=>o});var r=i(53678);let n={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},s={...n,transform:t=>(0,r.q)(0,1,t)},o={...n,default:1}},58437:(t,e,i)=>{i.d(e,{I:()=>o});var r=i(23387);let n=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var s=i(24744);function o(t,e){let i=!1,o=!0,a={delta:0,timestamp:0,isProcessing:!1},l=()=>i=!0,u=n.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,r=new Set,n=!1,o=!1,a=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},u=0;function h(e){a.has(e)&&(d.schedule(e),t()),u++,e(l)}let d={schedule:(t,e=!1,s=!1)=>{let o=s&&n?i:r;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{r.delete(t),a.delete(t)},process:t=>{if(l=t,n){o=!0;return}n=!0,[i,r]=[r,i],i.forEach(h),e&&s.Q.value&&s.Q.value.frameloop[e].push(u),u=0,i.clear(),n=!1,o&&(o=!1,d.process(t))}};return d}(l,e?i:void 0),t),{}),{setup:h,read:d,resolveKeyframes:c,preUpdate:p,update:m,preRender:f,render:g,postRender:v}=u,y=()=>{let n=r.W.useManualTiming?a.timestamp:performance.now();i=!1,r.W.useManualTiming||(a.delta=o?1e3/60:Math.max(Math.min(n-a.timestamp,40),1)),a.timestamp=n,a.isProcessing=!0,h.process(a),d.process(a),c.process(a),p.process(a),m.process(a),f.process(a),g.process(a),v.process(a),a.isProcessing=!1,i&&e&&(o=!1,t(y))},x=()=>{i=!0,o=!0,a.isProcessing||t(y)};return{schedule:n.reduce((t,e)=>{let r=u[e];return t[e]=(t,e=!1,n=!1)=>(i||x(),r.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<n.length;e++)u[n[e]].cancel(t)},state:a,steps:u}}},58892:(t,e,i)=>{function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function n(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function s(t,e,i,r){if("function"==typeof e){let[s,o]=n(r);e=e(void 0!==i?i:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,o]=n(r);e=e(void 0!==i?i:t.custom,s,o)}return e}function o(t,e,i){let r=t.getProps();return s(r,e,void 0!==i?i:r.custom,t)}function a(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>no});var l,u,h=i(69515);let d=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],c=new Set(d),p=new Set(["width","height","top","left","right","bottom",...d]);var m=i(60098);let f=t=>Array.isArray(t);var g=i(23387),v=i(64803);function y(t,e){let i=t.getValue("willChange");if((0,v.S)(i)&&i.add)return i.add(e);if(!i&&g.W.WillChange){let i=new g.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let x=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),b="data-"+x("framerAppearId");var w=i(30532),T=i(19827),S=i(74261),P=i(76778);let A=t=>180*t/Math.PI,k=t=>M(A(Math.atan2(t[1],t[0]))),V={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:k,rotateZ:k,skewX:t=>A(Math.atan(t[1])),skewY:t=>A(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},M=t=>((t%=360)<0&&(t+=360),t),E=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),C=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),D={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:E,scaleY:C,scale:t=>(E(t)+C(t))/2,rotateX:t=>M(A(Math.atan2(t[6],t[5]))),rotateY:t=>M(A(Math.atan2(-t[2],t[0]))),rotateZ:k,rotate:k,skewX:t=>A(Math.atan(t[4])),skewY:t=>A(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function j(t){return+!!t.includes("scale")}function R(t,e){let i,r;if(!t||"none"===t)return j(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=D,r=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=V,r=e}if(!r)return j(e);let s=i[e],o=r[1].split(",").map(F);return"function"==typeof s?s(o):o[s]}let L=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return R(i,e)};function F(t){return parseFloat(t.trim())}var B=i(57887),O=i(34158);let I=t=>t===B.ai||t===O.px,N=new Set(["x","y","z"]),z=d.filter(t=>!N.has(t)),W={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>R(e,"x"),y:(t,{transform:e})=>R(e,"y")};W.translateX=W.x,W.translateY=W.y;let $=new Set,U=!1,G=!1,q=!1;function X(){if(G){let t=Array.from($).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return z.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}G=!1,U=!1,$.forEach(t=>t.complete(q)),$.clear()}function Y(){$.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(G=!0)})}class K{constructor(t,e,i,r,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?($.add(this),U||(U=!0,h.Gt.read(Y),h.Gt.resolveKeyframes(X))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;if(null===t[0]){let n=r?.get(),s=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let r=i.readValue(e,s);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=s),r&&void 0===n&&r.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),$.delete(this)}cancel(){"scheduled"===this.state&&($.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}var H=i(47215),Q=i(54542);let _=t=>t.startsWith("--");function Z(t){let e;return()=>(void 0===e&&(e=t()),e)}let J=Z(()=>void 0!==window.ScrollTimeline);var tt=i(63894),te=i(63704),ti=i(24744),tr=i(68589);let tn={},ts=function(t,e){let i=Z(t);return()=>tn[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing");var to=i(47705);let ta=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,tl={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ta([0,.65,.55,1]),circOut:ta([.55,0,1,.45]),backIn:ta([.31,.01,.66,-.59]),backOut:ta([.33,1.53,.69,.99])};function tu(t){return"function"==typeof t&&"applyToOptions"in t}class th extends tt.q{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:r,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=t,(0,Q.V)("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return tu(t)&&ts()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?ts()?(0,to.K)(e,i):"ease-out":(0,tr.D)(e)?ta(e):Array.isArray(e)?e.map(e=>t(e,i)||tl.easeOut):tl[e]}(a,n);Array.isArray(d)&&(h.easing=d),ti.Q.value&&te.q.waapi++;let c={delay:r,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(c.pseudoElement=u);let p=t.animate(h,c);return ti.Q.value&&p.finished.finally(()=>{te.q.waapi--}),p}(e,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=(0,P.X)(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){_(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let t=this.animation.effect?.getComputedTiming?.().duration||0;return(0,H.X)(Number(t))}get time(){return(0,H.X)(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=(0,H.f)(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&J())?(this.animation.timeline=t,T.l):e(this)}}var td=i(70144),tc=i(46009),tp=i(33972),tm=i(7712);let tf={anticipate:tc.b,backInOut:tp.ZZ,circInOut:tm.tn};class tg extends th{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in tf&&(t.ease=tf[t.ease])}(t),(0,td.E)(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:r,element:n,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new w.s({...s,autoplay:!1}),a=(0,H.f)(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}var tv=i(60010);let ty=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tv.f.test(t)||"0"===t)&&!t.startsWith("url("));var tx=i(27351);let tb=new Set(["opacity","clipPath","filter","transform"]),tw=Z(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tT extends tt.q{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=S.k.now();let d={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:n,repeatType:s,name:a,motionValue:l,element:u,...h},c=u?.KeyframeResolver||K;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,r){this.keyframeResolver=void 0;let{name:n,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:u}=i;this.resolvedAt=S.k.now(),!function(t,e,i,r){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=ty(n,e),a=ty(s,e);return(0,Q.$)(o===a,`You are trying to animate ${e} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||tu(i))&&r)}(t,n,s,o)&&((g.W.instantAnimations||!a)&&u?.((0,P.X)(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let h={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:n,damping:s,type:o}=t;if(!(0,tx.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return tw()&&i&&tb.has(i)&&("transform"!==i||!l)&&!a&&!r&&"mirror"!==n&&0!==s&&"inertia"!==o}(h)?new tg({...h,element:h.motionValue.owner.current}):new w.s(h);d.finished.then(()=>this.notifyFinished()).catch(T.l),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),q=!0,Y(),X(),q=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let tS=t=>null!==t,tP={type:"spring",stiffness:500,damping:25,restSpeed:10},tA=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),tk={type:"keyframes",duration:.8},tV={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},tM=(t,{keyframes:e})=>e.length>2?tk:c.has(t)?t.startsWith("scale")?tA(e[1]):tP:tV,tE=(t,e,i,r={},n,s)=>o=>{let l=a(r,t)||{},u=l.delay||r.delay||0,{elapsed:d=0}=r;d-=(0,H.f)(u);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-d,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:s?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(l)&&Object.assign(c,tM(t,c)),c.duration&&(c.duration=(0,H.f)(c.duration)),c.repeatDelay&&(c.repeatDelay=(0,H.f)(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(p=!0)),(g.W.instantAnimations||g.W.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),c.allowFlatten=!l.type&&!l.ease,p&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},r){let n=t.filter(tS),s=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[s]}(c.keyframes,l);if(void 0!==t)return void h.Gt.update(()=>{c.onUpdate(t),c.onComplete()})}return l.isSync?new w.s(c):new tT(c)};function tC(t,e,{delay:i=0,transitionOverride:r,type:n}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:l,...u}=e;r&&(s=r);let d=[],c=n&&t.animationState&&t.animationState.getState()[n];for(let e in u){let r=t.getValue(e,t.latestValues[e]??null),n=u[e];if(void 0===n||c&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(c,e))continue;let o={delay:i,...a(s||{},e)},l=r.get();if(void 0!==l&&!r.isAnimating&&!Array.isArray(n)&&n===l&&!o.velocity)continue;let m=!1;if(window.MotionHandoffAnimation){let i=t.props[b];if(i){let t=window.MotionHandoffAnimation(i,e,h.Gt);null!==t&&(o.startTime=t,m=!0)}}y(t,e),r.start(tE(e,r,n,t.shouldReduceMotion&&p.has(e)?{type:!1}:o,t,m));let f=r.animation;f&&d.push(f)}return l&&Promise.all(d).then(()=>{h.Gt.update(()=>{l&&function(t,e){let{transitionEnd:i={},transition:r={},...n}=o(t,e)||{};for(let e in n={...n,...i}){var s;let i=f(s=n[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,m.OQ)(i))}}(t,l)})}),d}function tD(t,e,i={}){let r=o(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let s=r?()=>Promise.all(tC(t,r,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,i=0,r=0,n=1,s){let o=[],a=(t.variantChildren.size-1)*r,l=1===n?(t=0)=>t*r:(t=0)=>a-t*r;return Array.from(t.variantChildren).sort(tj).forEach((t,r)=>{t.notify("AnimationStart",e),o.push(tD(t,e,{...s,delay:i+l(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+r,o,a,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([s(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[s,a]:[a,s];return t().then(()=>e())}}function tj(t,e){return t.sortNodePosition(e)}function tR(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}function tL(t){return"string"==typeof t||Array.isArray(t)}let tF=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],tB=["initial",...tF],tO=tB.length,tI=[...tF].reverse(),tN=tF.length;function tz(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function tW(){return{animate:tz(!0),whileInView:tz(),whileHover:tz(),whileTap:tz(),whileDrag:tz(),whileFocus:tz(),exit:tz()}}class t${constructor(t){this.isMounted=!1,this.node=t}update(){}}class tU extends t${constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>tD(t,e,i)));else if("string"==typeof e)r=tD(t,e,i);else{let n="function"==typeof e?o(t,e,i.custom):e;r=Promise.all(tC(t,n,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=tW(),n=!0,s=e=>(i,r)=>{let n=o(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...r}=n;i={...i,...r,...e}}return i};function a(a){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<tO;t++){let r=tB[t],n=e.props[r];(tL(n)||!1===n)&&(i[r]=n)}return i}(t.parent)||{},h=[],d=new Set,c={},p=1/0;for(let e=0;e<tN;e++){var m,g;let o=tI[e],v=i[o],y=void 0!==l[o]?l[o]:u[o],x=tL(y),b=o===a?v.isActive:null;!1===b&&(p=e);let w=y===u[o]&&y!==l[o]&&x;if(w&&n&&t.manuallyAnimateOnMount&&(w=!1),v.protectedKeys={...c},!v.isActive&&null===b||!y&&!v.prevProp||r(y)||"boolean"==typeof y)continue;let T=(m=v.prevProp,"string"==typeof(g=y)?g!==m:!!Array.isArray(g)&&!tR(g,m)),S=T||o===a&&v.isActive&&!w&&x||e>p&&x,P=!1,A=Array.isArray(y)?y:[y],k=A.reduce(s(o),{});!1===b&&(k={});let{prevResolvedValues:V={}}=v,M={...V,...k},E=e=>{S=!0,d.has(e)&&(P=!0,d.delete(e)),v.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=k[t],i=V[t];if(c.hasOwnProperty(t))continue;let r=!1;(f(e)&&f(i)?tR(e,i):e===i)?void 0!==e&&d.has(t)?E(t):v.protectedKeys[t]=!0:null!=e?E(t):d.add(t)}v.prevProp=y,v.prevResolvedValues=k,v.isActive&&(c={...c,...k}),n&&t.blockInitialAnimation&&(S=!1);let C=!(w&&T)||P;S&&C&&h.push(...A.map(t=>({animation:t,options:{type:o}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=o(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let r=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=r??null}),h.push({animation:e})}let v=!!h.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(v=!1),n=!1,v?e(h):Promise.resolve()}return{animateChanges:a,setActive:function(e,r){if(i[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),i[e].isActive=r;let n=a(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=tW(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let tG=0;class tq extends t${constructor(){super(...arguments),this.id=tG++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let tX={x:!1,y:!1};var tY=i(33210);function tK(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}let tH=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function tQ(t){return{point:{x:t.pageX,y:t.pageY}}}let t_=t=>e=>tH(e)&&t(e,tQ(e));function tZ(t,e,i,r){return tK(t,e,t_(i),r)}function tJ({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function t0(t){return t.max-t.min}function t1(t,e,i,r=.5){t.origin=r,t.originPoint=(0,tY.k)(e.min,e.max,t.origin),t.scale=t0(i)/t0(e),t.translate=(0,tY.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function t5(t,e,i,r){t1(t.x,e.x,i.x,r?r.originX:void 0),t1(t.y,e.y,i.y,r?r.originY:void 0)}function t2(t,e,i){t.min=i.min+e.min,t.max=t.min+t0(e)}function t3(t,e,i){t.min=e.min-i.min,t.max=t.min+t0(e)}function t4(t,e,i){t3(t.x,e.x,i.x),t3(t.y,e.y,i.y)}let t8=()=>({translate:0,scale:1,origin:0,originPoint:0}),t6=()=>({x:t8(),y:t8()}),t7=()=>({min:0,max:0}),t9=()=>({x:t7(),y:t7()});function et(t){return[t("x"),t("y")]}function ee(t){return void 0===t||1===t}function ei({scale:t,scaleX:e,scaleY:i}){return!ee(t)||!ee(e)||!ee(i)}function er(t){return ei(t)||en(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function en(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function es(t,e,i,r,n){return void 0!==n&&(t=r+n*(t-r)),r+i*(t-r)+e}function eo(t,e=0,i=1,r,n){t.min=es(t.min,e,i,r,n),t.max=es(t.max,e,i,r,n)}function ea(t,{x:e,y:i}){eo(t.x,e.translate,e.scale,e.originPoint),eo(t.y,i.translate,i.scale,i.originPoint)}function el(t,e){t.min=t.min+e,t.max=t.max+e}function eu(t,e,i,r,n=.5){let s=(0,tY.k)(t.min,t.max,n);eo(t,e,i,s,r)}function eh(t,e){eu(t.x,e.x,e.scaleX,e.scale,e.originX),eu(t.y,e.y,e.scaleY,e.scale,e.originY)}function ed(t,e){return tJ(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let ec=({current:t})=>t?t.ownerDocument.defaultView:null;function ep(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}var em=i(53191);let ef=(t,e)=>Math.abs(t-e);class eg{constructor(t,e,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=ex(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(ef(t.x,e.x)**2+ef(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:r}=t,{timestamp:n}=h.uv;this.history.push({...r,timestamp:n});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=ev(e,this.transformPagePoint),h.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=ex("pointercancel"===t.type?this.lastMoveEventInfo:ev(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),r&&r(t,s)},!tH(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=r||window;let s=ev(tQ(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=h.uv;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,ex(s,this.history)),this.removeListeners=(0,em.F)(tZ(this.contextWindow,"pointermove",this.handlePointerMove),tZ(this.contextWindow,"pointerup",this.handlePointerUp),tZ(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,h.WG)(this.updatePoint)}}function ev(t,e){return e?{point:e(t.point)}:t}function ey(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ex({point:t},e){return{point:t,delta:ey(t,eb(e)),offset:ey(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,n=eb(t);for(;i>=0&&(r=t[i],!(n.timestamp-r.timestamp>(0,H.f)(.1)));)i--;if(!r)return{x:0,y:0};let s=(0,H.X)(n.timestamp-r.timestamp);if(0===s)return{x:0,y:0};let o={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function eb(t){return t[t.length-1]}var ew=i(45818),eT=i(53678);function eS(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function eP(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function eA(t,e,i){return{min:ek(t,e),max:ek(t,i)}}function ek(t,e){return"number"==typeof t?t:t[e]||0}let eV=new WeakMap;class eM{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=t9(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new eg(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(tQ(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(tX[t])return null;else return tX[t]=!0,()=>{tX[t]=!1};return tX.x||tX.y?null:(tX.x=tX.y=!0,()=>{tX.x=tX.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),et(t=>{let e=this.getAxisMotionValue(t).get()||0;if(O.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=t0(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&h.Gt.postRender(()=>n(t,e)),y(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>et(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:ec(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=e;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&h.Gt.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!eE(t,r,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?(0,tY.k)(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?(0,tY.k)(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;t&&ep(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:r,right:n}){return{x:eS(t.x,i,n),y:eS(t.y,e,r)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:eA(t,"left","right"),y:eA(t,"top","bottom")}}(e),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&et(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!ep(e))return!1;let r=e.current;(0,Q.V)(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,i){let r=ed(t,i),{scroll:n}=e;return n&&(el(r.x,n.offset.x),el(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:eP(t.x,s.x),y:eP(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=tJ(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(et(o=>{if(!eE(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return y(this.visualElement,t),i.start(tE(t,i,0,e,this.visualElement,!1))}stopAnimation(){et(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){et(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){et(e=>{let{drag:i}=this.getProps();if(!eE(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[e];n.set(t[e]-(0,tY.k)(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!ep(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};et(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=t0(t),n=t0(e);return n>r?i=(0,ew.q)(e.min,e.max-r,t.min):r>n&&(i=(0,ew.q)(t.min,t.max-n,e.min)),(0,eT.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),et(e=>{if(!eE(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];i.set((0,tY.k)(n,s,r[e]))})}addListeners(){if(!this.visualElement.current)return;eV.set(this.visualElement,this);let t=tZ(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();ep(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),h.Gt.read(e);let n=tK(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(et(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),r(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function eE(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class eC extends t${constructor(t){super(t),this.removeGroupControls=T.l,this.removeListeners=T.l,this.controls=new eM(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||T.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let eD=t=>(e,i)=>{t&&h.Gt.postRender(()=>t(e,i))};class ej extends t${constructor(){super(...arguments),this.removePointerDownListener=T.l}onPointerDown(t){this.session=new eg(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ec(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:eD(t),onStart:eD(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&h.Gt.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=tZ(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var eR=i(95155);let{schedule:eL}=(0,i(58437).I)(queueMicrotask,!1);var eF=i(12115),eB=i(32082),eO=i(90869);let eI=(0,eF.createContext)({}),eN={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ez(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let eW={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!O.px.test(t))return t;else t=parseFloat(t);let i=ez(t,e.target.x),r=ez(t,e.target.y);return`${i}% ${r}%`}};var e$=i(78606);let eU={};class eG extends eF.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=t;for(let t in eX)eU[t]=eX[t],(0,e$.j)(t)&&(eU[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&r&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),eN.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,r||t.layoutDependency!==e||void 0===e||t.isPresent!==n?s.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?s.promote():s.relegate()||h.Gt.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),eL.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function eq(t){let[e,i]=(0,eB.xQ)(),r=(0,eF.useContext)(eO.L);return(0,eR.jsx)(eG,{...t,layoutGroup:r,switchLayoutGroup:(0,eF.useContext)(eI),isPresent:e,safeToRemove:i})}let eX={borderRadius:{...eW,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:eW,borderTopRightRadius:eW,borderBottomLeftRadius:eW,borderBottomRightRadius:eW,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=tv.f.parse(t);if(r.length>5)return t;let n=tv.f.createTransformer(t),s=+("number"!=typeof r[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;r[0+s]/=o,r[1+s]/=a;let l=(0,tY.k)(o,a,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}};var eY=i(6983);function eK(t){return(0,eY.G)(t)&&"ownerSVGElement"in t}var eH=i(75626),eQ=i(56668);let e_=(t,e)=>t.depth-e.depth;class eZ{constructor(){this.children=[],this.isDirty=!1}add(t){(0,eQ.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,eQ.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(e_),this.isDirty=!1,this.children.forEach(t)}}function eJ(t){return(0,v.S)(t)?t.get():t}let e0=["TopLeft","TopRight","BottomLeft","BottomRight"],e1=e0.length,e5=t=>"string"==typeof t?parseFloat(t):t,e2=t=>"number"==typeof t||O.px.test(t);function e3(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let e4=e6(0,.5,tm.yT),e8=e6(.5,.95,T.l);function e6(t,e,i){return r=>r<t?0:r>e?1:i((0,ew.q)(t,e,r))}function e7(t,e){t.min=e.min,t.max=e.max}function e9(t,e){e7(t.x,e.x),e7(t.y,e.y)}function it(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ie(t,e,i,r,n){return t-=e,t=r+1/i*(t-r),void 0!==n&&(t=r+1/n*(t-r)),t}function ii(t,e,[i,r,n],s,o){!function(t,e=0,i=1,r=.5,n,s=t,o=t){if(O.KN.test(e)&&(e=parseFloat(e),e=(0,tY.k)(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=(0,tY.k)(s.min,s.max,r);t===s&&(a-=e),t.min=ie(t.min,e,i,a,n),t.max=ie(t.max,e,i,a,n)}(t,e[i],e[r],e[n],e.scale,s,o)}let ir=["x","scaleX","originX"],is=["y","scaleY","originY"];function io(t,e,i,r){ii(t.x,e,ir,i?i.x:void 0,r?r.x:void 0),ii(t.y,e,is,i?i.y:void 0,r?r.y:void 0)}function ia(t){return 0===t.translate&&1===t.scale}function il(t){return ia(t.x)&&ia(t.y)}function iu(t,e){return t.min===e.min&&t.max===e.max}function ih(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function id(t,e){return ih(t.x,e.x)&&ih(t.y,e.y)}function ic(t){return t0(t.x)/t0(t.y)}function ip(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class im{constructor(){this.members=[]}add(t){(0,eQ.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,eQ.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let ig={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},iv=["","X","Y","Z"],iy={visibility:"hidden"},ix=0;function ib(t,e,i,r){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),r&&(r[t]=0))}function iw({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=ix++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ti.Q.value&&(ig.nodes=ig.calculatedTargetDeltas=ig.calculatedProjections=0),this.nodes.forEach(iP),this.nodes.forEach(iD),this.nodes.forEach(ij),this.nodes.forEach(iA),ti.Q.addProjectionMetrics&&ti.Q.addProjectionMetrics(ig)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new eZ)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new eH.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=eK(e)&&!(eK(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=S.k.now(),r=({timestamp:n})=>{let s=n-i;s>=250&&((0,h.WG)(r),t(s-e))};return h.Gt.setup(r,!0),()=>(0,h.WG)(r)}(r,250),eN.hasAnimatedSinceResize&&(eN.hasAnimatedSinceResize=!1,this.nodes.forEach(iC))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||iI,{onLayoutAnimationStart:o,onLayoutAnimationComplete:l}=n.getProps(),u=!this.targetLayout||!id(this.targetLayout,r),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,h);let e={...a(s,"layout"),onPlay:o,onComplete:l};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||iC(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,h.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(iR),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[b];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",h.Gt,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(iV);return}this.isUpdating||this.nodes.forEach(iM),this.isUpdating=!1,this.nodes.forEach(iE),this.nodes.forEach(iT),this.nodes.forEach(iS),this.clearAllSnapshots();let t=S.k.now();h.uv.delta=(0,eT.q)(0,1e3/60,t-h.uv.timestamp),h.uv.timestamp=t,h.uv.isProcessing=!0,h.PP.update.process(h.uv),h.PP.preRender.process(h.uv),h.PP.render.process(h.uv),h.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,eL.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(ik),this.sharedNodes.forEach(iL)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,h.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){h.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||t0(this.snapshot.measuredBox.x)||t0(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=t9(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!il(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||er(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),iW((e=r).x),iW(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return t9();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(iU))){let{scroll:t}=this.root;t&&(el(e.x,t.offset.x),el(e.y,t.offset.y))}return e}removeElementScroll(t){let e=t9();if(e9(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&e9(e,t),el(e.x,n.offset.x),el(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=t9();e9(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&eh(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),er(r.latestValues)&&eh(i,r.latestValues)}return er(this.latestValues)&&eh(i,this.latestValues),i}removeTransform(t){let e=t9();e9(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!er(i.latestValues))continue;ei(i.latestValues)&&i.updateSnapshot();let r=t9();e9(r,i.measurePageBox()),io(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return er(this.latestValues)&&io(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==h.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:n}=this.options;if(this.layout&&(r||n)){if(this.resolvedRelativeTargetAt=h.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=t9(),this.relativeTargetOrigin=t9(),t4(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),e9(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=t9(),this.targetWithTransforms=t9()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,t2(s.x,o.x,a.x),t2(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):e9(this.target,this.layout.layoutBox),ea(this.target,this.targetDelta)):e9(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=t9(),this.relativeTargetOrigin=t9(),t4(this.relativeTargetOrigin,this.target,t.target),e9(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ti.Q.value&&ig.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ei(this.parent.latestValues)||en(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===h.uv.timestamp&&(i=!1),i)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;e9(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,r=!1){let n,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&eh(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,ea(t,s)),r&&er(n.latestValues)&&eh(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=t9());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(it(this.prevProjectionDelta.x,this.projectionDelta.x),it(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),t5(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&ip(this.projectionDelta.x,this.prevProjectionDelta.x)&&ip(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),ti.Q.value&&ig.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=t6(),this.projectionDelta=t6(),this.projectionDeltaWithTransform=t6()}setAnimationOrigin(t,e=!1){let i,r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},o=t6();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=t9(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(iO));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(iF(o.x,t.x,r),iF(o.y,t.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,g;t4(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=r,iB(p.x,m.x,f.x,g),iB(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,c=i,iu(u.x,c.x)&&iu(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=t9()),e9(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,r,n,s){n?(t.opacity=(0,tY.k)(0,i.opacity??1,e4(r)),t.opacityExit=(0,tY.k)(e.opacity??1,0,e8(r))):s&&(t.opacity=(0,tY.k)(e.opacity??1,i.opacity??1,r));for(let n=0;n<e1;n++){let s=`border${e0[n]}Radius`,o=e3(e,s),a=e3(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||e2(o)===e2(a)?(t[s]=Math.max((0,tY.k)(e5(o),e5(a),r),0),(O.KN.test(a)||O.KN.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,tY.k)(e.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&((0,h.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=h.Gt.update(()=>{eN.hasAnimatedSinceResize=!0,te.q.layout++,this.motionValue||(this.motionValue=(0,m.OQ)(0)),this.currentAnimation=function(t,e,i){let r=(0,v.S)(t)?t:(0,m.OQ)(t);return r.start(tE("",r,e,i)),r.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{te.q.layout--},onComplete:()=>{te.q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:n}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&i$(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||t9();let e=t0(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=t0(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}e9(e,i),eh(e,n),t5(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new im),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&ib("z",t,r,this.animationValues);for(let e=0;e<iv.length;e++)ib(`rotate${iv[e]}`,t,r,this.animationValues),ib(`skew${iv[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return iy;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=eJ(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=eJ(t?.pointerEvents)||""),this.hasProjected&&!er(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=r.animationValues||r.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let r="",n=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((n||s||o)&&(r=`translate3d(${n}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:s,skewX:o,skewY:a}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),o&&(r+=`skewX(${o}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:s,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,r.animationValues?e.opacity=r===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,eU){if(void 0===n[t])continue;let{correct:i,applyTo:s,isCSSVariable:o}=eU[t],a="none"===e.transform?n[t]:i(n[t],r);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=r===this?eJ(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop(!1)),this.root.nodes.forEach(iV),this.root.sharedNodes.clear()}}}function iT(t){t.updateLayout()}function iS(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=t.layout,{animationType:n}=t.options,s=e.source!==t.layout.source;"size"===n?et(t=>{let r=s?e.measuredBox[t]:e.layoutBox[t],n=t0(r);r.min=i[t].min,r.max=r.min+n}):i$(n,e.layoutBox,i)&&et(r=>{let n=s?e.measuredBox[r]:e.layoutBox[r],o=t0(i[r]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+o)});let o=t6();t5(o,i,e.layoutBox);let a=t6();s?t5(a,t.applyTransform(r,!0),e.measuredBox):t5(a,i,e.layoutBox);let l=!il(o),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let o=t9();t4(o,e.layoutBox,n.layoutBox);let a=t9();t4(a,i,s.layoutBox),id(o,a)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function iP(t){ti.Q.value&&ig.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function iA(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function ik(t){t.clearSnapshot()}function iV(t){t.clearMeasurements()}function iM(t){t.isLayoutDirty=!1}function iE(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function iC(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function iD(t){t.resolveTargetDelta()}function ij(t){t.calcProjection()}function iR(t){t.resetSkewAndRotation()}function iL(t){t.removeLeadSnapshot()}function iF(t,e,i){t.translate=(0,tY.k)(e.translate,0,i),t.scale=(0,tY.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function iB(t,e,i,r){t.min=(0,tY.k)(e.min,i.min,r),t.max=(0,tY.k)(e.max,i.max,r)}function iO(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let iI={duration:.45,ease:[.4,0,.1,1]},iN=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),iz=iN("applewebkit/")&&!iN("chrome/")?Math.round:T.l;function iW(t){t.min=iz(t.min),t.max=iz(t.max)}function i$(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(ic(e)-ic(i)))}function iU(t){return t!==t.root&&t.scroll?.wasRoot}let iG=iw({attachResizeListener:(t,e)=>tK(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),iq={current:void 0},iX=iw({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!iq.current){let t=new iG({});t.mount(window),t.setOptions({layoutScroll:!0}),iq.current=t}return iq.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function iY(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function iK(t){return!("touch"===t.pointerType||tX.x||tX.y)}function iH(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&h.Gt.postRender(()=>n(e,tQ(e)))}class iQ extends t${mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=iY(t,i),o=t=>{if(!iK(t))return;let{target:i}=t,r=e(i,t);if("function"!=typeof r||!i)return;let s=t=>{iK(t)&&(r(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(t=>{t.addEventListener("pointerenter",o,n)}),s}(t,(t,e)=>(iH(this.node,e,"Start"),t=>iH(this.node,t,"End"))))}unmount(){}}class i_ extends t${constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,em.F)(tK(this.node.current,"focus",()=>this.onFocus()),tK(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let iZ=(t,e)=>!!e&&(t===e||iZ(t,e.parentElement)),iJ=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),i0=new WeakSet;function i1(t){return e=>{"Enter"===e.key&&t(e)}}function i5(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let i2=(t,e)=>{let i=t.currentTarget;if(!i)return;let r=i1(()=>{if(i0.has(i))return;i5(i,"down");let t=i1(()=>{i5(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>i5(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)};function i3(t){return tH(t)&&!(tX.x||tX.y)}function i4(t,e,i){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&h.Gt.postRender(()=>n(e,tQ(e)))}class i8 extends t${mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=iY(t,i),o=t=>{let r=t.currentTarget;if(!i3(t))return;i0.add(r);let s=e(r,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),i0.has(r)&&i0.delete(r),i3(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,r===window||r===document||i.useGlobalTarget||iZ(r,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return r.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),(0,tx.s)(t))&&(t.addEventListener("focus",t=>i2(t,n)),iJ.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(i4(this.node,e,"Start"),(t,{success:e})=>i4(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let i6=new WeakMap,i7=new WeakMap,i9=t=>{let e=i6.get(t.target);e&&e(t)},rt=t=>{t.forEach(i9)},re={some:0,all:1};class ri extends t${constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:re[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;i7.has(i)||i7.set(i,{});let r=i7.get(i),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(rt,{root:t,...e})),r[n]}(e);return i6.set(t,i),r.observe(t),()=>{i6.delete(t),r.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=e?i:r;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rr=(0,eF.createContext)({strict:!1});var rn=i(51508);let rs=(0,eF.createContext)({});function ro(t){return r(t.animate)||tB.some(e=>tL(t[e]))}function ra(t){return!!(ro(t)||t.variants)}function rl(t){return Array.isArray(t)?t.join(" "):t}var ru=i(68972);let rh={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rd={};for(let t in rh)rd[t]={isEnabled:e=>rh[t].some(t=>!!e[t])};let rc=Symbol.for("motionComponentSymbol");var rp=i(80845),rm=i(97494);function rf(t,{layout:e,layoutId:i}){return c.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!eU[t]||"opacity"===t)}let rg=(t,e)=>e&&"number"==typeof t?e.transform(t):t,rv={...B.ai,transform:Math.round},ry={rotate:O.uj,rotateX:O.uj,rotateY:O.uj,rotateZ:O.uj,scale:B.hs,scaleX:B.hs,scaleY:B.hs,scaleZ:B.hs,skew:O.uj,skewX:O.uj,skewY:O.uj,distance:O.px,translateX:O.px,translateY:O.px,translateZ:O.px,x:O.px,y:O.px,z:O.px,perspective:O.px,transformPerspective:O.px,opacity:B.X4,originX:O.gQ,originY:O.gQ,originZ:O.px},rx={borderWidth:O.px,borderTopWidth:O.px,borderRightWidth:O.px,borderBottomWidth:O.px,borderLeftWidth:O.px,borderRadius:O.px,radius:O.px,borderTopLeftRadius:O.px,borderTopRightRadius:O.px,borderBottomRightRadius:O.px,borderBottomLeftRadius:O.px,width:O.px,maxWidth:O.px,height:O.px,maxHeight:O.px,top:O.px,right:O.px,bottom:O.px,left:O.px,padding:O.px,paddingTop:O.px,paddingRight:O.px,paddingBottom:O.px,paddingLeft:O.px,margin:O.px,marginTop:O.px,marginRight:O.px,marginBottom:O.px,marginLeft:O.px,backgroundPositionX:O.px,backgroundPositionY:O.px,...ry,zIndex:rv,fillOpacity:B.X4,strokeOpacity:B.X4,numOctaves:rv},rb={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rw=d.length;function rT(t,e,i){let{style:r,vars:n,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(c.has(t)){o=!0;continue}if((0,e$.j)(t)){n[t]=i;continue}{let e=rg(i,rx[t]);t.startsWith("origin")?(a=!0,s[t]=e):r[t]=e}}if(!e.transform&&(o||i?r.transform=function(t,e,i){let r="",n=!0;for(let s=0;s<rw;s++){let o=d[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=rg(a,rx[o]);if(!l){n=!1;let e=rb[o]||o;r+=`${e}(${t}) `}i&&(e[o]=t)}}return r=r.trim(),i?r=i(e,n?"":r):n&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;r.transformOrigin=`${t} ${e} ${i}`}}let rS=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rP(t,e,i){for(let r in e)(0,v.S)(e[r])||rf(r,i)||(t[r]=e[r])}let rA={offset:"stroke-dashoffset",array:"stroke-dasharray"},rk={offset:"strokeDashoffset",array:"strokeDasharray"};function rV(t,{attrX:e,attrY:i,attrScale:r,pathLength:n,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(rT(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==r&&(d.scale=r),void 0!==n&&function(t,e,i=1,r=0,n=!0){t.pathLength=1;let s=n?rA:rk;t[s.offset]=O.px.transform(-r);let o=O.px.transform(e),a=O.px.transform(i);t[s.array]=`${o} ${a}`}(d,n,s,o,!1)}let rM=()=>({...rS(),attrs:{}}),rE=t=>"string"==typeof t&&"svg"===t.toLowerCase(),rC=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function rD(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rC.has(t)}let rj=t=>!rD(t);try{!function(t){t&&(rj=e=>e.startsWith("on")?!rD(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let rR=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function rL(t){if("string"!=typeof t||t.includes("-"));else if(rR.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var rF=i(82885);let rB=t=>(e,i)=>{let n=(0,eF.useContext)(rs),o=(0,eF.useContext)(rp.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,o){return{latestValues:function(t,e,i,n){let o={},a=n(t,{});for(let t in a)o[t]=eJ(a[t]);let{initial:l,animate:u}=t,h=ro(t),d=ra(t);e&&d&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!r(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let r=s(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(o[t]=e)}for(let e in t)o[e]=t[e]}}}return o}(i,n,o,t),renderState:e()}})(t,e,n,o);return i?a():(0,rF.M)(a)};function rO(t,e,i){let{style:r}=t,n={};for(let s in r)((0,v.S)(r[s])||e.style&&(0,v.S)(e.style[s])||rf(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(n[s]=r[s]);return n}let rI={useVisualState:rB({scrapeMotionValuesFromProps:rO,createRenderState:rS})};function rN(t,e,i){let r=rO(t,e,i);for(let i in t)((0,v.S)(t[i])||(0,v.S)(e[i]))&&(r[-1!==d.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}let rz={useVisualState:rB({scrapeMotionValuesFromProps:rN,createRenderState:rM})},rW=t=>e=>e.test(t),r$=[B.ai,O.px,O.KN,O.uj,O.vw,O.vh,{test:t=>"auto"===t,parse:t=>t}],rU=t=>r$.find(rW(t)),rG=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),rq=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,rX=t=>/^0[^.\s]+$/u.test(t);var rY=i(30614);let rK=new Set(["brightness","contrast","saturate","opacity"]);function rH(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(rY.S)||[];if(!r)return t;let n=i.replace(r,""),s=+!!rK.has(e);return r!==i&&(s*=100),e+"("+s+n+")"}let rQ=/\b([a-z-]*)\(.*?\)/gu,r_={...tv.f,getAnimatableNone:t=>{let e=t.match(rQ);return e?e.map(rH).join(" "):t}};var rZ=i(4272);let rJ={...rx,color:rZ.y,backgroundColor:rZ.y,outlineColor:rZ.y,fill:rZ.y,stroke:rZ.y,borderColor:rZ.y,borderTopColor:rZ.y,borderRightColor:rZ.y,borderBottomColor:rZ.y,borderLeftColor:rZ.y,filter:r_,WebkitFilter:r_},r0=t=>rJ[t];function r1(t,e){let i=r0(t);return i!==r_&&(i=tv.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let r5=new Set(["auto","none","0"]);class r2 extends K{constructor(t,e,i,r,n){super(t,e,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&(r=r.trim(),(0,e$.p)(r))){let n=function t(e,i,r=1){(0,Q.V)(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,s]=function(t){let e=rq.exec(t);if(!e)return[,];let[,i,r,n]=e;return[`--${i??r}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return rG(t)?parseFloat(t):t}return(0,e$.p)(s)?t(s,i,r+1):s}(r,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!p.has(i)||2!==t.length)return;let[r,n]=t,s=rU(r),o=rU(n);if(s!==o)if(I(s)&&I(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else W[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;(null===t[e]||("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||rX(r)))&&i.push(e)}i.length&&function(t,e,i){let r,n=0;for(;n<t.length&&!r;){let e=t[n];"string"==typeof e&&!r5.has(e)&&(0,tv.V)(e).values.length&&(r=t[n]),n++}if(r&&i)for(let n of e)t[n]=r1(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=W[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let r=t.getValue(e);r&&r.jump(this.measuredOrigin,!1);let n=i.length-1,s=i[n];i[n]=W[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let r3=[...r$,rZ.y,tv.f],r4=t=>r3.find(rW(t)),r8={current:null},r6={current:!1},r7=new WeakMap,r9=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class nt{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=K,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=S.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,h.Gt.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=ro(e),this.isVariantNode=ra(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&(0,v.S)(e)&&e.set(a[t],!1)}}mount(t){this.current=t,r7.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),r6.current||function(){if(r6.current=!0,ru.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>r8.current=t.matches;t.addListener(e),e()}else r8.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||r8.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,h.WG)(this.notifyUpdate),(0,h.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=c.has(t);r&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&h.Gt.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rd){let e=rd[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):t9()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<r9.length;e++){let i=r9[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let n=e[r],s=i[r];if((0,v.S)(n))t.addValue(r,n);else if((0,v.S)(s))t.addValue(r,(0,m.OQ)(n,{owner:t}));else if(s!==n)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(r);t.addValue(r,(0,m.OQ)(void 0!==e?e:n,{owner:t}))}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,m.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(rG(i)||rX(i))?i=parseFloat(i):!r4(i)&&tv.f.test(e)&&(i=r1(t,e)),this.setBaseTarget(t,(0,v.S)(i)?i.get():i)),(0,v.S)(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=s(this.props,i,this.presenceContext?.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||(0,v.S)(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new eH.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class ne extends nt{constructor(){super(...arguments),this.KeyframeResolver=r2}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,v.S)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function ni(t,{style:e,vars:i},r,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(r)),i)t.style.setProperty(s,i[s])}class nr extends ne{constructor(){super(...arguments),this.type="html",this.renderInstance=ni}readValueFromInstance(t,e){if(c.has(e))return this.projection?.isProjecting?j(e):L(t,e);{let i=window.getComputedStyle(t),r=((0,e$.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return ed(t,e)}build(t,e,i){rT(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return rO(t,e,i)}}let nn=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class ns extends ne{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=t9}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(c.has(e)){let t=r0(e);return t&&t.default||0}return e=nn.has(e)?e:x(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return rN(t,e,i)}build(t,e,i){rV(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,r){for(let i in ni(t,e,void 0,r),e.attrs)t.setAttribute(nn.has(i)?i:x(i),e.attrs[i])}mount(t){this.isSVGTag=rE(t.tagName),super.mount(t)}}let no=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,r)=>"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}((l={animation:{Feature:tU},exit:{Feature:tq},inView:{Feature:ri},tap:{Feature:i8},focus:{Feature:i_},hover:{Feature:iQ},pan:{Feature:ej},drag:{Feature:eC,ProjectionNode:iX,MeasureLayout:eq},layout:{ProjectionNode:iX,MeasureLayout:eq}},u=(t,e)=>rL(t)?new ns(e):new nr(e,{allowProjection:t!==eF.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:r,createVisualElement:n,useRender:s,useVisualState:o,Component:a}=t;function l(t,e){var i,r,l;let u,h={...(0,eF.useContext)(rn.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,eF.useContext)(eO.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=h,c=function(t){let{initial:e,animate:i}=function(t,e){if(ro(t)){let{initial:e,animate:i}=t;return{initial:!1===e||tL(e)?e:void 0,animate:tL(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,eF.useContext)(rs));return(0,eF.useMemo)(()=>({initial:e,animate:i}),[rl(e),rl(i)])}(t),p=o(t,d);if(!d&&ru.B){r=0,l=0,(0,eF.useContext)(rr).strict;let t=function(t){let{drag:e,layout:i}=rd;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(h);u=t.MeasureLayout,c.visualElement=function(t,e,i,r,n){let{visualElement:s}=(0,eF.useContext)(rs),o=(0,eF.useContext)(rr),a=(0,eF.useContext)(rp.t),l=(0,eF.useContext)(rn.Q).reducedMotion,u=(0,eF.useRef)(null);r=r||o.renderer,!u.current&&r&&(u.current=r(t,{visualState:e,parent:s,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,d=(0,eF.useContext)(eI);h&&!h.projection&&n&&("html"===h.type||"svg"===h.type)&&function(t,e,i,r){let{layoutId:n,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!o||a&&ep(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,n,d);let c=(0,eF.useRef)(!1);(0,eF.useInsertionEffect)(()=>{h&&c.current&&h.update(i,a)});let p=i[b],m=(0,eF.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,rm.E)(()=>{h&&(c.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),eL.render(h.render),m.current&&h.animationState&&h.animationState.animateChanges())}),(0,eF.useEffect)(()=>{h&&(!m.current&&h.animationState&&h.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),h}(a,p,h,n,t.ProjectionNode)}return(0,eR.jsxs)(rs.Provider,{value:c,children:[u&&c.visualElement?(0,eR.jsx)(u,{visualElement:c.visualElement,...h}):null,s(a,t,(i=c.visualElement,(0,eF.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):ep(e)&&(e.current=t))},[i])),p,d,c.visualElement)]})}r&&function(t){for(let e in t)rd[e]={...rd[e],...t[e]}}(r),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(i=null!=(e=a.displayName)?e:a.name)?i:"",")"));let u=(0,eF.forwardRef)(l);return u[rc]=a,u}({...rL(t)?rz:rI,preloadedFeatures:l,useRender:function(t=!1){return(e,i,r,{latestValues:n},s)=>{let o=(rL(e)?function(t,e,i,r){let n=(0,eF.useMemo)(()=>{let i=rM();return rV(i,e,rE(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};rP(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return rP(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,eF.useMemo)(()=>{let i=rS();return rT(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,n,s,e),a=function(t,e,i){let r={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(rj(n)||!0===i&&rD(n)||!e&&!rD(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}(i,"string"==typeof e,t),l=e!==eF.Fragment?{...a,...o,ref:r}:{},{children:u}=i,h=(0,eF.useMemo)(()=>(0,v.S)(u)?u.get():u,[u]);return(0,eF.createElement)(e,{...l,children:h})}}(e),createVisualElement:u,Component:t})}))},60010:(t,e,i)=>{i.d(e,{V:()=>h,f:()=>m});var r=i(4272);let n=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var s=i(30614),o=i(11557);let a="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function h(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},s=[],o=0,h=e.replace(u,t=>(r.y.test(t)?(n.color.push(o),s.push(l),i.push(r.y.parse(t))):t.startsWith("var(")?(n.var.push(o),s.push("var"),i.push(t)):(n.number.push(o),s.push(a),i.push(parseFloat(t))),++o,"${}")).split("${}");return{values:i,split:h,indexes:n,types:s}}function d(t){return h(t).values}function c(t){let{split:e,types:i}=h(t),n=e.length;return t=>{let s="";for(let u=0;u<n;u++)if(s+=e[u],void 0!==t[u]){let e=i[u];e===a?s+=(0,o.a)(t[u]):e===l?s+=r.y.transform(t[u]):s+=t[u]}return s}}let p=t=>"number"==typeof t?0:t,m={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(s.S)?.length||0)+(t.match(n)?.length||0)>0},parse:d,createTransformer:c,getAnimatableNone:function(t){let e=d(t);return c(t)(e.map(p))}}},60098:(t,e,i)=>{i.d(e,{OQ:()=>h,bt:()=>l});var r=i(75626),n=i(62923),s=i(74261),o=i(69515);let a=t=>!isNaN(parseFloat(t)),l={current:void 0};class u{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=s.k.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=s.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=a(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new r.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),o.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=s.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,n.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(t,e){return new u(t,e)}},62483:(t,e,i)=>{i.d(e,{A:()=>s});var r=i(19827);let n=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function s(t,e,i,s){if(t===e&&i===s)return r.l;let o=e=>(function(t,e,i,r,s){let o,a,l=0;do(o=n(a=e+(i-e)/2,r,s)-t)>0?i=a:e=a;while(Math.abs(o)>1e-7&&++l<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:n(o(t),e,s)}},62923:(t,e,i)=>{i.d(e,{f:()=>r});function r(t,e){return e?1e3/e*t:0}},63704:(t,e,i)=>{i.d(e,{q:()=>r});let r={layout:0,mainThread:0,waapi:0}},63894:(t,e,i)=>{i.d(e,{q:()=>r});class r{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}},64803:(t,e,i)=>{i.d(e,{S:()=>r});let r=t=>!!(t&&t.getVelocity)},66977:(t,e,i)=>{i.d(e,{f:()=>r,z:()=>n});let r=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()}},68589:(t,e,i)=>{i.d(e,{D:()=>r});let r=t=>Array.isArray(t)&&"number"==typeof t[0]},68972:(t,e,i)=>{i.d(e,{B:()=>r});let r="undefined"!=typeof window},69515:(t,e,i)=>{i.d(e,{Gt:()=>n,PP:()=>a,WG:()=>s,uv:()=>o});var r=i(19827);let{schedule:n,cancel:s,state:o,steps:a}=(0,i(58437).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:r.l,!0)},70144:(t,e,i)=>{i.d(e,{E:()=>a});var r=i(56330),n=i(44188),s=i(82886);let o={decay:r.B,inertia:r.B,tween:n.i,keyframes:n.i,spring:s.o};function a(t){"string"==typeof t.type&&(t.type=o[t.type])}},73945:(t,e,i)=>{i.d(e,{Y:()=>n});var r=i(62923);function n(t,e,i){let n=Math.max(e-5,0);return(0,r.f)(i-t(n),e-n)}},74261:(t,e,i)=>{let r;i.d(e,{k:()=>a});var n=i(23387),s=i(69515);function o(){r=void 0}let a={now:()=>(void 0===r&&a.set(s.uv.isProcessing||n.W.useManualTiming?s.uv.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(o)}}},75626:(t,e,i)=>{i.d(e,{v:()=>n});var r=i(56668);class n{constructor(){this.subscriptions=[]}add(t){return(0,r.Kq)(this.subscriptions,t),()=>(0,r.Ai)(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},76778:(t,e,i)=>{i.d(e,{X:()=>n});let r=t=>null!==t;function n(t,{repeat:e,repeatType:i="loop"},s,o=1){let a=t.filter(r),l=o<0||e&&"loop"!==i&&e%2==1?0:a.length-1;return l&&void 0!==s?s:a[l]}},78606:(t,e,i)=>{i.d(e,{j:()=>n,p:()=>o});let r=t=>e=>"string"==typeof e&&e.startsWith(t),n=r("--"),s=r("var(--"),o=t=>!!s(t)&&a.test(t.split("/*")[0].trim()),a=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},80845:(t,e,i)=>{i.d(e,{t:()=>r});let r=(0,i(12115).createContext)(null)},82885:(t,e,i)=>{i.d(e,{M:()=>n});var r=i(12115);function n(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}},82886:(t,e,i)=>{i.d(e,{o:()=>m});var r=i(53678),n=i(47215),s=i(47705),o=i(52458),a=i(73945);let l={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var u=i(54542);function h(t,e){return t*Math.sqrt(1-e*e)}let d=["duration","bounce"],c=["stiffness","damping","mass"];function p(t,e){return e.some(e=>void 0!==t[e])}function m(t=l.visualDuration,e=l.bounce){let i,f="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:g,restDelta:v}=f,y=f.keyframes[0],x=f.keyframes[f.keyframes.length-1],b={done:!1,value:y},{stiffness:w,damping:T,mass:S,duration:P,velocity:A,isResolvedFromDuration:k}=function(t){let e={velocity:l.velocity,stiffness:l.stiffness,damping:l.damping,mass:l.mass,isResolvedFromDuration:!1,...t};if(!p(t,c)&&p(t,d))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,s=2*(0,r.q)(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:l.mass,stiffness:n,damping:s}}else{let i=function({duration:t=l.duration,bounce:e=l.bounce,velocity:i=l.velocity,mass:s=l.mass}){let o,a;(0,u.$)(t<=(0,n.f)(l.maxDuration),"Spring duration must be 10 seconds or less");let d=1-e;d=(0,r.q)(l.minDamping,l.maxDamping,d),t=(0,r.q)(l.minDuration,l.maxDuration,(0,n.X)(t)),d<1?(o=e=>{let r=e*d,n=r*t;return .001-(r-i)/h(e,d)*Math.exp(-n)},a=e=>{let r=e*d*t,n=Math.pow(d,2)*Math.pow(e,2)*t,s=Math.exp(-r),a=h(Math.pow(e,2),d);return(r*i+i-n)*s*(-o(e)+.001>0?-1:1)/a}):(o=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),a=e=>t*t*(i-e)*Math.exp(-e*t));let c=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(o,a,5/t);if(t=(0,n.f)(t),isNaN(c))return{stiffness:l.stiffness,damping:l.damping,duration:t};{let e=Math.pow(c,2)*s;return{stiffness:e,damping:2*d*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:l.mass}).isResolvedFromDuration=!0}return e}({...f,velocity:-(0,n.X)(f.velocity||0)}),V=A||0,M=T/(2*Math.sqrt(w*S)),E=x-y,C=(0,n.X)(Math.sqrt(w/S)),D=5>Math.abs(E);if(g||(g=D?l.restSpeed.granular:l.restSpeed.default),v||(v=D?l.restDelta.granular:l.restDelta.default),M<1){let t=h(C,M);i=e=>x-Math.exp(-M*C*e)*((V+M*C*E)/t*Math.sin(t*e)+E*Math.cos(t*e))}else if(1===M)i=t=>x-Math.exp(-C*t)*(E+(V+C*E)*t);else{let t=C*Math.sqrt(M*M-1);i=e=>{let i=Math.exp(-M*C*e),r=Math.min(t*e,300);return x-i*((V+M*C*E)*Math.sinh(r)+t*E*Math.cosh(r))/t}}let j={calculatedDuration:k&&P||null,next:t=>{let e=i(t);if(k)b.done=t>=P;else{let r=0===t?V:0;M<1&&(r=0===t?(0,n.f)(V):(0,a.Y)(i,t,e));let s=Math.abs(x-e)<=v;b.done=Math.abs(r)<=g&&s}return b.value=b.done?x:e,b},toString:()=>{let t=Math.min((0,o.t)(j),o.Y),e=(0,s.K)(e=>j.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return j}m.applyToOptions=t=>{let e=function(t,e=100,i){let r=i({...t,keyframes:[0,e]}),s=Math.min((0,o.t)(r),o.Y);return{type:"keyframes",ease:t=>r.next(s*t).value/e,duration:(0,n.X)(s)}}(t,100,m);return t.ease=e.ease,t.duration=(0,n.f)(e.duration),t.type="keyframes",t}},90602:(t,e,i)=>{i.d(e,{A:()=>o});var r=i(12115),n=i(66977),s=i(47147);let o=(t,e)=>{let i=(0,r.forwardRef)((i,o)=>{let{className:a,...l}=i;return(0,r.createElement)(s.A,{ref:o,iconNode:e,className:(0,n.z)("lucide-".concat((0,n.f)(t)),a),...l})});return i.displayName="".concat(t),i}},90869:(t,e,i)=>{i.d(e,{L:()=>r});let r=(0,i(12115).createContext)({})},91765:(t,e,i)=>{i.d(e,{V:()=>r});let r=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2},97494:(t,e,i)=>{i.d(e,{E:()=>n});var r=i(12115);let n=i(68972).B?r.useLayoutEffect:r.useEffect},99708:(t,e,i)=>{i.d(e,{DX:()=>a,Dc:()=>u,TL:()=>o});var r=i(12115),n=i(6101),s=i(95155);function o(t){let e=function(t){let e=r.forwardRef((t,e)=>{let{children:i,...s}=t;if(r.isValidElement(i)){var o;let t,a,l=(o=i,(a=(t=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.ref:(a=(t=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(t,e){let i={...e};for(let r in e){let n=t[r],s=e[r];/^on[A-Z]/.test(r)?n&&s?i[r]=(...t)=>{let e=s(...t);return n(...t),e}:n&&(i[r]=n):"style"===r?i[r]={...n,...s}:"className"===r&&(i[r]=[n,s].filter(Boolean).join(" "))}return{...t,...i}}(s,i.props);return i.type!==r.Fragment&&(u.ref=e?(0,n.t)(e,l):l),r.cloneElement(i,u)}return r.Children.count(i)>1?r.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),i=r.forwardRef((t,i)=>{let{children:n,...o}=t,a=r.Children.toArray(n),l=a.find(h);if(l){let t=l.props.children,n=a.map(e=>e!==l?e:r.Children.count(t)>1?r.Children.only(null):r.isValidElement(t)?t.props.children:null);return(0,s.jsx)(e,{...o,ref:i,children:r.isValidElement(t)?r.cloneElement(t,void 0,n):null})}return(0,s.jsx)(e,{...o,ref:i,children:n})});return i.displayName=`${t}.Slot`,i}var a=o("Slot"),l=Symbol("radix.slottable");function u(t){let e=({children:t})=>(0,s.jsx)(s.Fragment,{children:t});return e.displayName=`${t}.Slottable`,e.__radixId=l,e}function h(t){return r.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===l}}}]);