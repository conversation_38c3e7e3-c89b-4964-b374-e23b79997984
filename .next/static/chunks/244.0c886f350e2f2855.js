"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[244],{92244:(e,t,a)=>{a.r(t),a.d(t,{default:()=>s});var r=a(95155),n=a(12115),o=a(10697),i=a(43264);let s=e=>{let{color:t="#3fb950",count:a=2e3,size:s=.06,mouseInfluence:l=.05}=e,u=(0,n.useRef)(null),{mouse:c,viewport:m}=(0,o.D)(),[h,p]=(0,n.useState)(null);if((0,n.useEffect)(()=>{let e=new Float32Array(3*a);for(let t=0;t<a;t++){let a=5*Math.random()+.5,r=Math.acos(2*Math.random()-1),n=Math.random()*Math.PI*2;e[3*t]=a*Math.sin(r)*Math.cos(n),e[3*t+1]=a*Math.sin(r)*Math.sin(n),e[3*t+2]=a*Math.cos(r)*.5}p(e)},[a]),(0,o.F)(e=>{u.current&&h&&(u.current.rotation.y+=8e-4,e.camera.position.x=i.cj9.lerp(e.camera.position.x,c.x*l,.05),e.camera.position.y=i.cj9.lerp(e.camera.position.y,c.y*l,.05),e.camera.lookAt(0,0,0),u.current.material instanceof i.BKk&&(u.current.material.uniforms.time.value=e.clock.getElapsedTime(),u.current.material.uniforms.mousePosition.value.set(c.x*m.width/2,c.y*m.height/2)))}),!h)return null;let f=new i.LoY;f.setAttribute("position",new i.THS(h,3));let M=new i.BH$({color:t,size:s,transparent:!0,opacity:.8,sizeAttenuation:!0});return(0,r.jsx)("points",{ref:u,geometry:f,material:M})}}}]);