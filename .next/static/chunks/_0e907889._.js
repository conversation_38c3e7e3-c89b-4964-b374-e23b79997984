(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_react-reconciler_25c8ec02._.js",
  "static/chunks/node_modules_three_build_three_core_df0bde7e.js",
  "static/chunks/node_modules_three_build_three_module_230a9f89.js",
  "static/chunks/node_modules_three_build_three_module_90b4155f.js",
  "static/chunks/node_modules_@react-three_fiber_dist_32aedd3f._.js",
  "static/chunks/node_modules_971b5526._.js",
  "static/chunks/node_modules_@react-three_fiber_dist_react-three-fiber_esm_de665881.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/components/3d/InteractiveThreeScene.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_three_build_three_core_df0bde7e.js",
  "static/chunks/node_modules_three_build_three_module_230a9f89.js",
  "static/chunks/node_modules_react-reconciler_25c8ec02._.js",
  "static/chunks/node_modules_5c245e00._.js",
  "static/chunks/src_components_3d_InteractiveThreeScene_tsx_8266d445._.js",
  "static/chunks/src_components_3d_InteractiveThreeScene_tsx_766cb464._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/3d/InteractiveThreeScene.tsx [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@splinetool/react-spline/dist/react-spline.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@splinetool_runtime_build_dd53564c._.js",
  "static/chunks/node_modules_@splinetool_runtime_build_runtime_ef1768d2.js",
  "static/chunks/node_modules_cc3c27ea._.js",
  "static/chunks/node_modules_@splinetool_react-spline_dist_react-spline_766cb464.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@splinetool/react-spline/dist/react-spline.js [app-client] (ecmascript)");
    });
});
}}),
}]);