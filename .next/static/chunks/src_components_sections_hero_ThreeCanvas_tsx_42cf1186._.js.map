{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/hero/ThreeCanvas.tsx"], "sourcesContent": ["'use client';\n\nimport React, { Suspense } from 'react';\nimport { Canvas } from '@react-three/fiber';\nimport dynamic from 'next/dynamic';\nimport ThreeFallback from './ThreeFallback';\n\n// Dynamically import the 3D scene\nconst InteractiveThreeScene = dynamic(\n  () => import('../../3d/InteractiveThreeScene'),\n  {\n    ssr: false,\n    loading: () => null\n  }\n);\n\nconst ThreeCanvas = () => {\n  return (\n    <Suspense fallback={<ThreeFallback />}>\n      <Canvas\n        camera={{ position: [0, 0, 6], fov: 50 }}\n        dpr={[1, 2]}\n        style={{ background: 'transparent' }}\n        gl={{\n          antialias: true,\n          alpha: true,\n          powerPreference: 'high-performance',\n          preserveDrawingBuffer: false,\n          failIfMajorPerformanceCaveat: false\n        }}\n        onCreated={({ gl }) => {\n          try {\n            gl.setClearColor(0x000000, 0);\n            console.log('3D Canvas initialized successfully');\n          } catch (error) {\n            console.error('Canvas creation error:', error);\n            // Error will be caught by the ErrorBoundary in parent component\n          }\n        }}\n        frameloop=\"demand\"\n        legacy={false}\n      >\n        <InteractiveThreeScene />\n      </Canvas>\n    </Suspense>\n  );\n};\n\nexport default ThreeCanvas;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;AALA;;;;;;AAOA,kCAAkC;AAClC,MAAM,wBAAwB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAClC;;;;;;IAEE,KAAK;IACL,SAAS,IAAM;;KAJb;AAQN,MAAM,cAAc;IAClB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBAAU,6LAAC,0JAAA,CAAA,UAAa;;;;;kBAChC,cAAA,6LAAC,sMAAA,CAAA,SAAM;YACL,QAAQ;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,KAAK;YAAG;YACvC,KAAK;gBAAC;gBAAG;aAAE;YACX,OAAO;gBAAE,YAAY;YAAc;YACnC,IAAI;gBACF,WAAW;gBACX,OAAO;gBACP,iBAAiB;gBACjB,uBAAuB;gBACvB,8BAA8B;YAChC;YACA,WAAW,CAAC,EAAE,EAAE,EAAE;gBAChB,IAAI;oBACF,GAAG,aAAa,CAAC,UAAU;oBAC3B,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,gEAAgE;gBAClE;YACF;YACA,WAAU;YACV,QAAQ;sBAER,cAAA,6LAAC;;;;;;;;;;;;;;;AAIT;MA9BM;uCAgCS", "debugId": null}}]}