(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/effects/AnimatedCursor.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_components_effects_AnimatedCursor_tsx_1a540175._.js",
  "static/chunks/src_components_effects_AnimatedCursor_tsx_3b357fc1._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/effects/AnimatedCursor.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/effects/ReactiveBackground.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_8f25e8e9._.js",
  "static/chunks/src_components_effects_ReactiveBackground_tsx_3b357fc1._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/effects/ReactiveBackground.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/sections/Chatbot.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_b2457114._.js",
  "static/chunks/src_components_sections_Chatbot_tsx_3b357fc1._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/sections/Chatbot.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);