(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[469],{26621:(e,t,n)=>{"use strict";n.d(t,{Kq:()=>Y,LM:()=>$,VY:()=>ee,bL:()=>J,bm:()=>en,hE:()=>Z,rc:()=>et});var r=n(12115),i=n(47650),o=n(85185),s=n(6101),a=n(37328),l=n(46081),u=n(19178),c=n(34378),d=n(28905),h=n(63655),f=n(39033),p=n(5845),m=n(52712),y=n(2564),v=n(95155),g="ToastProvider",[w,b,x]=(0,a.N)("Toast"),[C,E]=(0,l.A)("Toast",[x]),[T,R]=C(g),P=e=>{let{__scopeToast:t,label:n="Notification",duration:i=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:a}=e,[l,u]=r.useState(null),[c,d]=r.useState(0),h=r.useRef(!1),f=r.useRef(!1);return n.trim()||console.error("Invalid prop `label` supplied to `".concat(g,"`. Expected non-empty `string`.")),(0,v.jsx)(w.Provider,{scope:t,children:(0,v.jsx)(T,{scope:t,label:n,duration:i,swipeDirection:o,swipeThreshold:s,toastCount:c,viewport:l,onViewportChange:u,onToastAdd:r.useCallback(()=>d(e=>e+1),[]),onToastRemove:r.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:h,isClosePausedRef:f,children:a})})};P.displayName=g;var O="ToastViewport",S=["F8"],A="toast.viewportPause",D="toast.viewportResume",F=r.forwardRef((e,t)=>{let{__scopeToast:n,hotkey:i=S,label:o="Notifications ({hotkey})",...a}=e,l=R(O,n),c=b(n),d=r.useRef(null),f=r.useRef(null),p=r.useRef(null),m=r.useRef(null),y=(0,s.s)(t,m,l.onViewportChange),g=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=l.toastCount>0;r.useEffect(()=>{let e=e=>{var t;0!==i.length&&i.every(t=>e[t]||e.code===t)&&(null==(t=m.current)||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[i]),r.useEffect(()=>{let e=d.current,t=m.current;if(x&&e&&t){let n=()=>{if(!l.isClosePausedRef.current){let e=new CustomEvent(A);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},r=()=>{if(l.isClosePausedRef.current){let e=new CustomEvent(D);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},i=t=>{e.contains(t.relatedTarget)||r()},o=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",i),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",o),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",i),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",o),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}},[x,l.isClosePausedRef]);let C=r.useCallback(e=>{let{tabbingDirection:t}=e,n=c().map(e=>{let n=e.ref.current,r=[n,...function(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}(n)];return"forwards"===t?r:r.reverse()});return("forwards"===t?n.reverse():n).flat()},[c]);return r.useEffect(()=>{let e=m.current;if(e){let t=t=>{let n=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!n){var r,i,o;let n=document.activeElement,s=t.shiftKey;if(t.target===e&&s){null==(r=f.current)||r.focus();return}let a=C({tabbingDirection:s?"backwards":"forwards"}),l=a.findIndex(e=>e===n);X(a.slice(l+1))?t.preventDefault():s?null==(i=f.current)||i.focus():null==(o=p.current)||o.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,C]),(0,v.jsxs)(u.lg,{ref:d,role:"region","aria-label":o.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&(0,v.jsx)(j,{ref:f,onFocusFromOutsideViewport:()=>{X(C({tabbingDirection:"forwards"}))}}),(0,v.jsx)(w.Slot,{scope:n,children:(0,v.jsx)(h.sG.ol,{tabIndex:-1,...a,ref:y})}),x&&(0,v.jsx)(j,{ref:p,onFocusFromOutsideViewport:()=>{X(C({tabbingDirection:"backwards"}))}})]})});F.displayName=O;var L="ToastFocusProxy",j=r.forwardRef((e,t)=>{let{__scopeToast:n,onFocusFromOutsideViewport:r,...i}=e,o=R(L,n);return(0,v.jsx)(y.s6,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let n=e.relatedTarget;(null==(t=o.viewport)?void 0:t.contains(n))||r()}})});j.displayName=L;var k="Toast",q=r.forwardRef((e,t)=>{let{forceMount:n,open:r,defaultOpen:i,onOpenChange:s,...a}=e,[l,u]=(0,p.i)({prop:r,defaultProp:null==i||i,onChange:s,caller:k});return(0,v.jsx)(d.C,{present:n||l,children:(0,v.jsx)(I,{open:l,...a,ref:t,onClose:()=>u(!1),onPause:(0,f.c)(e.onPause),onResume:(0,f.c)(e.onResume),onSwipeStart:(0,o.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,o.m)(e.onSwipeMove,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(n,"px"))}),onSwipeCancel:(0,o.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,o.m)(e.onSwipeEnd,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(n,"px")),u(!1)})})})});q.displayName=k;var[M,N]=C(k,{onClose(){}}),I=r.forwardRef((e,t)=>{let{__scopeToast:n,type:a="foreground",duration:l,open:c,onClose:d,onEscapeKeyDown:p,onPause:m,onResume:y,onSwipeStart:g,onSwipeMove:b,onSwipeCancel:x,onSwipeEnd:C,...E}=e,T=R(k,n),[P,O]=r.useState(null),S=(0,s.s)(t,e=>O(e)),F=r.useRef(null),L=r.useRef(null),j=l||T.duration,q=r.useRef(0),N=r.useRef(j),I=r.useRef(0),{onToastAdd:Q,onToastRemove:K}=T,U=(0,f.c)(()=>{var e;(null==P?void 0:P.contains(document.activeElement))&&(null==(e=T.viewport)||e.focus()),d()}),_=r.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(I.current),q.current=new Date().getTime(),I.current=window.setTimeout(U,e))},[U]);r.useEffect(()=>{let e=T.viewport;if(e){let t=()=>{_(N.current),null==y||y()},n=()=>{let e=new Date().getTime()-q.current;N.current=N.current-e,window.clearTimeout(I.current),null==m||m()};return e.addEventListener(A,n),e.addEventListener(D,t),()=>{e.removeEventListener(A,n),e.removeEventListener(D,t)}}},[T.viewport,j,m,y,_]),r.useEffect(()=>{c&&!T.isClosePausedRef.current&&_(j)},[c,j,T.isClosePausedRef,_]),r.useEffect(()=>(Q(),()=>K()),[Q,K]);let G=r.useMemo(()=>P?function e(t){let n=[];return Array.from(t.childNodes).forEach(t=>{var r;if(t.nodeType===t.TEXT_NODE&&t.textContent&&n.push(t.textContent),(r=t).nodeType===r.ELEMENT_NODE){let r=t.ariaHidden||t.hidden||"none"===t.style.display,i=""===t.dataset.radixToastAnnounceExclude;if(!r)if(i){let e=t.dataset.radixToastAnnounceAlt;e&&n.push(e)}else n.push(...e(t))}}),n}(P):null,[P]);return T.viewport?(0,v.jsxs)(v.Fragment,{children:[G&&(0,v.jsx)(H,{__scopeToast:n,role:"status","aria-live":"foreground"===a?"assertive":"polite","aria-atomic":!0,children:G}),(0,v.jsx)(M,{scope:n,onClose:U,children:i.createPortal((0,v.jsx)(w.ItemSlot,{scope:n,children:(0,v.jsx)(u.bL,{asChild:!0,onEscapeKeyDown:(0,o.m)(p,()=>{T.isFocusedToastEscapeKeyDownRef.current||U(),T.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,v.jsx)(h.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":T.swipeDirection,...E,ref:S,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,o.m)(e.onKeyDown,e=>{"Escape"===e.key&&(null==p||p(e.nativeEvent),e.nativeEvent.defaultPrevented||(T.isFocusedToastEscapeKeyDownRef.current=!0,U()))}),onPointerDown:(0,o.m)(e.onPointerDown,e=>{0===e.button&&(F.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,o.m)(e.onPointerMove,e=>{if(!F.current)return;let t=e.clientX-F.current.x,n=e.clientY-F.current.y,r=!!L.current,i=["left","right"].includes(T.swipeDirection),o=["left","up"].includes(T.swipeDirection)?Math.min:Math.max,s=i?o(0,t):0,a=i?0:o(0,n),l="touch"===e.pointerType?10:2,u={x:s,y:a},c={originalEvent:e,delta:u};r?(L.current=u,z("toast.swipeMove",b,c,{discrete:!1})):V(u,T.swipeDirection,l)?(L.current=u,z("toast.swipeStart",g,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(n)>l)&&(F.current=null)}),onPointerUp:(0,o.m)(e.onPointerUp,e=>{let t=L.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),L.current=null,F.current=null,t){let n=e.currentTarget,r={originalEvent:e,delta:t};V(t,T.swipeDirection,T.swipeThreshold)?z("toast.swipeEnd",C,r,{discrete:!0}):z("toast.swipeCancel",x,r,{discrete:!0}),n.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),T.viewport)})]}):null}),H=e=>{let{__scopeToast:t,children:n,...i}=e,o=R(k,t),[s,a]=r.useState(!1),[l,u]=r.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,f.c)(e);(0,m.N)(()=>{let e=0,n=0;return e=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}},[t])}(()=>a(!0)),r.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,v.jsx)(c.Z,{asChild:!0,children:(0,v.jsx)(y.s6,{...i,children:s&&(0,v.jsxs)(v.Fragment,{children:[o.label," ",n]})})})},Q=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,v.jsx)(h.sG.div,{...r,ref:t})});Q.displayName="ToastTitle";var K=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,v.jsx)(h.sG.div,{...r,ref:t})});K.displayName="ToastDescription";var U="ToastAction",_=r.forwardRef((e,t)=>{let{altText:n,...r}=e;return n.trim()?(0,v.jsx)(W,{altText:n,asChild:!0,children:(0,v.jsx)(B,{...r,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(U,"`. Expected non-empty `string`.")),null)});_.displayName=U;var G="ToastClose",B=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e,i=N(G,n);return(0,v.jsx)(W,{asChild:!0,children:(0,v.jsx)(h.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,i.onClose)})})});B.displayName=G;var W=r.forwardRef((e,t)=>{let{__scopeToast:n,altText:r,...i}=e;return(0,v.jsx)(h.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...i,ref:t})});function z(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,h.hO)(o,s):o.dispatchEvent(s)}var V=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=Math.abs(e.x),i=Math.abs(e.y),o=r>i;return"left"===t||"right"===t?o&&r>n:!o&&i>n};function X(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var Y=P,$=F,J=q,Z=Q,ee=K,et=_,en=B},26715:(e,t,n)=>{"use strict";n.d(t,{Ht:()=>s});var r=n(12115),i=n(95155),o=r.createContext(void 0),s=e=>{let{client:t,children:n}=e;return r.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,i.jsx)(o.Provider,{value:t,children:n})}},51362:(e,t,n)=>{"use strict";n.d(t,{D:()=>a});var r=n(12115),i="(prefers-color-scheme: dark)",o=r.createContext(void 0),s={setTheme:e=>{},themes:[]},a=()=>{var e;return null!=(e=r.useContext(o))?e:s},l=null,u=(e,t)=>{let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},c=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},d=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},60760:(e,t,n)=>{"use strict";n.d(t,{N:()=>g});var r=n(95155),i=n(12115),o=n(90869),s=n(82885),a=n(97494),l=n(80845),u=n(27351),c=n(51508);class d extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,u.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h(e){let{children:t,isPresent:n,anchorX:o}=e,s=(0,i.useId)(),a=(0,i.useRef)(null),l=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,i.useContext)(c.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:i,right:c}=l.current;if(n||!a.current||!e||!t)return;a.current.dataset.motionPopId=s;let d=document.createElement("style");return u&&(d.nonce=u),document.head.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(s,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===o?"left: ".concat(i):"right: ".concat(c),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[n]),(0,r.jsx)(d,{isPresent:n,childRef:a,sizeRef:l,children:i.cloneElement(t,{ref:a})})}let f=e=>{let{children:t,initial:n,isPresent:o,onExitComplete:a,custom:u,presenceAffectsLayout:c,mode:d,anchorX:f}=e,m=(0,s.M)(p),y=(0,i.useId)(),v=!0,g=(0,i.useMemo)(()=>(v=!1,{id:y,initial:n,isPresent:o,custom:u,onExitComplete:e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;a&&a()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[o,m,a]);return c&&v&&(g={...g}),(0,i.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[o]),i.useEffect(()=>{o||m.size||!a||a()},[o]),"popLayout"===d&&(t=(0,r.jsx)(h,{isPresent:o,anchorX:f,children:t})),(0,r.jsx)(l.t.Provider,{value:g,children:t})};function p(){return new Map}var m=n(32082);let y=e=>e.key||"";function v(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let g=e=>{let{children:t,custom:n,initial:l=!0,onExitComplete:u,presenceAffectsLayout:c=!0,mode:d="sync",propagate:h=!1,anchorX:p="left"}=e,[g,w]=(0,m.xQ)(h),b=(0,i.useMemo)(()=>v(t),[t]),x=h&&!g?[]:b.map(y),C=(0,i.useRef)(!0),E=(0,i.useRef)(b),T=(0,s.M)(()=>new Map),[R,P]=(0,i.useState)(b),[O,S]=(0,i.useState)(b);(0,a.E)(()=>{C.current=!1,E.current=b;for(let e=0;e<O.length;e++){let t=y(O[e]);x.includes(t)?T.delete(t):!0!==T.get(t)&&T.set(t,!1)}},[O,x.length,x.join("-")]);let A=[];if(b!==R){let e=[...b];for(let t=0;t<O.length;t++){let n=O[t],r=y(n);x.includes(r)||(e.splice(t,0,n),A.push(n))}return"wait"===d&&A.length&&(e=A),S(v(e)),P(b),null}let{forceRender:D}=(0,i.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:O.map(e=>{let t=y(e),i=(!h||!!g)&&(b===O||x.includes(t));return(0,r.jsx)(f,{isPresent:i,initial:(!C.current||!!l)&&void 0,custom:n,presenceAffectsLayout:c,mode:d,onExitComplete:i?void 0:()=>{if(!T.has(t))return;T.set(t,!0);let e=!0;T.forEach(t=>{t||(e=!1)}),e&&(null==D||D(),S(E.current),h&&(null==w||w()),u&&u())},anchorX:p,children:e},t)})})}},61764:(e,t,n)=>{"use strict";n.d(t,{UC:()=>tg,Kq:()=>tm,bL:()=>ty,l9:()=>tv});var r=n(12115),i=n(85185),o=n(6101),s=n(46081),a=n(19178),l=n(61285);let u=["top","right","bottom","left"],c=Math.min,d=Math.max,h=Math.round,f=Math.floor,p=e=>({x:e,y:e}),m={left:"right",right:"left",bottom:"top",top:"bottom"},y={start:"end",end:"start"};function v(e,t){return"function"==typeof e?e(t):e}function g(e){return e.split("-")[0]}function w(e){return e.split("-")[1]}function b(e){return"x"===e?"y":"x"}function x(e){return"y"===e?"height":"width"}function C(e){return["top","bottom"].includes(g(e))?"y":"x"}function E(e){return e.replace(/start|end/g,e=>y[e])}function T(e){return e.replace(/left|right|bottom|top/g,e=>m[e])}function R(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function P(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function O(e,t,n){let r,{reference:i,floating:o}=e,s=C(t),a=b(C(t)),l=x(a),u=g(t),c="y"===s,d=i.x+i.width/2-o.width/2,h=i.y+i.height/2-o.height/2,f=i[l]/2-o[l]/2;switch(u){case"top":r={x:d,y:i.y-o.height};break;case"bottom":r={x:d,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:h};break;case"left":r={x:i.x-o.width,y:h};break;default:r={x:i.x,y:i.y}}switch(w(t)){case"start":r[a]-=f*(n&&c?-1:1);break;case"end":r[a]+=f*(n&&c?-1:1)}return r}let S=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:s}=n,a=o.filter(Boolean),l=await (null==s.isRTL?void 0:s.isRTL(t)),u=await s.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:d}=O(u,r,l),h=r,f={},p=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:y,y:v,data:g,reset:w}=await m({x:c,y:d,initialPlacement:r,placement:h,strategy:i,middlewareData:f,rects:u,platform:s,elements:{reference:e,floating:t}});c=null!=y?y:c,d=null!=v?v:d,f={...f,[o]:{...f[o],...g}},w&&p<=50&&(p++,"object"==typeof w&&(w.placement&&(h=w.placement),w.rects&&(u=!0===w.rects?await s.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:c,y:d}=O(u,h,l)),n=-1)}return{x:c,y:d,placement:h,strategy:i,middlewareData:f}};async function A(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:s,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:h=!1,padding:f=0}=v(t,e),p=R(f),m=a[h?"floating"===d?"reference":"floating":d],y=P(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(m)))||n?m:m.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),g="floating"===d?{x:r,y:i,width:s.floating.width,height:s.floating.height}:s.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),b=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},x=P(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:w,strategy:l}):g);return{top:(y.top-x.top+p.top)/b.y,bottom:(x.bottom-y.bottom+p.bottom)/b.y,left:(y.left-x.left+p.left)/b.x,right:(x.right-y.right+p.right)/b.x}}function D(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function F(e){return u.some(t=>e[t]>=0)}async function L(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),s=g(n),a=w(n),l="y"===C(n),u=["left","top"].includes(s)?-1:1,c=o&&l?-1:1,d=v(t,e),{mainAxis:h,crossAxis:f,alignmentAxis:p}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof p&&(f="end"===a?-1*p:p),l?{x:f*c,y:h*u}:{x:h*u,y:f*c}}function j(){return"undefined"!=typeof window}function k(e){return N(e)?(e.nodeName||"").toLowerCase():"#document"}function q(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function M(e){var t;return null==(t=(N(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function N(e){return!!j()&&(e instanceof Node||e instanceof q(e).Node)}function I(e){return!!j()&&(e instanceof Element||e instanceof q(e).Element)}function H(e){return!!j()&&(e instanceof HTMLElement||e instanceof q(e).HTMLElement)}function Q(e){return!!j()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof q(e).ShadowRoot)}function K(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=W(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function U(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function _(e){let t=G(),n=I(e)?W(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function G(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(k(e))}function W(e){return q(e).getComputedStyle(e)}function z(e){return I(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function V(e){if("html"===k(e))return e;let t=e.assignedSlot||e.parentNode||Q(e)&&e.host||M(e);return Q(t)?t.host:t}function X(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=V(t);return B(n)?t.ownerDocument?t.ownerDocument.body:t.body:H(n)&&K(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),s=q(i);if(o){let e=Y(s);return t.concat(s,s.visualViewport||[],K(i)?i:[],e&&n?X(e):[])}return t.concat(i,X(i,[],n))}function Y(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function $(e){let t=W(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=H(e),o=i?e.offsetWidth:n,s=i?e.offsetHeight:r,a=h(n)!==o||h(r)!==s;return a&&(n=o,r=s),{width:n,height:r,$:a}}function J(e){return I(e)?e:e.contextElement}function Z(e){let t=J(e);if(!H(t))return p(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=$(t),s=(o?h(n.width):n.width)/r,a=(o?h(n.height):n.height)/i;return s&&Number.isFinite(s)||(s=1),a&&Number.isFinite(a)||(a=1),{x:s,y:a}}let ee=p(0);function et(e){let t=q(e);return G()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ee}function en(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),s=J(e),a=p(1);t&&(r?I(r)&&(a=Z(r)):a=Z(e));let l=(void 0===(i=n)&&(i=!1),r&&(!i||r===q(s))&&i)?et(s):p(0),u=(o.left+l.x)/a.x,c=(o.top+l.y)/a.y,d=o.width/a.x,h=o.height/a.y;if(s){let e=q(s),t=r&&I(r)?q(r):r,n=e,i=Y(n);for(;i&&r&&t!==n;){let e=Z(i),t=i.getBoundingClientRect(),r=W(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,s=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,h*=e.y,u+=o,c+=s,i=Y(n=q(i))}}return P({width:d,height:h,x:u,y:c})}function er(e,t){let n=z(e).scrollLeft;return t?t.left+n:en(M(e)).left+n}function ei(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:er(e,r)),y:r.top+t.scrollTop}}function eo(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=q(e),r=M(e),i=n.visualViewport,o=r.clientWidth,s=r.clientHeight,a=0,l=0;if(i){o=i.width,s=i.height;let e=G();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,l=i.offsetTop)}return{width:o,height:s,x:a,y:l}}(e,n);else if("document"===t)r=function(e){let t=M(e),n=z(e),r=e.ownerDocument.body,i=d(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=d(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),s=-n.scrollLeft+er(e),a=-n.scrollTop;return"rtl"===W(r).direction&&(s+=d(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:s,y:a}}(M(e));else if(I(t))r=function(e,t){let n=en(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=H(e)?Z(e):p(1),s=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:s,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=et(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return P(r)}function es(e){return"static"===W(e).position}function ea(e,t){if(!H(e)||"fixed"===W(e).position)return null;if(t)return t(e);let n=e.offsetParent;return M(e)===n&&(n=n.ownerDocument.body),n}function el(e,t){let n=q(e);if(U(e))return n;if(!H(e)){let t=V(e);for(;t&&!B(t);){if(I(t)&&!es(t))return t;t=V(t)}return n}let r=ea(e,t);for(;r&&["table","td","th"].includes(k(r))&&es(r);)r=ea(r,t);return r&&B(r)&&es(r)&&!_(r)?n:r||function(e){let t=V(e);for(;H(t)&&!B(t);){if(_(t))return t;if(U(t))break;t=V(t)}return null}(e)||n}let eu=async function(e){let t=this.getOffsetParent||el,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=H(t),i=M(t),o="fixed"===n,s=en(e,!0,o,t),a={scrollLeft:0,scrollTop:0},l=p(0);if(r||!r&&!o)if(("body"!==k(t)||K(i))&&(a=z(t)),r){let e=en(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=er(i));o&&!r&&i&&(l.x=er(i));let u=!i||r||o?p(0):ei(i,a);return{x:s.left+a.scrollLeft-l.x-u.x,y:s.top+a.scrollTop-l.y-u.y,width:s.width,height:s.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ec={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,s=M(r),a=!!t&&U(t.floating);if(r===s||a&&o)return n;let l={scrollLeft:0,scrollTop:0},u=p(1),c=p(0),d=H(r);if((d||!d&&!o)&&(("body"!==k(r)||K(s))&&(l=z(r)),H(r))){let e=en(r);u=Z(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let h=!s||d||o?p(0):ei(s,l,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+c.x+h.x,y:n.y*u.y-l.scrollTop*u.y+c.y+h.y}},getDocumentElement:M,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,o=[..."clippingAncestors"===n?U(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=X(e,[],!1).filter(e=>I(e)&&"body"!==k(e)),i=null,o="fixed"===W(e).position,s=o?V(e):e;for(;I(s)&&!B(s);){let t=W(s),n=_(s);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||K(s)&&!n&&function e(t,n){let r=V(t);return!(r===n||!I(r)||B(r))&&("fixed"===W(r).position||e(r,n))}(e,s))?r=r.filter(e=>e!==s):i=t,s=V(s)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=o[0],a=o.reduce((e,n)=>{let r=eo(t,n,i);return e.top=d(r.top,e.top),e.right=c(r.right,e.right),e.bottom=c(r.bottom,e.bottom),e.left=d(r.left,e.left),e},eo(t,s,i));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:el,getElementRects:eu,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=$(e);return{width:t,height:n}},getScale:Z,isElement:I,isRTL:function(e){return"rtl"===W(e).direction}};function ed(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eh=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:o,platform:s,elements:a,middlewareData:l}=t,{element:u,padding:h=0}=v(e,t)||{};if(null==u)return{};let f=R(h),p={x:n,y:r},m=b(C(i)),y=x(m),g=await s.getDimensions(u),E="y"===m,T=E?"clientHeight":"clientWidth",P=o.reference[y]+o.reference[m]-p[m]-o.floating[y],O=p[m]-o.reference[m],S=await (null==s.getOffsetParent?void 0:s.getOffsetParent(u)),A=S?S[T]:0;A&&await (null==s.isElement?void 0:s.isElement(S))||(A=a.floating[T]||o.floating[y]);let D=A/2-g[y]/2-1,F=c(f[E?"top":"left"],D),L=c(f[E?"bottom":"right"],D),j=A-g[y]-L,k=A/2-g[y]/2+(P/2-O/2),q=d(F,c(k,j)),M=!l.arrow&&null!=w(i)&&k!==q&&o.reference[y]/2-(k<F?F:L)-g[y]/2<0,N=M?k<F?k-F:k-j:0;return{[m]:p[m]+N,data:{[m]:q,centerOffset:k-q-N,...M&&{alignmentOffset:N}},reset:M}}}),ef=(e,t,n)=>{let r=new Map,i={platform:ec,...n},o={...i.platform,_c:r};return S(e,t,{...i,platform:o})};var ep=n(47650),em="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ey(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ey(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!ey(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ev(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eg(e,t){let n=ev(e);return Math.round(t*n)/n}function ew(e){let t=r.useRef(e);return em(()=>{t.current=e}),t}let eb=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eh({element:n.current,padding:r}).fn(t):{}:n?eh({element:n,padding:r}).fn(t):{}}}),ex=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:s,middlewareData:a}=t,l=await L(t,e);return s===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+l.x,y:o+l.y,data:{...l,placement:s}}}}}(e),options:[e,t]}),eC=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:o=!0,crossAxis:s=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...l}=v(e,t),u={x:n,y:r},h=await A(t,l),f=C(g(i)),p=b(f),m=u[p],y=u[f];if(o){let e="y"===p?"top":"left",t="y"===p?"bottom":"right",n=m+h[e],r=m-h[t];m=d(n,c(m,r))}if(s){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=y+h[e],r=y-h[t];y=d(n,c(y,r))}let w=a.fn({...t,[p]:m,[f]:y});return{...w,data:{x:w.x-n,y:w.y-r,enabled:{[p]:o,[f]:s}}}}}}(e),options:[e,t]}),eE=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:s}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=v(e,t),c={x:n,y:r},d=C(i),h=b(d),f=c[h],p=c[d],m=v(a,t),y="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(l){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+y.mainAxis,n=o.reference[h]+o.reference[e]-y.mainAxis;f<t?f=t:f>n&&(f=n)}if(u){var w,x;let e="y"===h?"width":"height",t=["top","left"].includes(g(i)),n=o.reference[d]-o.floating[e]+(t&&(null==(w=s.offset)?void 0:w[d])||0)+(t?0:y.crossAxis),r=o.reference[d]+o.reference[e]+(t?0:(null==(x=s.offset)?void 0:x[d])||0)-(t?y.crossAxis:0);p<n?p=n:p>r&&(p=r)}return{[h]:f,[d]:p}}}}(e),options:[e,t]}),eT=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,s,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:d,platform:h,elements:f}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:y,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:O=!0,...S}=v(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let D=g(l),F=C(d),L=g(d)===d,j=await (null==h.isRTL?void 0:h.isRTL(f.floating)),k=y||(L||!O?[T(d)]:function(e){let t=T(e);return[E(e),t,E(t)]}(d)),q="none"!==P;!y&&q&&k.push(...function(e,t,n,r){let i=w(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(g(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(E)))),o}(d,O,P,j));let M=[d,...k],N=await A(t,S),I=[],H=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&I.push(N[D]),m){let e=function(e,t,n){void 0===n&&(n=!1);let r=w(e),i=b(C(e)),o=x(i),s="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(s=T(s)),[s,T(s)]}(l,c,j);I.push(N[e[0]],N[e[1]])}if(H=[...H,{placement:l,overflows:I}],!I.every(e=>e<=0)){let e=((null==(i=u.flip)?void 0:i.index)||0)+1,t=M[e];if(t){let n="alignment"===m&&F!==C(t),r=(null==(s=H[0])?void 0:s.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:H},reset:{placement:t}}}let n=null==(o=H.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(R){case"bestFit":{let e=null==(a=H.filter(e=>{if(q){let t=C(e.placement);return t===F||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=d}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eR=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,o,{placement:s,rects:a,platform:l,elements:u}=t,{apply:h=()=>{},...f}=v(e,t),p=await A(t,f),m=g(s),y=w(s),b="y"===C(s),{width:x,height:E}=a.floating;"top"===m||"bottom"===m?(i=m,o=y===(await (null==l.isRTL?void 0:l.isRTL(u.floating))?"start":"end")?"left":"right"):(o=m,i="end"===y?"top":"bottom");let T=E-p.top-p.bottom,R=x-p.left-p.right,P=c(E-p[i],T),O=c(x-p[o],R),S=!t.middlewareData.shift,D=P,F=O;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(F=R),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(D=T),S&&!y){let e=d(p.left,0),t=d(p.right,0),n=d(p.top,0),r=d(p.bottom,0);b?F=x-2*(0!==e||0!==t?e+t:d(p.left,p.right)):D=E-2*(0!==n||0!==r?n+r:d(p.top,p.bottom))}await h({...t,availableWidth:F,availableHeight:D});let L=await l.getDimensions(u.floating);return x!==L.width||E!==L.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=v(e,t);switch(r){case"referenceHidden":{let e=D(await A(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:F(e)}}}case"escaped":{let e=D(await A(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:F(e)}}}default:return{}}}}}(e),options:[e,t]}),eO=(e,t)=>({...eb(e),options:[e,t]});var eS=n(63655),eA=n(95155),eD=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eA.jsx)(eS.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eA.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eD.displayName="Arrow";var eF=n(39033),eL=n(52712),ej="Popper",[ek,eq]=(0,s.A)(ej),[eM,eN]=ek(ej),eI=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eA.jsx)(eM,{scope:t,anchor:i,onAnchorChange:o,children:n})};eI.displayName=ej;var eH="PopperAnchor",eQ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...s}=e,a=eN(eH,n),l=r.useRef(null),u=(0,o.s)(t,l);return r.useEffect(()=>{a.onAnchorChange((null==i?void 0:i.current)||l.current)}),i?null:(0,eA.jsx)(eS.sG.div,{...s,ref:u})});eQ.displayName=eH;var eK="PopperContent",[eU,e_]=ek(eK),eG=r.forwardRef((e,t)=>{var n,i,s,a,l,u,h,p;let{__scopePopper:m,side:y="bottom",sideOffset:v=0,align:g="center",alignOffset:w=0,arrowPadding:b=0,avoidCollisions:x=!0,collisionBoundary:C=[],collisionPadding:E=0,sticky:T="partial",hideWhenDetached:R=!1,updatePositionStrategy:P="optimized",onPlaced:O,...S}=e,A=eN(eK,m),[D,F]=r.useState(null),L=(0,o.s)(t,e=>F(e)),[j,k]=r.useState(null),q=function(e){let[t,n]=r.useState(void 0);return(0,eL.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(j),N=null!=(h=null==q?void 0:q.width)?h:0,I=null!=(p=null==q?void 0:q.height)?p:0,H="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},Q=Array.isArray(C)?C:[C],K=Q.length>0,U={padding:H,boundary:Q.filter(eV),altBoundary:K},{refs:_,floatingStyles:G,placement:B,isPositioned:W,middlewareData:z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:s,floating:a}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,h]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[f,p]=r.useState(i);ey(f,i)||p(i);let[m,y]=r.useState(null),[v,g]=r.useState(null),w=r.useCallback(e=>{e!==E.current&&(E.current=e,y(e))},[]),b=r.useCallback(e=>{e!==T.current&&(T.current=e,g(e))},[]),x=s||m,C=a||v,E=r.useRef(null),T=r.useRef(null),R=r.useRef(d),P=null!=u,O=ew(u),S=ew(o),A=ew(c),D=r.useCallback(()=>{if(!E.current||!T.current)return;let e={placement:t,strategy:n,middleware:f};S.current&&(e.platform=S.current),ef(E.current,T.current,e).then(e=>{let t={...e,isPositioned:!1!==A.current};F.current&&!ey(R.current,t)&&(R.current=t,ep.flushSync(()=>{h(t)}))})},[f,t,n,S,A]);em(()=>{!1===c&&R.current.isPositioned&&(R.current.isPositioned=!1,h(e=>({...e,isPositioned:!1})))},[c]);let F=r.useRef(!1);em(()=>(F.current=!0,()=>{F.current=!1}),[]),em(()=>{if(x&&(E.current=x),C&&(T.current=C),x&&C){if(O.current)return O.current(x,C,D);D()}},[x,C,D,O,P]);let L=r.useMemo(()=>({reference:E,floating:T,setReference:w,setFloating:b}),[w,b]),j=r.useMemo(()=>({reference:x,floating:C}),[x,C]),k=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=eg(j.floating,d.x),r=eg(j.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...ev(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,j.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:D,refs:L,elements:j,floatingStyles:k}),[d,D,L,j,k])}({strategy:"fixed",placement:y+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,h=J(e),p=o||s?[...h?X(h):[],...X(t)]:[];p.forEach(e=>{o&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let m=h&&l?function(e,t){let n,r=null,i=M(e);function o(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(a,l){void 0===a&&(a=!1),void 0===l&&(l=1),o();let u=e.getBoundingClientRect(),{left:h,top:p,width:m,height:y}=u;if(a||t(),!m||!y)return;let v=f(p),g=f(i.clientWidth-(h+m)),w={rootMargin:-v+"px "+-g+"px "+-f(i.clientHeight-(p+y))+"px "+-f(h)+"px",threshold:d(0,c(1,l))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==l){if(!b)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||ed(u,e.getBoundingClientRect())||s(),b=!1}try{r=new IntersectionObserver(x,{...w,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),o}(h,n):null,y=-1,v=null;a&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===h&&v&&(v.unobserve(t),cancelAnimationFrame(y),y=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),h&&!u&&v.observe(h),v.observe(t));let g=u?en(e):null;return u&&function t(){let r=en(e);g&&!ed(g,r)&&n(),g=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{o&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=v)||e.disconnect(),v=null,u&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===P})},elements:{reference:A.anchor},middleware:[ex({mainAxis:v+I,alignmentAxis:w}),x&&eC({mainAxis:!0,crossAxis:!1,limiter:"partial"===T?eE():void 0,...U}),x&&eT({...U}),eR({...U,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:s}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(s,"px"))}}),j&&eO({element:j,padding:b}),eX({arrowWidth:N,arrowHeight:I}),R&&eP({strategy:"referenceHidden",...U})]}),[V,Y]=eY(B),$=(0,eF.c)(O);(0,eL.N)(()=>{W&&(null==$||$())},[W,$]);let Z=null==(n=z.arrow)?void 0:n.x,ee=null==(i=z.arrow)?void 0:i.y,et=(null==(s=z.arrow)?void 0:s.centerOffset)!==0,[er,ei]=r.useState();return(0,eL.N)(()=>{D&&ei(window.getComputedStyle(D).zIndex)},[D]),(0,eA.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:W?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:er,"--radix-popper-transform-origin":[null==(a=z.transformOrigin)?void 0:a.x,null==(l=z.transformOrigin)?void 0:l.y].join(" "),...(null==(u=z.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eA.jsx)(eU,{scope:m,placedSide:V,onArrowChange:k,arrowX:Z,arrowY:ee,shouldHideArrow:et,children:(0,eA.jsx)(eS.sG.div,{"data-side":V,"data-align":Y,...S,ref:L,style:{...S.style,animation:W?void 0:"none"}})})})});eG.displayName=eK;var eB="PopperArrow",eW={top:"bottom",right:"left",bottom:"top",left:"right"},ez=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=e_(eB,n),o=eW[i.placedSide];return(0,eA.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eA.jsx)(eD,{...r,ref:t,style:{...r.style,display:"block"}})})});function eV(e){return null!==e}ez.displayName=eB;var eX=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,s;let{placement:a,rects:l,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,h=c?0:e.arrowHeight,[f,p]=eY(a),m={start:"0%",center:"50%",end:"100%"}[p],y=(null!=(o=null==(r=u.arrow)?void 0:r.x)?o:0)+d/2,v=(null!=(s=null==(i=u.arrow)?void 0:i.y)?s:0)+h/2,g="",w="";return"bottom"===f?(g=c?m:"".concat(y,"px"),w="".concat(-h,"px")):"top"===f?(g=c?m:"".concat(y,"px"),w="".concat(l.floating.height+h,"px")):"right"===f?(g="".concat(-h,"px"),w=c?m:"".concat(v,"px")):"left"===f&&(g="".concat(l.floating.width+h,"px"),w=c?m:"".concat(v,"px")),{data:{x:g,y:w}}}});function eY(e){let[t,n="center"]=e.split("-");return[t,n]}n(34378);var e$=n(28905),eJ=n(99708),eZ=n(5845),e0=n(2564),[e1,e5]=(0,s.A)("Tooltip",[eq]),e2=eq(),e6="TooltipProvider",e8="tooltip.open",[e3,e9]=e1(e6),e7=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:i=300,disableHoverableContent:o=!1,children:s}=e,a=r.useRef(!0),l=r.useRef(!1),u=r.useRef(0);return r.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,eA.jsx)(e3,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,i)},[i]),isPointerInTransitRef:l,onPointerInTransitChange:r.useCallback(e=>{l.current=e},[]),disableHoverableContent:o,children:s})};e7.displayName=e6;var e4="Tooltip",[te,tt]=e1(e4),tn=e=>{let{__scopeTooltip:t,children:n,open:i,defaultOpen:o,onOpenChange:s,disableHoverableContent:a,delayDuration:u}=e,c=e9(e4,e.__scopeTooltip),d=e2(t),[h,f]=r.useState(null),p=(0,l.B)(),m=r.useRef(0),y=null!=a?a:c.disableHoverableContent,v=null!=u?u:c.delayDuration,g=r.useRef(!1),[w,b]=(0,eZ.i)({prop:i,defaultProp:null!=o&&o,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(e8))):c.onClose(),null==s||s(e)},caller:e4}),x=r.useMemo(()=>w?g.current?"delayed-open":"instant-open":"closed",[w]),C=r.useCallback(()=>{window.clearTimeout(m.current),m.current=0,g.current=!1,b(!0)},[b]),E=r.useCallback(()=>{window.clearTimeout(m.current),m.current=0,b(!1)},[b]),T=r.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{g.current=!0,b(!0),m.current=0},v)},[v,b]);return r.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,eA.jsx)(eI,{...d,children:(0,eA.jsx)(te,{scope:t,contentId:p,open:w,stateAttribute:x,trigger:h,onTriggerChange:f,onTriggerEnter:r.useCallback(()=>{c.isOpenDelayedRef.current?T():C()},[c.isOpenDelayedRef,T,C]),onTriggerLeave:r.useCallback(()=>{y?E():(window.clearTimeout(m.current),m.current=0)},[E,y]),onOpen:C,onClose:E,disableHoverableContent:y,children:n})})};tn.displayName=e4;var tr="TooltipTrigger",ti=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...s}=e,a=tt(tr,n),l=e9(tr,n),u=e2(n),c=r.useRef(null),d=(0,o.s)(t,c,a.onTriggerChange),h=r.useRef(!1),f=r.useRef(!1),p=r.useCallback(()=>h.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),(0,eA.jsx)(eQ,{asChild:!0,...u,children:(0,eA.jsx)(eS.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...s,ref:d,onPointerMove:(0,i.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(f.current||l.isPointerInTransitRef.current||(a.onTriggerEnter(),f.current=!0))}),onPointerLeave:(0,i.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),f.current=!1}),onPointerDown:(0,i.m)(e.onPointerDown,()=>{a.open&&a.onClose(),h.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:(0,i.m)(e.onFocus,()=>{h.current||a.onOpen()}),onBlur:(0,i.m)(e.onBlur,a.onClose),onClick:(0,i.m)(e.onClick,a.onClose)})})});ti.displayName=tr;var[to,ts]=e1("TooltipPortal",{forceMount:void 0}),ta="TooltipContent",tl=r.forwardRef((e,t)=>{let n=ts(ta,e.__scopeTooltip),{forceMount:r=n.forceMount,side:i="top",...o}=e,s=tt(ta,e.__scopeTooltip);return(0,eA.jsx)(e$.C,{present:r||s.open,children:s.disableHoverableContent?(0,eA.jsx)(tf,{side:i,...o,ref:t}):(0,eA.jsx)(tu,{side:i,...o,ref:t})})}),tu=r.forwardRef((e,t)=>{let n=tt(ta,e.__scopeTooltip),i=e9(ta,e.__scopeTooltip),s=r.useRef(null),a=(0,o.s)(t,s),[l,u]=r.useState(null),{trigger:c,onClose:d}=n,h=s.current,{onPointerInTransitChange:f}=i,p=r.useCallback(()=>{u(null),f(!1)},[f]),m=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},i=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,r,i,o)){case o:return"left";case i:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,i),...function(e){let{top:t,right:n,bottom:r,left:i}=e;return[{x:i,y:t},{x:n,y:t},{x:n,y:r},{x:i,y:r}]}(t.getBoundingClientRect())])),f(!0)},[f]);return r.useEffect(()=>()=>p(),[p]),r.useEffect(()=>{if(c&&h){let e=e=>m(e,h),t=e=>m(e,c);return c.addEventListener("pointerleave",e),h.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),h.removeEventListener("pointerleave",t)}}},[c,h,m,p]),r.useEffect(()=>{if(l){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==h?void 0:h.contains(t)),i=!function(e,t){let{x:n,y:r}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let s=t[e],a=t[o],l=s.x,u=s.y,c=a.x,d=a.y;u>r!=d>r&&n<(c-l)*(r-u)/(d-u)+l&&(i=!i)}return i}(n,l);r?p():i&&(p(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,h,l,d,p]),(0,eA.jsx)(tf,{...e,ref:a})}),[tc,td]=e1(e4,{isInside:!1}),th=(0,eJ.Dc)("TooltipContent"),tf=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:i,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:l,...u}=e,c=tt(ta,n),d=e2(n),{onClose:h}=c;return r.useEffect(()=>(document.addEventListener(e8,h),()=>document.removeEventListener(e8,h)),[h]),r.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(c.trigger))&&h()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,h]),(0,eA.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:h,children:(0,eA.jsxs)(eG,{"data-state":c.stateAttribute,...d,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,eA.jsx)(th,{children:i}),(0,eA.jsx)(tc,{scope:n,isInside:!0,children:(0,eA.jsx)(e0.bL,{id:c.contentId,role:"tooltip",children:o||i})})]})})});tl.displayName=ta;var tp="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,i=e2(n);return td(tp,n).isInside?null:(0,eA.jsx)(ez,{...i,...r,ref:t})}).displayName=tp;var tm=e7,ty=tn,tv=ti,tg=tl},72922:(e,t,n)=>{"use strict";n.d(t,{E:()=>M});var r="undefined"==typeof window||"Deno"in globalThis;function i(){}function o(e,t){return"function"==typeof e?e(t):e}function s(e,t){let{type:n="all",exact:r,fetchStatus:i,predicate:o,queryKey:s,stale:a}=e;if(s){if(r){if(t.queryHash!==l(s,t.options))return!1}else if(!c(t.queryKey,s))return!1}if("all"!==n){let e=t.isActive();if("active"===n&&!e||"inactive"===n&&e)return!1}return("boolean"!=typeof a||t.isStale()===a)&&(!i||i===t.state.fetchStatus)&&(!o||!!o(t))}function a(e,t){let{exact:n,status:r,predicate:i,mutationKey:o}=e;if(o){if(!t.options.mutationKey)return!1;if(n){if(u(t.options.mutationKey)!==u(o))return!1}else if(!c(t.options.mutationKey,o))return!1}return(!r||t.state.status===r)&&(!i||!!i(t))}function l(e,t){return(t?.queryKeyHashFn||u)(e)}function u(e){return JSON.stringify(e,(e,t)=>h(t)?Object.keys(t).sort().reduce((e,n)=>(e[n]=t[n],e),{}):t)}function c(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(n=>c(e[n],t[n]))}function d(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function h(e){if(!f(e))return!1;let t=e.constructor;if(void 0===t)return!0;let n=t.prototype;return!!f(n)&&!!n.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function f(e){return"[object Object]"===Object.prototype.toString.call(e)}function p(e,t,n=0){let r=[...e,t];return n&&r.length>n?r.slice(1):r}function m(e,t,n=0){let r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var y=Symbol();function v(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==y?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}var g=e=>setTimeout(e,0),w=function(){let e=[],t=0,n=e=>{e()},r=e=>{e()},i=g,o=r=>{t?e.push(r):i(()=>{n(r)})},s=()=>{let t=e;e=[],t.length&&i(()=>{r(()=>{t.forEach(e=>{n(e)})})})};return{batch:e=>{let n;t++;try{n=e()}finally{--t||s()}return n},batchCalls:e=>(...t)=>{o(()=>{e(...t)})},schedule:o,setNotifyFunction:e=>{n=e},setBatchNotifyFunction:e=>{r=e},setScheduler:e=>{i=e}}}(),b=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},x=new class extends b{#e;#t;#n;constructor(){super(),this.#n=e=>{if(!r&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},C=new class extends b{#r=!0;#t;#n;constructor(){super(),this.#n=e=>{if(!r&&window.addEventListener){let t=()=>e(!0),n=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",n)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#r!==e&&(this.#r=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#r}};function E(e){return Math.min(1e3*2**e,3e4)}function T(e){return(e??"online")!=="online"||C.isOnline()}var R=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function P(e){return e instanceof R}function O(e){let t,n=!1,i=0,o=!1,s=function(){let e,t,n=new Promise((n,r)=>{e=n,t=r});function r(e){Object.assign(n,e),delete n.resolve,delete n.reject}return n.status="pending",n.catch(()=>{}),n.resolve=t=>{r({status:"fulfilled",value:t}),e(t)},n.reject=e=>{r({status:"rejected",reason:e}),t(e)},n}(),a=()=>x.isFocused()&&("always"===e.networkMode||C.isOnline())&&e.canRun(),l=()=>T(e.networkMode)&&e.canRun(),u=n=>{o||(o=!0,e.onSuccess?.(n),t?.(),s.resolve(n))},c=n=>{o||(o=!0,e.onError?.(n),t?.(),s.reject(n))},d=()=>new Promise(n=>{t=e=>{(o||a())&&n(e)},e.onPause?.()}).then(()=>{t=void 0,o||e.onContinue?.()}),h=()=>{let t;if(o)return;let s=0===i?e.initialPromise:void 0;try{t=s??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(u).catch(t=>{if(o)return;let s=e.retry??3*!r,l=e.retryDelay??E,u="function"==typeof l?l(i,t):l,f=!0===s||"number"==typeof s&&i<s||"function"==typeof s&&s(i,t);if(n||!f)return void c(t);i++,e.onFail?.(i,t),new Promise(e=>{setTimeout(e,u)}).then(()=>a()?void 0:d()).then(()=>{n?c(t):h()})})};return{promise:s,cancel:t=>{o||(c(new R(t)),e.abort?.())},continue:()=>(t?.(),s),cancelRetry:()=>{n=!0},continueRetry:()=>{n=!1},canStart:l,start:()=>(l()?h():d().then(h),s)}}var S=class{#i;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#i=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(r?1/0:3e5))}clearGcTimeout(){this.#i&&(clearTimeout(this.#i),this.#i=void 0)}},A=class extends S{#o;#s;#a;#l;#u;#c;#d;constructor(e){super(),this.#d=!1,this.#c=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#l=e.client,this.#a=this.#l.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#o=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,n=void 0!==t,r=n?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#o,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#u?.promise}setOptions(e){this.options={...this.#c,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#a.remove(this)}setData(e,t){var n,r;let i=(n=this.state.data,"function"==typeof(r=this.options).structuralSharing?r.structuralSharing(n,e):!1!==r.structuralSharing?function e(t,n){if(t===n)return t;let r=d(t)&&d(n);if(r||h(t)&&h(n)){let i=r?t:Object.keys(t),o=i.length,s=r?n:Object.keys(n),a=s.length,l=r?[]:{},u=0;for(let o=0;o<a;o++){let a=r?o:s[o];(!r&&i.includes(a)||r)&&void 0===t[a]&&void 0===n[a]?(l[a]=void 0,u++):(l[a]=e(t[a],n[a]),l[a]===t[a]&&void 0!==t[a]&&u++)}return o===a&&u===o?t:l}return n}(n,e):e);return this.#h({data:i,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),i}setState(e,t){this.#h({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#u?.promise;return this.#u?.cancel(e),t?t.then(i).catch(i):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#o)}isActive(){return this.observers.some(e=>{var t;return!1!==(t=e.options.enabled,"function"==typeof t?t(this):t)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===y||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!Math.max(this.state.dataUpdatedAt+(e||0)-Date.now(),0)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#u?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#u?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#a.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#u&&(this.#d?this.#u.cancel({revert:!0}):this.#u.cancelRetry()),this.scheduleGc()),this.#a.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#h({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#u)return this.#u.continueRetry(),this.#u.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let n=new AbortController,r=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#d=!0,n.signal)})},i={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#l,state:this.state,fetchFn:()=>{let e=v(this.options,t),n={client:this.#l,queryKey:this.queryKey,meta:this.meta};return(r(n),this.#d=!1,this.options.persister)?this.options.persister(e,n,this):e(n)}};r(i),this.options.behavior?.onFetch(i,this),this.#s=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==i.fetchOptions?.meta)&&this.#h({type:"fetch",meta:i.fetchOptions?.meta});let o=e=>{P(e)&&e.silent||this.#h({type:"error",error:e}),P(e)||(this.#a.config.onError?.(e,this),this.#a.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#u=O({initialPromise:t?.initialPromise,fn:i.fetchFn,abort:n.abort.bind(n),onSuccess:e=>{if(void 0===e)return void o(Error(`${this.queryHash} data is undefined`));try{this.setData(e)}catch(e){o(e);return}this.#a.config.onSuccess?.(e,this),this.#a.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:o,onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:()=>{this.#h({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#u.start()}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":var n;return{...t,...(n=t.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:T(this.options.networkMode)?"fetching":"paused",...void 0===n&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=e.error;if(P(r)&&r.revert&&this.#s)return{...this.#s,fetchStatus:"idle"};return{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),w.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#a.notify({query:this,type:"updated",action:e})})}},D=class extends b{constructor(e={}){super(),this.config=e,this.#f=new Map}#f;build(e,t,n){let r=t.queryKey,i=t.queryHash??l(r,t),o=this.get(i);return o||(o=new A({client:e,queryKey:r,queryHash:i,options:e.defaultQueryOptions(t),state:n,defaultOptions:e.getQueryDefaults(r)}),this.add(o)),o}add(e){this.#f.has(e.queryHash)||(this.#f.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#f.get(e.queryHash);t&&(e.destroy(),t===e&&this.#f.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){w.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#f.get(e)}getAll(){return[...this.#f.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>s(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>s(e,t)):t}notify(e){w.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){w.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){w.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},F=class extends S{#p;#m;#u;constructor(e){super(),this.mutationId=e.mutationId,this.#m=e.mutationCache,this.#p=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#p.includes(e)||(this.#p.push(e),this.clearGcTimeout(),this.#m.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#p=this.#p.filter(t=>t!==e),this.scheduleGc(),this.#m.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#m.remove(this))}continue(){return this.#u?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#h({type:"continue"})};this.#u=O({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#m.canRun(this)});let n="pending"===this.state.status,r=!this.#u.canStart();try{if(n)t();else{this.#h({type:"pending",variables:e,isPaused:r}),await this.#m.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#h({type:"pending",context:t,variables:e,isPaused:r})}let i=await this.#u.start();return await this.#m.config.onSuccess?.(i,e,this.state.context,this),await this.options.onSuccess?.(i,e,this.state.context),await this.#m.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,e,this.state.context),this.#h({type:"success",data:i}),i}catch(t){try{throw await this.#m.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#m.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#h({type:"error",error:t})}}finally{this.#m.runNext(this)}}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),w.batch(()=>{this.#p.forEach(t=>{t.onMutationUpdate(e)}),this.#m.notify({mutation:this,type:"updated",action:e})})}},L=class extends b{constructor(e={}){super(),this.config=e,this.#y=new Set,this.#v=new Map,this.#g=0}#y;#v;#g;build(e,t,n){let r=new F({mutationCache:this,mutationId:++this.#g,options:e.defaultMutationOptions(t),state:n});return this.add(r),r}add(e){this.#y.add(e);let t=j(e);if("string"==typeof t){let n=this.#v.get(t);n?n.push(e):this.#v.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#y.delete(e)){let t=j(e);if("string"==typeof t){let n=this.#v.get(t);if(n)if(n.length>1){let t=n.indexOf(e);-1!==t&&n.splice(t,1)}else n[0]===e&&this.#v.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=j(e);if("string"!=typeof t)return!0;{let n=this.#v.get(t),r=n?.find(e=>"pending"===e.state.status);return!r||r===e}}runNext(e){let t=j(e);if("string"!=typeof t)return Promise.resolve();{let n=this.#v.get(t)?.find(t=>t!==e&&t.state.isPaused);return n?.continue()??Promise.resolve()}}clear(){w.batch(()=>{this.#y.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#y.clear(),this.#v.clear()})}getAll(){return Array.from(this.#y)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>a(t,e))}findAll(e={}){return this.getAll().filter(t=>a(e,t))}notify(e){w.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return w.batch(()=>Promise.all(e.map(e=>e.continue().catch(i))))}};function j(e){return e.options.scope?.id}function k(e){return{onFetch:(t,n)=>{let r=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,o=t.state.data?.pages||[],s=t.state.data?.pageParams||[],a={pages:[],pageParams:[]},l=0,u=async()=>{let n=!1,u=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?n=!0:t.signal.addEventListener("abort",()=>{n=!0}),t.signal)})},c=v(t.options,t.fetchOptions),d=async(e,r,i)=>{if(n)return Promise.reject();if(null==r&&e.pages.length)return Promise.resolve(e);let o={client:t.client,queryKey:t.queryKey,pageParam:r,direction:i?"backward":"forward",meta:t.options.meta};u(o);let s=await c(o),{maxPages:a}=t.options,l=i?m:p;return{pages:l(e.pages,s,a),pageParams:l(e.pageParams,r,a)}};if(i&&o.length){let e="backward"===i,t={pages:o,pageParams:s},n=(e?function(e,{pages:t,pageParams:n}){return t.length>0?e.getPreviousPageParam?.(t[0],t,n[0],n):void 0}:q)(r,t);a=await d(t,n,e)}else{let t=e??o.length;do{let e=0===l?s[0]??r.initialPageParam:q(r,a);if(l>0&&null==e)break;a=await d(a,e),l++}while(l<t)}return a};t.options.persister?t.fetchFn=()=>t.options.persister?.(u,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n):t.fetchFn=u}}}function q(e,{pages:t,pageParams:n}){let r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}var M=class{#w;#m;#c;#b;#x;#C;#E;#T;constructor(e={}){this.#w=e.queryCache||new D,this.#m=e.mutationCache||new L,this.#c=e.defaultOptions||{},this.#b=new Map,this.#x=new Map,this.#C=0}mount(){this.#C++,1===this.#C&&(this.#E=x.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#w.onFocus())}),this.#T=C.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#w.onOnline())}))}unmount(){this.#C--,0===this.#C&&(this.#E?.(),this.#E=void 0,this.#T?.(),this.#T=void 0)}isFetching(e){return this.#w.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#m.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#w.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),n=this.#w.build(this,t),r=n.state.data;return void 0===r?this.fetchQuery(e):(e.revalidateIfStale&&n.isStaleByTime(o(t.staleTime,n))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return this.#w.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,n){let r=this.defaultQueryOptions({queryKey:e}),i=this.#w.get(r.queryHash),o=i?.state.data,s="function"==typeof t?t(o):t;if(void 0!==s)return this.#w.build(this,r).setData(s,{...n,manual:!0})}setQueriesData(e,t,n){return w.batch(()=>this.#w.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,n)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#w.get(t.queryHash)?.state}removeQueries(e){let t=this.#w;w.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let n=this.#w;return w.batch(()=>(n.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let n={revert:!0,...t};return Promise.all(w.batch(()=>this.#w.findAll(e).map(e=>e.cancel(n)))).then(i).catch(i)}invalidateQueries(e,t={}){return w.batch(()=>(this.#w.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let n={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(w.batch(()=>this.#w.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,n);return n.throwOnError||(t=t.catch(i)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(i)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let n=this.#w.build(this,t);return n.isStaleByTime(o(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(i).catch(i)}fetchInfiniteQuery(e){return e.behavior=k(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(i).catch(i)}ensureInfiniteQueryData(e){return e.behavior=k(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return C.isOnline()?this.#m.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#w}getMutationCache(){return this.#m}getDefaultOptions(){return this.#c}setDefaultOptions(e){this.#c=e}setQueryDefaults(e,t){this.#b.set(u(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#b.values()],n={};return t.forEach(t=>{c(e,t.queryKey)&&Object.assign(n,t.defaultOptions)}),n}setMutationDefaults(e,t){this.#x.set(u(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#x.values()],n={};return t.forEach(t=>{c(e,t.mutationKey)&&Object.assign(n,t.defaultOptions)}),n}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#c.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=l(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===y&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#c.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#w.clear(),this.#m.clear()}}},74466:(e,t,n)=>{"use strict";n.d(t,{F:()=>s});var r=n(52596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.$,s=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:s,defaultVariants:a}=t,l=Object.keys(s).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let o=i(t)||i(r);return s[e][o]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,l,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...u}[t]):({...a,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},78346:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}}}]);