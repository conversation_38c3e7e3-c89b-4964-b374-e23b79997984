{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/tooltip.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,sKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/theme/ThemeProvider.tsx"], "sourcesContent": ["\n'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\ntype Theme = 'dark' | 'light';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  // Default to dark theme for \"Green Hacker\" theme\n  const [theme, setTheme] = useState<Theme>('dark');\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    // Check if we're on the client side\n    if (typeof window === 'undefined') return;\n\n    const savedTheme = localStorage.getItem('theme') as Theme | null;\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n\n    if (savedTheme) {\n      setTheme(savedTheme);\n      document.documentElement.classList.toggle('dark', savedTheme === 'dark');\n    } else if (prefersDark) {\n      setTheme('dark');\n      document.documentElement.classList.add('dark');\n    }\n  }, []);\n\n  // Update HTML class and localStorage when theme changes\n  const toggleTheme = () => {\n    const newTheme = theme === 'dark' ? 'light' : 'dark';\n    setTheme(newTheme);\n\n    // Check if we're on the client side\n    if (typeof window !== 'undefined') {\n      document.documentElement.classList.toggle('dark', newTheme === 'dark');\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport const useTheme = (): ThemeContextType => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAGA;;;AAFA;;AAWA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,gBAAyD,CAAC,EAAE,QAAQ,EAAE;;IACjF,iDAAiD;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAE1C,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,oCAAoC;YACpC,uCAAmC;;YAAM;YAEzC,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;YAE7E,IAAI,YAAY;gBACd,SAAS;gBACT,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,eAAe;YACnE,OAAO,IAAI,aAAa;gBACtB,SAAS;gBACT,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACzC;QACF;kCAAG,EAAE;IAEL,wDAAwD;IACxD,MAAM,cAAc;QAClB,MAAM,WAAW,UAAU,SAAS,UAAU;QAC9C,SAAS;QAET,oCAAoC;QACpC,wCAAmC;YACjC,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,aAAa;YAC/D,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAY;kBAChD;;;;;;AAGP;GAtCa;KAAA;AAwCN,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/hooks/use-toast.ts"], "sourcesContent": ["import * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAAA;;;AAOA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;8BAAE;YACd,UAAU,IAAI,CAAC;YACf;sCAAO;oBACL,MAAM,QAAQ,UAAU,OAAO,CAAC;oBAChC,IAAI,QAAQ,CAAC,GAAG;wBACd,UAAU,MAAM,CAAC,OAAO;oBAC1B;gBACF;;QACF;6BAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF;GAlBS", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/toast.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AAEA;;;;;;;AAEA,MAAM,gBAAgB,oKAAA,CAAA,WAAwB;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG,oKAAA,CAAA,WAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAI3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,qBACE,6LAAC,oKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;;AACA,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,SAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sgBACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAA,CAAA,SAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,oKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/toaster.tsx"], "sourcesContent": ["import { useToast } from \"@/hooks/use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AASO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAE1B,qBACE,6LAAC,oIAAA,CAAA,gBAAa;;YACX,OAAO,GAAG,CAAC,SAAU,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO;gBAChE,qBACE,6LAAC,oIAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACvB,6LAAC;4BAAI,WAAU;;gCACZ,uBAAS,6LAAC,oIAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,6LAAC,oIAAA,CAAA,aAAU;;;;;;mBARD;;;;;YAWhB;0BACA,6LAAC,oIAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB;GAtBgB;;QACK,+HAAA,CAAA,WAAQ;;;KADb", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/sonner.tsx"], "sourcesContent": ["import { useTheme } from \"next-themes\"\nimport { Toaster as Sonner, toast } from \"sonner\"\n\ntype ToasterProps = React.ComponentProps<typeof Sonner>\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      toastOptions={{\n        classNames: {\n          toast:\n            \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n          description: \"group-[.toast]:text-muted-foreground\",\n          actionButton:\n            \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n          cancelButton:\n            \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\",\n        },\n      }}\n      {...props}\n    />\n  )\n}\n\nexport { Toaster, toast }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAIA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,cAAc;YACZ,YAAY;gBACV,OACE;gBACF,aAAa;gBACb,cACE;gBACF,cACE;YACJ;QACF;QACC,GAAG,KAAK;;;;;;AAGf;GArBM;;QACyB,mJAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/LoadingScreen.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\n\nconst LoadingScreen = () => {\n  const [loadingProgress, setLoadingProgress] = useState(0);\n  const [loadingText, setLoadingText] = useState('Initializing system...');\n  const [showCursor, setShowCursor] = useState(true);\n  const [commandComplete, setCommandComplete] = useState(false);\n\n  const loadingSteps = [\n    { text: 'Initializing system...', duration: 1200 },\n    { text: 'Establishing secure connection...', duration: 1000 },\n    { text: 'Authenticating credentials...', duration: 800 },\n    { text: 'Bypassing security protocols...', duration: 1500 },\n    { text: 'Loading developer assets...', duration: 1000 },\n    { text: 'Compiling portfolio data...', duration: 1200 },\n    { text: 'Optimizing display modules...', duration: 900 },\n    { text: 'Rendering interface...', duration: 1300 },\n    { text: 'System ready. Welcome to GreenHacker portfolio v2.0', duration: 1000 }\n  ];\n\n  useEffect(() => {\n    // Cursor blinking effect\n    const cursorInterval = setInterval(() => {\n      setShowCursor((prev) => !prev);\n    }, 500);\n\n    // Loading progress simulation\n    let step = 0;\n    const progressInterval = setTimeout(function runStep() {\n      if (step < loadingSteps.length) {\n        const { text, duration } = loadingSteps[step];\n        setLoadingText(text);\n        setLoadingProgress(Math.min(100, Math.round((step + 1) / loadingSteps.length * 100)));\n\n        step++;\n        setTimeout(runStep, duration);\n      } else {\n        setCommandComplete(true);\n        setTimeout(() => {\n          if (typeof window !== 'undefined') {\n            const event = new Event('loadingComplete');\n            window.dispatchEvent(event);\n          }\n        }, 1000);\n      }\n    }, 500);\n\n    return () => {\n      clearInterval(cursorInterval);\n      clearTimeout(progressInterval);\n    };\n  }, []);\n\n  const terminalVariants = {\n    hidden: { opacity: 0 },\n    visible: { opacity: 1, transition: { duration: 0.8 } }\n  };\n\n  return (\n    <motion.div\n      className=\"fixed inset-0 bg-black flex items-center justify-center z-50\"\n      initial={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      transition={{ duration: 0.6, ease: \"easeInOut\" }}\n    >\n      <motion.div\n        className=\"w-full max-w-3xl bg-black border border-neon-green p-6 rounded-md shadow-neon-green terminal-window\"\n        variants={terminalVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"terminal-header flex items-center justify-between mb-4\">\n          <div className=\"text-neon-green font-mono text-sm\">~/green-hacker/portfolio</div>\n          <div className=\"flex space-x-2\">\n            <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n            <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n            <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n          </div>\n        </div>\n\n        <div className=\"terminal-content space-y-2 font-mono text-sm overflow-hidden\">\n          <div className=\"line\">\n            <span className=\"text-neon-blue\">$ </span>\n            <span className=\"text-white\">load portfolio --env=production --secure</span>\n          </div>\n\n          <motion.div\n            className=\"line text-neon-green\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.3 }}\n          >\n            {loadingText}{showCursor ? '▋' : ' '}\n          </motion.div>\n\n          <motion.div\n            className=\"line\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.5 }}\n          >\n            <div className=\"text-github-text\">Progress: {loadingProgress}%</div>\n            <div className=\"w-full bg-github-dark rounded-full h-2 mt-1\">\n              <motion.div\n                className=\"h-2 rounded-full bg-neon-green\"\n                initial={{ width: 0 }}\n                animate={{ width: `${loadingProgress}%` }}\n                transition={{ duration: 0.5 }}\n              ></motion.div>\n            </div>\n          </motion.div>\n\n          {commandComplete && (\n            <>\n              <motion.div\n                className=\"line\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.2 }}\n              >\n                <span className=\"text-neon-blue\">$ </span>\n                <span className=\"text-white\">launch --mode=interactive</span>\n              </motion.div>\n              <motion.div\n                className=\"line text-neon-purple\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.4 }}\n              >\n                Launching portfolio interface...\n              </motion.div>\n            </>\n          )}\n        </div>\n\n        <div className=\"ascii-art mt-8 text-neon-green font-mono text-xs whitespace-pre\">\n{` ██████╗ ██████╗ ███████╗███████╗███╗   ██╗██╗  ██╗ █████╗  ██████╗██╗  ██╗███████╗██████╗\n██╔════╝ ██╔══██╗██╔════╝██╔════╝████╗  ██║██║  ██║██╔══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗\n██║  ███╗██████╔╝█████╗  █████╗  ██╔██╗ ██║███████║███████║██║     █████╔╝ █████╗  ██████╔╝\n██║   ██║██╔══██╗██╔══╝  ██╔══╝  ██║╚██╗██║██╔══██║██╔══██║██║     ██╔═██╗ ██╔══╝  ██╔══██╗\n╚██████╔╝██║  ██║███████╗███████╗██║ ╚████║██║  ██║██║  ██║╚██████╗██║  ██╗███████╗██║  ██║\n ╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝`}\n        </div>\n\n        {commandComplete && (\n          <motion.div\n            className=\"mt-6 text-center\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.8 }}\n          >\n            <span className=\"text-github-text text-sm\">Press </span>\n            <span className=\"px-2 py-1 bg-github-light rounded text-white text-sm mx-1\">ENTER</span>\n            <span className=\"text-github-text text-sm\"> to continue</span>\n          </motion.div>\n        )}\n      </motion.div>\n\n      <style dangerouslySetInnerHTML={{__html: `\n        .terminal-window {\n          box-shadow: 0 0 10px rgba(63, 185, 80, 0.3), 0 0 20px rgba(63, 185, 80, 0.2);\n        }\n\n        @keyframes scan {\n          from { top: 0; }\n          to { top: 100%; }\n        }\n\n        .terminal-window::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          height: 3px;\n          background-color: rgba(63, 185, 80, 0.5);\n          animation: scan 3s linear infinite;\n        }\n      `}} />\n    </motion.div>\n  );\n};\n\nexport default LoadingScreen;\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAHA;;;AAKA,MAAM,gBAAgB;;IACpB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,eAAe;QACnB;YAAE,MAAM;YAA0B,UAAU;QAAK;QACjD;YAAE,MAAM;YAAqC,UAAU;QAAK;QAC5D;YAAE,MAAM;YAAiC,UAAU;QAAI;QACvD;YAAE,MAAM;YAAmC,UAAU;QAAK;QAC1D;YAAE,MAAM;YAA+B,UAAU;QAAK;QACtD;YAAE,MAAM;YAA+B,UAAU;QAAK;QACtD;YAAE,MAAM;YAAiC,UAAU;QAAI;QACvD;YAAE,MAAM;YAA0B,UAAU;QAAK;QACjD;YAAE,MAAM;YAAuD,UAAU;QAAK;KAC/E;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,yBAAyB;YACzB,MAAM,iBAAiB;0DAAY;oBACjC;kEAAc,CAAC,OAAS,CAAC;;gBAC3B;yDAAG;YAEH,8BAA8B;YAC9B,IAAI,OAAO;YACX,MAAM,mBAAmB,WAAW,SAAS;gBAC3C,IAAI,OAAO,aAAa,MAAM,EAAE;oBAC9B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,YAAY,CAAC,KAAK;oBAC7C,eAAe;oBACf,mBAAmB,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM,GAAG;oBAE/E;oBACA,WAAW,SAAS;gBACtB,OAAO;oBACL,mBAAmB;oBACnB;4EAAW;4BACT,wCAAmC;gCACjC,MAAM,QAAQ,IAAI,MAAM;gCACxB,OAAO,aAAa,CAAC;4BACvB;wBACF;2EAAG;gBACL;YACF,GAAG;YAEH;2CAAO;oBACL,cAAc;oBACd,aAAa;gBACf;;QACF;kCAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YAAE,SAAS;YAAG,YAAY;gBAAE,UAAU;YAAI;QAAE;IACvD;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAY;;0BAE/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAQ;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAoC;;;;;;0CACnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;kCAInB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAiB;;;;;;kDACjC,6LAAC;wCAAK,WAAU;kDAAa;;;;;;;;;;;;0CAG/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAI;;oCAExB;oCAAa,aAAa,MAAM;;;;;;;0CAGnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,6LAAC;wCAAI,WAAU;;4CAAmB;4CAAW;4CAAgB;;;;;;;kDAC7D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC;4CAAC;4CACxC,YAAY;gDAAE,UAAU;4CAAI;;;;;;;;;;;;;;;;;4BAKjC,iCACC;;kDACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,6LAAC;gDAAK,WAAU;0DAAa;;;;;;;;;;;;kDAE/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,OAAO;wCAAI;kDAC1B;;;;;;;;;;;;;;kCAOP,6LAAC;wBAAI,WAAU;kCACtB,CAAC;;;;;2FAKyF,CAAC;;;;;;oBAGnF,iCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,6LAAC;gCAAK,WAAU;0CAA2B;;;;;;0CAC3C,6LAAC;gCAAK,WAAU;0CAA4D;;;;;;0CAC5E,6LAAC;gCAAK,WAAU;0CAA2B;;;;;;;;;;;;;;;;;;0BAKjD,6LAAC;gBAAM,yBAAyB;oBAAC,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;MAoB1C,CAAC;gBAAA;;;;;;;;;;;;AAGP;GAnLM;KAAA;uCAqLS", "debugId": null}}, {"offset": {"line": 1031, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/effects/AnimatedCursor.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\n\nconst AnimatedCursor = () => {\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n  const [clicked, setClicked] = useState(false);\n  const [hovered, setHovered] = useState(false);\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      setPosition({ x: e.clientX, y: e.clientY });\n    };\n\n    const handleMouseDown = () => {\n      setClicked(true);\n      setTimeout(() => setClicked(false), 300);\n    };\n\n    const handleMouseEnter = () => {\n      document.body.style.cursor = 'none';\n    };\n\n    const handleMouseLeave = () => {\n      document.body.style.cursor = 'auto';\n    };\n\n    // Add hover detection for interactive elements\n    const handleMouseOver = (e: MouseEvent) => {\n      const target = e.target as HTMLElement;\n      const isInteractive =\n        target.tagName.toLowerCase() === 'button' ||\n        target.tagName.toLowerCase() === 'a' ||\n        target.closest('button') ||\n        target.closest('a');\n\n      setHovered(!!isInteractive);\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mousedown', handleMouseDown);\n    document.addEventListener('mouseenter', handleMouseEnter);\n    document.addEventListener('mouseleave', handleMouseLeave);\n    document.addEventListener('mouseover', handleMouseOver);\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mousedown', handleMouseDown);\n      document.removeEventListener('mouseenter', handleMouseEnter);\n      document.removeEventListener('mouseleave', handleMouseLeave);\n      document.removeEventListener('mouseover', handleMouseOver);\n      document.body.style.cursor = 'auto';\n    };\n  }, []);\n\n  return (\n    <>\n      {/* Main cursor */}\n      <motion.div\n        className=\"fixed top-0 left-0 w-8 h-8 rounded-full border border-neon-green z-[9999] pointer-events-none\"\n        animate={{\n          x: position.x - 16,\n          y: position.y - 16,\n          scale: clicked ? 0.8 : hovered ? 1.5 : 1,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 300,\n          damping: 20,\n          mass: 0.5\n        }}\n      />\n\n      {/* Cursor dot */}\n      <motion.div\n        className=\"fixed top-0 left-0 w-2 h-2 bg-neon-green rounded-full z-[10000] pointer-events-none\"\n        animate={{\n          x: position.x - 4,\n          y: position.y - 4,\n          opacity: clicked ? 0.5 : 1,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 400,\n          damping: 15,\n        }}\n      />\n    </>\n  );\n};\n\nexport default AnimatedCursor;\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAHA;;;AAKA,MAAM,iBAAiB;;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;4DAAkB,CAAC;oBACvB,YAAY;wBAAE,GAAG,EAAE,OAAO;wBAAE,GAAG,EAAE,OAAO;oBAAC;gBAC3C;;YAEA,MAAM;4DAAkB;oBACtB,WAAW;oBACX;oEAAW,IAAM,WAAW;mEAAQ;gBACtC;;YAEA,MAAM;6DAAmB;oBACvB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC/B;;YAEA,MAAM;6DAAmB;oBACvB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC/B;;YAEA,+CAA+C;YAC/C,MAAM;4DAAkB,CAAC;oBACvB,MAAM,SAAS,EAAE,MAAM;oBACvB,MAAM,gBACJ,OAAO,OAAO,CAAC,WAAW,OAAO,YACjC,OAAO,OAAO,CAAC,WAAW,OAAO,OACjC,OAAO,OAAO,CAAC,aACf,OAAO,OAAO,CAAC;oBAEjB,WAAW,CAAC,CAAC;gBACf;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,cAAc;YACxC,SAAS,gBAAgB,CAAC,cAAc;YACxC,SAAS,gBAAgB,CAAC,aAAa;YAEvC;4CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,mBAAmB,CAAC,cAAc;oBAC3C,SAAS,mBAAmB,CAAC,cAAc;oBAC3C,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC/B;;QACF;mCAAG,EAAE;IAEL,qBACE;;0BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,SAAS,CAAC,GAAG;oBAChB,GAAG,SAAS,CAAC,GAAG;oBAChB,OAAO,UAAU,MAAM,UAAU,MAAM;gBACzC;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;oBACT,MAAM;gBACR;;;;;;0BAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,SAAS,CAAC,GAAG;oBAChB,GAAG,SAAS,CAAC,GAAG;oBAChB,SAAS,UAAU,MAAM;gBAC3B;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;;;;;;;;AAIR;GArFM;KAAA;uCAuFS", "debugId": null}}, {"offset": {"line": 1157, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/effects/ReactiveBackground.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { motion, useMotionValue, useSpring } from 'framer-motion';\n\nconst ReactiveBackground = () => {\n  const mouseX = useMotionValue(0);\n  const mouseY = useMotionValue(0);\n\n  const springConfig = { stiffness: 50, damping: 50 };\n  const springX = useSpring(mouseX, springConfig);\n  const springY = useSpring(mouseY, springConfig);\n\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      // Get container dimensions\n      if (!containerRef.current) return;\n      const rect = containerRef.current.getBoundingClientRect();\n\n      // Calculate mouse position relative to container center (in -1 to 1 range)\n      const centerX = rect.left + rect.width / 2;\n      const centerY = rect.top + rect.height / 2;\n\n      const relativeX = (e.clientX - centerX) / (rect.width / 2);\n      const relativeY = (e.clientY - centerY) / (rect.height / 2);\n\n      mouseX.set(relativeX * 10); // Adjust sensitivity\n      mouseY.set(relativeY * 10);\n    };\n\n    window.addEventListener('mousemove', handleMouseMove);\n\n    return () => {\n      window.removeEventListener('mousemove', handleMouseMove);\n    };\n  }, [mouseX, mouseY]);\n\n  return (\n    <div\n      ref={containerRef}\n      className=\"fixed inset-0 pointer-events-none z-0 overflow-hidden\"\n    >\n      <motion.div\n        className=\"absolute inset-0 opacity-20\"\n        style={{\n          translateX: springX,\n          translateY: springY,\n        }}\n      >\n        <div className=\"absolute top-0 -left-4 w-[50vw] h-[50vw] bg-neon-purple rounded-full mix-blend-screen filter blur-[100px] opacity-70\"></div>\n        <div className=\"absolute top-[30%] -right-[10%] w-[40vw] h-[40vw] bg-neon-green rounded-full mix-blend-screen filter blur-[100px] opacity-70\"></div>\n        <div className=\"absolute -bottom-[20%] left-[20%] w-[60vw] h-[60vw] bg-neon-blue rounded-full mix-blend-screen filter blur-[100px] opacity-40\"></div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default ReactiveBackground;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;;;AAHA;;;AAKA,MAAM,qBAAqB;;IACzB,MAAM,SAAS,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IAC9B,MAAM,SAAS,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IAE9B,MAAM,eAAe;QAAE,WAAW;QAAI,SAAS;IAAG;IAClD,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;IAClC,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;IAElC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;gEAAkB,CAAC;oBACvB,2BAA2B;oBAC3B,IAAI,CAAC,aAAa,OAAO,EAAE;oBAC3B,MAAM,OAAO,aAAa,OAAO,CAAC,qBAAqB;oBAEvD,2EAA2E;oBAC3E,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;oBACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;oBAEzC,MAAM,YAAY,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC;oBACzD,MAAM,YAAY,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,CAAC;oBAE1D,OAAO,GAAG,CAAC,YAAY,KAAK,qBAAqB;oBACjD,OAAO,GAAG,CAAC,YAAY;gBACzB;;YAEA,OAAO,gBAAgB,CAAC,aAAa;YAErC;gDAAO;oBACL,OAAO,mBAAmB,CAAC,aAAa;gBAC1C;;QACF;uCAAG;QAAC;QAAQ;KAAO;IAEnB,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;kBAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,OAAO;gBACL,YAAY;gBACZ,YAAY;YACd;;8BAEA,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;GApDM;;QACW,qLAAA,CAAA,iBAAc;QACd,qLAAA,CAAA,iBAAc;QAGb,4KAAA,CAAA,YAAS;QACT,4KAAA,CAAA,YAAS;;;KANrB;uCAsDS", "debugId": null}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/Chatbot.tsx"], "sourcesContent": ["\n'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Terminal, Send, X, Maximize2, Minimize2 } from 'lucide-react';\n\n// Chatbot commands and responses\nconst COMMANDS = {\n  help: [\n    '💻 Available Commands:',\n    '- help: Display this help message',\n    '- about: Learn about Green Hacker',\n    '- skills: View technical skills',\n    '- projects: Show recent projects',\n    '- contact: Get contact information',\n    '- clear: Clear the terminal',\n    '- exit: Close the chatbot',\n    '',\n    'You can also just chat naturally!'\n  ],\n  about: [\n    'Hey there! 👋 I\\'m <PERSON>, a full-stack developer and ML enthusiast.',\n    'When I\\'m not coding, I\\'m probably hiking, gaming, or learning something new.',\n    'I specialize in creating interactive web experiences and AI-powered applications.'\n  ],\n  skills: [\n    '🚀 Technical Skills:',\n    '- Frontend: React, TypeScript, Tailwind CSS, Framer Motion',\n    '- Backend: Node.js, Express, FastAPI, GraphQL',\n    '- ML/AI: <PERSON>y<PERSON><PERSON><PERSON>, TensorFlow, Computer Vision',\n    '- DevOps: Docker, AWS, CI/CD, Kubernetes',\n    '- Other: Three.js, React Three Fiber, WebGL'\n  ],\n  projects: [\n    '📁 Recent Projects:',\n    '1. AI Photo Platform - Face recognition for intelligent photo organization',\n    '2. Portfolio Website - You\\'re looking at it right now!',\n    '3. ML Research Tool - Natural language processing for scientific papers',\n    '4. Real-time Collaboration App - WebRTC and WebSockets for seamless teamwork',\n    '',\n    'Type \"project [number]\" for more details!'\n  ],\n  'project 1': [\n    '📷 AI Photo Platform',\n    'A machine learning application that uses facial recognition to organize and tag photos.',\n    'Tech stack: React, TypeScript, PyTorch, AWS S3, Tailwind CSS',\n    'Features: Face recognition, automatic tagging, search by person, cloud storage'\n  ],\n  'project 2': [\n    '🌐 Portfolio Website',\n    'An interactive portfolio showcasing my projects and skills with 3D elements.',\n    'Tech stack: React, Three.js, Framer Motion, Tailwind CSS',\n    'Features: 3D visualization, interactive components, responsive design'\n  ],\n  'project 3': [\n    '📚 ML Research Tool',\n    'An AI-powered tool that helps researchers find relevant papers and extract insights.',\n    'Tech stack: Python, TensorFlow, FastAPI, React',\n    'Features: Paper recommendation, text summarization, citation network analysis'\n  ],\n  'project 4': [\n    '👥 Real-time Collaboration App',\n    'A platform for teams to collaborate with document sharing and real-time editing.',\n    'Tech stack: React, Node.js, Socket.io, WebRTC, MongoDB',\n    'Features: Live document editing, video chat, project management tools'\n  ],\n  contact: [\n    '📫 Contact Information:',\n    'Email: <EMAIL>',\n    'GitHub: github.com/greenhacker',\n    'LinkedIn: linkedin.com/in/greenhacker',\n    'Twitter: @greenhacker'\n  ],\n  clear: [''],\n  exit: ['👋 Goodbye! You can open me again by clicking the terminal icon.']\n};\n\ninterface Message {\n  type: 'user' | 'bot';\n  content: string[];\n}\n\nconst Chatbot = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      type: 'bot',\n      content: [\n        '👋 Hi there! I\\'m GREENHACKER\\'s AI assistant.',\n        'I can tell you about GREENHACKER, their skills, projects, or how to get in touch.',\n        'Type \"help\" to see what I can do!'\n      ]\n    }\n  ]);\n  const [input, setInput] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // Scroll to bottom of chat when new messages are added\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [messages]);\n\n  // Focus input when chat opens\n  useEffect(() => {\n    if (isOpen) {\n      inputRef.current?.focus();\n    }\n  }, [isOpen]);\n\n  const toggleChat = () => {\n    setIsOpen(!isOpen);\n  };\n\n  const toggleExpand = () => {\n    setIsExpanded(!isExpanded);\n  };\n\n  const processCommand = (command: string) => {\n    const lowercaseCommand = command.toLowerCase().trim();\n\n    if (lowercaseCommand === 'exit') {\n      setMessages([...messages, { type: 'user', content: [command] }, { type: 'bot', content: COMMANDS.exit }]);\n      setTimeout(() => setIsOpen(false), 1000);\n      return;\n    }\n\n    if (lowercaseCommand === 'clear') {\n      setMessages([]);\n      return;\n    }\n\n    if (COMMANDS[lowercaseCommand as keyof typeof COMMANDS]) {\n      setMessages([...messages, { type: 'user', content: [command] }, { type: 'bot', content: COMMANDS[lowercaseCommand as keyof typeof COMMANDS] }]);\n      return;\n    }\n\n    // Simulate an AI-generated response for unknown commands\n    setMessages([...messages, { type: 'user', content: [command] }]);\n    setIsTyping(true);\n\n    // Simulate typing delay\n    setTimeout(() => {\n      const aiResponse = generateAIResponse(command);\n      setMessages(prev => [...prev, { type: 'bot', content: aiResponse }]);\n      setIsTyping(false);\n    }, 1000 + Math.random() * 1000);\n  };\n\n  const generateAIResponse = (input: string) => {\n    // This is a simple simulation - in a real app, you'd call an API like Gemini\n    const lowercaseInput = input.toLowerCase();\n\n    if (lowercaseInput.includes('hi') || lowercaseInput.includes('hello') || lowercaseInput.includes('hey')) {\n      return ['Hello! How can I help you today? 😊', 'Type \"help\" to see what I can do.'];\n    } else if (lowercaseInput.includes('thanks') || lowercaseInput.includes('thank you')) {\n      return ['You\\'re welcome! Anything else you\\'d like to know?'];\n    } else if (lowercaseInput.includes('experience') || lowercaseInput.includes('work')) {\n      return ['GREENHACKER has over 5 years of experience in full-stack development and machine learning projects.', 'They\\'ve worked with startups and enterprise companies on various AI-powered applications.'];\n    } else if (lowercaseInput.includes('education')) {\n      return ['GREENHACKER has a Master\\'s degree in Computer Science with a specialization in Artificial Intelligence.', 'They\\'re also continually learning through courses and self-study.'];\n    } else if (lowercaseInput.includes('name')) {\n      return ['My name is GreenBot! I\\'m GREENHACKER\\'s AI assistant.'];\n    } else {\n      return ['I\\'m not sure I understand that query.', 'Type \"help\" to see what commands are available.'];\n    }\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (input.trim()) {\n      processCommand(input);\n      setInput('');\n    }\n  };\n\n  return (\n    <>\n      {/* Chatbot Toggle Button */}\n      <motion.button\n        className=\"fixed bottom-8 right-8 bg-neon-green text-black h-12 w-12 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform z-50\"\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n        onClick={toggleChat}\n      >\n        <Terminal size={20} />\n      </motion.button>\n\n      {/* Chatbot Interface */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            className={`fixed ${isExpanded ? 'inset-4 md:inset-10' : 'bottom-24 right-8 w-[350px] md:w-[400px] h-[500px]'} bg-black border border-neon-green/50 rounded-lg shadow-lg overflow-hidden z-50 flex flex-col`}\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: 50 }}\n            transition={{ duration: 0.3 }}\n          >\n            {/* Chatbot Header */}\n            <div className=\"flex items-center justify-between p-3 border-b border-neon-green/30 bg-black\">\n              <div className=\"flex items-center\">\n                <Terminal className=\"text-neon-green mr-2\" size={18} />\n                <h3 className=\"text-neon-green font-mono text-sm\">GREENHACKER Terminal</h3>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <button\n                  className=\"text-neon-green hover:text-white transition-colors focus:outline-none\"\n                  onClick={toggleExpand}\n                >\n                  {isExpanded ? <Minimize2 size={16} /> : <Maximize2 size={16} />}\n                </button>\n                <button\n                  className=\"text-neon-green hover:text-white transition-colors focus:outline-none\"\n                  onClick={toggleChat}\n                >\n                  <X size={16} />\n                </button>\n              </div>\n            </div>\n\n            {/* Chatbot Messages */}\n            <div className=\"flex-grow overflow-y-auto p-4\" style={{ backgroundColor: '#0d1117' }}>\n              <div className=\"space-y-4\">\n                {messages.map((message, idx) => (\n                  <div key={idx} className={`${message.type === 'user' ? 'ml-auto max-w-[80%]' : 'mr-auto max-w-[80%]'}`}>\n                    <div className={`rounded-lg p-3 ${message.type === 'user' ? 'bg-neon-green/20 text-white' : 'bg-github-light text-neon-green'}`}>\n                      {message.content.map((line, lineIdx) => (\n                        <React.Fragment key={lineIdx}>\n                          {line === '' ? <br /> : <p className=\"font-mono text-sm\">{line}</p>}\n                        </React.Fragment>\n                      ))}\n                    </div>\n                    <p className=\"text-xs text-github-text mt-1\">\n                      {message.type === 'user' ? 'You' : 'GREENHACKER Bot'} • {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                    </p>\n                  </div>\n                ))}\n                {isTyping && (\n                  <div className=\"mr-auto\">\n                    <div className=\"bg-github-light rounded-lg p-3 max-w-[80%]\">\n                      <div className=\"flex space-x-1\">\n                        <div className=\"h-2 w-2 bg-neon-green rounded-full animate-bounce\"></div>\n                        <div className=\"h-2 w-2 bg-neon-green rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                        <div className=\"h-2 w-2 bg-neon-green rounded-full animate-bounce\" style={{ animationDelay: '0.4s' }}></div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n                <div ref={messagesEndRef} />\n              </div>\n            </div>\n\n            {/* Chatbot Input */}\n            <form onSubmit={handleSubmit} className=\"p-3 border-t border-neon-green/30 bg-github-dark\">\n              <div className=\"flex items-center\">\n                <span className=\"text-neon-green font-mono mr-2\">$</span>\n                <input\n                  ref={inputRef}\n                  type=\"text\"\n                  value={input}\n                  onChange={(e) => setInput(e.target.value)}\n                  className=\"flex-grow bg-transparent border-none text-white font-mono focus:outline-none text-sm\"\n                  placeholder=\"Type a message or command...\"\n                />\n                <button\n                  type=\"submit\"\n                  className=\"text-neon-green hover:text-white transition-colors focus:outline-none\"\n                >\n                  <Send size={16} />\n                </button>\n              </div>\n            </form>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default Chatbot;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,iCAAiC;AACjC,MAAM,WAAW;IACf,MAAM;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,OAAO;QACL;QACA;QACA;KACD;IACD,QAAQ;QACN;QACA;QACA;QACA;QACA;QACA;KACD;IACD,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,aAAa;QACX;QACA;QACA;QACA;KACD;IACD,aAAa;QACX;QACA;QACA;QACA;KACD;IACD,aAAa;QACX;QACA;QACA;QACA;KACD;IACD,aAAa;QACX;QACA;QACA;QACA;KACD;IACD,SAAS;QACP;QACA;QACA;QACA;QACA;KACD;IACD,OAAO;QAAC;KAAG;IACX,MAAM;QAAC;KAAmE;AAC5E;AAOA,MAAM,UAAU;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,MAAM;YACN,SAAS;gBACP;gBACA;gBACA;aACD;QACH;KACD;IACD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,uDAAuD;IACvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,eAAe,OAAO,EAAE,eAAe;gBAAE,UAAU;YAAS;QAC9D;4BAAG;QAAC;KAAS;IAEb,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,QAAQ;gBACV,SAAS,OAAO,EAAE;YACpB;QACF;4BAAG;QAAC;KAAO;IAEX,MAAM,aAAa;QACjB,UAAU,CAAC;IACb;IAEA,MAAM,eAAe;QACnB,cAAc,CAAC;IACjB;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,mBAAmB,QAAQ,WAAW,GAAG,IAAI;QAEnD,IAAI,qBAAqB,QAAQ;YAC/B,YAAY;mBAAI;gBAAU;oBAAE,MAAM;oBAAQ,SAAS;wBAAC;qBAAQ;gBAAC;gBAAG;oBAAE,MAAM;oBAAO,SAAS,SAAS,IAAI;gBAAC;aAAE;YACxG,WAAW,IAAM,UAAU,QAAQ;YACnC;QACF;QAEA,IAAI,qBAAqB,SAAS;YAChC,YAAY,EAAE;YACd;QACF;QAEA,IAAI,QAAQ,CAAC,iBAA0C,EAAE;YACvD,YAAY;mBAAI;gBAAU;oBAAE,MAAM;oBAAQ,SAAS;wBAAC;qBAAQ;gBAAC;gBAAG;oBAAE,MAAM;oBAAO,SAAS,QAAQ,CAAC,iBAA0C;gBAAC;aAAE;YAC9I;QACF;QAEA,yDAAyD;QACzD,YAAY;eAAI;YAAU;gBAAE,MAAM;gBAAQ,SAAS;oBAAC;iBAAQ;YAAC;SAAE;QAC/D,YAAY;QAEZ,wBAAwB;QACxB,WAAW;YACT,MAAM,aAAa,mBAAmB;YACtC,YAAY,CAAA,OAAQ;uBAAI;oBAAM;wBAAE,MAAM;wBAAO,SAAS;oBAAW;iBAAE;YACnE,YAAY;QACd,GAAG,OAAO,KAAK,MAAM,KAAK;IAC5B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,6EAA6E;QAC7E,MAAM,iBAAiB,MAAM,WAAW;QAExC,IAAI,eAAe,QAAQ,CAAC,SAAS,eAAe,QAAQ,CAAC,YAAY,eAAe,QAAQ,CAAC,QAAQ;YACvG,OAAO;gBAAC;gBAAuC;aAAoC;QACrF,OAAO,IAAI,eAAe,QAAQ,CAAC,aAAa,eAAe,QAAQ,CAAC,cAAc;YACpF,OAAO;gBAAC;aAAsD;QAChE,OAAO,IAAI,eAAe,QAAQ,CAAC,iBAAiB,eAAe,QAAQ,CAAC,SAAS;YACnF,OAAO;gBAAC;gBAAuG;aAA6F;QAC9M,OAAO,IAAI,eAAe,QAAQ,CAAC,cAAc;YAC/C,OAAO;gBAAC;gBAA4G;aAAqE;QAC3L,OAAO,IAAI,eAAe,QAAQ,CAAC,SAAS;YAC1C,OAAO;gBAAC;aAAyD;QACnE,OAAO;YACL,OAAO;gBAAC;gBAA0C;aAAkD;QACtG;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,MAAM,IAAI,IAAI;YAChB,eAAe;YACf,SAAS;QACX;IACF;IAEA,qBACE;;0BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;gBACvB,SAAS;0BAET,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,MAAM;;;;;;;;;;;0BAIlB,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,CAAC,MAAM,EAAE,aAAa,wBAAwB,qDAAqD,6FAA6F,CAAC;oBAC5M,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC1B,YAAY;wBAAE,UAAU;oBAAI;;sCAG5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAuB,MAAM;;;;;;sDACjD,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;;8CAEpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,SAAS;sDAER,2BAAa,6LAAC,mNAAA,CAAA,YAAS;gDAAC,MAAM;;;;;qEAAS,6LAAC,mNAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;;;;;;sDAE3D,6LAAC;4CACC,WAAU;4CACV,SAAS;sDAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAMf,6LAAC;4BAAI,WAAU;4BAAgC,OAAO;gCAAE,iBAAiB;4BAAU;sCACjF,cAAA,6LAAC;gCAAI,WAAU;;oCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,oBACtB,6LAAC;4CAAc,WAAW,GAAG,QAAQ,IAAI,KAAK,SAAS,wBAAwB,uBAAuB;;8DACpG,6LAAC;oDAAI,WAAW,CAAC,eAAe,EAAE,QAAQ,IAAI,KAAK,SAAS,gCAAgC,mCAAmC;8DAC5H,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,wBAC1B,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;sEACZ,SAAS,mBAAK,6LAAC;;;;qFAAQ,6LAAC;gEAAE,WAAU;0EAAqB;;;;;;2DADvC;;;;;;;;;;8DAKzB,6LAAC;oDAAE,WAAU;;wDACV,QAAQ,IAAI,KAAK,SAAS,QAAQ;wDAAkB;wDAAI,IAAI,OAAO,kBAAkB,CAAC,EAAE,EAAE;4DAAE,MAAM;4DAAW,QAAQ;wDAAU;;;;;;;;2CAT1H;;;;;oCAaX,0BACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;wDAAoD,OAAO;4DAAE,gBAAgB;wDAAO;;;;;;kEACnG,6LAAC;wDAAI,WAAU;wDAAoD,OAAO;4DAAE,gBAAgB;wDAAO;;;;;;;;;;;;;;;;;;;;;;kDAK3G,6LAAC;wCAAI,KAAK;;;;;;;;;;;;;;;;;sCAKd,6LAAC;4BAAK,UAAU;4BAAc,WAAU;sCACtC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAiC;;;;;;kDACjD,6LAAC;wCACC,KAAK;wCACL,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,WAAU;wCACV,aAAY;;;;;;kDAEd,6LAAC;wCACC,MAAK;wCACL,WAAU;kDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9B;GArMM;KAAA;uCAuMS", "debugId": null}}, {"offset": {"line": 1837, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/providers.tsx"], "sourcesContent": ["'use client';\n\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\nimport { TooltipProvider } from \"@/components/ui/tooltip\";\nimport { ThemeProvider } from \"@/components/theme/ThemeProvider\";\nimport { Toaster } from \"@/components/ui/toaster\";\nimport { Toaster as Sonner } from \"@/components/ui/sonner\";\nimport { useState, useEffect } from \"react\";\nimport LoadingScreen from \"@/components/sections/LoadingScreen\";\nimport AnimatedCursor from \"@/components/effects/AnimatedCursor\";\nimport ReactiveBackground from \"@/components/effects/ReactiveBackground\";\nimport Chatbot from \"@/components/sections/Chatbot\";\n\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      staleTime: 60 * 1000, // 1 minute\n      refetchOnWindowFocus: false,\n    },\n  },\n});\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  const [isLoading, setIsLoading] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n\n  useEffect(() => {\n    // Check if user is on mobile device\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n    \n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    \n    // Check if user has already seen the loading screen\n    const hasLoadingBeenShown = sessionStorage.getItem('loadingShown');\n    \n    if (hasLoadingBeenShown) {\n      setIsLoading(false);\n    } else {\n      // Add event listener for when loading is complete\n      const handleLoadingComplete = () => {\n        setTimeout(() => {\n          setIsLoading(false);\n          sessionStorage.setItem('loadingShown', 'true');\n        }, 1000);\n      };\n      \n      window.addEventListener('loadingComplete', handleLoadingComplete);\n      \n      // Fallback in case loading screen gets stuck\n      const timeout = setTimeout(() => {\n        setIsLoading(false);\n        sessionStorage.setItem('loadingShown', 'true');\n      }, 12000);\n      \n      return () => {\n        window.removeEventListener('loadingComplete', handleLoadingComplete);\n        window.removeEventListener('resize', checkMobile);\n        clearTimeout(timeout);\n      };\n    }\n    \n    return () => {\n      window.removeEventListener('resize', checkMobile);\n    };\n  }, []);\n\n  return (\n    <ThemeProvider>\n      <QueryClientProvider client={queryClient}>\n        <TooltipProvider>\n          <Toaster />\n          <Sonner />\n          \n          {isLoading && <LoadingScreen />}\n          \n          {/* Add reactive background for global effect */}\n          <ReactiveBackground />\n          \n          {/* Only show custom cursor on desktop */}\n          {!isMobile && <AnimatedCursor />}\n          \n          {children}\n          \n          <Chatbot />\n        </TooltipProvider>\n      </QueryClientProvider>\n    </ThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAaA,MAAM,cAAc,IAAI,gLAAA,CAAA,cAAW,CAAC;IAClC,gBAAgB;QACd,SAAS;YACP,WAAW,KAAK;YAChB,sBAAsB;QACxB;IACF;AACF;AAEO,SAAS,UAAU,EAAE,QAAQ,EAAiC;;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,oCAAoC;YACpC,MAAM;mDAAc;oBAClB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC,oDAAoD;YACpD,MAAM,sBAAsB,eAAe,OAAO,CAAC;YAEnD,IAAI,qBAAqB;gBACvB,aAAa;YACf,OAAO;gBACL,kDAAkD;gBAClD,MAAM;iEAAwB;wBAC5B;yEAAW;gCACT,aAAa;gCACb,eAAe,OAAO,CAAC,gBAAgB;4BACzC;wEAAG;oBACL;;gBAEA,OAAO,gBAAgB,CAAC,mBAAmB;gBAE3C,6CAA6C;gBAC7C,MAAM,UAAU;mDAAW;wBACzB,aAAa;wBACb,eAAe,OAAO,CAAC,gBAAgB;oBACzC;kDAAG;gBAEH;2CAAO;wBACL,OAAO,mBAAmB,CAAC,mBAAmB;wBAC9C,OAAO,mBAAmB,CAAC,UAAU;wBACrC,aAAa;oBACf;;YACF;YAEA;uCAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;8BAAG,EAAE;IAEL,qBACE,6LAAC,+IAAA,CAAA,gBAAa;kBACZ,cAAA,6LAAC,yLAAA,CAAA,sBAAmB;YAAC,QAAQ;sBAC3B,cAAA,6LAAC,sIAAA,CAAA,kBAAe;;kCACd,6LAAC,sIAAA,CAAA,UAAO;;;;;kCACR,6LAAC,qJAAA,CAAA,UAAM;;;;;oBAEN,2BAAa,6LAAC,kJAAA,CAAA,UAAa;;;;;kCAG5B,6LAAC,sJAAA,CAAA,UAAkB;;;;;oBAGlB,CAAC,0BAAY,6LAAC,kJAAA,CAAA,UAAc;;;;;oBAE5B;kCAED,6LAAC,4IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;AAKlB;GArEgB;KAAA", "debugId": null}}]}