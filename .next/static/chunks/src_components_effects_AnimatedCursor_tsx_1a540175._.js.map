{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/effects/AnimatedCursor.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\n\nconst AnimatedCursor = () => {\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n  const [clicked, setClicked] = useState(false);\n  const [hovered, setHovered] = useState(false);\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      setPosition({ x: e.clientX, y: e.clientY });\n    };\n\n    const handleMouseDown = () => {\n      setClicked(true);\n      setTimeout(() => setClicked(false), 300);\n    };\n\n    const handleMouseEnter = () => {\n      document.body.style.cursor = 'none';\n    };\n\n    const handleMouseLeave = () => {\n      document.body.style.cursor = 'auto';\n    };\n\n    // Add hover detection for interactive elements\n    const handleMouseOver = (e: MouseEvent) => {\n      const target = e.target as HTMLElement;\n      const isInteractive =\n        target.tagName.toLowerCase() === 'button' ||\n        target.tagName.toLowerCase() === 'a' ||\n        target.closest('button') ||\n        target.closest('a');\n\n      setHovered(!!isInteractive);\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mousedown', handleMouseDown);\n    document.addEventListener('mouseenter', handleMouseEnter);\n    document.addEventListener('mouseleave', handleMouseLeave);\n    document.addEventListener('mouseover', handleMouseOver);\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mousedown', handleMouseDown);\n      document.removeEventListener('mouseenter', handleMouseEnter);\n      document.removeEventListener('mouseleave', handleMouseLeave);\n      document.removeEventListener('mouseover', handleMouseOver);\n      document.body.style.cursor = 'auto';\n    };\n  }, []);\n\n  return (\n    <>\n      {/* Main cursor */}\n      <motion.div\n        className=\"fixed top-0 left-0 w-8 h-8 rounded-full border border-neon-green z-[9999] pointer-events-none\"\n        animate={{\n          x: position.x - 16,\n          y: position.y - 16,\n          scale: clicked ? 0.8 : hovered ? 1.5 : 1,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 300,\n          damping: 20,\n          mass: 0.5\n        }}\n      />\n\n      {/* Cursor dot */}\n      <motion.div\n        className=\"fixed top-0 left-0 w-2 h-2 bg-neon-green rounded-full z-[10000] pointer-events-none\"\n        animate={{\n          x: position.x - 4,\n          y: position.y - 4,\n          opacity: clicked ? 0.5 : 1,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 400,\n          damping: 15,\n        }}\n      />\n    </>\n  );\n};\n\nexport default AnimatedCursor;\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAHA;;;AAKA,MAAM,iBAAiB;;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;4DAAkB,CAAC;oBACvB,YAAY;wBAAE,GAAG,EAAE,OAAO;wBAAE,GAAG,EAAE,OAAO;oBAAC;gBAC3C;;YAEA,MAAM;4DAAkB;oBACtB,WAAW;oBACX;oEAAW,IAAM,WAAW;mEAAQ;gBACtC;;YAEA,MAAM;6DAAmB;oBACvB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC/B;;YAEA,MAAM;6DAAmB;oBACvB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC/B;;YAEA,+CAA+C;YAC/C,MAAM;4DAAkB,CAAC;oBACvB,MAAM,SAAS,EAAE,MAAM;oBACvB,MAAM,gBACJ,OAAO,OAAO,CAAC,WAAW,OAAO,YACjC,OAAO,OAAO,CAAC,WAAW,OAAO,OACjC,OAAO,OAAO,CAAC,aACf,OAAO,OAAO,CAAC;oBAEjB,WAAW,CAAC,CAAC;gBACf;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,cAAc;YACxC,SAAS,gBAAgB,CAAC,cAAc;YACxC,SAAS,gBAAgB,CAAC,aAAa;YAEvC;4CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,mBAAmB,CAAC,cAAc;oBAC3C,SAAS,mBAAmB,CAAC,cAAc;oBAC3C,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC/B;;QACF;mCAAG,EAAE;IAEL,qBACE;;0BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,SAAS,CAAC,GAAG;oBAChB,GAAG,SAAS,CAAC,GAAG;oBAChB,OAAO,UAAU,MAAM,UAAU,MAAM;gBACzC;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;oBACT,MAAM;gBACR;;;;;;0BAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,SAAS,CAAC,GAAG;oBAChB,GAAG,SAAS,CAAC,GAAG;oBAChB,SAAS,UAAU,MAAM;gBAC3B;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;;;;;;;;AAIR;GArFM;KAAA;uCAuFS", "debugId": null}}]}