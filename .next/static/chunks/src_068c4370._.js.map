{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/tooltip.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,sKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/theme/ThemeProvider.tsx"], "sourcesContent": ["\n'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\ntype Theme = 'dark' | 'light';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  // Default to dark theme for \"Green Hacker\" theme\n  const [theme, setTheme] = useState<Theme>('dark');\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    // Check if we're on the client side\n    if (typeof window === 'undefined') return;\n\n    const savedTheme = localStorage.getItem('theme') as Theme | null;\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n\n    if (savedTheme) {\n      setTheme(savedTheme);\n      document.documentElement.classList.toggle('dark', savedTheme === 'dark');\n    } else if (prefersDark) {\n      setTheme('dark');\n      document.documentElement.classList.add('dark');\n    }\n  }, []);\n\n  // Update HTML class and localStorage when theme changes\n  const toggleTheme = () => {\n    const newTheme = theme === 'dark' ? 'light' : 'dark';\n    setTheme(newTheme);\n\n    // Check if we're on the client side\n    if (typeof window !== 'undefined') {\n      document.documentElement.classList.toggle('dark', newTheme === 'dark');\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport const useTheme = (): ThemeContextType => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAGA;;;AAFA;;AAWA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,gBAAyD,CAAC,EAAE,QAAQ,EAAE;;IACjF,iDAAiD;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAE1C,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,oCAAoC;YACpC,uCAAmC;;YAAM;YAEzC,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;YAE7E,IAAI,YAAY;gBACd,SAAS;gBACT,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,eAAe;YACnE,OAAO,IAAI,aAAa;gBACtB,SAAS;gBACT,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACzC;QACF;kCAAG,EAAE;IAEL,wDAAwD;IACxD,MAAM,cAAc;QAClB,MAAM,WAAW,UAAU,SAAS,UAAU;QAC9C,SAAS;QAET,oCAAoC;QACpC,wCAAmC;YACjC,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,aAAa;YAC/D,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAY;kBAChD;;;;;;AAGP;GAtCa;KAAA;AAwCN,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/hooks/use-toast.ts"], "sourcesContent": ["import * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAAA;;;AAOA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;8BAAE;YACd,UAAU,IAAI,CAAC;YACf;sCAAO;oBACL,MAAM,QAAQ,UAAU,OAAO,CAAC;oBAChC,IAAI,QAAQ,CAAC,GAAG;wBACd,UAAU,MAAM,CAAC,OAAO;oBAC1B;gBACF;;QACF;6BAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF;GAlBS", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/toast.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AAEA;;;;;;;AAEA,MAAM,gBAAgB,oKAAA,CAAA,WAAwB;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG,oKAAA,CAAA,WAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAI3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,qBACE,6LAAC,oKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;;AACA,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,SAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sgBACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAA,CAAA,SAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,oKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/toaster.tsx"], "sourcesContent": ["import { useToast } from \"@/hooks/use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AASO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAE1B,qBACE,6LAAC,oIAAA,CAAA,gBAAa;;YACX,OAAO,GAAG,CAAC,SAAU,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO;gBAChE,qBACE,6LAAC,oIAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACvB,6LAAC;4BAAI,WAAU;;gCACZ,uBAAS,6LAAC,oIAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,6LAAC,oIAAA,CAAA,aAAU;;;;;;mBARD;;;;;YAWhB;0BACA,6LAAC,oIAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB;GAtBgB;;QACK,+HAAA,CAAA,WAAQ;;;KADb", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/sonner.tsx"], "sourcesContent": ["import { useTheme } from \"next-themes\"\nimport { Toaster as Sonner, toast } from \"sonner\"\n\ntype ToasterProps = React.ComponentProps<typeof Sonner>\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      toastOptions={{\n        classNames: {\n          toast:\n            \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n          description: \"group-[.toast]:text-muted-foreground\",\n          actionButton:\n            \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n          cancelButton:\n            \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\",\n        },\n      }}\n      {...props}\n    />\n  )\n}\n\nexport { Toaster, toast }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAIA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,cAAc;YACZ,YAAY;gBACV,OACE;gBACF,aAAa;gBACb,cACE;gBACF,cACE;YACJ;QACF;QACC,GAAG,KAAK;;;;;;AAGf;GArBM;;QACyB,mJAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/LoadingScreen.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\n\nconst LoadingScreen = () => {\n  const [loadingProgress, setLoadingProgress] = useState(0);\n  const [loadingText, setLoadingText] = useState('Initializing system...');\n  const [showCursor, setShowCursor] = useState(true);\n  const [commandComplete, setCommandComplete] = useState(false);\n\n  const loadingSteps = [\n    { text: 'Initializing system...', duration: 1200 },\n    { text: 'Establishing secure connection...', duration: 1000 },\n    { text: 'Authenticating credentials...', duration: 800 },\n    { text: 'Bypassing security protocols...', duration: 1500 },\n    { text: 'Loading developer assets...', duration: 1000 },\n    { text: 'Compiling portfolio data...', duration: 1200 },\n    { text: 'Optimizing display modules...', duration: 900 },\n    { text: 'Rendering interface...', duration: 1300 },\n    { text: 'System ready. Welcome to GreenHacker portfolio v2.0', duration: 1000 }\n  ];\n\n  useEffect(() => {\n    // Cursor blinking effect\n    const cursorInterval = setInterval(() => {\n      setShowCursor((prev) => !prev);\n    }, 500);\n\n    // Loading progress simulation\n    let step = 0;\n    const progressInterval = setTimeout(function runStep() {\n      if (step < loadingSteps.length) {\n        const { text, duration } = loadingSteps[step];\n        setLoadingText(text);\n        setLoadingProgress(Math.min(100, Math.round((step + 1) / loadingSteps.length * 100)));\n\n        step++;\n        setTimeout(runStep, duration);\n      } else {\n        setCommandComplete(true);\n        setTimeout(() => {\n          if (typeof window !== 'undefined') {\n            const event = new Event('loadingComplete');\n            window.dispatchEvent(event);\n          }\n        }, 1000);\n      }\n    }, 500);\n\n    return () => {\n      clearInterval(cursorInterval);\n      clearTimeout(progressInterval);\n    };\n  }, []);\n\n  const terminalVariants = {\n    hidden: { opacity: 0 },\n    visible: { opacity: 1, transition: { duration: 0.8 } }\n  };\n\n  return (\n    <motion.div\n      className=\"fixed inset-0 bg-black flex items-center justify-center z-50\"\n      initial={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      transition={{ duration: 0.6, ease: \"easeInOut\" }}\n    >\n      <motion.div\n        className=\"w-full max-w-3xl bg-black border border-neon-green p-6 rounded-md shadow-neon-green terminal-window\"\n        variants={terminalVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"terminal-header flex items-center justify-between mb-4\">\n          <div className=\"text-neon-green font-mono text-sm\">~/green-hacker/portfolio</div>\n          <div className=\"flex space-x-2\">\n            <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n            <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n            <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n          </div>\n        </div>\n\n        <div className=\"terminal-content space-y-2 font-mono text-sm overflow-hidden\">\n          <div className=\"line\">\n            <span className=\"text-neon-blue\">$ </span>\n            <span className=\"text-white\">load portfolio --env=production --secure</span>\n          </div>\n\n          <motion.div\n            className=\"line text-neon-green\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.3 }}\n          >\n            {loadingText}{showCursor ? '▋' : ' '}\n          </motion.div>\n\n          <motion.div\n            className=\"line\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.5 }}\n          >\n            <div className=\"text-github-text\">Progress: {loadingProgress}%</div>\n            <div className=\"w-full bg-github-dark rounded-full h-2 mt-1\">\n              <motion.div\n                className=\"h-2 rounded-full bg-neon-green\"\n                initial={{ width: 0 }}\n                animate={{ width: `${loadingProgress}%` }}\n                transition={{ duration: 0.5 }}\n              ></motion.div>\n            </div>\n          </motion.div>\n\n          {commandComplete && (\n            <>\n              <motion.div\n                className=\"line\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.2 }}\n              >\n                <span className=\"text-neon-blue\">$ </span>\n                <span className=\"text-white\">launch --mode=interactive</span>\n              </motion.div>\n              <motion.div\n                className=\"line text-neon-purple\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.4 }}\n              >\n                Launching portfolio interface...\n              </motion.div>\n            </>\n          )}\n        </div>\n\n        <div className=\"ascii-art mt-8 text-neon-green font-mono text-xs whitespace-pre\">\n{` ██████╗ ██████╗ ███████╗███████╗███╗   ██╗██╗  ██╗ █████╗  ██████╗██╗  ██╗███████╗██████╗\n██╔════╝ ██╔══██╗██╔════╝██╔════╝████╗  ██║██║  ██║██╔══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗\n██║  ███╗██████╔╝█████╗  █████╗  ██╔██╗ ██║███████║███████║██║     █████╔╝ █████╗  ██████╔╝\n██║   ██║██╔══██╗██╔══╝  ██╔══╝  ██║╚██╗██║██╔══██║██╔══██║██║     ██╔═██╗ ██╔══╝  ██╔══██╗\n╚██████╔╝██║  ██║███████╗███████╗██║ ╚████║██║  ██║██║  ██║╚██████╗██║  ██╗███████╗██║  ██║\n ╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝`}\n        </div>\n\n        {commandComplete && (\n          <motion.div\n            className=\"mt-6 text-center\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.8 }}\n          >\n            <span className=\"text-github-text text-sm\">Press </span>\n            <span className=\"px-2 py-1 bg-github-light rounded text-white text-sm mx-1\">ENTER</span>\n            <span className=\"text-github-text text-sm\"> to continue</span>\n          </motion.div>\n        )}\n      </motion.div>\n\n      <style dangerouslySetInnerHTML={{__html: `\n        .terminal-window {\n          box-shadow: 0 0 10px rgba(63, 185, 80, 0.3), 0 0 20px rgba(63, 185, 80, 0.2);\n        }\n\n        @keyframes scan {\n          from { top: 0; }\n          to { top: 100%; }\n        }\n\n        .terminal-window::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          height: 3px;\n          background-color: rgba(63, 185, 80, 0.5);\n          animation: scan 3s linear infinite;\n        }\n      `}} />\n    </motion.div>\n  );\n};\n\nexport default LoadingScreen;\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAHA;;;AAKA,MAAM,gBAAgB;;IACpB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,eAAe;QACnB;YAAE,MAAM;YAA0B,UAAU;QAAK;QACjD;YAAE,MAAM;YAAqC,UAAU;QAAK;QAC5D;YAAE,MAAM;YAAiC,UAAU;QAAI;QACvD;YAAE,MAAM;YAAmC,UAAU;QAAK;QAC1D;YAAE,MAAM;YAA+B,UAAU;QAAK;QACtD;YAAE,MAAM;YAA+B,UAAU;QAAK;QACtD;YAAE,MAAM;YAAiC,UAAU;QAAI;QACvD;YAAE,MAAM;YAA0B,UAAU;QAAK;QACjD;YAAE,MAAM;YAAuD,UAAU;QAAK;KAC/E;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,yBAAyB;YACzB,MAAM,iBAAiB;0DAAY;oBACjC;kEAAc,CAAC,OAAS,CAAC;;gBAC3B;yDAAG;YAEH,8BAA8B;YAC9B,IAAI,OAAO;YACX,MAAM,mBAAmB,WAAW,SAAS;gBAC3C,IAAI,OAAO,aAAa,MAAM,EAAE;oBAC9B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,YAAY,CAAC,KAAK;oBAC7C,eAAe;oBACf,mBAAmB,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM,GAAG;oBAE/E;oBACA,WAAW,SAAS;gBACtB,OAAO;oBACL,mBAAmB;oBACnB;4EAAW;4BACT,wCAAmC;gCACjC,MAAM,QAAQ,IAAI,MAAM;gCACxB,OAAO,aAAa,CAAC;4BACvB;wBACF;2EAAG;gBACL;YACF,GAAG;YAEH;2CAAO;oBACL,cAAc;oBACd,aAAa;gBACf;;QACF;kCAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YAAE,SAAS;YAAG,YAAY;gBAAE,UAAU;YAAI;QAAE;IACvD;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAY;;0BAE/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAQ;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAoC;;;;;;0CACnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;kCAInB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAiB;;;;;;kDACjC,6LAAC;wCAAK,WAAU;kDAAa;;;;;;;;;;;;0CAG/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAI;;oCAExB;oCAAa,aAAa,MAAM;;;;;;;0CAGnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,6LAAC;wCAAI,WAAU;;4CAAmB;4CAAW;4CAAgB;;;;;;;kDAC7D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC;4CAAC;4CACxC,YAAY;gDAAE,UAAU;4CAAI;;;;;;;;;;;;;;;;;4BAKjC,iCACC;;kDACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,6LAAC;gDAAK,WAAU;0DAAa;;;;;;;;;;;;kDAE/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,OAAO;wCAAI;kDAC1B;;;;;;;;;;;;;;kCAOP,6LAAC;wBAAI,WAAU;kCACtB,CAAC;;;;;2FAKyF,CAAC;;;;;;oBAGnF,iCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,6LAAC;gCAAK,WAAU;0CAA2B;;;;;;0CAC3C,6LAAC;gCAAK,WAAU;0CAA4D;;;;;;0CAC5E,6LAAC;gCAAK,WAAU;0CAA2B;;;;;;;;;;;;;;;;;;0BAKjD,6LAAC;gBAAM,yBAAyB;oBAAC,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;MAoB1C,CAAC;gBAAA;;;;;;;;;;;;AAGP;GAnLM;KAAA;uCAqLS", "debugId": null}}, {"offset": {"line": 1031, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/providers.tsx"], "sourcesContent": ["\n'use client';\n\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\nimport { TooltipProvider } from \"@/components/ui/tooltip\";\nimport { ThemeProvider } from \"@/components/theme/ThemeProvider\";\nimport { Toaster } from \"@/components/ui/toaster\";\nimport { Toaster as Sonner } from \"@/components/ui/sonner\";\nimport { useState, useEffect } from \"react\";\nimport LoadingScreen from \"@/components/sections/LoadingScreen\";\nimport dynamic from \"next/dynamic\";\n\n// Dynamically import client-only components\nconst AnimatedCursor = dynamic(() => import(\"@/components/effects/AnimatedCursor\"), {\n  ssr: false\n});\n\nconst ReactiveBackground = dynamic(() => import(\"@/components/effects/ReactiveBackground\"), {\n  ssr: false\n});\n\nconst Chatbot = dynamic(() => import(\"@/components/sections/Chatbot\"), {\n  ssr: false\n});\n\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      staleTime: 60 * 1000, // 1 minute\n      refetchOnWindowFocus: false,\n    },\n  },\n});\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  const [isLoading, setIsLoading] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n    \n    // Check if user is on mobile device\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n    \n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    \n    // Check if user has already seen the loading screen\n    const hasLoadingBeenShown = sessionStorage.getItem('loadingShown');\n    \n    if (hasLoadingBeenShown) {\n      setIsLoading(false);\n    } else {\n      // Add event listener for when loading is complete\n      const handleLoadingComplete = () => {\n        setTimeout(() => {\n          setIsLoading(false);\n          sessionStorage.setItem('loadingShown', 'true');\n        }, 1000);\n      };\n      \n      window.addEventListener('loadingComplete', handleLoadingComplete);\n      \n      // Fallback in case loading screen gets stuck\n      const timeout = setTimeout(() => {\n        setIsLoading(false);\n        sessionStorage.setItem('loadingShown', 'true');\n      }, 12000);\n      \n      return () => {\n        window.removeEventListener('loadingComplete', handleLoadingComplete);\n        window.removeEventListener('resize', checkMobile);\n        clearTimeout(timeout);\n      };\n    }\n    \n    return () => {\n      window.removeEventListener('resize', checkMobile);\n    };\n  }, []);\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <ThemeProvider>\n      <QueryClientProvider client={queryClient}>\n        <TooltipProvider>\n          <Toaster />\n          <Sonner />\n          \n          {isLoading && <LoadingScreen />}\n          \n          {/* Add reactive background for global effect */}\n          <ReactiveBackground />\n          \n          {/* Only show custom cursor on desktop */}\n          {!isMobile && <AnimatedCursor />}\n          \n          {children}\n          \n          <Chatbot />\n        </TooltipProvider>\n      </QueryClientProvider>\n    </ThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;AATA;;;;;;;;;AAWA,4CAA4C;AAC5C,MAAM,iBAAiB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAC7B,KAAK;;KADD;AAIN,MAAM,qBAAqB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACjC,KAAK;;MADD;AAIN,MAAM,UAAU,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACtB,KAAK;;MADD;AAIN,MAAM,cAAc,IAAI,gLAAA,CAAA,cAAW,CAAC;IAClC,gBAAgB;QACd,SAAS;YACP,WAAW,KAAK;YAChB,sBAAsB;QACxB;IACF;AACF;AAEO,SAAS,UAAU,EAAE,QAAQ,EAAiC;;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,WAAW;YAEX,oCAAoC;YACpC,MAAM;mDAAc;oBAClB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC,oDAAoD;YACpD,MAAM,sBAAsB,eAAe,OAAO,CAAC;YAEnD,IAAI,qBAAqB;gBACvB,aAAa;YACf,OAAO;gBACL,kDAAkD;gBAClD,MAAM;iEAAwB;wBAC5B;yEAAW;gCACT,aAAa;gCACb,eAAe,OAAO,CAAC,gBAAgB;4BACzC;wEAAG;oBACL;;gBAEA,OAAO,gBAAgB,CAAC,mBAAmB;gBAE3C,6CAA6C;gBAC7C,MAAM,UAAU;mDAAW;wBACzB,aAAa;wBACb,eAAe,OAAO,CAAC,gBAAgB;oBACzC;kDAAG;gBAEH;2CAAO;wBACL,OAAO,mBAAmB,CAAC,mBAAmB;wBAC9C,OAAO,mBAAmB,CAAC,UAAU;wBACrC,aAAa;oBACf;;YACF;YAEA;uCAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;8BAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC,+IAAA,CAAA,gBAAa;kBACZ,cAAA,6LAAC,yLAAA,CAAA,sBAAmB;YAAC,QAAQ;sBAC3B,cAAA,6LAAC,sIAAA,CAAA,kBAAe;;kCACd,6LAAC,sIAAA,CAAA,UAAO;;;;;kCACR,6LAAC,qJAAA,CAAA,UAAM;;;;;oBAEN,2BAAa,6LAAC,kJAAA,CAAA,UAAa;;;;;kCAG5B,6LAAC;;;;;oBAGA,CAAC,0BAAY,6LAAC;;;;;oBAEd;kCAED,6LAAC;;;;;;;;;;;;;;;;;;;;;AAKX;GA5EgB;MAAA", "debugId": null}}]}