(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[345],{42979:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>v});var n=i(95155),a=i(6874),r=i.n(a),s=i(58892),o=i(57340),l=i(47924),c=i(35169),d=i(12115),u=i(99708),h=i(74466),g=i(59434);let x=(0,h.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=d.forwardRef((e,t)=>{let{className:i,variant:a,size:r,asChild:s=!1,...o}=e,l=s?u.DX:"button";return(0,n.jsx)(l,{className:(0,g.cn)(x({variant:a,size:r,className:i})),ref:t,...o})});function v(){return(0,n.jsx)("div",{className:"min-h-screen bg-github-dark text-github-text flex items-center justify-center",children:(0,n.jsx)("div",{className:"max-w-md mx-auto text-center px-6",children:(0,n.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,n.jsx)(s.P.div,{className:"text-8xl font-bold text-neon-green mb-8",initial:{scale:.5},animate:{scale:1},transition:{duration:.5,type:"spring",stiffness:200},children:"404"}),(0,n.jsx)(s.P.h1,{className:"text-3xl font-bold text-white mb-4",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:"Page Not Found"}),(0,n.jsx)(s.P.p,{className:"text-github-text mb-8 leading-relaxed",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:"Oops! The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL."}),(0,n.jsxs)(s.P.div,{className:"flex flex-col sm:flex-row gap-4 justify-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,n.jsx)(m,{asChild:!0,className:"bg-neon-green text-black hover:bg-neon-green/90",children:(0,n.jsxs)(r(),{href:"/",className:"flex items-center gap-2",children:[(0,n.jsx)(o.A,{size:18}),"Go Home"]})}),(0,n.jsx)(m,{variant:"outline",asChild:!0,className:"border-github-border text-github-text hover:bg-github-light",children:(0,n.jsxs)(r(),{href:"/#contact",className:"flex items-center gap-2",children:[(0,n.jsx)(l.A,{size:18}),"Contact Support"]})})]}),(0,n.jsx)(s.P.div,{className:"mt-8",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:(0,n.jsxs)(m,{variant:"ghost",onClick:()=>window.history.back(),className:"text-github-text hover:text-white flex items-center gap-2",children:[(0,n.jsx)(c.A,{size:18}),"Go Back"]})}),(0,n.jsx)(s.P.div,{className:"absolute top-1/4 left-1/4 w-2 h-2 bg-neon-green rounded-full opacity-50",animate:{scale:[1,1.5,1],opacity:[.5,1,.5]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}),(0,n.jsx)(s.P.div,{className:"absolute top-1/3 right-1/4 w-1 h-1 bg-neon-blue rounded-full opacity-50",animate:{scale:[1,2,1],opacity:[.3,.8,.3]},transition:{duration:3,repeat:1/0,ease:"easeInOut",delay:1}})]})})})}m.displayName="Button"},59434:(e,t,i)=>{"use strict";i.d(t,{cn:()=>r});var n=i(52596),a=i(39688);function r(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return(0,a.QP)((0,n.$)(t))}},74466:(e,t,i)=>{"use strict";i.d(t,{F:()=>s});var n=i(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,r=n.$,s=(e,t)=>i=>{var n;if((null==t?void 0:t.variants)==null)return r(e,null==i?void 0:i.class,null==i?void 0:i.className);let{variants:s,defaultVariants:o}=t,l=Object.keys(s).map(e=>{let t=null==i?void 0:i[e],n=null==o?void 0:o[e];if(null===t)return null;let r=a(t)||a(n);return s[e][r]}),c=i&&Object.entries(i).reduce((e,t)=>{let[i,n]=t;return void 0===n||(e[i]=n),e},{});return r(e,l,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:i,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,i]=e;return Array.isArray(i)?i.includes({...o,...c}[t]):({...o,...c})[t]===i})?[...e,i,n]:e},[]),null==i?void 0:i.class,null==i?void 0:i.className)}},75796:(e,t,i)=>{Promise.resolve().then(i.bind(i,42979))}},e=>{var t=t=>e(e.s=t);e.O(0,[710,390,441,684,358],()=>t(75796)),_N_E=e.O()}]);