(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@splinetool/runtime/build/navmesh.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@splinetool_runtime_build_navmesh_584c831c.js",
  "static/chunks/node_modules_@splinetool_runtime_build_navmesh_a9d736ad.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@splinetool/runtime/build/navmesh.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@splinetool/runtime/build/physics.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@splinetool_runtime_build_physics_241a2f2d.js",
  "static/chunks/node_modules_@splinetool_runtime_build_physics_a9d736ad.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@splinetool/runtime/build/physics.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@splinetool/runtime/build/process.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@splinetool_runtime_build_process_62f6d1c5.js",
  "static/chunks/node_modules_@splinetool_runtime_build_process_a9d736ad.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@splinetool/runtime/build/process.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@splinetool/runtime/build/boolean.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@splinetool_runtime_build_boolean_b52c3173.js",
  "static/chunks/node_modules_@splinetool_runtime_build_boolean_a9d736ad.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@splinetool/runtime/build/boolean.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@splinetool/runtime/build/howler.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@splinetool_runtime_build_howler_3d5b02da.js",
  "static/chunks/node_modules_@splinetool_runtime_build_howler_a9d736ad.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@splinetool/runtime/build/howler.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@splinetool/runtime/build/opentype.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@splinetool_runtime_build_opentype_83a09204.js",
  "static/chunks/node_modules_@splinetool_runtime_build_opentype_a9d736ad.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@splinetool/runtime/build/opentype.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@splinetool/runtime/build/ui.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@splinetool_runtime_build_ui_9675aec7.js",
  "static/chunks/node_modules_@splinetool_runtime_build_ui_a9d736ad.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@splinetool/runtime/build/ui.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@splinetool/runtime/build/gaussian-splat-compression.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@splinetool_runtime_build_gaussian-splat-compression_776d14b4.js",
  "static/chunks/node_modules_@splinetool_runtime_build_gaussian-splat-compression_a9d736ad.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@splinetool/runtime/build/gaussian-splat-compression.js [app-client] (ecmascript)");
    });
});
}}),
}]);