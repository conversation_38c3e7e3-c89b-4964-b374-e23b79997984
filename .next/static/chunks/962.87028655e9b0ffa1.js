"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[962],{13962:(e,t,r)=>{r.r(t),r.d(t,{Canvas:()=>O,ReactThreeFiber:()=>i.t,_roots:()=>i.z,act:()=>i.x,addAfterEffect:()=>i.p,addEffect:()=>i.o,addTail:()=>i.q,advance:()=>i.n,applyProps:()=>i.k,buildGraph:()=>i.y,context:()=>i.g,createEvents:()=>i.f,createPointerEvents:()=>i.c,createPortal:()=>i.h,createRoot:()=>i.b,dispose:()=>i.l,events:()=>i.c,extend:()=>i.e,flushGlobalEffects:()=>i.s,flushSync:()=>i.v,getRootState:()=>i.w,invalidate:()=>i.m,reconciler:()=>i.j,render:()=>i.r,unmountComponentAtNode:()=>i.d,useFrame:()=>i.F,useGraph:()=>i.G,useInstanceHandle:()=>i.A,useLoader:()=>i.H,useStore:()=>i.C,useThree:()=>i.D});var n,o,i=r(10697),l=r(12115),s=r(97431);function u(e,t){let r;return(...n)=>{window.clearTimeout(r),r=window.setTimeout(()=>e(...n),t)}}let c=["x","y","top","bottom","left","right","width","height"],a=(e,t)=>c.every(r=>e[r]===t[r]);var d=Object.defineProperty,f=Object.defineProperties,h=Object.getOwnPropertyDescriptors,v=Object.getOwnPropertySymbols,p=Object.prototype.hasOwnProperty,w=Object.prototype.propertyIsEnumerable,m=(e,t,r)=>t in e?d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,b=(e,t)=>{for(var r in t||(t={}))p.call(t,r)&&m(e,r,t[r]);if(v)for(var r of v(t))w.call(t,r)&&m(e,r,t[r]);return e},y=(e,t)=>f(e,h(t));"undefined"!=typeof window&&((null==(n=window.document)?void 0:n.createElement)||(null==(o=window.navigator)?void 0:o.product)==="ReactNative")?l.useLayoutEffect:l.useEffect;function E(e){try{return Object.defineProperties(e,{_currentRenderer:{get:()=>null,set(){}},_currentRenderer2:{get:()=>null,set(){}}})}catch(t){return e}}let g=console.error;console.error=function(){let e=[...arguments].join("");if((null==e?void 0:e.startsWith("Warning:"))&&e.includes("useContext")){console.error=g;return}return g.apply(this,arguments)};let x=E(l.createContext(null));class z extends l.Component{render(){return l.createElement(x.Provider,{value:this._reactInternals},this.props.children)}}var C=r(95155);r(61933),r(45220),r(72407);let j=l.forwardRef(function(e,t){let{children:r,fallback:n,resize:o,style:c,gl:d,events:f=i.c,eventSource:h,eventPrefix:v,shadows:p,linear:w,flat:m,legacy:g,orthographic:j,frameloop:O,dpr:R,performance:L,raycaster:S,camera:P,scene:H,onPointerMissed:_,onCreated:k,...M}=e;l.useMemo(()=>(0,i.e)(s),[]);let T=function(){let e=function(){let e=function(){let e=l.useContext(x);if(null===e)throw Error("its-fine: useFiber must be called within a <FiberProvider />!");let t=l.useId();return l.useMemo(()=>{for(let r of[e,null==e?void 0:e.alternate]){if(!r)continue;let e=function e(t,r,n){if(!t)return;if(!0===n(t))return t;let o=r?t.return:t.child;for(;o;){let t=e(o,r,n);if(t)return t;o=r?null:o.sibling}}(r,!1,e=>{let r=e.memoizedState;for(;r;){if(r.memoizedState===t)return!0;r=r.next}});if(e)return e}},[e,t])}(),[t]=l.useState(()=>new Map);t.clear();let r=e;for(;r;){if(r.type&&"object"==typeof r.type){let e=void 0===r.type._context&&r.type.Provider===r.type?r.type:r.type._context;e&&e!==x&&!t.has(e)&&t.set(e,l.useContext(E(e)))}r=r.return}return t}();return l.useMemo(()=>Array.from(e.keys()).reduce((t,r)=>n=>l.createElement(t,null,l.createElement(r.Provider,y(b({},n),{value:e.get(r)}))),e=>l.createElement(z,b({},e))),[e])}(),[F,A]=function({debounce:e,scroll:t,polyfill:r,offsetSize:n}={debounce:0,scroll:!1,offsetSize:!1}){var o,i,s;let c=r||("undefined"==typeof window?class{}:window.ResizeObserver);if(!c)throw Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");let[d,f]=(0,l.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),h=(0,l.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:d,orientationHandler:null}),v=e?"number"==typeof e?e:e.scroll:null,p=e?"number"==typeof e?e:e.resize:null,w=(0,l.useRef)(!1);(0,l.useEffect)(()=>(w.current=!0,()=>void(w.current=!1)));let[m,b,y]=(0,l.useMemo)(()=>{let e=()=>{if(!h.current.element)return;let{left:e,top:t,width:r,height:o,bottom:i,right:l,x:s,y:u}=h.current.element.getBoundingClientRect(),c={left:e,top:t,width:r,height:o,bottom:i,right:l,x:s,y:u};h.current.element instanceof HTMLElement&&n&&(c.height=h.current.element.offsetHeight,c.width=h.current.element.offsetWidth),Object.freeze(c),w.current&&!a(h.current.lastBounds,c)&&f(h.current.lastBounds=c)};return[e,p?u(e,p):e,v?u(e,v):e]},[f,n,v,p]);function E(){h.current.scrollContainers&&(h.current.scrollContainers.forEach(e=>e.removeEventListener("scroll",y,!0)),h.current.scrollContainers=null),h.current.resizeObserver&&(h.current.resizeObserver.disconnect(),h.current.resizeObserver=null),h.current.orientationHandler&&("orientation"in screen&&"removeEventListener"in screen.orientation?screen.orientation.removeEventListener("change",h.current.orientationHandler):"onorientationchange"in window&&window.removeEventListener("orientationchange",h.current.orientationHandler))}function g(){h.current.element&&(h.current.resizeObserver=new c(y),h.current.resizeObserver.observe(h.current.element),t&&h.current.scrollContainers&&h.current.scrollContainers.forEach(e=>e.addEventListener("scroll",y,{capture:!0,passive:!0})),h.current.orientationHandler=()=>{y()},"orientation"in screen&&"addEventListener"in screen.orientation?screen.orientation.addEventListener("change",h.current.orientationHandler):"onorientationchange"in window&&window.addEventListener("orientationchange",h.current.orientationHandler))}return o=y,i=!!t,(0,l.useEffect)(()=>{if(i)return window.addEventListener("scroll",o,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",o,!0)},[o,i]),s=b,(0,l.useEffect)(()=>(window.addEventListener("resize",s),()=>void window.removeEventListener("resize",s)),[s]),(0,l.useEffect)(()=>{E(),g()},[t,y,b]),(0,l.useEffect)(()=>E,[]),[e=>{e&&e!==h.current.element&&(E(),h.current.element=e,h.current.scrollContainers=function e(t){let r=[];if(!t||t===document.body)return r;let{overflow:n,overflowX:o,overflowY:i}=window.getComputedStyle(t);return[n,o,i].some(e=>"auto"===e||"scroll"===e)&&r.push(t),[...r,...e(t.parentElement)]}(e),g())},d,m]}({scroll:!0,debounce:{scroll:50,resize:0},...o}),B=l.useRef(null),I=l.useRef(null);l.useImperativeHandle(t,()=>B.current);let G=(0,i.u)(_),[N,W]=l.useState(!1),[D,X]=l.useState(!1);if(N)throw N;if(D)throw D;let Y=l.useRef(null);(0,i.a)(()=>{let e=B.current;A.width>0&&A.height>0&&e&&(Y.current||(Y.current=(0,i.b)(e)),Y.current.configure({gl:d,events:f,shadows:p,linear:w,flat:m,legacy:g,orthographic:j,frameloop:O,dpr:R,performance:L,raycaster:S,camera:P,scene:H,size:A,onPointerMissed:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return null==G.current?void 0:G.current(...t)},onCreated:e=>{null==e.events.connect||e.events.connect(h?(0,i.i)(h)?h.current:h:I.current),v&&e.setEvents({compute:(e,t)=>{let r=e[v+"X"],n=e[v+"Y"];t.pointer.set(r/t.size.width*2-1,-(2*(n/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),null==k||k(e)}}),Y.current.render((0,C.jsx)(T,{children:(0,C.jsx)(i.E,{set:X,children:(0,C.jsx)(l.Suspense,{fallback:(0,C.jsx)(i.B,{set:W}),children:null!=r?r:null})})})))}),l.useEffect(()=>{let e=B.current;if(e)return()=>(0,i.d)(e)},[]);let q=h?"none":"auto";return(0,C.jsx)("div",{ref:I,style:{position:"relative",width:"100%",height:"100%",overflow:"hidden",pointerEvents:q,...c},...M,children:(0,C.jsx)("div",{ref:F,style:{width:"100%",height:"100%"},children:(0,C.jsx)("canvas",{ref:B,style:{display:"block"},children:n})})})}),O=l.forwardRef(function(e,t){return(0,C.jsx)(z,{children:(0,C.jsx)(j,{...e,ref:t})})})}}]);