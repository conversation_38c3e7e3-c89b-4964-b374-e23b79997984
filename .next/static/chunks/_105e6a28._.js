(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_react-reconciler_25c8ec02._.js",
  "static/chunks/node_modules_three_build_three_core_df0bde7e.js",
  "static/chunks/node_modules_three_build_three_module_230a9f89.js",
  "static/chunks/node_modules_three_build_three_module_90b4155f.js",
  "static/chunks/node_modules_@react-three_fiber_dist_711ab369._.js",
  "static/chunks/node_modules_971b5526._.js",
  "static/chunks/node_modules_@react-three_fiber_dist_react-three-fiber_esm_94162c2c.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/3d/InteractiveThreeScene.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_three_build_three_core_df0bde7e.js",
  "static/chunks/node_modules_three_build_three_module_230a9f89.js",
  "static/chunks/node_modules_react-reconciler_25c8ec02._.js",
  "static/chunks/node_modules_5c245e00._.js",
  "static/chunks/src_components_3d_InteractiveThreeScene_tsx_c48e654b._.js",
  "static/chunks/src_components_3d_InteractiveThreeScene_tsx_92011980._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/3d/InteractiveThreeScene.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/node_modules/@splinetool/react-spline/dist/react-spline.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@splinetool_runtime_build_80d22d18._.js",
  "static/chunks/node_modules_@splinetool_runtime_build_runtime_ef1768d2.js",
  "static/chunks/node_modules_2db15e2e._.js",
  "static/chunks/node_modules_@splinetool_react-spline_dist_react-spline_92011980.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@splinetool/react-spline/dist/react-spline.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);