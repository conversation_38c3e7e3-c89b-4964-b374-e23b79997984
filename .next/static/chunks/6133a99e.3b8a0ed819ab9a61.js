"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[666],{31315:(e,t,r)=>{r.r(t),r.d(t,{default:()=>o});var n=r(49509),a=(e=>"u">typeof require?require:"u">typeof Proxy?new Proxy(e,{get:(e,t)=>("u">typeof require?require:e)[t]}):e)(function(e){if("u">typeof require)return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});let o=((e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports))((e,t)=>{var r=(()=>{var e="u">typeof document&&document.currentScript?document.currentScript.src:void 0;return"u">typeof __filename&&(e=e||__filename),function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};t.ready=new Promise((e,t)=>{o=e,i=t}),t.Id=t.Id||[],t.Id.push(function(){t.MakeSWCanvasSurface=function(e){var r=e,n="u">typeof OffscreenCanvas&&r instanceof OffscreenCanvas;if(!("u">typeof HTMLCanvasElement&&r instanceof HTMLCanvasElement||n||(r=document.getElementById(e))))throw"Canvas with id "+e+" was not found";return(e=t.MakeSurface(r.width,r.height))&&(e.ie=r),e},t.MakeCanvasSurface||(t.MakeCanvasSurface=t.MakeSWCanvasSurface),t.MakeSurface=function(e,r){var n={width:e,height:r,colorType:t.ColorType.RGBA_8888,alphaType:t.AlphaType.Unpremul,colorSpace:t.ColorSpace.SRGB},a=e*r*4,o=t._malloc(a);return(n=t.Surface._makeRasterDirect(n,o,4*e))&&(n.ie=null,n.Pe=e,n.Me=r,n.Ne=a,n.re=o,n.getCanvas().clear(t.TRANSPARENT)),n},t.MakeRasterDirectSurface=function(e,r,n){return t.Surface._makeRasterDirect(e,r.byteOffset,n)},t.Surface.prototype.flush=function(e){if(t.Fd(this.Ed),this._flush(),this.ie){var r=new Uint8ClampedArray(t.HEAPU8.buffer,this.re,this.Ne);r=new ImageData(r,this.Pe,this.Me),e?this.ie.getContext("2d").putImageData(r,0,0,e[0],e[1],e[2]-e[0],e[3]-e[1]):this.ie.getContext("2d").putImageData(r,0,0)}},t.Surface.prototype.dispose=function(){this.re&&t._free(this.re),this.delete()},t.Fd=t.Fd||function(){},t.je=t.je||function(){return null}}),t.Id=t.Id||[],t.Id.push(function(){function e(e,t,r){return e&&e.hasOwnProperty(t)?e[t]:r}function r(e){var t=td(tr);return tr[t]=e,t}function n(e){return e.naturalHeight||e.videoHeight||e.displayHeight||e.height}function a(e){return e.naturalWidth||e.videoWidth||e.displayWidth||e.width}function o(e,r,n,a){return e.bindTexture(e.TEXTURE_2D,r),a||n.alphaType!==t.AlphaType.Premul||e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),r}function i(e,r,n){n||r.alphaType!==t.AlphaType.Premul||e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),e.bindTexture(e.TEXTURE_2D,null)}t.GetWebGLContext=function(t,r){if(!t)throw"null canvas passed into makeWebGLContext";var n,a,o,i,u,c,l,f={alpha:e(r,"alpha",1),depth:e(r,"depth",1),stencil:e(r,"stencil",8),antialias:e(r,"antialias",0),premultipliedAlpha:e(r,"premultipliedAlpha",1),preserveDrawingBuffer:e(r,"preserveDrawingBuffer",0),preferLowPowerToHighPerformance:e(r,"preferLowPowerToHighPerformance",0),failIfMajorPerformanceCaveat:e(r,"failIfMajorPerformanceCaveat",0),enableExtensionsByDefault:e(r,"enableExtensionsByDefault",1),explicitSwapControl:e(r,"explicitSwapControl",0),renderViaOffscreenBackBuffer:e(r,"renderViaOffscreenBackBuffer",0)};if(f.majorVersion=r&&r.majorVersion?r.majorVersion:"u">typeof WebGL2RenderingContext?2:1,f.explicitSwapControl)throw"explicitSwapControl is not supported";return(n=t,a=f,n.be||(n.be=n.getContext,n.getContext=function(e,t){return t=n.be(e,t),"webgl"==e==t instanceof WebGLRenderingContext?t:null}),t=(o=1<a.majorVersion?n.getContext("webgl2",a):n.getContext("webgl",a))?(i=o,u=a,l={handle:c=td(to),attributes:u,version:u.majorVersion,Qd:i},i.canvas&&(i.canvas.Ke=l),to[c]=l,(typeof u.Ue>"u"||u.Ue)&&function(e){if(e||(e=ty),!e.bf){e.bf=!0;var t,r,n,a=e.Qd;(t=a.getExtension("ANGLE_instanced_arrays"))&&(a.vertexAttribDivisor=function(e,r){t.vertexAttribDivisorANGLE(e,r)},a.drawArraysInstanced=function(e,r,n,a){t.drawArraysInstancedANGLE(e,r,n,a)},a.drawElementsInstanced=function(e,r,n,a,o){t.drawElementsInstancedANGLE(e,r,n,a,o)}),(r=a.getExtension("OES_vertex_array_object"))&&(a.createVertexArray=function(){return r.createVertexArrayOES()},a.deleteVertexArray=function(e){r.deleteVertexArrayOES(e)},a.bindVertexArray=function(e){r.bindVertexArrayOES(e)},a.isVertexArray=function(e){return r.isVertexArrayOES(e)}),(n=a.getExtension("WEBGL_draw_buffers"))&&(a.drawBuffers=function(e,t){n.drawBuffersWEBGL(e,t)}),a.ze=a.getExtension("WEBGL_draw_instanced_base_vertex_base_instance"),a.De=a.getExtension("WEBGL_multi_draw_instanced_base_vertex_base_instance"),2<=e.version&&(a.Ae=a.getExtension("EXT_disjoint_timer_query_webgl2")),(2>e.version||!a.Ae)&&(a.Ae=a.getExtension("EXT_disjoint_timer_query")),a.rf=a.getExtension("WEBGL_multi_draw"),(a.getSupportedExtensions()||[]).forEach(function(e){e.includes("lose_context")||e.includes("debug")||a.getExtension(e)})}}(l),c):0)?(th(t),ty.Qd.getExtension("WEBGL_debug_renderer_info"),t):0},t.deleteContext=function(e){ty===to[e]&&(ty=null),"object"==typeof JSEvents&&JSEvents.tf(to[e].Qd.canvas),to[e]&&to[e].Qd.canvas&&(to[e].Qd.canvas.Ke=void 0),to[e]=null},t._setTextureCleanup({deleteTexture:function(e,t){var r=tr[t];r&&to[e].Qd.deleteTexture(r),tr[t]=null}}),t.MakeWebGLContext=function(e){if(!this.Fd(e))return null;var r=this._MakeGrContext();if(!r)return null;r.Ed=e;var n=r.delete.bind(r);return r.delete=(function(){t.Fd(this.Ed),n()}).bind(r),ty.te=r},t.MakeGrContext=t.MakeWebGLContext,t.GrDirectContext.prototype.getResourceCacheLimitBytes=function(){t.Fd(this.Ed),this._getResourceCacheLimitBytes()},t.GrDirectContext.prototype.getResourceCacheUsageBytes=function(){t.Fd(this.Ed),this._getResourceCacheUsageBytes()},t.GrDirectContext.prototype.releaseResourcesAndAbandonContext=function(){t.Fd(this.Ed),this._releaseResourcesAndAbandonContext()},t.GrDirectContext.prototype.setResourceCacheLimitBytes=function(e){t.Fd(this.Ed),this._setResourceCacheLimitBytes(e)},t.MakeOnScreenGLSurface=function(e,t,r,n,a,o){return this.Fd(e.Ed)&&(t=void 0===a||void 0===o?this._MakeOnScreenGLSurface(e,t,r,n):this._MakeOnScreenGLSurface(e,t,r,n,a,o))?(t.Ed=e.Ed,t):null},t.MakeRenderTarget=function(){var e=arguments[0];if(!this.Fd(e.Ed))return null;if(3==arguments.length){var t=this._MakeRenderTargetWH(e,arguments[1],arguments[2]);if(!t)return null}else if(2!=arguments.length)return null;else if(t=this._MakeRenderTargetII(e,arguments[1]),!t)return null;return t.Ed=e.Ed,t},t.MakeWebGLCanvasSurface=function(e,r,n){r=r||null;var a=e,o="u">typeof OffscreenCanvas&&a instanceof OffscreenCanvas;if(!("u">typeof HTMLCanvasElement&&a instanceof HTMLCanvasElement||o||(a=document.getElementById(e))))throw"Canvas with id "+e+" was not found";if(!(e=this.GetWebGLContext(a,n))||0>e)throw"failed to create webgl context: err "+e;return e=this.MakeWebGLContext(e),(r=this.MakeOnScreenGLSurface(e,a.width,a.height,r))||(r=a.cloneNode(!0),a.parentNode.replaceChild(r,a),r.classList.add("ck-replaced"),t.MakeSWCanvasSurface(r))},t.MakeCanvasSurface=t.MakeWebGLCanvasSurface,t.Surface.prototype.makeImageFromTexture=function(e,n){return t.Fd(this.Ed),e=r(e),(n=this._makeImageFromTexture(this.Ed,e,n))&&(n.de=e),n},t.Surface.prototype.makeImageFromTextureSource=function(e,r,u){r||(r={height:n(e),width:a(e),colorType:t.ColorType.RGBA_8888,alphaType:u?t.AlphaType.Premul:t.AlphaType.Unpremul}),r.colorSpace||(r.colorSpace=t.ColorSpace.SRGB),t.Fd(this.Ed);var c=ty.Qd;return u=o(c,c.createTexture(),r,u),2===ty.version?c.texImage2D(c.TEXTURE_2D,0,c.RGBA,r.width,r.height,0,c.RGBA,c.UNSIGNED_BYTE,e):c.texImage2D(c.TEXTURE_2D,0,c.RGBA,c.RGBA,c.UNSIGNED_BYTE,e),i(c,r),this._resetContext(),this.makeImageFromTexture(u,r)},t.Surface.prototype.updateTextureFromSource=function(e,u,c){if(e.de){t.Fd(this.Ed);var l=e.getImageInfo(),f=ty.Qd,s=o(f,tr[e.de],l,c);2===ty.version?f.texImage2D(f.TEXTURE_2D,0,f.RGBA,a(u),n(u),0,f.RGBA,f.UNSIGNED_BYTE,u):f.texImage2D(f.TEXTURE_2D,0,f.RGBA,f.RGBA,f.UNSIGNED_BYTE,u),i(f,l,c),this._resetContext(),tr[e.de]=null,e.de=r(s),l.colorSpace=e.getColorSpace(),u=this._makeImageFromTexture(this.Ed,e.de,l),c=e.Dd.Hd,f=e.Dd.Ld,e.Dd.Hd=u.Dd.Hd,e.Dd.Ld=u.Dd.Ld,u.Dd.Hd=c,u.Dd.Ld=f,u.delete(),l.colorSpace.delete()}},t.MakeLazyImageFromTextureSource=function(e,u,c){u||(u={height:n(e),width:a(e),colorType:t.ColorType.RGBA_8888,alphaType:c?t.AlphaType.Premul:t.AlphaType.Unpremul}),u.colorSpace||(u.colorSpace=t.ColorSpace.SRGB);var l={makeTexture:function(){var t=ty,n=t.Qd,a=o(n,n.createTexture(),u,c);return 2===t.version?n.texImage2D(n.TEXTURE_2D,0,n.RGBA,u.width,u.height,0,n.RGBA,n.UNSIGNED_BYTE,e):n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,e),i(n,u,c),r(a)},freeSrc:function(){}};return"VideoFrame"===e.constructor.name&&(l.freeSrc=function(){e.close()}),t.Image._makeFromGenerator(u,l)},t.Fd=function(e){return!!e&&th(e)},t.je=function(){return ty&&ty.te&&!ty.te.isDeleted()?ty.te:null}}),function(e){function r(e){return(o(255*e[3])<<24|o(255*e[0])<<16|o(255*e[1])<<8|(0|o(255*e[2])))>>>0}function n(e){if(e&&e._ck)return e;if(e instanceof Float32Array){for(var t=Math.floor(e.length/4),n=new Uint32Array(t),a=0;a<t;a++)n[a]=r(e.slice(4*a,4*(a+1)));return n}return e instanceof Uint32Array?e:e instanceof Array&&e[0]instanceof Float32Array?e.map(r):void 0}function a(e){if(void 0===e)return 1;var t=parseFloat(e);return e&&-1!==e.indexOf("%")?t/100:t}function o(e){return Math.round(Math.max(0,Math.min(e||0,255)))}function i(t,r){r&&r._ck||e._free(t)}function u(t,r,n){if(!t||!t.length)return R;if(t&&t._ck)return t.byteOffset;var a=e[r].BYTES_PER_ELEMENT;return n||(n=e._malloc(t.length*a)),e[r].set(t,n/a),n}function c(t){var r={Nd:R,count:t.length,colorType:e.ColorType.RGBA_F32};if(t instanceof Float32Array)r.Nd=u(t,"HEAPF32"),r.count=t.length/4;else if(t instanceof Uint32Array)r.Nd=u(t,"HEAPU32"),r.colorType=e.ColorType.RGBA_8888;else if(t instanceof Array){if(t&&t.length){for(var n=e._malloc(16*t.length),a=0,o=n/4,i=0;i<t.length;i++)for(var c=0;4>c;c++)e.HEAPF32[o+a]=t[i][c],a++;t=n}else t=R;r.Nd=t}else throw"Invalid argument to copyFlexibleColorArray, Not a color array "+typeof t;return r}function l(t){if(!t)return R;var r=y.toTypedArray();if(t.length){if(6===t.length||9===t.length)return u(t,"HEAPF32",b),6===t.length&&e.HEAPF32.set(M,6+b/4),b;if(16===t.length)return r[0]=t[0],r[1]=t[1],r[2]=t[3],r[3]=t[4],r[4]=t[5],r[5]=t[7],r[6]=t[12],r[7]=t[13],r[8]=t[15],b;throw"invalid matrix size"}if(void 0===t.m11)throw"invalid matrix argument";return r[0]=t.m11,r[1]=t.m21,r[2]=t.m41,r[3]=t.m12,r[4]=t.m22,r[5]=t.m42,r[6]=t.m14,r[7]=t.m24,r[8]=t.m44,b}function f(e,t){return u(e,"HEAPF32",t||A)}function s(e,t,r,n){var a=m.toTypedArray();return a[0]=e,a[1]=t,a[2]=r,a[3]=n,A}function d(t){for(var r=new Float32Array(4),n=0;4>n;n++)r[n]=e.HEAPF32[t/4+n];return r}function h(e,t){return u(e,"HEAPF32",t||E)}function p(e,t){return u(e,"HEAPF32",t||x)}e.Color=function(t,r,n,a){return void 0===a&&(a=1),e.Color4f(o(t)/255,o(r)/255,o(n)/255,a)},e.ColorAsInt=function(e,t,r,n){return void 0===n&&(n=255),(o(n)<<24|o(e)<<16|o(t)<<8|(0|o(r))&0xfffffff)>>>0},e.Color4f=function(e,t,r,n){return void 0===n&&(n=1),Float32Array.of(e,t,r,n)},Object.defineProperty(e,"TRANSPARENT",{get:function(){return e.Color4f(0,0,0,0)}}),Object.defineProperty(e,"BLACK",{get:function(){return e.Color4f(0,0,0,1)}}),Object.defineProperty(e,"WHITE",{get:function(){return e.Color4f(1,1,1,1)}}),Object.defineProperty(e,"RED",{get:function(){return e.Color4f(1,0,0,1)}}),Object.defineProperty(e,"GREEN",{get:function(){return e.Color4f(0,1,0,1)}}),Object.defineProperty(e,"BLUE",{get:function(){return e.Color4f(0,0,1,1)}}),Object.defineProperty(e,"YELLOW",{get:function(){return e.Color4f(1,1,0,1)}}),Object.defineProperty(e,"CYAN",{get:function(){return e.Color4f(0,1,1,1)}}),Object.defineProperty(e,"MAGENTA",{get:function(){return e.Color4f(1,0,1,1)}}),e.getColorComponents=function(e){return[Math.floor(255*e[0]),Math.floor(255*e[1]),Math.floor(255*e[2]),e[3]]},e.parseColorString=function(t,r){if((t=t.toLowerCase()).startsWith("#")){switch(r=255,t.length){case 9:r=parseInt(t.slice(7,9),16);case 7:var n=parseInt(t.slice(1,3),16),o=parseInt(t.slice(3,5),16),i=parseInt(t.slice(5,7),16);break;case 5:r=17*parseInt(t.slice(4,5),16);case 4:n=17*parseInt(t.slice(1,2),16),o=17*parseInt(t.slice(2,3),16),i=17*parseInt(t.slice(3,4),16)}return e.Color(n,o,i,r/255)}return t.startsWith("rgba")?(t=(t=t.slice(5,-1)).split(","),e.Color(+t[0],+t[1],+t[2],a(t[3]))):t.startsWith("rgb")?(t=(t=t.slice(4,-1)).split(","),e.Color(+t[0],+t[1],+t[2],a(t[3]))):t.startsWith("gray(")||t.startsWith("hsl")||!r||void 0===(t=r[t])?e.BLACK:t},e.multiplyByAlpha=function(e,t){return(e=e.slice())[3]=Math.max(0,Math.min(e[3]*t,1)),e},e.Malloc=function(t,r){var n=e._malloc(r*t.BYTES_PER_ELEMENT);return{_ck:!0,length:r,byteOffset:n,Xd:null,subarray:function(e,t){return(e=this.toTypedArray().subarray(e,t))._ck=!0,e},toTypedArray:function(){return this.Xd&&this.Xd.length||(this.Xd=new t(e.HEAPU8.buffer,n,r),this.Xd._ck=!0),this.Xd}}},e.Free=function(t){e._free(t.byteOffset),t.byteOffset=R,t.toTypedArray=null,t.Xd=null};var y,g,m,v,_,b=R,P=R,A=R,E=R,C=R,T=R,F=R,S=R,x=R,k=R,M=Float32Array.of(0,0,1),R=0;e.onRuntimeInitialized=function(){function t(t,r,n,a,o,i,u){i||(i=4*a.width,a.colorType===e.ColorType.RGBA_F16?i*=2:a.colorType===e.ColorType.RGBA_F32&&(i*=4));var c=i*a.height,l=o?o.byteOffset:e._malloc(c);if(u?!t._readPixels(a,l,i,r,n,u):!t._readPixels(a,l,i,r,n))return o||e._free(l),null;if(o)return o.toTypedArray();switch(a.colorType){case e.ColorType.RGBA_8888:case e.ColorType.RGBA_F16:t=new Uint8Array(e.HEAPU8.buffer,l,c).slice();break;case e.ColorType.RGBA_F32:t=new Float32Array(e.HEAPU8.buffer,l,c).slice();break;default:return null}return e._free(l),t}A=(m=e.Malloc(Float32Array,4)).byteOffset,P=(g=e.Malloc(Float32Array,16)).byteOffset,b=(y=e.Malloc(Float32Array,9)).byteOffset,x=e.Malloc(Float32Array,12).byteOffset,k=e.Malloc(Float32Array,12).byteOffset,E=(v=e.Malloc(Float32Array,4)).byteOffset,C=e.Malloc(Float32Array,4).byteOffset,T=e.Malloc(Float32Array,3).byteOffset,F=e.Malloc(Float32Array,3).byteOffset,S=(_=e.Malloc(Int32Array,4)).byteOffset,e.ColorSpace.SRGB=e.ColorSpace._MakeSRGB(),e.ColorSpace.DISPLAY_P3=e.ColorSpace._MakeDisplayP3(),e.ColorSpace.ADOBE_RGB=e.ColorSpace._MakeAdobeRGB(),e.GlyphRunFlags={IsWhiteSpace:e._GlyphRunFlags_isWhiteSpace},e.Path.MakeFromCmds=function(t){var r=u(t,"HEAPF32"),n=e.Path._MakeFromCmds(r,t.length);return i(r,t),n},e.Path.MakeFromVerbsPointsWeights=function(t,r,n){var a=u(t,"HEAPU8"),o=u(r,"HEAPF32"),c=u(n,"HEAPF32"),l=e.Path._MakeFromVerbsPointsWeights(a,t.length,o,r.length,c,n&&n.length||0);return i(a,t),i(o,r),i(c,n),l},e.Path.prototype.addArc=function(e,t,r){return e=h(e),this._addArc(e,t,r),this},e.Path.prototype.addCircle=function(e,t,r,n){return this._addCircle(e,t,r,!!n),this},e.Path.prototype.addOval=function(e,t,r){return void 0===r&&(r=1),e=h(e),this._addOval(e,!!t,r),this},e.Path.prototype.addPath=function(){var e=Array.prototype.slice.call(arguments),t=e[0],r=!1;if("boolean"==typeof e[e.length-1]&&(r=e.pop()),1===e.length)this._addPath(t,1,0,0,0,1,0,0,0,1,r);else if(2===e.length)e=e[1],this._addPath(t,e[0],e[1],e[2],e[3],e[4],e[5],e[6]||0,e[7]||0,e[8]||1,r);else{if(7!==e.length&&10!==e.length)return null;this._addPath(t,e[1],e[2],e[3],e[4],e[5],e[6],e[7]||0,e[8]||0,e[9]||1,r)}return this},e.Path.prototype.addPoly=function(e,t){var r=u(e,"HEAPF32");return this._addPoly(r,e.length/2,t),i(r,e),this},e.Path.prototype.addRect=function(e,t){return e=h(e),this._addRect(e,!!t),this},e.Path.prototype.addRRect=function(e,t){return e=p(e),this._addRRect(e,!!t),this},e.Path.prototype.addVerbsPointsWeights=function(e,t,r){var n=u(e,"HEAPU8"),a=u(t,"HEAPF32"),o=u(r,"HEAPF32");this._addVerbsPointsWeights(n,e.length,a,t.length,o,r&&r.length||0),i(n,e),i(a,t),i(o,r)},e.Path.prototype.arc=function(t,r,n,a,o,i){return t=e.LTRBRect(t-n,r-n,t+n,r+n),o=(o-a)/Math.PI*180-360*!!i,(i=new e.Path).addArc(t,a/Math.PI*180,o),this.addPath(i,!0),i.delete(),this},e.Path.prototype.arcToOval=function(e,t,r,n){return e=h(e),this._arcToOval(e,t,r,n),this},e.Path.prototype.arcToRotated=function(e,t,r,n,a,o,i){return this._arcToRotated(e,t,r,!!n,!!a,o,i),this},e.Path.prototype.arcToTangent=function(e,t,r,n,a){return this._arcToTangent(e,t,r,n,a),this},e.Path.prototype.close=function(){return this._close(),this},e.Path.prototype.conicTo=function(e,t,r,n,a){return this._conicTo(e,t,r,n,a),this},e.Path.prototype.computeTightBounds=function(e){this._computeTightBounds(E);var t=v.toTypedArray();return e?(e.set(t),e):t.slice()},e.Path.prototype.cubicTo=function(e,t,r,n,a,o){return this._cubicTo(e,t,r,n,a,o),this},e.Path.prototype.dash=function(e,t,r){return this._dash(e,t,r)?this:null},e.Path.prototype.getBounds=function(e){this._getBounds(E);var t=v.toTypedArray();return e?(e.set(t),e):t.slice()},e.Path.prototype.lineTo=function(e,t){return this._lineTo(e,t),this},e.Path.prototype.moveTo=function(e,t){return this._moveTo(e,t),this},e.Path.prototype.offset=function(e,t){return this._transform(1,0,e,0,1,t,0,0,1),this},e.Path.prototype.quadTo=function(e,t,r,n){return this._quadTo(e,t,r,n),this},e.Path.prototype.rArcTo=function(e,t,r,n,a,o,i){return this._rArcTo(e,t,r,n,a,o,i),this},e.Path.prototype.rConicTo=function(e,t,r,n,a){return this._rConicTo(e,t,r,n,a),this},e.Path.prototype.rCubicTo=function(e,t,r,n,a,o){return this._rCubicTo(e,t,r,n,a,o),this},e.Path.prototype.rLineTo=function(e,t){return this._rLineTo(e,t),this},e.Path.prototype.rMoveTo=function(e,t){return this._rMoveTo(e,t),this},e.Path.prototype.rQuadTo=function(e,t,r,n){return this._rQuadTo(e,t,r,n),this},e.Path.prototype.stroke=function(t){return(t=t||{}).width=t.width||1,t.miter_limit=t.miter_limit||4,t.cap=t.cap||e.StrokeCap.Butt,t.join=t.join||e.StrokeJoin.Miter,t.precision=t.precision||1,this._stroke(t)?this:null},e.Path.prototype.transform=function(){if(1==arguments.length){var e=arguments[0];this._transform(e[0],e[1],e[2],e[3],e[4],e[5],e[6]||0,e[7]||0,e[8]||1)}else if(6==arguments.length||9==arguments.length)e=arguments,this._transform(e[0],e[1],e[2],e[3],e[4],e[5],e[6]||0,e[7]||0,e[8]||1);else throw"transform expected to take 1 or 9 arguments. Got "+arguments.length;return this},e.Path.prototype.trim=function(e,t,r){return this._trim(e,t,!!r)?this:null},e.Image.prototype.encodeToBytes=function(t,r){var n=e.je();return t=t||e.ImageFormat.PNG,r=r||100,n?this._encodeToBytes(t,r,n):this._encodeToBytes(t,r)},e.Image.prototype.makeShaderCubic=function(e,t,r,n,a){return a=l(a),this._makeShaderCubic(e,t,r,n,a)},e.Image.prototype.makeShaderOptions=function(e,t,r,n,a){return a=l(a),this._makeShaderOptions(e,t,r,n,a)},e.Image.prototype.readPixels=function(r,n,a,o,i){return t(this,r,n,a,o,i,e.je())},e.Canvas.prototype.clear=function(t){e.Fd(this.Ed),t=f(t),this._clear(t)},e.Canvas.prototype.clipRRect=function(t,r,n){e.Fd(this.Ed),t=p(t),this._clipRRect(t,r,n)},e.Canvas.prototype.clipRect=function(t,r,n){e.Fd(this.Ed),t=h(t),this._clipRect(t,r,n)},e.Canvas.prototype.concat=function(t){e.Fd(this.Ed),t=function(e){if(!e)return R;var t=g.toTypedArray();if(e.length){if(16!==e.length&&6!==e.length&&9!==e.length)throw"invalid matrix size";return 16===e.length?u(e,"HEAPF32",P):(t.fill(0),t[0]=e[0],t[1]=e[1],t[3]=e[2],t[4]=e[3],t[5]=e[4],t[7]=e[5],t[10]=1,t[12]=e[6],t[13]=e[7],t[15]=e[8],6===e.length&&(t[12]=0,t[13]=0,t[15]=1),P)}if(void 0===e.m11)throw"invalid matrix argument";return t[0]=e.m11,t[1]=e.m21,t[2]=e.m31,t[3]=e.m41,t[4]=e.m12,t[5]=e.m22,t[6]=e.m32,t[7]=e.m42,t[8]=e.m13,t[9]=e.m23,t[10]=e.m33,t[11]=e.m43,t[12]=e.m14,t[13]=e.m24,t[14]=e.m34,t[15]=e.m44,P}(t),this._concat(t)},e.Canvas.prototype.drawArc=function(t,r,n,a,o){e.Fd(this.Ed),t=h(t),this._drawArc(t,r,n,a,o)},e.Canvas.prototype.drawAtlas=function(t,r,a,o,c,l,f){if(t&&o&&r&&a&&r.length===a.length){e.Fd(this.Ed),c||(c=e.BlendMode.SrcOver);var s=u(r,"HEAPF32"),d=u(a,"HEAPF32"),h=a.length/4,p=u(n(l),"HEAPU32");if(f&&"B"in f&&"C"in f)this._drawAtlasCubic(t,d,s,p,h,c,f.B,f.C,o);else{let r=e.FilterMode.Linear,n=e.MipmapMode.None;f&&(r=f.filter,"mipmap"in f&&(n=f.mipmap)),this._drawAtlasOptions(t,d,s,p,h,c,r,n,o)}i(s,r),i(d,a),i(p,l)}},e.Canvas.prototype.drawCircle=function(t,r,n,a){e.Fd(this.Ed),this._drawCircle(t,r,n,a)},e.Canvas.prototype.drawColor=function(t,r){e.Fd(this.Ed),t=f(t),void 0!==r?this._drawColor(t,r):this._drawColor(t)},e.Canvas.prototype.drawColorInt=function(t,r){e.Fd(this.Ed),this._drawColorInt(t,r||e.BlendMode.SrcOver)},e.Canvas.prototype.drawColorComponents=function(t,r,n,a,o){e.Fd(this.Ed),t=s(t,r,n,a),void 0!==o?this._drawColor(t,o):this._drawColor(t)},e.Canvas.prototype.drawDRRect=function(t,r,n){e.Fd(this.Ed),t=p(t,x),r=p(r,k),this._drawDRRect(t,r,n)},e.Canvas.prototype.drawImage=function(t,r,n,a){e.Fd(this.Ed),this._drawImage(t,r,n,a||null)},e.Canvas.prototype.drawImageCubic=function(t,r,n,a,o,i){e.Fd(this.Ed),this._drawImageCubic(t,r,n,a,o,i||null)},e.Canvas.prototype.drawImageOptions=function(t,r,n,a,o,i){e.Fd(this.Ed),this._drawImageOptions(t,r,n,a,o,i||null)},e.Canvas.prototype.drawImageNine=function(t,r,n,a,o){e.Fd(this.Ed),r=u(r,"HEAP32",S),n=h(n),this._drawImageNine(t,r,n,a,o||null)},e.Canvas.prototype.drawImageRect=function(t,r,n,a,o){e.Fd(this.Ed),h(r,E),h(n,C),this._drawImageRect(t,E,C,a,!!o)},e.Canvas.prototype.drawImageRectCubic=function(t,r,n,a,o,i){e.Fd(this.Ed),h(r,E),h(n,C),this._drawImageRectCubic(t,E,C,a,o,i||null)},e.Canvas.prototype.drawImageRectOptions=function(t,r,n,a,o,i){e.Fd(this.Ed),h(r,E),h(n,C),this._drawImageRectOptions(t,E,C,a,o,i||null)},e.Canvas.prototype.drawLine=function(t,r,n,a,o){e.Fd(this.Ed),this._drawLine(t,r,n,a,o)},e.Canvas.prototype.drawOval=function(t,r){e.Fd(this.Ed),t=h(t),this._drawOval(t,r)},e.Canvas.prototype.drawPaint=function(t){e.Fd(this.Ed),this._drawPaint(t)},e.Canvas.prototype.drawParagraph=function(t,r,n){e.Fd(this.Ed),this._drawParagraph(t,r,n)},e.Canvas.prototype.drawPatch=function(t,r,a,o,c){if(24>t.length)throw"Need 12 cubic points";if(r&&4>r.length)throw"Need 4 colors";if(a&&8>a.length)throw"Need 4 shader coordinates";e.Fd(this.Ed);let l=u(t,"HEAPF32"),f=r?u(n(r),"HEAPU32"):R,s=a?u(a,"HEAPF32"):R;o||(o=e.BlendMode.Modulate),this._drawPatch(l,f,s,o,c),i(s,a),i(f,r),i(l,t)},e.Canvas.prototype.drawPath=function(t,r){e.Fd(this.Ed),this._drawPath(t,r)},e.Canvas.prototype.drawPicture=function(t){e.Fd(this.Ed),this._drawPicture(t)},e.Canvas.prototype.drawPoints=function(t,r,n){e.Fd(this.Ed);var a=u(r,"HEAPF32");this._drawPoints(t,a,r.length/2,n),i(a,r)},e.Canvas.prototype.drawRRect=function(t,r){e.Fd(this.Ed),t=p(t),this._drawRRect(t,r)},e.Canvas.prototype.drawRect=function(t,r){e.Fd(this.Ed),t=h(t),this._drawRect(t,r)},e.Canvas.prototype.drawRect4f=function(t,r,n,a,o){e.Fd(this.Ed),this._drawRect4f(t,r,n,a,o)},e.Canvas.prototype.drawShadow=function(t,r,n,a,o,c,l){e.Fd(this.Ed);var f=u(o,"HEAPF32"),s=u(c,"HEAPF32");r=u(r,"HEAPF32",T),n=u(n,"HEAPF32",F),this._drawShadow(t,r,n,a,f,s,l),i(f,o),i(s,c)},e.getShadowLocalBounds=function(e,t,r,n,a,o,i){return e=l(e),r=u(r,"HEAPF32",T),n=u(n,"HEAPF32",F),this._getShadowLocalBounds(e,t,r,n,a,o,E)?(t=v.toTypedArray(),i?(i.set(t),i):t.slice()):null},e.Canvas.prototype.drawTextBlob=function(t,r,n,a){e.Fd(this.Ed),this._drawTextBlob(t,r,n,a)},e.Canvas.prototype.drawVertices=function(t,r,n){e.Fd(this.Ed),this._drawVertices(t,r,n)},e.Canvas.prototype.getDeviceClipBounds=function(e){this._getDeviceClipBounds(S);var t=_.toTypedArray();return e?e.set(t):e=t.slice(),e},e.Canvas.prototype.getLocalToDevice=function(){this._getLocalToDevice(P);for(var t=P,r=Array(16),n=0;16>n;n++)r[n]=e.HEAPF32[t/4+n];return r},e.Canvas.prototype.getTotalMatrix=function(){this._getTotalMatrix(b);for(var t=Array(9),r=0;9>r;r++)t[r]=e.HEAPF32[b/4+r];return t},e.Canvas.prototype.makeSurface=function(e){return(e=this._makeSurface(e)).Ed=this.Ed,e},e.Canvas.prototype.readPixels=function(r,n,a,o,i){return e.Fd(this.Ed),t(this,r,n,a,o,i)},e.Canvas.prototype.saveLayer=function(e,t,r,n){return t=h(t),this._saveLayer(e||null,t,r||null,n||0)},e.Canvas.prototype.writePixels=function(t,r,n,a,o,c,l,f){if(t.byteLength%(r*n))throw"pixels length must be a multiple of the srcWidth * srcHeight";e.Fd(this.Ed);var s=t.byteLength/(r*n);c=c||e.AlphaType.Unpremul,l=l||e.ColorType.RGBA_8888,f=f||e.ColorSpace.SRGB;var d=s*r;return s=u(t,"HEAPU8"),r=this._writePixels({width:r,height:n,colorType:l,alphaType:c,colorSpace:f},s,d,a,o),i(s,t),r},e.ColorFilter.MakeBlend=function(t,r,n){return t=f(t),n=n||e.ColorSpace.SRGB,e.ColorFilter._MakeBlend(t,r,n)},e.ColorFilter.MakeMatrix=function(t){if(!t||20!==t.length)throw"invalid color matrix";var r=u(t,"HEAPF32"),n=e.ColorFilter._makeMatrix(r);return i(r,t),n},e.ContourMeasure.prototype.getPosTan=function(e,t){return this._getPosTan(e,E),e=v.toTypedArray(),t?(t.set(e),t):e.slice()},e.ImageFilter.prototype.getOutputBounds=function(e,t,r){return e=h(e,E),t=l(t),this._getOutputBounds(e,t,S),t=_.toTypedArray(),r?(r.set(t),r):t.slice()},e.ImageFilter.MakeDropShadow=function(t,r,n,a,o,i){return o=f(o,A),e.ImageFilter._MakeDropShadow(t,r,n,a,o,i)},e.ImageFilter.MakeDropShadowOnly=function(t,r,n,a,o,i){return o=f(o,A),e.ImageFilter._MakeDropShadowOnly(t,r,n,a,o,i)},e.ImageFilter.MakeImage=function(t,r,n,a){if(n=h(n,E),a=h(a,C),"B"in r&&"C"in r)return e.ImageFilter._MakeImageCubic(t,r.B,r.C,n,a);let o=r.filter,i=e.MipmapMode.None;return"mipmap"in r&&(i=r.mipmap),e.ImageFilter._MakeImageOptions(t,o,i,n,a)},e.ImageFilter.MakeMatrixTransform=function(t,r,n){if(t=l(t),"B"in r&&"C"in r)return e.ImageFilter._MakeMatrixTransformCubic(t,r.B,r.C,n);let a=r.filter,o=e.MipmapMode.None;return"mipmap"in r&&(o=r.mipmap),e.ImageFilter._MakeMatrixTransformOptions(t,a,o,n)},e.Paint.prototype.getColor=function(){return this._getColor(A),d(A)},e.Paint.prototype.setColor=function(e,t){t=t||null,e=f(e),this._setColor(e,t)},e.Paint.prototype.setColorComponents=function(e,t,r,n,a){a=a||null,e=s(e,t,r,n),this._setColor(e,a)},e.Path.prototype.getPoint=function(e,t){return this._getPoint(e,E),e=v.toTypedArray(),t?(t[0]=e[0],t[1]=e[1],t):e.slice(0,2)},e.Picture.prototype.makeShader=function(e,t,r,n,a){return n=l(n),a=h(a),this._makeShader(e,t,r,n,a)},e.Picture.prototype.cullRect=function(e){this._cullRect(E);var t=v.toTypedArray();return e?(e.set(t),e):t.slice()},e.PictureRecorder.prototype.beginRecording=function(e,t){return e=h(e),this._beginRecording(e,!!t)},e.Surface.prototype.getCanvas=function(){var e=this._getCanvas();return e.Ed=this.Ed,e},e.Surface.prototype.makeImageSnapshot=function(t){return e.Fd(this.Ed),t=u(t,"HEAP32",S),this._makeImageSnapshot(t)},e.Surface.prototype.makeSurface=function(t){return e.Fd(this.Ed),(t=this._makeSurface(t)).Ed=this.Ed,t},e.Surface.prototype.Oe=function(t,r){return this.ce||(this.ce=this.getCanvas()),requestAnimationFrame((function(){e.Fd(this.Ed),t(this.ce),this.flush(r)}).bind(this))},e.Surface.prototype.requestAnimationFrame||(e.Surface.prototype.requestAnimationFrame=e.Surface.prototype.Oe),e.Surface.prototype.Le=function(t,r){this.ce||(this.ce=this.getCanvas()),requestAnimationFrame((function(){e.Fd(this.Ed),t(this.ce),this.flush(r),this.dispose()}).bind(this))},e.Surface.prototype.drawOnce||(e.Surface.prototype.drawOnce=e.Surface.prototype.Le),e.PathEffect.MakeDash=function(t,r){if(r||(r=0),!t.length||t.length%2==1)throw"Intervals array must have even length";var n=u(t,"HEAPF32");return r=e.PathEffect._MakeDash(n,t.length,r),i(n,t),r},e.PathEffect.MakeLine2D=function(t,r){return r=l(r),e.PathEffect._MakeLine2D(t,r)},e.PathEffect.MakePath2D=function(t,r){return t=l(t),e.PathEffect._MakePath2D(t,r)},e.Shader.MakeColor=function(t,r){return r=r||null,t=f(t),e.Shader._MakeColor(t,r)},e.Shader.Blend=e.Shader.MakeBlend,e.Shader.Color=e.Shader.MakeColor,e.Shader.MakeLinearGradient=function(t,r,n,a,o,f,s,d){d=d||null;var h=c(n),p=u(a,"HEAPF32");s=s||0,f=l(f);var y=v.toTypedArray();return y.set(t),y.set(r,2),t=e.Shader._MakeLinearGradient(E,h.Nd,h.colorType,p,h.count,o,s,f,d),i(h.Nd,n),a&&i(p,a),t},e.Shader.MakeRadialGradient=function(t,r,n,a,o,f,s,d){d=d||null;var h=c(n),p=u(a,"HEAPF32");return s=s||0,f=l(f),t=e.Shader._MakeRadialGradient(t[0],t[1],r,h.Nd,h.colorType,p,h.count,o,s,f,d),i(h.Nd,n),a&&i(p,a),t},e.Shader.MakeSweepGradient=function(t,r,n,a,o,f,s,d,h,p){p=p||null;var y=c(n),g=u(a,"HEAPF32");return s=s||0,d=d||0,h=h||360,f=l(f),t=e.Shader._MakeSweepGradient(t,r,y.Nd,y.colorType,g,y.count,o,d,h,s,f,p),i(y.Nd,n),a&&i(g,a),t},e.Shader.MakeTwoPointConicalGradient=function(t,r,n,a,o,f,s,d,h,p){p=p||null;var y=c(o),g=u(f,"HEAPF32");h=h||0,d=l(d);var m=v.toTypedArray();return m.set(t),m.set(n,2),t=e.Shader._MakeTwoPointConicalGradient(E,r,a,y.Nd,y.colorType,g,y.count,s,h,d,p),i(y.Nd,o),f&&i(g,f),t},e.Vertices.prototype.bounds=function(e){this._bounds(E);var t=v.toTypedArray();return e?(e.set(t),e):t.slice()},e.Id&&e.Id.forEach(function(e){e()})},e.computeTonalColors=function(e){var t=u(e.ambient,"HEAPF32"),r=u(e.spot,"HEAPF32");this._computeTonalColors(t,r);var n={ambient:d(t),spot:d(r)};return i(t,e.ambient),i(r,e.spot),n},e.LTRBRect=function(e,t,r,n){return Float32Array.of(e,t,r,n)},e.XYWHRect=function(e,t,r,n){return Float32Array.of(e,t,e+r,t+n)},e.LTRBiRect=function(e,t,r,n){return Int32Array.of(e,t,r,n)},e.XYWHiRect=function(e,t,r,n){return Int32Array.of(e,t,e+r,t+n)},e.RRectXY=function(e,t,r){return Float32Array.of(e[0],e[1],e[2],e[3],t,r,t,r,t,r,t,r)},e.MakeAnimatedImageFromEncoded=function(t){t=new Uint8Array(t);var r=e._malloc(t.byteLength);return e.HEAPU8.set(t,r),(t=e._decodeAnimatedImage(r,t.byteLength))?t:null},e.MakeImageFromEncoded=function(t){t=new Uint8Array(t);var r=e._malloc(t.byteLength);return e.HEAPU8.set(t,r),(t=e._decodeImage(r,t.byteLength))?t:null};var D=null;e.MakeImageFromCanvasImageSource=function(t){var r=t.width,n=t.height;D||(D=document.createElement("canvas")),D.width=r,D.height=n;var a=D.getContext("2d",{willReadFrequently:!0});return a.drawImage(t,0,0),t=a.getImageData(0,0,r,n),e.MakeImage({width:r,height:n,alphaType:e.AlphaType.Unpremul,colorType:e.ColorType.RGBA_8888,colorSpace:e.ColorSpace.SRGB},t.data,4*r)},e.MakeImage=function(t,r,n){var a=e._malloc(r.length);return e.HEAPU8.set(r,a),e._MakeImage(t,a,r.length,n)},e.MakeVertices=function(t,r,a,o,i,c){var l=i&&i.length||0,f=0;return a&&a.length&&(f|=1),o&&o.length&&(f|=2),void 0===c||c||(f|=4),t=new e._VerticesBuilder(t,r.length/2,l,f),u(r,"HEAPF32",t.positions()),t.texCoords()&&u(a,"HEAPF32",t.texCoords()),t.colors()&&u(n(o),"HEAPU32",t.colors()),t.indices()&&u(i,"HEAPU16",t.indices()),t.detach()},t.Id=t.Id||[],t.Id.push(function(){function e(e){return e&&(e.dir=0===e.dir?t.TextDirection.RTL:t.TextDirection.LTR),e}function r(e){if(!e||!e.length)return[];for(var r=[],n=0;n<e.length;n+=5){var a=t.LTRBRect(e[n],e[n+1],e[n+2],e[n+3]),o=t.TextDirection.LTR;0===e[n+4]&&(o=t.TextDirection.RTL),r.push({rect:a,dir:o})}return t._free(e.byteOffset),r}function n(e){return void 0===(e=e||{}).weight&&(e.weight=t.FontWeight.Normal),e.width=e.width||t.FontWidth.Normal,e.slant=e.slant||t.FontSlant.Upright,e}function a(e){if(!e||!e.length)return R;for(var t=[],r=0;r<e.length;r++){var n=o(e[r]);t.push(n)}return u(t,"HEAPU32")}function o(e){if(d[e])return d[e];var r=eX(e)+1,n=t._malloc(r);return eY(e,w,n,r),d[e]=n}function l(e){if(e._colorPtr=f(e.color),e._foregroundColorPtr=R,e._backgroundColorPtr=R,e._decorationColorPtr=R,e.foregroundColor&&(e._foregroundColorPtr=f(e.foregroundColor,h)),e.backgroundColor&&(e._backgroundColorPtr=f(e.backgroundColor,p)),e.decorationColor&&(e._decorationColorPtr=f(e.decorationColor,y)),Array.isArray(e.fontFamilies)&&e.fontFamilies.length?(e._fontFamiliesPtr=a(e.fontFamilies),e._fontFamiliesLen=e.fontFamilies.length):(e._fontFamiliesPtr=R,e._fontFamiliesLen=0),e.locale){var r=e.locale;e._localePtr=o(r),e._localeLen=eX(r)+1}else e._localePtr=R,e._localeLen=0;if(Array.isArray(e.shadows)&&e.shadows.length){var n=(r=e.shadows).map(function(e){return e.color||t.BLACK}),i=r.map(function(e){return e.blurRadius||0});e._shadowLen=r.length;for(var l=t._malloc(8*r.length),s=l/4,d=0;d<r.length;d++){var g=r[d].offset||[0,0];t.HEAPF32[s]=g[0],t.HEAPF32[s+1]=g[1],s+=2}e._shadowColorsPtr=c(n).Nd,e._shadowOffsetsPtr=l,e._shadowBlurRadiiPtr=u(i,"HEAPF32")}else e._shadowLen=0,e._shadowColorsPtr=R,e._shadowOffsetsPtr=R,e._shadowBlurRadiiPtr=R;Array.isArray(e.fontFeatures)&&e.fontFeatures.length?(n=(r=e.fontFeatures).map(function(e){return e.name}),i=r.map(function(e){return e.value}),e._fontFeatureLen=r.length,e._fontFeatureNamesPtr=a(n),e._fontFeatureValuesPtr=u(i,"HEAPU32")):(e._fontFeatureLen=0,e._fontFeatureNamesPtr=R,e._fontFeatureValuesPtr=R),Array.isArray(e.fontVariations)&&e.fontVariations.length?(n=(r=e.fontVariations).map(function(e){return e.axis}),i=r.map(function(e){return e.value}),e._fontVariationLen=r.length,e._fontVariationAxesPtr=a(n),e._fontVariationValuesPtr=u(i,"HEAPF32")):(e._fontVariationLen=0,e._fontVariationAxesPtr=R,e._fontVariationValuesPtr=R)}function s(e){t._free(e._fontFamiliesPtr),t._free(e._shadowColorsPtr),t._free(e._shadowOffsetsPtr),t._free(e._shadowBlurRadiiPtr),t._free(e._fontFeatureNamesPtr),t._free(e._fontFeatureValuesPtr),t._free(e._fontVariationAxesPtr),t._free(e._fontVariationValuesPtr)}t.Paragraph.prototype.getRectsForRange=function(e,t,n,a){return r(e=this._getRectsForRange(e,t,n,a))},t.Paragraph.prototype.getRectsForPlaceholders=function(){return r(this._getRectsForPlaceholders())},t.Paragraph.prototype.getGlyphInfoAt=function(t){return e(this._getGlyphInfoAt(t))},t.Paragraph.prototype.getClosestGlyphInfoAtCoordinate=function(t,r){return e(this._getClosestGlyphInfoAtCoordinate(t,r))},t.TypefaceFontProvider.prototype.registerFont=function(e,r){if(!(e=t.Typeface.MakeFreeTypeFaceFromData(e)))return null;r=o(r),this._registerFont(e,r)},t.ParagraphStyle=function(e){if(e.disableHinting=e.disableHinting||!1,e.ellipsis){var r=e.ellipsis;e._ellipsisPtr=o(r),e._ellipsisLen=eX(r)+1}else e._ellipsisPtr=R,e._ellipsisLen=0;return null==e.heightMultiplier&&(e.heightMultiplier=-1),e.maxLines=e.maxLines||0,e.replaceTabCharacters=e.replaceTabCharacters||!1,(r=(r=e.strutStyle)||{}).strutEnabled=r.strutEnabled||!1,r.strutEnabled&&Array.isArray(r.fontFamilies)&&r.fontFamilies.length?(r._fontFamiliesPtr=a(r.fontFamilies),r._fontFamiliesLen=r.fontFamilies.length):(r._fontFamiliesPtr=R,r._fontFamiliesLen=0),r.fontStyle=n(r.fontStyle),null==r.fontSize&&(r.fontSize=-1),null==r.heightMultiplier&&(r.heightMultiplier=-1),r.halfLeading=r.halfLeading||!1,r.leading=r.leading||0,r.forceStrutHeight=r.forceStrutHeight||!1,e.strutStyle=r,e.textAlign=e.textAlign||t.TextAlign.Start,e.textDirection=e.textDirection||t.TextDirection.LTR,e.textHeightBehavior=e.textHeightBehavior||t.TextHeightBehavior.All,e.textStyle=t.TextStyle(e.textStyle),e.applyRoundingHack=!1!==e.applyRoundingHack,e},t.TextStyle=function(e){return e.color||(e.color=t.BLACK),e.decoration=e.decoration||0,e.decorationThickness=e.decorationThickness||0,e.decorationStyle=e.decorationStyle||t.DecorationStyle.Solid,e.textBaseline=e.textBaseline||t.TextBaseline.Alphabetic,null==e.fontSize&&(e.fontSize=-1),e.letterSpacing=e.letterSpacing||0,e.wordSpacing=e.wordSpacing||0,null==e.heightMultiplier&&(e.heightMultiplier=-1),e.halfLeading=e.halfLeading||!1,e.fontStyle=n(e.fontStyle),e};var d={},h=t._malloc(16),p=t._malloc(16),y=t._malloc(16);t.ParagraphBuilder.Make=function(e,r){return l(e.textStyle),r=t.ParagraphBuilder._Make(e,r),s(e.textStyle),r},t.ParagraphBuilder.MakeFromFontProvider=function(e,r){return l(e.textStyle),r=t.ParagraphBuilder._MakeFromFontProvider(e,r),s(e.textStyle),r},t.ParagraphBuilder.MakeFromFontCollection=function(e,r){return l(e.textStyle),r=t.ParagraphBuilder._MakeFromFontCollection(e,r),s(e.textStyle),r},t.ParagraphBuilder.ShapeText=function(e,r,n){let a=0;for(let e of r)a+=e.length;if(a!==e.length)throw"Accumulated block lengths must equal text.length";return t.ParagraphBuilder._ShapeText(e,r,n)},t.ParagraphBuilder.prototype.pushStyle=function(e){l(e),this._pushStyle(e),s(e)},t.ParagraphBuilder.prototype.pushPaintStyle=function(e,t,r){l(e),this._pushPaintStyle(e,t,r),s(e)},t.ParagraphBuilder.prototype.addPlaceholder=function(e,r,n,a,o){n=n||t.PlaceholderAlignment.Baseline,a=a||t.TextBaseline.Alphabetic,this._addPlaceholder(e||0,r||0,n,a,o||0)},t.ParagraphBuilder.prototype.setWordsUtf8=function(e){var t=u(e,"HEAPU32");this._setWordsUtf8(t,e&&e.length||0),i(t,e)},t.ParagraphBuilder.prototype.setWordsUtf16=function(e){var t=u(e,"HEAPU32");this._setWordsUtf16(t,e&&e.length||0),i(t,e)},t.ParagraphBuilder.prototype.setGraphemeBreaksUtf8=function(e){var t=u(e,"HEAPU32");this._setGraphemeBreaksUtf8(t,e&&e.length||0),i(t,e)},t.ParagraphBuilder.prototype.setGraphemeBreaksUtf16=function(e){var t=u(e,"HEAPU32");this._setGraphemeBreaksUtf16(t,e&&e.length||0),i(t,e)},t.ParagraphBuilder.prototype.setLineBreaksUtf8=function(e){var t=u(e,"HEAPU32");this._setLineBreaksUtf8(t,e&&e.length||0),i(t,e)},t.ParagraphBuilder.prototype.setLineBreaksUtf16=function(e){var t=u(e,"HEAPU32");this._setLineBreaksUtf16(t,e&&e.length||0),i(t,e)}}),e.Id=e.Id||[],e.Id.push(function(){e.Path.prototype.op=function(e,t){return this._op(e,t)?this:null},e.Path.prototype.simplify=function(){return this._simplify()?this:null}}),e.Id=e.Id||[],e.Id.push(function(){e.Canvas.prototype.drawText=function(t,r,n,a,o){var i=eX(t),u=e._malloc(i+1);eY(t,w,u,i+1),this._drawSimpleText(u,i,r,n,o,a),e._free(u)},e.Canvas.prototype.drawGlyphs=function(t,r,n,a,o,c){if(!(2*t.length<=r.length))throw"Not enough positions for the array of gyphs";e.Fd(this.Ed);let l=u(t,"HEAPU16"),f=u(r,"HEAPF32");this._drawGlyphs(t.length,l,f,n,a,o,c),i(f,r),i(l,t)},e.Font.prototype.getGlyphBounds=function(t,r,n){var a=u(t,"HEAPU16"),o=e._malloc(16*t.length);return this._getGlyphWidthBounds(a,t.length,R,o,r||null),r=new Float32Array(e.HEAPU8.buffer,o,4*t.length),i(a,t),n?(n.set(r),e._free(o),n):(t=Float32Array.from(r),e._free(o),t)},e.Font.prototype.getGlyphIDs=function(t,r,n){r||(r=t.length);var a=eX(t)+1,o=e._malloc(a);return eY(t,w,o,a),t=e._malloc(2*r),r=this._getGlyphIDs(o,a-1,r,t),e._free(o),0>r?(e._free(t),null):(o=new Uint16Array(e.HEAPU8.buffer,t,r),n?n.set(o):n=Uint16Array.from(o),e._free(t),n)},e.Font.prototype.getGlyphIntercepts=function(e,t,r,n){var a=u(e,"HEAPU16"),o=u(t,"HEAPF32");return this._getGlyphIntercepts(a,e.length,!(e&&e._ck),o,t.length,!(t&&t._ck),r,n)},e.Font.prototype.getGlyphWidths=function(t,r,n){var a=u(t,"HEAPU16"),o=e._malloc(4*t.length);return this._getGlyphWidthBounds(a,t.length,o,R,r||null),r=new Float32Array(e.HEAPU8.buffer,o,t.length),i(a,t),n?(n.set(r),e._free(o),n):(t=Float32Array.from(r),e._free(o),t)},e.FontMgr.FromData=function(){if(!arguments.length)return null;var t=arguments;if(1===t.length&&Array.isArray(t[0])&&(t=arguments[0]),!t.length)return null;for(var r=[],n=[],a=0;a<t.length;a++){var o=new Uint8Array(t[a]),i=u(o,"HEAPU8");r.push(i),n.push(o.byteLength)}return r=u(r,"HEAPU32"),n=u(n,"HEAPU32"),t=e.FontMgr._fromData(r,n,t.length),e._free(r),e._free(n),t},e.Typeface.MakeFreeTypeFaceFromData=function(t){var r=u(t=new Uint8Array(t),"HEAPU8");return(t=e.Typeface._MakeFreeTypeFaceFromData(r,t.byteLength))?t:null},e.Typeface.prototype.getGlyphIDs=function(t,r,n){r||(r=t.length);var a=eX(t)+1,o=e._malloc(a);return eY(t,w,o,a),t=e._malloc(2*r),r=this._getGlyphIDs(o,a-1,r,t),e._free(o),0>r?(e._free(t),null):(o=new Uint16Array(e.HEAPU8.buffer,t,r),n?n.set(o):n=Uint16Array.from(o),e._free(t),n)},e.TextBlob.MakeOnPath=function(t,r,n,a){if(t&&t.length&&r&&r.countPoints()){if(1===r.countPoints())return this.MakeFromText(t,n);a||(a=0);var o=n.getGlyphIDs(t);o=n.getGlyphWidths(o);var i=[];r=new e.ContourMeasureIter(r,!1,1);for(var u=r.next(),c=new Float32Array(4),l=0;l<t.length&&u;l++){var f=o[l];if((a+=f/2)>u.length()){if(u.delete(),!(u=r.next())){t=t.substring(0,l);break}a=f/2}u.getPosTan(a,c);var s=c[2],d=c[3];i.push(s,d,c[0]-f/2*s,c[1]-f/2*d),a+=f/2}return t=this.MakeFromRSXform(t,i,n),u&&u.delete(),r.delete(),t}},e.TextBlob.MakeFromRSXform=function(t,r,n){var a=eX(t)+1,o=e._malloc(a);return eY(t,w,o,a),t=u(r,"HEAPF32"),n=e.TextBlob._MakeFromRSXform(o,a-1,t,n),e._free(o),n||null},e.TextBlob.MakeFromRSXformGlyphs=function(t,r,n){var a=u(t,"HEAPU16");return r=u(r,"HEAPF32"),n=e.TextBlob._MakeFromRSXformGlyphs(a,2*t.length,r,n),i(a,t),n||null},e.TextBlob.MakeFromGlyphs=function(t,r){var n=u(t,"HEAPU16");return r=e.TextBlob._MakeFromGlyphs(n,2*t.length,r),i(n,t),r||null},e.TextBlob.MakeFromText=function(t,r){var n=eX(t)+1,a=e._malloc(n);return eY(t,w,a,n),t=e.TextBlob._MakeFromText(a,n-1,r),e._free(a),t||null},e.MallocGlyphIDs=function(t){return e.Malloc(Uint16Array,t)}}),e.Id=e.Id||[],e.Id.push(function(){e.MakePicture=function(t){t=new Uint8Array(t);var r=e._malloc(t.byteLength);return e.HEAPU8.set(t,r),(t=e._MakePicture(r,t.byteLength))?t:null}}),e.Id=e.Id||[],e.Id.push(function(){e.RuntimeEffect.Make=function(t,r){return e.RuntimeEffect._Make(t,{onError:r||function(e){console.log("RuntimeEffect error",e)}})},e.RuntimeEffect.MakeForBlender=function(t,r){return e.RuntimeEffect._MakeForBlender(t,{onError:r||function(e){console.log("RuntimeEffect error",e)}})},e.RuntimeEffect.prototype.makeShader=function(e,t){var r=!e._ck,n=u(e,"HEAPF32");return t=l(t),this._makeShader(n,4*e.length,r,t)},e.RuntimeEffect.prototype.makeShaderWithChildren=function(e,t,r){var n=!e._ck,a=u(e,"HEAPF32");r=l(r);for(var o=[],i=0;i<t.length;i++)o.push(t[i].Dd.Hd);return t=u(o,"HEAPU32"),this._makeShaderWithChildren(a,4*e.length,n,t,o.length,r)},e.RuntimeEffect.prototype.makeBlender=function(e){var t=!e._ck,r=u(e,"HEAPF32");return this._makeBlender(r,4*e.length,t)}})}(t);var r,o,i,u,c,l,f=Object.assign({},t),s="./this.program",d="object"==typeof window,h="function"==typeof importScripts,p="object"==typeof n&&"object"==typeof n.versions&&"string"==typeof n.versions.node,y="";if(p){var g=a("fs"),m=a("path");y=h?m.dirname(y)+"/":__dirname+"/",u=(e,t)=>(e=e.startsWith("file://")?new URL(e):m.normalize(e),g.readFileSync(e,t?void 0:"utf8")),l=e=>((e=u(e,!0)).buffer||(e=new Uint8Array(e)),e),c=function(e,t,r){let n=!(arguments.length>3)||void 0===arguments[3]||arguments[3];e=e.startsWith("file://")?new URL(e):m.normalize(e),g.readFile(e,n?void 0:"utf8",(e,a)=>{e?r(e):t(n?a.buffer:a)})},!t.thisProgram&&1<n.argv.length&&(s=n.argv[1].replace(/\\/g,"/")),n.argv.slice(2),t.inspect=()=>"[Emscripten Module object]"}else(d||h)&&(h?y=self.location.href:"u">typeof document&&document.currentScript&&(y=document.currentScript.src),e&&(y=e),y=0!==y.indexOf("blob:")?y.substr(0,y.replace(/[?#].*/,"").lastIndexOf("/")+1):"",u=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},h&&(l=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),c=(e,t,r)=>{var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=()=>{200==n.status||0==n.status&&n.response?t(n.response):r()},n.onerror=r,n.send(null)});var v=t.print||console.log.bind(console),_=t.printErr||console.error.bind(console);Object.assign(t,f),f=null,t.thisProgram&&(s=t.thisProgram),t.wasmBinary&&(b=t.wasmBinary),t.noExitRuntime,"object"!=typeof WebAssembly&&O("no native wasm support detected");var b,P,A,E,w,C,T,F,S,x,k,M=!1;function R(){var e=P.buffer;t.HEAP8=E=new Int8Array(e),t.HEAP16=C=new Int16Array(e),t.HEAP32=F=new Int32Array(e),t.HEAPU8=w=new Uint8Array(e),t.HEAPU16=T=new Uint16Array(e),t.HEAPU32=S=new Uint32Array(e),t.HEAPF32=x=new Float32Array(e),t.HEAPF64=k=new Float64Array(e)}var D,I=[],B=[],G=[],L=0,H=null,U=null;function O(e){throw t.onAbort&&t.onAbort(e),_(e="Aborted("+e+")"),M=!0,e=new WebAssembly.RuntimeError(e+". Build with -sASSERTIONS for more info."),i(e),e}function j(e){return e.startsWith("data:application/octet-stream;base64,")}if(!j(tp="canvaskit.wasm")){var W=tp;tp=t.locateFile?t.locateFile(W,y):y+W}function N(e){if(e==tp&&b)return new Uint8Array(b);if(l)return l(e);throw"both async and sync fetching of the wasm failed"}function V(e,t,r){return(function(e){if(!b&&(d||h)){if("function"==typeof fetch&&!e.startsWith("file://"))return fetch(e,{credentials:"same-origin"}).then(t=>{if(!t.ok)throw"failed to load wasm binary file at '"+e+"'";return t.arrayBuffer()}).catch(()=>N(e));if(c)return new Promise((t,r)=>{c(e,e=>t(new Uint8Array(e)),r)})}return Promise.resolve().then(()=>N(e))})(e).then(e=>WebAssembly.instantiate(e,t)).then(e=>e).then(r,e=>{_("failed to asynchronously prepare wasm: "+e),O(e)})}var $=e=>{for(;0<e.length;)e.shift()(t)},Y="u">typeof TextDecoder?new TextDecoder("utf8"):void 0,X=(e,t,r)=>{var n=t+r;for(r=t;e[r]&&!(r>=n);)++r;if(16<r-t&&e.buffer&&Y)return Y.decode(e.subarray(t,r));for(n="";t<r;){var a=e[t++];if(128&a){var o=63&e[t++];if((224&a)==192)n+=String.fromCharCode((31&a)<<6|o);else{var i=63&e[t++];65536>(a=(240&a)==224?(15&a)<<12|o<<6|i:(7&a)<<18|o<<12|i<<6|63&e[t++])?n+=String.fromCharCode(a):(a-=65536,n+=String.fromCharCode(55296|a>>10,56320|1023&a))}}else n+=String.fromCharCode(a)}return n},q={};function z(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function J(e){return this.fromWireType(F[e>>2])}var K={},Q={},Z={},ee=void 0;function et(e){throw new ee(e)}function er(e,t,r){function n(t){(t=r(t)).length!==e.length&&et("Mismatched type converter count");for(var n=0;n<e.length;++n)ec(e[n],t[n])}e.forEach(function(e){Z[e]=t});var a=Array(t.length),o=[],i=0;t.forEach((e,t)=>{Q.hasOwnProperty(e)?a[t]=Q[e]:(o.push(e),K.hasOwnProperty(e)||(K[e]=[]),K[e].push(()=>{a[t]=Q[e],++i===o.length&&n(a)}))}),0===o.length&&n(a)}function en(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw TypeError("Unknown type size: ".concat(e))}}var ea=void 0;function eo(e){for(var t="";w[e];)t+=ea[w[e++]];return t}var ei=void 0;function eu(e){throw new ei(e)}function ec(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!("argPackAdvance"in t))throw TypeError("registerType registeredInstance requires argPackAdvance");!function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};var n=t.name;if(e||eu('type "'.concat(n,'" must have a positive integer typeid pointer')),Q.hasOwnProperty(e)){if(r.af)return;eu("Cannot register type '".concat(n,"' twice"))}Q[e]=t,delete Z[e],K.hasOwnProperty(e)&&(t=K[e],delete K[e],t.forEach(e=>e()))}(e,t,r)}function el(e){eu(e.Dd.Jd.Gd.name+" instance already deleted")}var ef=!1;function es(){}function ed(e){--e.count.value,0===e.count.value&&(e.Ld?e.Pd.Td(e.Ld):e.Jd.Gd.Td(e.Hd))}var eh={},ep=[];function ey(){for(;ep.length;){var e=ep.pop();e.Dd.$d=!1,e.delete()}}var eg=void 0,em={};function ev(e,t){return t.Jd&&t.Hd||et("makeClassHandle requires ptr and ptrType"),!!t.Pd!=!!t.Ld&&et("Both smartPtrType and smartPtr must be specified"),t.count={value:1},e_(Object.create(e,{Dd:{value:t}}))}function e_(e){return typeof FinalizationRegistry>"u"?(e_=e=>e,e):(ef=new FinalizationRegistry(e=>{ed(e.Dd)}),e_=e=>{var t=e.Dd;return t.Ld&&ef.register(e,{Dd:t},e),e},es=e=>{ef.unregister(e)},e_(e))}function eb(){}function eP(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return 48<=t&&57>=t?"_".concat(e):e}function eA(e,t){return({[e=eP(e)]:function(){return t.apply(this,arguments)}})[e]}function eE(e,t,r){if(void 0===e[t].Kd){var n=e[t];e[t]=function(){return e[t].Kd.hasOwnProperty(arguments.length)||eu("Function '".concat(r,"' called with an invalid number of arguments (").concat(arguments.length,") - expects one of (").concat(e[t].Kd,")!")),e[t].Kd[arguments.length].apply(this,arguments)},e[t].Kd=[],e[t].Kd[n.Yd]=n}}function ew(e,r,n){t.hasOwnProperty(e)?((void 0===n||void 0!==t[e].Kd&&void 0!==t[e].Kd[n])&&eu("Cannot register public name '".concat(e,"' twice")),eE(t,e,e),t.hasOwnProperty(n)&&eu("Cannot register multiple overloads of a function with the same number of arguments (".concat(n,")!")),t[e].Kd[n]=r):(t[e]=r,void 0!==n&&(t[e].sf=n))}function eC(e,t,r,n,a,o,i,u){this.name=e,this.constructor=t,this.ae=r,this.Td=n,this.Md=a,this.We=o,this.ge=i,this.Te=u,this.ef=[]}function eT(e,t,r){for(;t!==r;)t.ge||eu("Expected null or instance of ".concat(r.name,", got an instance of ").concat(t.name)),e=t.ge(e),t=t.Md;return e}function eF(e,t){return null===t?(this.ue&&eu("null is not a valid ".concat(this.name)),0):(t.Dd||eu('Cannot pass "'.concat(e$(t),'" as a ').concat(this.name)),t.Dd.Hd||eu("Cannot pass deleted object as a pointer of type ".concat(this.name)),eT(t.Dd.Hd,t.Dd.Jd.Gd,this.Gd))}function eS(e,t){if(null===t){if(this.ue&&eu("null is not a valid ".concat(this.name)),this.le){var r=this.ve();return null!==e&&e.push(this.Td,r),r}return 0}if(t.Dd||eu('Cannot pass "'.concat(e$(t),'" as a ').concat(this.name)),t.Dd.Hd||eu("Cannot pass deleted object as a pointer of type ".concat(this.name)),!this.ke&&t.Dd.Jd.ke&&eu("Cannot convert argument of type ".concat(t.Dd.Pd?t.Dd.Pd.name:t.Dd.Jd.name," to parameter type ").concat(this.name)),r=eT(t.Dd.Hd,t.Dd.Jd.Gd,this.Gd),this.le)switch(void 0===t.Dd.Ld&&eu("Passing raw pointer to smart pointer is illegal"),this.kf){case 0:t.Dd.Pd===this?r=t.Dd.Ld:eu("Cannot convert argument of type ".concat(t.Dd.Pd?t.Dd.Pd.name:t.Dd.Jd.name," to parameter type ").concat(this.name));break;case 1:r=t.Dd.Ld;break;case 2:if(t.Dd.Pd===this)r=t.Dd.Ld;else{var n=t.clone();r=this.ff(r,eN(function(){n.delete()})),null!==e&&e.push(this.Td,r)}break;default:eu("Unsupporting sharing policy")}return r}function ex(e,t){return null===t?(this.ue&&eu("null is not a valid ".concat(this.name)),0):(t.Dd||eu('Cannot pass "'.concat(e$(t),'" as a ').concat(this.name)),t.Dd.Hd||eu("Cannot pass deleted object as a pointer of type ".concat(this.name)),t.Dd.Jd.ke&&eu("Cannot convert argument of type ".concat(t.Dd.Jd.name," to parameter type ").concat(this.name)),eT(t.Dd.Hd,t.Dd.Jd.Gd,this.Gd))}function ek(e,t,r,n,a,o,i,u,c,l,f){this.name=e,this.Gd=t,this.ue=r,this.ke=n,this.le=a,this.df=o,this.kf=i,this.Ee=u,this.ve=c,this.ff=l,this.Td=f,a||void 0!==t.Md?this.toWireType=eS:(this.toWireType=n?eF:ex,this.Od=null)}function eM(e,r,n){t.hasOwnProperty(e)||et("Replacing nonexistant public symbol"),void 0!==t[e].Kd&&void 0!==n?t[e].Kd[n]=r:(t[e]=r,t[e].Yd=n)}var eR=(e,r)=>{var n=[];return function(){if(n.length=0,Object.assign(n,arguments),e.includes("j")){var a=t["dynCall_"+e];a=n&&n.length?a.apply(null,[r].concat(n)):a.call(null,r)}else a=D.get(r).apply(null,n);return a}};function eD(e,t){var r=(e=eo(e)).includes("j")?eR(e,t):D.get(t);return"function"!=typeof r&&eu("unknown function pointer with signature ".concat(e,": ").concat(t)),r}var eI=void 0;function eB(e){var t=eo(e=tK(e));return tz(e),t}function eG(e,t){var r=[],n={};throw t.forEach(function e(t){n[t]||Q[t]||(Z[t]?Z[t].forEach(e):(r.push(t),n[t]=!0))}),new eI("".concat(e,": ")+r.map(eB).join([", "]))}function eL(e,t,r,n,a){var o=t.length;2>o&&eu("argTypes array size mismatch! Must at least get return value and 'this' types!");var i=null!==t[1]&&null!==r,u=!1;for(r=1;r<t.length;++r)if(null!==t[r]&&void 0===t[r].Od){u=!0;break}var c="void"!==t[0].name,l=o-2,f=Array(l),s=[],d=[];return function(){if(arguments.length!==l&&eu("function ".concat(e," called with ").concat(arguments.length," arguments, expected ").concat(l," args!")),d.length=0,s.length=i?2:1,s[0]=a,i){var r=t[1].toWireType(d,this);s[1]=r}for(var o=0;o<l;++o)f[o]=t[o+2].toWireType(d,arguments[o]),s.push(f[o]);if(o=n.apply(null,s),u)z(d);else for(var h=i?1:2;h<t.length;h++){var p=1===h?r:f[h-2];null!==t[h].Od&&t[h].Od(p)}return c?t[0].fromWireType(o):void 0}}function eH(e,t){for(var r=[],n=0;n<e;n++)r.push(S[t+4*n>>2]);return r}function eU(){this.Sd=[void 0],this.Ce=[]}var eO=new eU;function ej(e){e>=eO.be&&0==--eO.get(e).Fe&&eO.Je(e)}var eW=e=>(e||eu("Cannot use deleted val. handle = "+e),eO.get(e).value),eN=e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return eO.Ie({Fe:1,value:e})}};function eV(e,t){var r=Q[e];return void 0===r&&eu(t+" has unknown type "+eB(e)),r}function e$(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}var eY=(e,t,r,n)=>{if(!(0<n))return 0;var a=r;n=r+n-1;for(var o=0;o<e.length;++o){var i=e.charCodeAt(o);if(55296<=i&&57343>=i&&(i=65536+((1023&i)<<10)|1023&e.charCodeAt(++o)),127>=i){if(r>=n)break;t[r++]=i}else{if(2047>=i){if(r+1>=n)break;t[r++]=192|i>>6}else{if(65535>=i){if(r+2>=n)break;t[r++]=224|i>>12}else{if(r+3>=n)break;t[r++]=240|i>>18,t[r++]=128|i>>12&63}t[r++]=128|i>>6&63}t[r++]=128|63&i}}return t[r]=0,r-a},eX=e=>{for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);127>=n?t++:2047>=n?t+=2:55296<=n&&57343>=n?(t+=4,++r):t+=3}return t},eq="u">typeof TextDecoder?new TextDecoder("utf-16le"):void 0,ez=(e,t)=>{for(var r=e>>1,n=r+t/2;!(r>=n)&&T[r];)++r;if(32<(r<<=1)-e&&eq)return eq.decode(w.subarray(e,r));for(r="",n=0;!(n>=t/2);++n){var a=C[e+2*n>>1];if(0==a)break;r+=String.fromCharCode(a)}return r},eJ=(e,t,r)=>{if(void 0===r&&(r=0x7fffffff),2>r)return 0;r-=2;var n=t;r=r<2*e.length?r/2:e.length;for(var a=0;a<r;++a)C[t>>1]=e.charCodeAt(a),t+=2;return C[t>>1]=0,t-n},eK=e=>2*e.length,eQ=(e,t)=>{for(var r=0,n="";!(r>=t/4);){var a=F[e+4*r>>2];if(0==a)break;++r,65536<=a?(a-=65536,n+=String.fromCharCode(55296|a>>10,56320|1023&a)):n+=String.fromCharCode(a)}return n},eZ=(e,t,r)=>{if(void 0===r&&(r=0x7fffffff),4>r)return 0;var n=t;r=n+r-4;for(var a=0;a<e.length;++a){var o=e.charCodeAt(a);if(55296<=o&&57343>=o&&(o=65536+((1023&o)<<10)|1023&e.charCodeAt(++a)),F[t>>2]=o,(t+=4)+4>r)break}return F[t>>2]=0,t-n},e0=e=>{for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);55296<=n&&57343>=n&&++r,t+=4}return t},e2={};function e1(e){var t=e2[e];return void 0===t?eo(e):t}var e3=[];function e4(){function e(e){e.$$$embind_global$$$=e;var t="object"==typeof $$$embind_global$$$&&e.$$$embind_global$$$==e;return t||delete e.$$$embind_global$$$,t}if("object"==typeof globalThis)return globalThis;if("object"==typeof $$$embind_global$$$||("object"==typeof global&&e(global)?$$$embind_global$$$=global:"object"==typeof self&&e(self)&&($$$embind_global$$$=self),"object"==typeof $$$embind_global$$$))return $$$embind_global$$$;throw Error("unable to get global object.")}var e8=[],e6={},e5=1,e7=[],e9=[],te=[],tt=[],tr=[],tn=[],ta=[],to=[],ti=[],tu=[],tc={},tl={},tf=4;function ts(e){tg||(tg=e)}function td(e){for(var t=e5++,r=e.length;r<t;r++)e[r]=null;return t}function th(e){return t.qf=tV=(ty=to[e])&&ty.Qd,!(e&&!tV)}var tp,ty,tg,tm,tv={},t_=()=>{if(!tm){var e,t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:s||"./this.program"};for(e in tv)void 0===tv[e]?delete t[e]:t[e]=tv[e];var r=[];for(e in t)r.push("".concat(e,"=").concat(t[e]));tm=r}return tm},tb=[null,[],[]];function tP(e){tV.bindVertexArray(ta[e])}function tA(e,t){for(var r=0;r<e;r++){var n=F[t+4*r>>2];tV.deleteVertexArray(ta[n]),ta[n]=null}}var tE=[];function tw(e,t,r,n){tV.drawElements(e,t,r,n)}function tC(e,t,r,n){for(var a=0;a<e;a++){var o=tV[r](),i=o&&td(n);o?(o.name=i,n[i]=o):ts(1282),F[t+4*a>>2]=i}}function tT(e,t){tC(e,t,"createVertexArray",ta)}function tF(e,t,r){if(t){var n=void 0;switch(e){case 36346:n=1;break;case 36344:0!=r&&1!=r&&ts(1280);return;case 34814:case 36345:n=0;break;case 34466:var a=tV.getParameter(34467);n=a?a.length:0;break;case 33309:if(2>ty.version)return void ts(1282);n=2*(tV.getSupportedExtensions()||[]).length;break;case 33307:case 33308:if(2>ty.version)return void ts(1280);n=3*(33307==e)}if(void 0===n)switch(typeof(a=tV.getParameter(e))){case"number":n=a;break;case"boolean":n=+!!a;break;case"string":ts(1280);return;case"object":if(null===a)switch(e){case 34964:case 35725:case 34965:case 36006:case 36007:case 32873:case 34229:case 36662:case 36663:case 35053:case 35055:case 36010:case 35097:case 35869:case 32874:case 36389:case 35983:case 35368:case 34068:n=0;break;default:ts(1280);return}else{if(a instanceof Float32Array||a instanceof Uint32Array||a instanceof Int32Array||a instanceof Array){for(e=0;e<a.length;++e)switch(r){case 0:F[t+4*e>>2]=a[e];break;case 2:x[t+4*e>>2]=a[e];break;case 4:E[t+e|0]=+!!a[e]}return}try{n=0|a.name}catch(t){ts(1280),_("GL_INVALID_ENUM in glGet"+r+"v: Unknown object returned from WebGL getParameter("+e+")! (error: "+t+")");return}}break;default:ts(1280),_("GL_INVALID_ENUM in glGet"+r+"v: Native code calling glGet"+r+"v("+e+") and it returns "+a+" of type "+typeof a+"!");return}switch(r){case 1:r=n,S[t>>2]=r,S[t+4>>2]=(r-S[t>>2])/0x100000000;break;case 0:F[t>>2]=n;break;case 2:x[t>>2]=n;break;case 4:E[0|t]=+!!n}}else ts(1281)}var tS=e=>{var t=eX(e)+1,r=tJ(t);return r&&eY(e,w,r,t),r};function tx(e){return"]"==e.slice(-1)&&e.lastIndexOf("[")}function tk(e){return 0==(e-=5120)?E:1==e?w:2==e?C:4==e?F:6==e?x:5==e||28922==e||28520==e||30779==e||30782==e?S:T}function tM(e,t,r,n,a){var o=31-Math.clz32((e=tk(e)).BYTES_PER_ELEMENT),i=tf;return e.subarray(a>>o,a+n*(r*(({5:3,6:4,8:2,29502:3,29504:4,26917:2,26918:2,29846:3,29847:4})[t-6402]||1)*(1<<o)+i-1&-i)>>o)}function tR(e){var t=tV.Re;if(t){var r=t.fe[e];return"number"==typeof r&&(t.fe[e]=r=tV.getUniformLocation(t,t.Ge[e]+(0<r?"["+r+"]":""))),r}ts(1282)}var tD=[],tI=[],tB=e=>e%4==0&&(e%100!=0||e%400==0),tG=[31,29,31,30,31,30,31,31,30,31,30,31],tL=[31,28,31,30,31,30,31,31,30,31,30,31],tH=(e,t,r,n)=>{function a(e,t,r){for(e="number"==typeof e?e.toString():e||"";e.length<t;)e=r[0]+e;return e}function o(e,t){return a(e,t,"0")}function i(e,t){var r;function n(e){return 0>e?-1:+(0<e)}return 0===(r=n(e.getFullYear()-t.getFullYear()))&&0===(r=n(e.getMonth()-t.getMonth()))&&(r=n(e.getDate()-t.getDate())),r}function u(e){switch(e.getDay()){case 0:return new Date(e.getFullYear()-1,11,29);case 1:return e;case 2:return new Date(e.getFullYear(),0,3);case 3:return new Date(e.getFullYear(),0,2);case 4:return new Date(e.getFullYear(),0,1);case 5:return new Date(e.getFullYear()-1,11,31);case 6:return new Date(e.getFullYear()-1,11,30)}}function c(e){var t=e.Vd;for(e=new Date(new Date(e.Wd+1900,0,1).getTime());0<t;){var r=e.getMonth(),n=(tB(e.getFullYear())?tG:tL)[r];if(t>n-e.getDate())t-=n-e.getDate()+1,e.setDate(1),11>r?e.setMonth(r+1):(e.setMonth(0),e.setFullYear(e.getFullYear()+1));else{e.setDate(e.getDate()+t);break}}return r=new Date(e.getFullYear()+1,0,4),t=u(new Date(e.getFullYear(),0,4)),r=u(r),0>=i(t,e)?0>=i(r,e)?e.getFullYear()+1:e.getFullYear():e.getFullYear()-1}var l,f,s=F[n+40>>2];for(var d in n={nf:F[n>>2],mf:F[n+4>>2],pe:F[n+8>>2],we:F[n+12>>2],qe:F[n+16>>2],Wd:F[n+20>>2],Rd:F[n+24>>2],Vd:F[n+28>>2],uf:F[n+32>>2],lf:F[n+36>>2],pf:s&&s?X(w,s):""},r=r?X(w,r):"",s={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"})r=r.replace(RegExp(d,"g"),s[d]);var h="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),p="January February March April May June July August September October November December".split(" ");for(d in s={"%a":e=>h[e.Rd].substring(0,3),"%A":e=>h[e.Rd],"%b":e=>p[e.qe].substring(0,3),"%B":e=>p[e.qe],"%C":e=>o((e.Wd+1900)/100|0,2),"%d":e=>o(e.we,2),"%e":e=>a(e.we,2," "),"%g":e=>c(e).toString().substring(2),"%G":e=>c(e),"%H":e=>o(e.pe,2),"%I":e=>(0==(e=e.pe)?e=12:12<e&&(e-=12),o(e,2)),"%j":e=>{for(var t=0,r=0;r<=e.qe-1;t+=(tB(e.Wd+1900)?tG:tL)[r++]);return o(e.we+t,3)},"%m":e=>o(e.qe+1,2),"%M":e=>o(e.mf,2),"%n":()=>"\n","%p":e=>0<=e.pe&&12>e.pe?"AM":"PM","%S":e=>o(e.nf,2),"%t":()=>"	","%u":e=>e.Rd||7,"%U":e=>o(Math.floor((e.Vd+7-e.Rd)/7),2),"%V":e=>{var t=Math.floor((e.Vd+7-(e.Rd+6)%7)/7);if(2>=(e.Rd+371-e.Vd-2)%7&&t++,t)53==t&&(4==(r=(e.Rd+371-e.Vd)%7)||3==r&&tB(e.Wd)||(t=1));else{t=52;var r=(e.Rd+7-e.Vd-1)%7;(4==r||5==r&&tB(e.Wd%400-1))&&t++}return o(t,2)},"%w":e=>e.Rd,"%W":e=>o(Math.floor((e.Vd+7-(e.Rd+6)%7)/7),2),"%y":e=>(e.Wd+1900).toString().substring(2),"%Y":e=>e.Wd+1900,"%z":e=>(0<=(e=e.lf)?"+":"-")+String("0000"+((e=Math.abs(e)/60)/60*100+e%60)).slice(-4),"%Z":e=>e.pf,"%%":()=>"%"},r=r.replace(/%%/g,"\0\0"),s)r.includes(d)&&(r=r.replace(RegExp(d,"g"),s[d](n)));return f=Array(eX(l=r=r.replace(/\0\0/g,"%"))+1),eY(l,f,0,f.length),(d=f).length>t?0:(E.set(d,e),d.length-1)};ee=t.InternalError=class extends Error{constructor(e){super(e),this.name="InternalError"}};for(var tU=Array(256),tO=0;256>tO;++tO)tU[tO]=String.fromCharCode(tO);ea=tU,ei=t.BindingError=class extends Error{constructor(e){super(e),this.name="BindingError"}},eb.prototype.isAliasOf=function(e){if(!(this instanceof eb&&e instanceof eb))return!1;var t=this.Dd.Jd.Gd,r=this.Dd.Hd,n=e.Dd.Jd.Gd;for(e=e.Dd.Hd;t.Md;)r=t.ge(r),t=t.Md;for(;n.Md;)e=n.ge(e),n=n.Md;return t===n&&r===e},eb.prototype.clone=function(){if(this.Dd.Hd||el(this),this.Dd.ee)return this.Dd.count.value+=1,this;var e=e_,t=Object,r=t.create,n=Object.getPrototypeOf(this),a=this.Dd;return e=e(r.call(t,n,{Dd:{value:{count:a.count,$d:a.$d,ee:a.ee,Hd:a.Hd,Jd:a.Jd,Ld:a.Ld,Pd:a.Pd}}})),e.Dd.count.value+=1,e.Dd.$d=!1,e},eb.prototype.delete=function(){this.Dd.Hd||el(this),this.Dd.$d&&!this.Dd.ee&&eu("Object already scheduled for deletion"),es(this),ed(this.Dd),this.Dd.ee||(this.Dd.Ld=void 0,this.Dd.Hd=void 0)},eb.prototype.isDeleted=function(){return!this.Dd.Hd},eb.prototype.deleteLater=function(){return this.Dd.Hd||el(this),this.Dd.$d&&!this.Dd.ee&&eu("Object already scheduled for deletion"),ep.push(this),1===ep.length&&eg&&eg(ey),this.Dd.$d=!0,this},t.getInheritedInstanceCount=function(){return Object.keys(em).length},t.getLiveInheritedInstances=function(){var e,t=[];for(e in em)em.hasOwnProperty(e)&&t.push(em[e]);return t},t.flushPendingDeletes=ey,t.setDelayFunction=function(e){eg=e,ep.length&&eg&&eg(ey)},ek.prototype.Xe=function(e){return this.Ee&&(e=this.Ee(e)),e},ek.prototype.ye=function(e){this.Td&&this.Td(e)},ek.prototype.argPackAdvance=8,ek.prototype.readValueFromPointer=J,ek.prototype.deleteObject=function(e){null!==e&&e.delete()},ek.prototype.fromWireType=function(e){function t(){return this.le?ev(this.Gd.ae,{Jd:this.df,Hd:r,Pd:this,Ld:e}):ev(this.Gd.ae,{Jd:this,Hd:e})}var r=this.Xe(e);if(!r)return this.ye(e),null;var n=function(e,t){for(void 0===t&&eu("ptr should not be undefined");e.Md;)t=e.ge(t),e=e.Md;return em[t]}(this.Gd,r);if(void 0!==n)return 0===n.Dd.count.value?(n.Dd.Hd=r,n.Dd.Ld=e,n.clone()):(n=n.clone(),this.ye(e),n);if(!(n=eh[n=this.Gd.We(r)]))return t.call(this);n=this.ke?n.Qe:n.pointerType;var a=function e(t,r,n){return r===n?t:void 0===n.Md||null===(t=e(t,r,n.Md))?null:n.Te(t)}(r,this.Gd,n.Gd);return null===a?t.call(this):this.le?ev(n.Gd.ae,{Jd:n,Hd:a,Pd:this,Ld:e}):ev(n.Gd.ae,{Jd:n,Hd:a})},tj=Error,(tN=eA(tW="UnboundTypeError",function(e){this.name=tW,this.message=e,void 0!==(e=Error(e).stack)&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))})).prototype=Object.create(tj.prototype),tN.prototype.constructor=tN,tN.prototype.toString=function(){return void 0===this.message?this.name:"".concat(this.name,": ").concat(this.message)},eI=t.UnboundTypeError=tN,Object.assign(eU.prototype,{get(e){return this.Sd[e]},has(e){return void 0!==this.Sd[e]},Ie(e){var t=this.Ce.pop()||this.Sd.length;return this.Sd[t]=e,t},Je(e){this.Sd[e]=void 0,this.Ce.push(e)}}),eO.Sd.push({value:void 0},{value:null},{value:!0},{value:!1}),eO.be=eO.Sd.length,t.count_emval_handles=function(){for(var e=0,t=eO.be;t<eO.Sd.length;++t)void 0!==eO.Sd[t]&&++e;return e};for(var tj,tW,tN,tV,t$=0;32>t$;++t$)tE.push(Array(t$));var tY=new Float32Array(288);for(t$=0;288>t$;++t$)tD[t$]=tY.subarray(0,t$+1);var tX=new Int32Array(288);for(t$=0;288>t$;++t$)tI[t$]=tX.subarray(0,t$+1);var tq={Q:function(){return 0},Ab:()=>{},Cb:function(){return 0},xb:()=>{},yb:()=>{},R:function(){},zb:()=>{},v:function(e){var t=q[e];delete q[e];var r=t.ve,n=t.Td,a=t.Be;er([e],a.map(e=>e.$e).concat(a.map(e=>e.hf)),e=>{var o={};return a.forEach((t,r)=>{var n=e[r],i=t.Ye,u=t.Ze,c=e[r+a.length],l=t.gf,f=t.jf;o[t.Ve]={read:e=>n.fromWireType(i(u,e)),write:(e,t)=>{var r=[];l(f,e,c.toWireType(r,t)),z(r)}}}),[{name:t.name,fromWireType:function(e){var t,r={};for(t in o)r[t]=o[t].read(e);return n(e),r},toWireType:function(e,t){for(var a in o)if(!(a in t))throw TypeError('Missing field: "'.concat(a,'"'));var i=r();for(a in o)o[a].write(i,t[a]);return null!==e&&e.push(n,i),i},argPackAdvance:8,readValueFromPointer:J,Od:n}]})},pb:function(){},Gb:function(e,t,r,n,a){var o=en(r);ec(e,{name:t=eo(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?n:a},argPackAdvance:8,readValueFromPointer:function(e){if(1===r)var n=E;else if(2===r)n=C;else if(4===r)n=F;else throw TypeError("Unknown boolean type size: "+t);return this.fromWireType(n[e>>o])},Od:null})},k:function(e,t,r,n,a,o,i,u,c,l,f,s,d){f=eo(f),o=eD(a,o),u&&(u=eD(i,u)),l&&(l=eD(c,l)),d=eD(s,d);var h=eP(f);ew(h,function(){eG("Cannot construct ".concat(f," due to unbound types"),[n])}),er([e,t,r],n?[n]:[],function(t){if(t=t[0],n)var r=t.Gd,a=r.ae;else a=eb.prototype;var i=Object.create(a,{constructor:{value:t=eA(h,function(){if(Object.getPrototypeOf(this)!==i)throw new ei("Use 'new' to construct "+f);if(void 0===c.Ud)throw new ei(f+" has no accessible constructor");var e=c.Ud[arguments.length];if(void 0===e)throw new ei("Tried to invoke ctor of ".concat(f," with invalid number of parameters (").concat(arguments.length,") - expected (").concat(Object.keys(c.Ud).toString(),") parameters instead!"));return e.apply(this,arguments)})}});t.prototype=i;var c=new eC(f,t,i,d,r,o,u,l);c.Md&&(void 0===c.Md.he&&(c.Md.he=[]),c.Md.he.push(c)),r=new ek(f,c,!0,!1,!1),a=new ek(f+"*",c,!1,!1,!1);var s=new ek(f+" const*",c,!1,!0,!1);return eh[e]={pointerType:a,Qe:s},eM(h,t),[r,a,s]})},f:function(e,t,r,n,a,o,i){var u=eH(r,n);t=eo(t),o=eD(a,o),er([],[e],function(e){function n(){eG("Cannot call ".concat(a," due to unbound types"),u)}e=e[0];var a="".concat(e.name,".").concat(t);t.startsWith("@@")&&(t=Symbol[t.substring(2)]);var c=e.Gd.constructor;return void 0===c[t]?(n.Yd=r-1,c[t]=n):(eE(c,t,a),c[t].Kd[r-1]=n),er([],u,function(n){if(n=eL(a,n=[n[0],null].concat(n.slice(1)),null,o,i),void 0===c[t].Kd?(n.Yd=r-1,c[t]=n):c[t].Kd[r-1]=n,e.Gd.he)for(let r of e.Gd.he)r.constructor.hasOwnProperty(t)||(r.constructor[t]=n);return[]}),[]})},t:function(e,t,r,n,a,o){var i=eH(t,r);a=eD(n,a),er([],[e],function(e){e=e[0];var r="constructor ".concat(e.name);if(void 0===e.Gd.Ud&&(e.Gd.Ud=[]),void 0!==e.Gd.Ud[t-1])throw new ei("Cannot register multiple constructors with identical number of parameters (".concat(t-1,") for class '").concat(e.name,"'! Overload resolution is currently only performed using the parameter count, not actual type info!"));return e.Gd.Ud[t-1]=()=>{eG("Cannot construct ".concat(e.name," due to unbound types"),i)},er([],i,function(n){return n.splice(1,0,null),e.Gd.Ud[t-1]=eL(r,n,null,a,o),[]}),[]})},b:function(e,t,r,n,a,o,i,u){var c=eH(r,n);t=eo(t),o=eD(a,o),er([],[e],function(e){function n(){eG("Cannot call ".concat(a," due to unbound types"),c)}e=e[0];var a="".concat(e.name,".").concat(t);t.startsWith("@@")&&(t=Symbol[t.substring(2)]),u&&e.Gd.ef.push(t);var l=e.Gd.ae,f=l[t];return void 0===f||void 0===f.Kd&&f.className!==e.name&&f.Yd===r-2?(n.Yd=r-2,n.className=e.name,l[t]=n):(eE(l,t,a),l[t].Kd[r-2]=n),er([],c,function(n){return n=eL(a,n,e,o,i),void 0===l[t].Kd?(n.Yd=r-2,l[t]=n):l[t].Kd[r-2]=n,[]}),[]})},o:function(e,r,n){e=eo(e),er([],[r],function(r){return r=r[0],t[e]=r.fromWireType(n),[]})},Fb:function(e,t){ec(e,{name:t=eo(t),fromWireType:function(e){var t=eW(e);return ej(e),t},toWireType:function(e,t){return eN(t)},argPackAdvance:8,readValueFromPointer:J,Od:null})},j:function(e,t,r,n){function a(){}r=en(r),t=eo(t),a.values={},ec(e,{name:t,constructor:a,fromWireType:function(e){return this.constructor.values[e]},toWireType:function(e,t){return t.value},argPackAdvance:8,readValueFromPointer:function(e,t,r){switch(t){case 0:return function(e){return this.fromWireType((r?E:w)[e])};case 1:return function(e){return this.fromWireType((r?C:T)[e>>1])};case 2:return function(e){return this.fromWireType((r?F:S)[e>>2])};default:throw TypeError("Unknown integer type: "+e)}}(t,r,n),Od:null}),ew(t,a)},c:function(e,t,r){var n=eV(e,"enum");t=eo(t),e=n.constructor,n=Object.create(n.constructor.prototype,{value:{value:r},constructor:{value:eA("".concat(n.name,"_").concat(t),function(){})}}),e.values[r]=n,e[t]=n},T:function(e,t,r){r=en(r),ec(e,{name:t=eo(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:function(e,t){switch(t){case 2:return function(e){return this.fromWireType(x[e>>2])};case 3:return function(e){return this.fromWireType(k[e>>3])};default:throw TypeError("Unknown float type: "+e)}}(t,r),Od:null})},r:function(e,t,r,n,a,o){var i=eH(t,r);e=eo(e),a=eD(n,a),ew(e,function(){eG("Cannot call ".concat(e," due to unbound types"),i)},t-1),er([],i,function(r){return r=[r[0],null].concat(r.slice(1)),eM(e,eL(e,r,null,a,o),t-1),[]})},x:function(e,t,r,n,a){t=eo(t),-1===a&&(a=0xffffffff),a=en(r);var o=e=>e;if(0===n){var i=32-8*r;o=e=>e<<i>>>i}r=t.includes("unsigned")?function(e,t){return t>>>0}:function(e,t){return t},ec(e,{name:t,fromWireType:o,toWireType:r,argPackAdvance:8,readValueFromPointer:function(e,t,r){switch(t){case 0:return r?function(e){return E[e]}:function(e){return w[e]};case 1:return r?function(e){return C[e>>1]}:function(e){return T[e>>1]};case 2:return r?function(e){return F[e>>2]}:function(e){return S[e>>2]};default:throw TypeError("Unknown integer type: "+e)}}(t,a,0!==n),Od:null})},n:function(e,t,r){function n(e){e>>=2;var t=S;return new a(t.buffer,t[e+1],t[e])}var a=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];ec(e,{name:r=eo(r),fromWireType:n,argPackAdvance:8,readValueFromPointer:n},{af:!0})},m:function(e,t,r,n,a,o,i,u,c,l,f,s){r=eo(r),o=eD(a,o),u=eD(i,u),l=eD(c,l),s=eD(f,s),er([e],[t],function(e){return e=e[0],[new ek(r,e.Gd,!1,!1,!0,e,n,o,u,l,s)]})},S:function(e,t){var r="std::string"===(t=eo(t));ec(e,{name:t,fromWireType:function(e){var t=S[e>>2],n=e+4;if(r)for(var a=n,o=0;o<=t;++o){var i=n+o;if(o==t||0==w[i]){if(a=a?X(w,a,i-a):"",void 0===u)var u=a;else u+="\0",u+=a;a=i+1}}else{for(u=Array(t),o=0;o<t;++o)u[o]=String.fromCharCode(w[n+o]);u=u.join("")}return tz(e),u},toWireType:function(e,t){t instanceof ArrayBuffer&&(t=new Uint8Array(t));var n="string"==typeof t;n||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||eu("Cannot pass non-string to std::string");var a=r&&n?eX(t):t.length,o=tJ(4+a+1),i=o+4;if(S[o>>2]=a,r&&n)eY(t,w,i,a+1);else if(n)for(n=0;n<a;++n){var u=t.charCodeAt(n);255<u&&(tz(i),eu("String has UTF-16 code units that do not fit in 8 bits")),w[i+n]=u}else for(n=0;n<a;++n)w[i+n]=t[n];return null!==e&&e.push(tz,o),o},argPackAdvance:8,readValueFromPointer:J,Od:function(e){tz(e)}})},K:function(e,t,r){if(r=eo(r),2===t)var n=ez,a=eJ,o=eK,i=()=>T,u=1;else 4===t&&(n=eQ,a=eZ,o=e0,i=()=>S,u=2);ec(e,{name:r,fromWireType:function(e){for(var r,a=S[e>>2],o=i(),c=e+4,l=0;l<=a;++l){var f=e+4+l*t;(l==a||0==o[f>>u])&&(c=n(c,f-c),void 0===r?r=c:(r+="\0",r+=c),c=f+t)}return tz(e),r},toWireType:function(e,n){"string"!=typeof n&&eu("Cannot pass non-string to C++ string type ".concat(r));var i=o(n),c=tJ(4+i+t);return S[c>>2]=i>>u,a(n,c+4,i+t),null!==e&&e.push(tz,c),c},argPackAdvance:8,readValueFromPointer:J,Od:function(e){tz(e)}})},w:function(e,t,r,n,a,o){q[e]={name:eo(t),ve:eD(r,n),Td:eD(a,o),Be:[]}},e:function(e,t,r,n,a,o,i,u,c,l){q[e].Be.push({Ve:eo(t),$e:r,Ye:eD(n,a),Ze:o,hf:i,gf:eD(u,c),jf:l})},Hb:function(e,t){ec(e,{cf:!0,name:t=eo(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},Eb:()=>!0,tb:()=>{throw 1/0},y:function(e,t,r){e=eW(e),t=eV(t,"emval::as");var n=[],a=eN(n);return S[r>>2]=a,t.toWireType(n,e)},Y:function(e,t,r,n,a){e=e3[e],t=eW(t),r=e1(r);var o=[];return S[n>>2]=eN(o),e(t,r,o,a)},q:function(e,t,r,n){e=e3[e],e(t=eW(t),r=e1(r),null,n)},d:ej,H:function(e){return 0===e?eN(e4()):(e=e1(e),eN(e4()[e]))},p:function(e,t){var r,n,a=function(e,t){for(var r=Array(e),n=0;n<e;++n)r[n]=eV(S[t+4*n>>2],"parameter "+n);return r}(e,t),o=a[0],i=e8[t=o.name+"_$"+a.slice(1).map(function(e){return e.name}).join("_")+"$"];if(void 0!==i)return i;var u=Array(e-1);return r=(t,r,n,i)=>{for(var c=0,l=0;l<e-1;++l)u[l]=a[l+1].readValueFromPointer(i+c),c+=a[l+1].argPackAdvance;for(t=t[r].apply(t,u),l=0;l<e-1;++l)a[l+1].Se&&a[l+1].Se(u[l]);if(!o.cf)return o.toWireType(n,t)},n=e3.length,e3.push(r),i=n,e8[t]=i},s:function(e,t){return eN((e=eW(e))[t=eW(t)])},l:function(e){4<e&&(eO.get(e).Fe+=1)},G:function(e,t,r,n){e=eW(e);var a,o=e6[t];return o||(a=Array(t+1),o=function(e,r,n){a[0]=e;for(var o=0;o<t;++o){var i=eV(S[r+4*o>>2],"parameter "+o);a[o+1]=i.readValueFromPointer(n),n+=i.argPackAdvance}return eN(e=new(e.bind.apply(e,a)))},e6[t]=o),o(e,r,n)},C:function(){return eN([])},g:function(e){return eN(e1(e))},z:function(){return eN({})},jb:function(e){return!(e=eW(e))},u:function(e){z(eW(e)),ej(e)},i:function(e,t,r){e=eW(e),t=eW(t),r=eW(r),e[t]=r},h:function(e,t){return eN(e=(e=eV(e,"_emval_take_value")).readValueFromPointer(t))},mb:function(){return -52},nb:function(){},a:()=>{O("")},Db:()=>performance.now(),ub:e=>{var t=w.length;if(0x80000000<(e>>>=0))return!1;for(var r=1;4>=r;r*=2){var n=t*(1+.2/r);n=Math.min(n,e+0x6000000);var a=Math;n=Math.max(e,n);e:{a=a.min.call(a,0x80000000,n+(65536-n%65536)%65536)-P.buffer.byteLength+65535>>>16;try{P.grow(a),R();var o=1;break e}catch(e){}o=void 0}if(o)return!0}return!1},kb:function(){return ty?ty.handle:0},vb:(e,t)=>{var r=0;return t_().forEach(function(n,a){var o=t+r;for(a=S[e+4*a>>2]=o,o=0;o<n.length;++o)E[0|a++]=n.charCodeAt(o);E[0|a]=0,r+=n.length+1}),0},wb:(e,t)=>{var r=t_();S[e>>2]=r.length;var n=0;return r.forEach(function(e){n+=e.length+1}),S[t>>2]=n,0},J:()=>52,lb:function(){return 52},Bb:()=>52,ob:function(){return 70},P:(e,t,r,n)=>{for(var a=0,o=0;o<r;o++){var i=S[t>>2],u=S[t+4>>2];t+=8;for(var c=0;c<u;c++){var l=w[i+c],f=tb[e];0===l||10===l?((1===e?v:_)(X(f,0)),f.length=0):f.push(l)}a+=u}return S[n>>2]=a,0},$:function(e){tV.activeTexture(e)},aa:function(e,t){tV.attachShader(e9[e],tn[t])},ba:function(e,t,r){tV.bindAttribLocation(e9[e],t,r?X(w,r):"")},ca:function(e,t){35051==e?tV.se=t:35052==e&&(tV.Zd=t),tV.bindBuffer(e,e7[t])},_:function(e,t){tV.bindFramebuffer(e,te[t])},ac:function(e,t){tV.bindRenderbuffer(e,tt[t])},Mb:function(e,t){tV.bindSampler(e,ti[t])},da:function(e,t){tV.bindTexture(e,tr[t])},uc:tP,xc:tP,ea:function(e,t,r,n){tV.blendColor(e,t,r,n)},fa:function(e){tV.blendEquation(e)},ga:function(e,t){tV.blendFunc(e,t)},Wb:function(e,t,r,n,a,o,i,u,c,l){tV.blitFramebuffer(e,t,r,n,a,o,i,u,c,l)},ha:function(e,t,r,n){2<=ty.version?r&&t?tV.bufferData(e,w,n,r,t):tV.bufferData(e,t,n):tV.bufferData(e,r?w.subarray(r,r+t):t,n)},ia:function(e,t,r,n){2<=ty.version?r&&tV.bufferSubData(e,t,w,n,r):tV.bufferSubData(e,t,w.subarray(n,n+r))},bc:function(e){return tV.checkFramebufferStatus(e)},N:function(e){tV.clear(e)},Z:function(e,t,r,n){tV.clearColor(e,t,r,n)},O:function(e){tV.clearStencil(e)},rb:function(e,t,r,n){return tV.clientWaitSync(tu[e],t,(r>>>0)+0x100000000*n)},ja:function(e,t,r,n){tV.colorMask(!!e,!!t,!!r,!!n)},ka:function(e){tV.compileShader(tn[e])},la:function(e,t,r,n,a,o,i,u){2<=ty.version?tV.Zd||!i?tV.compressedTexImage2D(e,t,r,n,a,o,i,u):tV.compressedTexImage2D(e,t,r,n,a,o,w,u,i):tV.compressedTexImage2D(e,t,r,n,a,o,u?w.subarray(u,u+i):null)},ma:function(e,t,r,n,a,o,i,u,c){2<=ty.version?tV.Zd||!u?tV.compressedTexSubImage2D(e,t,r,n,a,o,i,u,c):tV.compressedTexSubImage2D(e,t,r,n,a,o,i,w,c,u):tV.compressedTexSubImage2D(e,t,r,n,a,o,i,c?w.subarray(c,c+u):null)},Ub:function(e,t,r,n,a){tV.copyBufferSubData(e,t,r,n,a)},na:function(e,t,r,n,a,o,i,u){tV.copyTexSubImage2D(e,t,r,n,a,o,i,u)},oa:function(){var e=td(e9),t=tV.createProgram();return t.name=e,t.oe=t.me=t.ne=0,t.xe=1,e9[e]=t,e},pa:function(e){var t=td(tn);return tn[t]=tV.createShader(e),t},qa:function(e){tV.cullFace(e)},ra:function(e,t){for(var r=0;r<e;r++){var n=F[t+4*r>>2],a=e7[n];a&&(tV.deleteBuffer(a),a.name=0,e7[n]=null,n==tV.se&&(tV.se=0),n==tV.Zd&&(tV.Zd=0))}},cc:function(e,t){for(var r=0;r<e;++r){var n=F[t+4*r>>2],a=te[n];a&&(tV.deleteFramebuffer(a),a.name=0,te[n]=null)}},sa:function(e){if(e){var t=e9[e];t?(tV.deleteProgram(t),t.name=0,e9[e]=null):ts(1281)}},dc:function(e,t){for(var r=0;r<e;r++){var n=F[t+4*r>>2],a=tt[n];a&&(tV.deleteRenderbuffer(a),a.name=0,tt[n]=null)}},Nb:function(e,t){for(var r=0;r<e;r++){var n=F[t+4*r>>2],a=ti[n];a&&(tV.deleteSampler(a),a.name=0,ti[n]=null)}},ta:function(e){if(e){var t=tn[e];t?(tV.deleteShader(t),tn[e]=null):ts(1281)}},Vb:function(e){if(e){var t=tu[e];t?(tV.deleteSync(t),t.name=0,tu[e]=null):ts(1281)}},ua:function(e,t){for(var r=0;r<e;r++){var n=F[t+4*r>>2],a=tr[n];a&&(tV.deleteTexture(a),a.name=0,tr[n]=null)}},vc:tA,yc:tA,va:function(e){tV.depthMask(!!e)},wa:function(e){tV.disable(e)},xa:function(e){tV.disableVertexAttribArray(e)},ya:function(e,t,r){tV.drawArrays(e,t,r)},sc:function(e,t,r,n){tV.drawArraysInstanced(e,t,r,n)},qc:function(e,t,r,n,a){tV.ze.drawArraysInstancedBaseInstanceWEBGL(e,t,r,n,a)},oc:function(e,t){for(var r=tE[e],n=0;n<e;n++)r[n]=F[t+4*n>>2];tV.drawBuffers(r)},za:tw,tc:function(e,t,r,n,a){tV.drawElementsInstanced(e,t,r,n,a)},rc:function(e,t,r,n,a,o,i){tV.ze.drawElementsInstancedBaseVertexBaseInstanceWEBGL(e,t,r,n,a,o,i)},ic:function(e,t,r,n,a,o){tw(e,n,a,o)},Aa:function(e){tV.enable(e)},Ba:function(e){tV.enableVertexAttribArray(e)},Sb:function(e,t){return(e=tV.fenceSync(e,t))?(t=td(tu),e.name=t,tu[t]=e,t):0},Ca:function(){tV.finish()},Da:function(){tV.flush()},ec:function(e,t,r,n){tV.framebufferRenderbuffer(e,t,r,tt[n])},fc:function(e,t,r,n,a){tV.framebufferTexture2D(e,t,r,tr[n],a)},Ea:function(e){tV.frontFace(e)},Fa:function(e,t){tC(e,t,"createBuffer",e7)},gc:function(e,t){tC(e,t,"createFramebuffer",te)},hc:function(e,t){tC(e,t,"createRenderbuffer",tt)},Ob:function(e,t){tC(e,t,"createSampler",ti)},Ga:function(e,t){tC(e,t,"createTexture",tr)},wc:tT,zc:tT,Yb:function(e){tV.generateMipmap(e)},Ha:function(e,t,r){r?F[r>>2]=tV.getBufferParameter(e,t):ts(1281)},Ia:function(){var e=tV.getError()||tg;return tg=0,e},Ja:function(e,t){tF(e,t,2)},Zb:function(e,t,r,n){((e=tV.getFramebufferAttachmentParameter(e,t,r))instanceof WebGLRenderbuffer||e instanceof WebGLTexture)&&(e=0|e.name),F[n>>2]=e},I:function(e,t){tF(e,t,0)},Ka:function(e,t,r,n){null===(e=tV.getProgramInfoLog(e9[e]))&&(e="(unknown error)"),t=0<t&&n?eY(e,w,n,t):0,r&&(F[r>>2]=t)},La:function(e,t,r){if(r)if(e>=e5)ts(1281);else if(e=e9[e],35716==t)null===(e=tV.getProgramInfoLog(e))&&(e="(unknown error)"),F[r>>2]=e.length+1;else if(35719==t){if(!e.oe)for(t=0;t<tV.getProgramParameter(e,35718);++t)e.oe=Math.max(e.oe,tV.getActiveUniform(e,t).name.length+1);F[r>>2]=e.oe}else if(35722==t){if(!e.me)for(t=0;t<tV.getProgramParameter(e,35721);++t)e.me=Math.max(e.me,tV.getActiveAttrib(e,t).name.length+1);F[r>>2]=e.me}else if(35381==t){if(!e.ne)for(t=0;t<tV.getProgramParameter(e,35382);++t)e.ne=Math.max(e.ne,tV.getActiveUniformBlockName(e,t).length+1);F[r>>2]=e.ne}else F[r>>2]=tV.getProgramParameter(e,t);else ts(1281)},_b:function(e,t,r){r?F[r>>2]=tV.getRenderbufferParameter(e,t):ts(1281)},Ma:function(e,t,r,n){null===(e=tV.getShaderInfoLog(tn[e]))&&(e="(unknown error)"),t=0<t&&n?eY(e,w,n,t):0,r&&(F[r>>2]=t)},Jb:function(e,t,r,n){e=tV.getShaderPrecisionFormat(e,t),F[r>>2]=e.rangeMin,F[r+4>>2]=e.rangeMax,F[n>>2]=e.precision},Na:function(e,t,r){r?35716==t?(null===(e=tV.getShaderInfoLog(tn[e]))&&(e="(unknown error)"),F[r>>2]=e?e.length+1:0):35720==t?(e=tV.getShaderSource(tn[e]),F[r>>2]=e?e.length+1:0):F[r>>2]=tV.getShaderParameter(tn[e],t):ts(1281)},M:function(e){var t=tc[e];if(!t){switch(e){case 7939:t=tS((t=(t=tV.getSupportedExtensions()||[]).concat(t.map(function(e){return"GL_"+e}))).join(" "));break;case 7936:case 7937:case 37445:case 37446:(t=tV.getParameter(e))||ts(1280),t=t&&tS(t);break;case 7938:t=tV.getParameter(7938),t=tS(t=2<=ty.version?"OpenGL ES 3.0 ("+t+")":"OpenGL ES 2.0 ("+t+")");break;case 35724:var r=(t=tV.getParameter(35724)).match(/^WebGL GLSL ES ([0-9]\.[0-9][0-9]?)(?:$| .*)/);null!==r&&(3==r[1].length&&(r[1]+="0"),t="OpenGL ES GLSL ES "+r[1]+" ("+t+")"),t=tS(t);break;default:ts(1280)}tc[e]=t}return t},ib:function(e,t){if(2>ty.version)return ts(1282),0;var r=tl[e];return r?0>t||t>=r.length?(ts(1281),0):r[t]:7939===e?(r=(r=(r=tV.getSupportedExtensions()||[]).concat(r.map(function(e){return"GL_"+e}))).map(function(e){return tS(e)}),r=tl[e]=r,0>t||t>=r.length?(ts(1281),0):r[t]):(ts(1280),0)},Oa:function(e,t){if(t=t?X(w,t):"",e=e9[e]){var r,n=e,a=n.fe,o=n.He;if(!a)for(n.fe=a={},n.Ge={},r=0;r<tV.getProgramParameter(n,35718);++r){var i=tV.getActiveUniform(n,r),u=i.name;i=i.size;var c=tx(u);c=0<c?u.slice(0,c):u;var l=n.xe;for(n.xe+=i,o[c]=[i,l],u=0;u<i;++u)a[l]=u,n.Ge[l++]=c}if(n=e.fe,a=0,o=t,0<(r=tx(t))&&(a=parseInt(t.slice(r+1))>>>0,o=t.slice(0,r)),(o=e.He[o])&&a<o[0]&&(n[a+=o[1]]=n[a]||tV.getUniformLocation(e,t)))return a}else ts(1281);return -1},Kb:function(e,t,r){for(var n=tE[t],a=0;a<t;a++)n[a]=F[r+4*a>>2];tV.invalidateFramebuffer(e,n)},Lb:function(e,t,r,n,a,o,i){for(var u=tE[t],c=0;c<t;c++)u[c]=F[r+4*c>>2];tV.invalidateSubFramebuffer(e,u,n,a,o,i)},Tb:function(e){return tV.isSync(tu[e])},Pa:function(e){return(e=tr[e])?tV.isTexture(e):0},Qa:function(e){tV.lineWidth(e)},Ra:function(e){e=e9[e],tV.linkProgram(e),e.fe=0,e.He={}},mc:function(e,t,r,n,a,o){tV.De.multiDrawArraysInstancedBaseInstanceWEBGL(e,F,t>>2,F,r>>2,F,n>>2,S,a>>2,o)},nc:function(e,t,r,n,a,o,i,u){tV.De.multiDrawElementsInstancedBaseVertexBaseInstanceWEBGL(e,F,t>>2,r,F,n>>2,F,a>>2,F,o>>2,S,i>>2,u)},Sa:function(e,t){3317==e&&(tf=t),tV.pixelStorei(e,t)},pc:function(e){tV.readBuffer(e)},Ta:function(e,t,r,n,a,o,i){if(2<=ty.version)if(tV.se)tV.readPixels(e,t,r,n,a,o,i);else{var u=tk(o);tV.readPixels(e,t,r,n,a,o,u,i>>31-Math.clz32(u.BYTES_PER_ELEMENT))}else(i=tM(o,a,r,n,i))?tV.readPixels(e,t,r,n,a,o,i):ts(1280)},$b:function(e,t,r,n){tV.renderbufferStorage(e,t,r,n)},Xb:function(e,t,r,n,a){tV.renderbufferStorageMultisample(e,t,r,n,a)},Pb:function(e,t,r){tV.samplerParameterf(ti[e],t,r)},Qb:function(e,t,r){tV.samplerParameteri(ti[e],t,r)},Rb:function(e,t,r){tV.samplerParameteri(ti[e],t,F[r>>2])},Ua:function(e,t,r,n){tV.scissor(e,t,r,n)},Va:function(e,t,r,n){for(var a="",o=0;o<t;++o){var i=n?F[n+4*o>>2]:-1,u=F[r+4*o>>2];a+=i=u?X(w,u,0>i?void 0:i):""}tV.shaderSource(tn[e],a)},Wa:function(e,t,r){tV.stencilFunc(e,t,r)},Xa:function(e,t,r,n){tV.stencilFuncSeparate(e,t,r,n)},Ya:function(e){tV.stencilMask(e)},Za:function(e,t){tV.stencilMaskSeparate(e,t)},_a:function(e,t,r){tV.stencilOp(e,t,r)},$a:function(e,t,r,n){tV.stencilOpSeparate(e,t,r,n)},ab:function(e,t,r,n,a,o,i,u,c){if(2<=ty.version)if(tV.Zd)tV.texImage2D(e,t,r,n,a,o,i,u,c);else if(c){var l=tk(u);tV.texImage2D(e,t,r,n,a,o,i,u,l,c>>31-Math.clz32(l.BYTES_PER_ELEMENT))}else tV.texImage2D(e,t,r,n,a,o,i,u,null);else tV.texImage2D(e,t,r,n,a,o,i,u,c?tM(u,i,n,a,c):null)},bb:function(e,t,r){tV.texParameterf(e,t,r)},cb:function(e,t,r){tV.texParameterf(e,t,x[r>>2])},db:function(e,t,r){tV.texParameteri(e,t,r)},eb:function(e,t,r){tV.texParameteri(e,t,F[r>>2])},jc:function(e,t,r,n,a){tV.texStorage2D(e,t,r,n,a)},fb:function(e,t,r,n,a,o,i,u,c){if(2<=ty.version)if(tV.Zd)tV.texSubImage2D(e,t,r,n,a,o,i,u,c);else if(c){var l=tk(u);tV.texSubImage2D(e,t,r,n,a,o,i,u,l,c>>31-Math.clz32(l.BYTES_PER_ELEMENT))}else tV.texSubImage2D(e,t,r,n,a,o,i,u,null);else l=null,c&&(l=tM(u,i,a,o,c)),tV.texSubImage2D(e,t,r,n,a,o,i,u,l)},gb:function(e,t){tV.uniform1f(tR(e),t)},hb:function(e,t,r){if(2<=ty.version)t&&tV.uniform1fv(tR(e),x,r>>2,t);else{if(288>=t)for(var n=tD[t-1],a=0;a<t;++a)n[a]=x[r+4*a>>2];else n=x.subarray(r>>2,r+4*t>>2);tV.uniform1fv(tR(e),n)}},Uc:function(e,t){tV.uniform1i(tR(e),t)},Vc:function(e,t,r){if(2<=ty.version)t&&tV.uniform1iv(tR(e),F,r>>2,t);else{if(288>=t)for(var n=tI[t-1],a=0;a<t;++a)n[a]=F[r+4*a>>2];else n=F.subarray(r>>2,r+4*t>>2);tV.uniform1iv(tR(e),n)}},Wc:function(e,t,r){tV.uniform2f(tR(e),t,r)},Xc:function(e,t,r){if(2<=ty.version)t&&tV.uniform2fv(tR(e),x,r>>2,2*t);else{if(144>=t)for(var n=tD[2*t-1],a=0;a<2*t;a+=2)n[a]=x[r+4*a>>2],n[a+1]=x[r+(4*a+4)>>2];else n=x.subarray(r>>2,r+8*t>>2);tV.uniform2fv(tR(e),n)}},Tc:function(e,t,r){tV.uniform2i(tR(e),t,r)},Sc:function(e,t,r){if(2<=ty.version)t&&tV.uniform2iv(tR(e),F,r>>2,2*t);else{if(144>=t)for(var n=tI[2*t-1],a=0;a<2*t;a+=2)n[a]=F[r+4*a>>2],n[a+1]=F[r+(4*a+4)>>2];else n=F.subarray(r>>2,r+8*t>>2);tV.uniform2iv(tR(e),n)}},Rc:function(e,t,r,n){tV.uniform3f(tR(e),t,r,n)},Qc:function(e,t,r){if(2<=ty.version)t&&tV.uniform3fv(tR(e),x,r>>2,3*t);else{if(96>=t)for(var n=tD[3*t-1],a=0;a<3*t;a+=3)n[a]=x[r+4*a>>2],n[a+1]=x[r+(4*a+4)>>2],n[a+2]=x[r+(4*a+8)>>2];else n=x.subarray(r>>2,r+12*t>>2);tV.uniform3fv(tR(e),n)}},Pc:function(e,t,r,n){tV.uniform3i(tR(e),t,r,n)},Oc:function(e,t,r){if(2<=ty.version)t&&tV.uniform3iv(tR(e),F,r>>2,3*t);else{if(96>=t)for(var n=tI[3*t-1],a=0;a<3*t;a+=3)n[a]=F[r+4*a>>2],n[a+1]=F[r+(4*a+4)>>2],n[a+2]=F[r+(4*a+8)>>2];else n=F.subarray(r>>2,r+12*t>>2);tV.uniform3iv(tR(e),n)}},Nc:function(e,t,r,n,a){tV.uniform4f(tR(e),t,r,n,a)},Mc:function(e,t,r){if(2<=ty.version)t&&tV.uniform4fv(tR(e),x,r>>2,4*t);else{if(72>=t){var n=tD[4*t-1],a=x;r>>=2;for(var o=0;o<4*t;o+=4){var i=r+o;n[o]=a[i],n[o+1]=a[i+1],n[o+2]=a[i+2],n[o+3]=a[i+3]}}else n=x.subarray(r>>2,r+16*t>>2);tV.uniform4fv(tR(e),n)}},Ac:function(e,t,r,n,a){tV.uniform4i(tR(e),t,r,n,a)},Bc:function(e,t,r){if(2<=ty.version)t&&tV.uniform4iv(tR(e),F,r>>2,4*t);else{if(72>=t)for(var n=tI[4*t-1],a=0;a<4*t;a+=4)n[a]=F[r+4*a>>2],n[a+1]=F[r+(4*a+4)>>2],n[a+2]=F[r+(4*a+8)>>2],n[a+3]=F[r+(4*a+12)>>2];else n=F.subarray(r>>2,r+16*t>>2);tV.uniform4iv(tR(e),n)}},Cc:function(e,t,r,n){if(2<=ty.version)t&&tV.uniformMatrix2fv(tR(e),!!r,x,n>>2,4*t);else{if(72>=t)for(var a=tD[4*t-1],o=0;o<4*t;o+=4)a[o]=x[n+4*o>>2],a[o+1]=x[n+(4*o+4)>>2],a[o+2]=x[n+(4*o+8)>>2],a[o+3]=x[n+(4*o+12)>>2];else a=x.subarray(n>>2,n+16*t>>2);tV.uniformMatrix2fv(tR(e),!!r,a)}},Dc:function(e,t,r,n){if(2<=ty.version)t&&tV.uniformMatrix3fv(tR(e),!!r,x,n>>2,9*t);else{if(32>=t)for(var a=tD[9*t-1],o=0;o<9*t;o+=9)a[o]=x[n+4*o>>2],a[o+1]=x[n+(4*o+4)>>2],a[o+2]=x[n+(4*o+8)>>2],a[o+3]=x[n+(4*o+12)>>2],a[o+4]=x[n+(4*o+16)>>2],a[o+5]=x[n+(4*o+20)>>2],a[o+6]=x[n+(4*o+24)>>2],a[o+7]=x[n+(4*o+28)>>2],a[o+8]=x[n+(4*o+32)>>2];else a=x.subarray(n>>2,n+36*t>>2);tV.uniformMatrix3fv(tR(e),!!r,a)}},Ec:function(e,t,r,n){if(2<=ty.version)t&&tV.uniformMatrix4fv(tR(e),!!r,x,n>>2,16*t);else{if(18>=t){var a=tD[16*t-1],o=x;n>>=2;for(var i=0;i<16*t;i+=16){var u=n+i;a[i]=o[u],a[i+1]=o[u+1],a[i+2]=o[u+2],a[i+3]=o[u+3],a[i+4]=o[u+4],a[i+5]=o[u+5],a[i+6]=o[u+6],a[i+7]=o[u+7],a[i+8]=o[u+8],a[i+9]=o[u+9],a[i+10]=o[u+10],a[i+11]=o[u+11],a[i+12]=o[u+12],a[i+13]=o[u+13],a[i+14]=o[u+14],a[i+15]=o[u+15]}}else a=x.subarray(n>>2,n+64*t>>2);tV.uniformMatrix4fv(tR(e),!!r,a)}},Fc:function(e){e=e9[e],tV.useProgram(e),tV.Re=e},Gc:function(e,t){tV.vertexAttrib1f(e,t)},Hc:function(e,t){tV.vertexAttrib2f(e,x[t>>2],x[t+4>>2])},Ic:function(e,t){tV.vertexAttrib3f(e,x[t>>2],x[t+4>>2],x[t+8>>2])},Jc:function(e,t){tV.vertexAttrib4f(e,x[t>>2],x[t+4>>2],x[t+8>>2],x[t+12>>2])},kc:function(e,t){tV.vertexAttribDivisor(e,t)},lc:function(e,t,r,n,a){tV.vertexAttribIPointer(e,t,r,n,a)},Kc:function(e,t,r,n,a,o){tV.vertexAttribPointer(e,t,r,!!n,a,o)},Lc:function(e,t,r,n){tV.viewport(e,t,r,n)},qb:function(e,t,r,n){tV.waitSync(tu[e],t,(r>>>0)+0x100000000*n)},W:function(e,t){var r=tZ();try{return D.get(e)(t)}catch(e){if(t0(r),e!==e+0)throw e;tQ(1,0)}},F:function(e,t,r){var n=tZ();try{return D.get(e)(t,r)}catch(e){if(t0(n),e!==e+0)throw e;tQ(1,0)}},E:function(e,t,r,n){var a=tZ();try{return D.get(e)(t,r,n)}catch(e){if(t0(a),e!==e+0)throw e;tQ(1,0)}},X:function(e,t,r,n,a){var o=tZ();try{return D.get(e)(t,r,n,a)}catch(e){if(t0(o),e!==e+0)throw e;tQ(1,0)}},Ib:function(e,t,r,n,a,o,i){var u=tZ();try{return D.get(e)(t,r,n,a,o,i)}catch(e){if(t0(u),e!==e+0)throw e;tQ(1,0)}},V:function(e,t,r,n,a,o,i,u,c,l){var f=tZ();try{return D.get(e)(t,r,n,a,o,i,u,c,l)}catch(e){if(t0(f),e!==e+0)throw e;tQ(1,0)}},U:function(e){var t=tZ();try{D.get(e)()}catch(e){if(t0(t),e!==e+0)throw e;tQ(1,0)}},A:function(e,t){var r=tZ();try{D.get(e)(t)}catch(e){if(t0(r),e!==e+0)throw e;tQ(1,0)}},B:function(e,t,r){var n=tZ();try{D.get(e)(t,r)}catch(e){if(t0(n),e!==e+0)throw e;tQ(1,0)}},D:function(e,t,r,n){var a=tZ();try{D.get(e)(t,r,n)}catch(e){if(t0(a),e!==e+0)throw e;tQ(1,0)}},L:function(e,t,r,n,a){var o=tZ();try{D.get(e)(t,r,n,a)}catch(e){if(t0(o),e!==e+0)throw e;tQ(1,0)}},sb:(e,t,r,n)=>tH(e,t,r,n)};!function(){function e(e){if(P=(A=e=e.exports).Yc,R(),D=A._c,B.unshift(A.Zc),L--,t.monitorRunDependencies&&t.monitorRunDependencies(L),0==L&&(null!==H&&(clearInterval(H),H=null),U)){var r=U;U=null,r()}return e}var r,n,a={a:tq};if(L++,t.monitorRunDependencies&&t.monitorRunDependencies(L),t.instantiateWasm)try{return t.instantiateWasm(a,e)}catch(e){_("Module.instantiateWasm callback failed with error: "+e),i(e)}(r=function(t){e(t.instance)},n=tp,b||"function"!=typeof WebAssembly.instantiateStreaming||j(n)||n.startsWith("file://")||p||"function"!=typeof fetch?V(n,a,r):fetch(n,{credentials:"same-origin"}).then(e=>WebAssembly.instantiateStreaming(e,a).then(r,function(e){return _("wasm streaming compile failed: "+e),_("falling back to ArrayBuffer instantiation"),V(n,a,r)}))).catch(i)}();var tz=t._free=e=>(tz=t._free=A.$c)(e),tJ=t._malloc=e=>(tJ=t._malloc=A.ad)(e),tK=e=>(tK=A.bd)(e);t.__embind_initialize_bindings=()=>(t.__embind_initialize_bindings=A.cd)();var tQ=(e,t)=>(tQ=A.dd)(e,t),tZ=()=>(tZ=A.ed)(),t0=e=>(t0=A.fd)(e);function t2(){function e(){if(!r&&(r=!0,t.calledRun=!0,!M)){if($(B),o(t),t.onRuntimeInitialized&&t.onRuntimeInitialized(),t.postRun)for("function"==typeof t.postRun&&(t.postRun=[t.postRun]);t.postRun.length;){var e=t.postRun.shift();G.unshift(e)}$(G)}}if(!(0<L)){if(t.preRun)for("function"==typeof t.preRun&&(t.preRun=[t.preRun]);t.preRun.length;)!function(){var e=t.preRun.shift();I.unshift(e)}();$(I),0<L||(t.setStatus?(t.setStatus("Running..."),setTimeout(function(){setTimeout(function(){t.setStatus("")},1),e()},1)):e())}}if(t.dynCall_viji=(e,r,n,a,o)=>(t.dynCall_viji=A.gd)(e,r,n,a,o),t.dynCall_vijiii=(e,r,n,a,o,i,u)=>(t.dynCall_vijiii=A.hd)(e,r,n,a,o,i,u),t.dynCall_viiiiij=(e,r,n,a,o,i,u,c)=>(t.dynCall_viiiiij=A.id)(e,r,n,a,o,i,u,c),t.dynCall_jii=(e,r,n)=>(t.dynCall_jii=A.jd)(e,r,n),t.dynCall_vij=(e,r,n,a)=>(t.dynCall_vij=A.kd)(e,r,n,a),t.dynCall_iiij=(e,r,n,a,o)=>(t.dynCall_iiij=A.ld)(e,r,n,a,o),t.dynCall_iiiij=(e,r,n,a,o,i)=>(t.dynCall_iiiij=A.md)(e,r,n,a,o,i),t.dynCall_viij=(e,r,n,a,o)=>(t.dynCall_viij=A.nd)(e,r,n,a,o),t.dynCall_viiij=(e,r,n,a,o,i)=>(t.dynCall_viiij=A.od)(e,r,n,a,o,i),t.dynCall_ji=(e,r)=>(t.dynCall_ji=A.pd)(e,r),t.dynCall_iij=(e,r,n,a)=>(t.dynCall_iij=A.qd)(e,r,n,a),t.dynCall_jiiiiii=(e,r,n,a,o,i,u)=>(t.dynCall_jiiiiii=A.rd)(e,r,n,a,o,i,u),t.dynCall_jiiiiji=(e,r,n,a,o,i,u,c)=>(t.dynCall_jiiiiji=A.sd)(e,r,n,a,o,i,u,c),t.dynCall_iijj=(e,r,n,a,o,i)=>(t.dynCall_iijj=A.td)(e,r,n,a,o,i),t.dynCall_iiiji=(e,r,n,a,o,i)=>(t.dynCall_iiiji=A.ud)(e,r,n,a,o,i),t.dynCall_iiji=(e,r,n,a,o)=>(t.dynCall_iiji=A.vd)(e,r,n,a,o),t.dynCall_iijjiii=(e,r,n,a,o,i,u,c,l)=>(t.dynCall_iijjiii=A.wd)(e,r,n,a,o,i,u,c,l),t.dynCall_vijjjii=(e,r,n,a,o,i,u,c,l,f)=>(t.dynCall_vijjjii=A.xd)(e,r,n,a,o,i,u,c,l,f),t.dynCall_jiji=(e,r,n,a,o)=>(t.dynCall_jiji=A.yd)(e,r,n,a,o),t.dynCall_viijii=(e,r,n,a,o,i,u)=>(t.dynCall_viijii=A.zd)(e,r,n,a,o,i,u),t.dynCall_iiiiij=(e,r,n,a,o,i,u)=>(t.dynCall_iiiiij=A.Ad)(e,r,n,a,o,i,u),t.dynCall_iiiiijj=(e,r,n,a,o,i,u,c,l)=>(t.dynCall_iiiiijj=A.Bd)(e,r,n,a,o,i,u,c,l),t.dynCall_iiiiiijj=(e,r,n,a,o,i,u,c,l,f)=>(t.dynCall_iiiiiijj=A.Cd)(e,r,n,a,o,i,u,c,l,f),U=function e(){r||t2(),r||(U=e)},t.preInit)for("function"==typeof t.preInit&&(t.preInit=[t.preInit]);0<t.preInit.length;)t.preInit.pop()();return t2(),t.ready}})();"object"==typeof e&&"object"==typeof t?t.exports=r:"function"==typeof define&&define.amd&&define([],()=>r)})()}}]);