(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@splinetool/react-spline/dist/react-spline.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@splinetool_runtime_build_9bb4c07f._.js",
  "static/chunks/node_modules_@splinetool_runtime_build_runtime_ef1768d2.js",
  "static/chunks/node_modules_cc3c27ea._.js",
  "static/chunks/node_modules_@splinetool_react-spline_dist_react-spline_118e4350.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@splinetool/react-spline/dist/react-spline.js [app-client] (ecmascript)");
    });
});
}}),
}]);