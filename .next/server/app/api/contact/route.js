(()=>{var e={};e.id=746,e.ids=[746],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},81350:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>p});var n=r(96559),o=r(48088),a=r(37719),i=r(32190);let u=new Map;function c(e){return e.trim().replace(/[<>]/g,"")}async function p(e){try{let t=e.headers.get("x-forwarded-for")||"unknown";if(function(e){let t=Date.now();if(!u.has(e))return u.set(e,{count:1,resetTime:t+9e5}),!1;let r=u.get(e);return t>r.resetTime?(u.set(e,{count:1,resetTime:t+9e5}),!1):r.count>=5||(r.count++,!1)}(t))return i.NextResponse.json({error:"Too many requests. Please try again later."},{status:429});let{name:r,email:s,subject:n,message:o}=await e.json();if(!r||!s||!n||!o)return i.NextResponse.json({error:"All fields are required."},{status:400});if(r.length<2||r.length>100)return i.NextResponse.json({error:"Name must be between 2 and 100 characters."},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s))return i.NextResponse.json({error:"Please provide a valid email address."},{status:400});if(n.length<5||n.length>200)return i.NextResponse.json({error:"Subject must be between 5 and 200 characters."},{status:400});if(o.length<10||o.length>2e3)return i.NextResponse.json({error:"Message must be between 10 and 2000 characters."},{status:400});let a={name:c(r),email:c(s),subject:c(n),message:c(o),timestamp:new Date().toISOString(),ip:t};if(console.log("Contact form submission:",a),!await l(a))return i.NextResponse.json({error:"Failed to send message. Please try again later."},{status:500});return i.NextResponse.json({success:!0,message:"Thank you for your message! I'll get back to you soon.",id:`msg_${Date.now()}_${Math.random().toString(36).substring(2,11)}`})}catch(e){return console.error("Contact form error:",e),i.NextResponse.json({error:"An unexpected error occurred. Please try again later."},{status:500})}}async function l(e){try{let t=process.env.SMTP_HOST,r=process.env.SMTP_USER,s=process.env.SMTP_PASS;return t&&r&&s||(console.warn("SMTP not configured, simulating email send for:",e.email),await new Promise(e=>setTimeout(e,1e3))),!0}catch(e){return console.error("Email sending error:",e),!1}}async function d(){return i.NextResponse.json({message:"Contact API endpoint is working!"},{status:200})}let m=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/contact/route",pathname:"/api/contact",filename:"route",bundlePath:"app/api/contact/route"},resolvedPagePath:"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/api/contact/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:x,serverHooks:f}=m;function h(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:x})}},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(81350));module.exports=s})();