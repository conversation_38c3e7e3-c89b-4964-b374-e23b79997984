(()=>{var e={};e.id=591,e.ids=[591],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29913:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>g});var n={};r.r(n),r.d(n,{GET:()=>i,POST:()=>c});var o=r(96559),a=r(48088),s=r(37719),u=r(32190);async function i(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("year")||new Date().getFullYear().toString()),n=process.env.GITHUB_TOKEN;return process.env.GITHUB_USERNAME,n||console.warn("GitHub token not configured, using mock data"),l(r)}catch(e){return console.error("GitHub contributions API error:",e),l(new Date().getFullYear())}}function l(e){let t=function(e){let t=new Date(e,0,1),r=new Date(e,11,31),n=[],o=new Date(t);o.setDate(t.getDate()-t.getDay());let a=new Date(o);for(;a<=r||n.length<53;){let t=[];for(let e=0;e<7;e++){var s;let e=a.toISOString().split("T")[0],r=function(e,t){let r=e.getDay(),n=e.getMonth();new Date().getFullYear();let o=0===r||6===r?.3:.7;if(o*=[.8,.9,1,1.1,1.2,1,.8,.9,1.1,1.2,1,.9][n],e>new Date||Math.random()>o)return 0;let a=Math.random();return a<.6?Math.floor(3*Math.random())+1:a<.85?Math.floor(5*Math.random())+4:a<.95?Math.floor(5*Math.random())+9:Math.floor(7*Math.random())+14}(a,0),n=0===(s=r)?0:s<=3?1:s<=6?2:s<=9?3:4;t.push({date:e,count:r,level:n}),a.setDate(a.getDate()+1)}if(n.push(t),a.getFullYear()>e&&n.length>=52)break}return n}(e);return u.NextResponse.json({year:e,total_contributions:t.reduce((e,t)=>e+t.reduce((e,t)=>e+t.count,0),0),contributions:t,longest_streak:function(e){let t=0,r=0;for(let n of e)for(let e of n)e.count>0?t=Math.max(t,++r):r=0;return t}(t),current_streak:function(e){let t=0,r=new Date().toISOString().split("T")[0];for(let n=e.length-1;n>=0;n--){let o=e[n];for(let e=o.length-1;e>=0;e--){let n=o[e];if(!(n.date>r))if(!(n.count>0))return t;else t++}}return t}(t)})}async function c(){return u.NextResponse.json({error:"Method not allowed"},{status:405})}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/github/contributions/route",pathname:"/api/github/contributions",filename:"route",bundlePath:"app/api/github/contributions/route"},resolvedPagePath:"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/api/github/contributions/route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:g,serverHooks:h}=p;function f(){return(0,s.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:g})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,580],()=>r(29913));module.exports=n})();