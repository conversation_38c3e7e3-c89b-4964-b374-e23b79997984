(()=>{var e={};e.id=495,e.ids=[495],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32856:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var o={};r.r(o),r.d(o,{GET:()=>p,POST:()=>l});var a=r(96559),s=r(48088),n=r(37719),i=r(32190);let u=new Map;async function p(e){try{var t;let r=e.headers.get("x-forwarded-for")||"unknown";if(function(e){let t=Date.now();if(!u.has(e))return u.set(e,{count:1,resetTime:t+6e4}),!1;let r=u.get(e);return t>r.resetTime?(u.set(e,{count:1,resetTime:t+6e4}),!1):r.count>=60||(r.count++,!1)}(r))return i.NextResponse.json({error:"Rate limit exceeded"},{status:429});let o=process.env.GITHUB_TOKEN,a=process.env.GITHUB_USERNAME||"GreenHacker420";if(!o)return console.warn("GitHub token not configured, using mock data"),c();let[s,n]=await Promise.all([fetch(`https://api.github.com/users/${a}`,{headers:{Authorization:`Bearer ${o}`,Accept:"application/vnd.github.v3+json","User-Agent":"Portfolio-App"},next:{revalidate:3600}}),fetch(`https://api.github.com/users/${a}/repos?per_page=100&sort=updated`,{headers:{Authorization:`Bearer ${o}`,Accept:"application/vnd.github.v3+json","User-Agent":"Portfolio-App"},next:{revalidate:3600}})]);if(!s.ok||!n.ok)return console.error("GitHub API error:",s.status,n.status),c();let p=await s.json(),l=await n.json(),d={totalStars:(t=l).reduce((e,t)=>e+t.stargazers_count,0),totalForks:t.reduce((e,t)=>e+t.forks_count,0),totalRepos:t.length,totalCommits:0,totalPRs:0,totalIssues:0,contributedRepos:t.filter(e=>!e.fork).length},g=function(e){let t={},r={JavaScript:"#f1e05a",TypeScript:"#3178c6",Python:"#3572A5",HTML:"#e34c26",CSS:"#563d7c",Java:"#b07219","C++":"#f34b7d",Go:"#00ADD8",Rust:"#dea584",PHP:"#4F5D95"};e.forEach(e=>{e.language&&(t[e.language]=(t[e.language]||0)+1)});let o=Object.values(t).reduce((e,t)=>e+t,0);return Object.entries(t).map(([e,t])=>({name:e,percentage:Math.round(t/o*100),color:r[e]||"#858585"})).sort((e,t)=>t.percentage-e.percentage).slice(0,5)}(l),h={user:{login:p.login,name:p.name||p.login,bio:p.bio||"Full-stack developer passionate about AI and open source",public_repos:p.public_repos,followers:p.followers,following:p.following,created_at:p.created_at,avatar_url:p.avatar_url,html_url:p.html_url},stats:d,languages:g,recentActivity:l.filter(e=>e.pushed_at).sort((e,t)=>new Date(t.pushed_at).getTime()-new Date(e.pushed_at).getTime()).slice(0,5).map(e=>({type:"push",repo:e.name,message:`Updated ${e.name}`,date:e.pushed_at,url:e.html_url}))};return i.NextResponse.json(h,{status:200})}catch(e){return console.error("GitHub API error:",e),c()}}function c(){let e={user:{login:"GreenHacker420",name:"GreenHacker",bio:"Full-stack developer passionate about AI and open source",public_repos:47,followers:123,following:89,created_at:"2021-01-15T00:00:00Z",avatar_url:"https://avatars.githubusercontent.com/u/placeholder",html_url:"https://github.com/GreenHacker420"},stats:{totalStars:47,totalCommits:430,totalPRs:28,totalIssues:15,contributedRepos:12},languages:[{name:"JavaScript",percentage:38,color:"#f1e05a"},{name:"TypeScript",percentage:24,color:"#3178c6"},{name:"Python",percentage:18,color:"#3572A5"},{name:"HTML",percentage:10,color:"#e34c26"},{name:"CSS",percentage:10,color:"#563d7c"}],recentActivity:[{type:"push",repo:"portfolio-nextjs",message:"Updated portfolio with new projects",date:new Date().toISOString()},{type:"star",repo:"awesome-react-components",message:"Starred repository",date:new Date(Date.now()-864e5).toISOString()}]};return i.NextResponse.json(e,{status:200})}async function l(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}let d=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/github/stats/route",pathname:"/api/github/stats",filename:"route",bundlePath:"app/api/github/stats/route"},resolvedPagePath:"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/api/github/stats/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:m}=d;function f(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,580],()=>r(32856));module.exports=o})();