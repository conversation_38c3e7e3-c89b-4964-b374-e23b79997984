(()=>{var e={};e.id=977,e.ids=[977],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},60272:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var a={};r.r(a),r.d(a,{GET:()=>p,POST:()=>u});var n=r(96559),s=r(48088),o=r(37719),i=r(32190);async function p(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),a=Math.min(parseInt(t.get("per_page")||"10"),100),n=process.env.GITHUB_TOKEN,s=process.env.GITHUB_USERNAME||"GreenHacker420";if(!n)return console.warn("GitHub token not configured, using mock data"),c();let o=await fetch(`https://api.github.com/users/${s}/repos?page=${r}&per_page=${a}&sort=updated&type=owner`,{headers:{Authorization:`Bearer ${n}`,Accept:"application/vnd.github.v3+json","User-Agent":"Portfolio-App"},next:{revalidate:1800}});if(!o.ok)return console.error("GitHub API error:",o.status),c();let p=(await o.json()).filter(e=>!e.fork&&!e.private).map(e=>({id:e.id,name:e.name,full_name:e.full_name,description:e.description,html_url:e.html_url,homepage:e.homepage,language:e.language,stargazers_count:e.stargazers_count,forks_count:e.forks_count,watchers_count:e.watchers_count,size:e.size,created_at:e.created_at,updated_at:e.updated_at,pushed_at:e.pushed_at,topics:e.topics||[],license:e.license?.name||null,default_branch:e.default_branch,open_issues_count:e.open_issues_count}));return i.NextResponse.json({repos:p,total_count:p.length,page:r,per_page:a})}catch(e){return console.error("GitHub repos API error:",e),c()}}function c(){let e=[{id:1,name:"portfolio-nextjs",full_name:"GreenHacker420/portfolio-nextjs",description:"Modern portfolio website built with Next.js, Three.js, and AI integration",html_url:"https://github.com/GreenHacker420/portfolio-nextjs",homepage:"https://greenhacker420.vercel.app",language:"TypeScript",stargazers_count:15,forks_count:3,watchers_count:15,size:2048,created_at:"2024-01-15T00:00:00Z",updated_at:new Date().toISOString(),pushed_at:new Date().toISOString(),topics:["nextjs","portfolio","threejs","ai","typescript"],license:"MIT",default_branch:"main",open_issues_count:2},{id:2,name:"ai-chat-assistant",full_name:"GreenHacker420/ai-chat-assistant",description:"Intelligent chat assistant powered by Gemini AI with advanced conversation capabilities",html_url:"https://github.com/GreenHacker420/ai-chat-assistant",homepage:null,language:"Python",stargazers_count:8,forks_count:2,watchers_count:8,size:1024,created_at:"2024-02-01T00:00:00Z",updated_at:new Date(Date.now()-864e5).toISOString(),pushed_at:new Date(Date.now()-864e5).toISOString(),topics:["ai","chatbot","gemini","python","machine-learning"],license:"Apache-2.0",default_branch:"main",open_issues_count:1},{id:3,name:"react-3d-components",full_name:"GreenHacker420/react-3d-components",description:"Collection of reusable 3D React components using Three.js and React Three Fiber",html_url:"https://github.com/GreenHacker420/react-3d-components",homepage:"https://react-3d-components.vercel.app",language:"JavaScript",stargazers_count:12,forks_count:4,watchers_count:12,size:1536,created_at:"2023-11-20T00:00:00Z",updated_at:new Date(Date.now()-1728e5).toISOString(),pushed_at:new Date(Date.now()-1728e5).toISOString(),topics:["react","threejs","components","3d","webgl"],license:"MIT",default_branch:"main",open_issues_count:0}];return i.NextResponse.json({repos:e,total_count:e.length,page:1,per_page:10})}async function u(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}let l=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/github/repos/route",pathname:"/api/github/repos",filename:"route",bundlePath:"app/api/github/repos/route"},resolvedPagePath:"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/api/github/repos/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:g}=l;function _(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,580],()=>r(60272));module.exports=a})();