"use strict";exports.id=831,exports.ids=[831],exports.modules={45831:(t,e,r)=>{r.r(e),r.d(e,{default:()=>n});let n=function(){var t="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return function(e){var r,n,o,_,p,i,a,s,c,u,l,y,m,d,f,g,e=void 0!==(e=e||{})?e:{};e.ready=new Promise(function(t,e){n=t,o=e});var b={};for(_ in e)e.hasOwnProperty(_)&&(b[_]=e[_]);var h=[],v="./this.program",C="";"undefined"!=typeof document&&document.currentScript&&(C=document.currentScript.src),t&&(C=t),C=0!==C.indexOf("blob:")?C.substr(0,C.replace(/[?#].*/,"").lastIndexOf("/")+1):"";var w=e.print||console.log.bind(console),A=e.printErr||console.warn.bind(console);for(_ in b)b.hasOwnProperty(_)&&(e[_]=b[_]);b=null,e.arguments&&(h=e.arguments),e.thisProgram&&(v=e.thisProgram),e.quit&&e.quit;var P=[];e.wasmBinary&&(a=e.wasmBinary),e.noExitRuntime,"object"!=typeof WebAssembly&&k("no native wasm support detected");var j=!1;function x(t,e){t||k("Assertion failed: "+e)}var D="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function S(t,e,r){for(var n=e+r,o=e;t[o]&&!(o>=n);)++o;if(o-e>16&&t.subarray&&D)return D.decode(t.subarray(e,o));for(var _="";e<o;){var p=t[e++];if(!(128&p)){_+=String.fromCharCode(p);continue}var i=63&t[e++];if((224&p)==192){_+=String.fromCharCode((31&p)<<6|i);continue}var a=63&t[e++];if((p=(240&p)==224?(15&p)<<12|i<<6|a:(7&p)<<18|i<<12|a<<6|63&t[e++])<65536)_+=String.fromCharCode(p);else{var s=p-65536;_+=String.fromCharCode(55296|s>>10,56320|1023&s)}}return _}function M(t,e){return t?S(l,t,e):""}function N(t){c=t,e.HEAP8=u=new Int8Array(t),e.HEAP16=new Int16Array(t),e.HEAP32=y=new Int32Array(t),e.HEAPU8=l=new Uint8Array(t),e.HEAPU16=new Uint16Array(t),e.HEAPU32=new Uint32Array(t),e.HEAPF32=m=new Float32Array(t),e.HEAPF64=new Float64Array(t)}e.INITIAL_MEMORY;var O=[],R=[],E=[],T=0,z=null,F=null;function k(t){e.onAbort&&e.onAbort(t),A(t="Aborted("+t+")"),j=!0,t+=". Build with -s ASSERTIONS=1 for more info.";var r=new WebAssembly.RuntimeError(t);throw o(r),r}function V(t){return t.startsWith("data:application/octet-stream;base64,")}function W(t){try{if(t==f&&a)return new Uint8Array(a);if(p)return p(t);throw"both async and sync fetching of the wasm failed"}catch(t){k(t)}}function H(t){for(;t.length>0;){var r=t.shift();if("function"==typeof r){r(e);continue}var n=r.func;"number"==typeof n?void 0===r.arg?L(n)():L(n)(r.arg):n(void 0===r.arg?null:r.arg)}}e.preloadedImages={},e.preloadedAudios={},V(f="navmesh.wasm")||(r=f,f=e.locateFile?e.locateFile(r,C):C+r);var I=[];function L(t){var e=I[t];return e||(t>=I.length&&(I.length=t+1),I[t]=e=d.get(t)),e}function Q(t,e){d.set(t,e),I[t]=e}function Y(t){this.excPtr=t,this.ptr=t-16,this.set_type=function(t){y[this.ptr+4>>2]=t},this.get_type=function(){return y[this.ptr+4>>2]},this.set_destructor=function(t){y[this.ptr+8>>2]=t},this.get_destructor=function(){return y[this.ptr+8>>2]},this.set_refcount=function(t){y[this.ptr>>2]=t},this.set_caught=function(t){t=+!!t,u[this.ptr+12|0]=t},this.get_caught=function(){return 0!=u[this.ptr+12|0]},this.set_rethrown=function(t){t=+!!t,u[this.ptr+13|0]=t},this.get_rethrown=function(){return 0!=u[this.ptr+13|0]},this.init=function(t,e){this.set_type(t),this.set_destructor(e),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var t=y[this.ptr>>2];y[this.ptr>>2]=t+1},this.release_ref=function(){var t=y[this.ptr>>2];return y[this.ptr>>2]=t-1,1===t}}var U=0,q={};function B(){if(!B.strings){var t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:v||"./this.program"};for(var e in q)void 0===q[e]?delete t[e]:t[e]=q[e];var r=[];for(var e in t)r.push(e+"="+t[e]);B.strings=r}return B.strings}var G={mappings:{},buffers:[null,[],[]],printChar:function(t,e){var r=G.buffers[t];0===e||10===e?((1===t?w:A)(S(r,0)),r.length=0):r.push(e)},varargs:void 0,get:function(){return G.varargs+=4,y[G.varargs-4>>2]},getStr:function(t){return M(t)},get64:function(t,e){return t}};function J(t){return t%4==0&&(t%100!=0||t%400==0)}function X(t,e){for(var r=0,n=0;n<=e;r+=t[n++]);return r}var K=[31,29,31,30,31,30,31,31,30,31,30,31],Z=[31,28,31,30,31,30,31,31,30,31,30,31];function $(t,e){for(var r=new Date(t.getTime());e>0;){var n=J(r.getFullYear()),o=r.getMonth(),_=(n?K:Z)[o];if(e>_-r.getDate())e-=_-r.getDate()+1,r.setDate(1),o<11?r.setMonth(o+1):(r.setMonth(0),r.setFullYear(r.getFullYear()+1));else{r.setDate(r.getDate()+e);break}}return r}var tt={l:function(t){return rr(t+16)+16},k:function(t,e,r){throw new Y(t).init(e,r),U++,t},b:function(){k("")},j:function(t,e,r){l.copyWithin(t,e,e+r)},a:function(t){var e=l.length;if((t>>>=0)>0x80000000)return!1;for(var r=1;r<=4;r*=2){var n,o=e*(1+.2/r);if(o=Math.min(o,t+0x6000000),function(t){try{return s.grow(t-c.byteLength+65535>>>16),N(s.buffer),1}catch(t){}}(Math.min(0x80000000,((n=Math.max(t,o))%65536>0&&(n+=65536-n%65536),n))))return!0}return!1},g:function(t,e){var r=0;return B().forEach(function(n,o){var _=e+r;y[t+4*o>>2]=_;for(var p=_,i=0;i<n.length;++i)u[0|p++]=n.charCodeAt(i);u[0|p]=0,r+=n.length+1}),0},h:function(t,e){var r=B();y[t>>2]=r.length;var n=0;return r.forEach(function(t){n+=t.length+1}),y[e>>2]=n,0},c:function(t){return 0},e:function(t,e,r,n){var o=G.getStreamFromFD(t),_=G.doReadv(o,e,r);return y[n>>2]=_,0},i:function(t,e,r,n,o){},d:function(t,e,r,n){for(var o=0,_=0;_<r;_++){var p=y[e>>2],i=y[e+4>>2];e+=8;for(var a=0;a<i;a++)G.printChar(t,l[p+a]);o+=i}return y[n>>2]=o,0},f:function(t,e,r,n){return function(t,e,r,n){var o,_,p,i,a,s=y[n+40>>2],c={tm_sec:y[n>>2],tm_min:y[n+4>>2],tm_hour:y[n+8>>2],tm_mday:y[n+12>>2],tm_mon:y[n+16>>2],tm_year:y[n+20>>2],tm_wday:y[n+24>>2],tm_yday:y[n+28>>2],tm_isdst:y[n+32>>2],tm_gmtoff:y[n+36>>2],tm_zone:s?M(s):""},l=M(r),m={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var d in m)l=l.replace(RegExp(d,"g"),m[d]);var f=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],g=["January","February","March","April","May","June","July","August","September","October","November","December"];function b(t,e,r){for(var n="number"==typeof t?t.toString():t||"";n.length<e;)n=r[0]+n;return n}function h(t,e){return b(t,e,"0")}function v(t,e){var r;function n(t){return t<0?-1:+(t>0)}return 0===(r=n(t.getFullYear()-e.getFullYear()))&&0===(r=n(t.getMonth()-e.getMonth()))&&(r=n(t.getDate()-e.getDate())),r}function C(t){switch(t.getDay()){case 0:return new Date(t.getFullYear()-1,11,29);case 1:return t;case 2:return new Date(t.getFullYear(),0,3);case 3:return new Date(t.getFullYear(),0,2);case 4:return new Date(t.getFullYear(),0,1);case 5:return new Date(t.getFullYear()-1,11,31);case 6:return new Date(t.getFullYear()-1,11,30)}}function w(t){var e=$(new Date(t.tm_year+1900,0,1),t.tm_yday),r=new Date(e.getFullYear(),0,4),n=new Date(e.getFullYear()+1,0,4),o=C(r),_=C(n);return 0>=v(o,e)?0>=v(_,e)?e.getFullYear()+1:e.getFullYear():e.getFullYear()-1}var A={"%a":function(t){return f[t.tm_wday].substring(0,3)},"%A":function(t){return f[t.tm_wday]},"%b":function(t){return g[t.tm_mon].substring(0,3)},"%B":function(t){return g[t.tm_mon]},"%C":function(t){return h((t.tm_year+1900)/100|0,2)},"%d":function(t){return h(t.tm_mday,2)},"%e":function(t){return b(t.tm_mday,2," ")},"%g":function(t){return w(t).toString().substring(2)},"%G":function(t){return w(t)},"%H":function(t){return h(t.tm_hour,2)},"%I":function(t){var e=t.tm_hour;return 0==e?e=12:e>12&&(e-=12),h(e,2)},"%j":function(t){return h(t.tm_mday+X(J(t.tm_year+1900)?K:Z,t.tm_mon-1),3)},"%m":function(t){return h(t.tm_mon+1,2)},"%M":function(t){return h(t.tm_min,2)},"%n":function(){return"\n"},"%p":function(t){return t.tm_hour>=0&&t.tm_hour<12?"AM":"PM"},"%S":function(t){return h(t.tm_sec,2)},"%t":function(){return"	"},"%u":function(t){return t.tm_wday||7},"%U":function(t){var e=new Date(t.tm_year+1900,0,1),r=0===e.getDay()?e:$(e,7-e.getDay()),n=new Date(t.tm_year+1900,t.tm_mon,t.tm_mday);if(0>v(r,n)){var o=X(J(n.getFullYear())?K:Z,n.getMonth()-1)-31;return h(Math.ceil((31-r.getDate()+o+n.getDate())/7),2)}return 0===v(r,e)?"01":"00"},"%V":function(t){var e,r=new Date(t.tm_year+1900,0,4),n=new Date(t.tm_year+1901,0,4),o=C(r),_=C(n),p=$(new Date(t.tm_year+1900,0,1),t.tm_yday);return 0>v(p,o)?"53":0>=v(_,p)?"01":h(Math.ceil((o.getFullYear()<t.tm_year+1900?t.tm_yday+32-o.getDate():t.tm_yday+1-o.getDate())/7),2)},"%w":function(t){return t.tm_wday},"%W":function(t){var e=new Date(t.tm_year,0,1),r=1===e.getDay()?e:$(e,0===e.getDay()?1:7-e.getDay()+1),n=new Date(t.tm_year+1900,t.tm_mon,t.tm_mday);if(0>v(r,n)){var o=X(J(n.getFullYear())?K:Z,n.getMonth()-1)-31;return h(Math.ceil((31-r.getDate()+o+n.getDate())/7),2)}return 0===v(r,e)?"01":"00"},"%y":function(t){return(t.tm_year+1900).toString().substring(2)},"%Y":function(t){return t.tm_year+1900},"%z":function(t){var e=t.tm_gmtoff;return(e>=0?"+":"-")+String("0000"+(e=(e=Math.abs(e)/60)/60*100+e%60)).slice(-4)},"%Z":function(t){return t.tm_zone},"%%":function(){return"%"}};for(var d in A)l.includes(d)&&(l=l.replace(RegExp(d,"g"),A[d](c)));var P=(o=l,_=!1,i=Array(function(t){for(var e=0,r=0;r<t.length;++r){var n=t.charCodeAt(r);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&t.charCodeAt(++r)),n<=127?++e:n<=2047?e+=2:n<=65535?e+=3:e+=4}return e}(o)+1),a=function(t,e,r,n){if(!(n>0))return 0;for(var o=r,_=r+n-1,p=0;p<t.length;++p){var i=t.charCodeAt(p);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&t.charCodeAt(++p)),i<=127){if(r>=_)break;e[r++]=i}else if(i<=2047){if(r+1>=_)break;e[r++]=192|i>>6,e[r++]=128|63&i}else if(i<=65535){if(r+2>=_)break;e[r++]=224|i>>12,e[r++]=128|i>>6&63,e[r++]=128|63&i}else{if(r+3>=_)break;e[r++]=240|i>>18,e[r++]=128|i>>12&63,e[r++]=128|i>>6&63,e[r++]=128|63&i}}return e[r]=0,r-o}(o,i,0,i.length),_&&(i.length=a),i);return P.length>e?0:(u.set(P,t),P.length-1)}(t,e,r,n)}};!function(){var t={a:tt};function r(t,r){var n,o=t.exports;e.asm=o,N((s=e.asm.m).buffer),d=e.asm.Jb,n=e.asm.n,R.unshift(n);if(T--,e.monitorRunDependencies&&e.monitorRunDependencies(T),0==T&&(null!==z&&(clearInterval(z),z=null),F)){var _=F;F=null,_()}}function n(t){r(t.instance)}function _(e){return(!a&&"function"==typeof fetch?fetch(f,{credentials:"same-origin"}).then(function(t){if(!t.ok)throw"failed to load wasm binary file at '"+f+"'";return t.arrayBuffer()}).catch(function(){return W(f)}):Promise.resolve().then(function(){return W(f)})).then(function(e){return WebAssembly.instantiate(e,t)}).then(function(t){return t}).then(e,function(t){A("failed to asynchronously prepare wasm: "+t),k(t)})}if(T++,e.monitorRunDependencies&&e.monitorRunDependencies(T),e.instantiateWasm)try{return e.instantiateWasm(t,r)}catch(t){return A("Module.instantiateWasm callback failed with error: "+t),!1}(!a&&"function"==typeof WebAssembly.instantiateStreaming&&!V(f)&&"function"==typeof fetch?fetch(f,{credentials:"same-origin"}).then(function(e){return WebAssembly.instantiateStreaming(e,t).then(n,function(t){return A("wasm streaming compile failed: "+t),A("falling back to ArrayBuffer instantiation"),_(n)})}):_(n)).catch(o)}(),e.___wasm_call_ctors=function(){return(e.___wasm_call_ctors=e.asm.n).apply(null,arguments)};var te=e._emscripten_bind_VoidPtr___destroy___0=function(){return(te=e._emscripten_bind_VoidPtr___destroy___0=e.asm.o).apply(null,arguments)},tr=e._emscripten_bind_rcConfig_rcConfig_0=function(){return(tr=e._emscripten_bind_rcConfig_rcConfig_0=e.asm.p).apply(null,arguments)},tn=e._emscripten_bind_rcConfig_get_width_0=function(){return(tn=e._emscripten_bind_rcConfig_get_width_0=e.asm.q).apply(null,arguments)},to=e._emscripten_bind_rcConfig_set_width_1=function(){return(to=e._emscripten_bind_rcConfig_set_width_1=e.asm.r).apply(null,arguments)},t_=e._emscripten_bind_rcConfig_get_height_0=function(){return(t_=e._emscripten_bind_rcConfig_get_height_0=e.asm.s).apply(null,arguments)},tp=e._emscripten_bind_rcConfig_set_height_1=function(){return(tp=e._emscripten_bind_rcConfig_set_height_1=e.asm.t).apply(null,arguments)},ti=e._emscripten_bind_rcConfig_get_tileSize_0=function(){return(ti=e._emscripten_bind_rcConfig_get_tileSize_0=e.asm.u).apply(null,arguments)},ta=e._emscripten_bind_rcConfig_set_tileSize_1=function(){return(ta=e._emscripten_bind_rcConfig_set_tileSize_1=e.asm.v).apply(null,arguments)},ts=e._emscripten_bind_rcConfig_get_borderSize_0=function(){return(ts=e._emscripten_bind_rcConfig_get_borderSize_0=e.asm.w).apply(null,arguments)},tc=e._emscripten_bind_rcConfig_set_borderSize_1=function(){return(tc=e._emscripten_bind_rcConfig_set_borderSize_1=e.asm.x).apply(null,arguments)},tu=e._emscripten_bind_rcConfig_get_cs_0=function(){return(tu=e._emscripten_bind_rcConfig_get_cs_0=e.asm.y).apply(null,arguments)},tl=e._emscripten_bind_rcConfig_set_cs_1=function(){return(tl=e._emscripten_bind_rcConfig_set_cs_1=e.asm.z).apply(null,arguments)},ty=e._emscripten_bind_rcConfig_get_ch_0=function(){return(ty=e._emscripten_bind_rcConfig_get_ch_0=e.asm.A).apply(null,arguments)},tm=e._emscripten_bind_rcConfig_set_ch_1=function(){return(tm=e._emscripten_bind_rcConfig_set_ch_1=e.asm.B).apply(null,arguments)},td=e._emscripten_bind_rcConfig_get_bmin_1=function(){return(td=e._emscripten_bind_rcConfig_get_bmin_1=e.asm.C).apply(null,arguments)},tf=e._emscripten_bind_rcConfig_set_bmin_2=function(){return(tf=e._emscripten_bind_rcConfig_set_bmin_2=e.asm.D).apply(null,arguments)},tg=e._emscripten_bind_rcConfig_get_bmax_1=function(){return(tg=e._emscripten_bind_rcConfig_get_bmax_1=e.asm.E).apply(null,arguments)},tb=e._emscripten_bind_rcConfig_set_bmax_2=function(){return(tb=e._emscripten_bind_rcConfig_set_bmax_2=e.asm.F).apply(null,arguments)},th=e._emscripten_bind_rcConfig_get_walkableSlopeAngle_0=function(){return(th=e._emscripten_bind_rcConfig_get_walkableSlopeAngle_0=e.asm.G).apply(null,arguments)},tv=e._emscripten_bind_rcConfig_set_walkableSlopeAngle_1=function(){return(tv=e._emscripten_bind_rcConfig_set_walkableSlopeAngle_1=e.asm.H).apply(null,arguments)},tC=e._emscripten_bind_rcConfig_get_walkableHeight_0=function(){return(tC=e._emscripten_bind_rcConfig_get_walkableHeight_0=e.asm.I).apply(null,arguments)},tw=e._emscripten_bind_rcConfig_set_walkableHeight_1=function(){return(tw=e._emscripten_bind_rcConfig_set_walkableHeight_1=e.asm.J).apply(null,arguments)},tA=e._emscripten_bind_rcConfig_get_walkableClimb_0=function(){return(tA=e._emscripten_bind_rcConfig_get_walkableClimb_0=e.asm.K).apply(null,arguments)},tP=e._emscripten_bind_rcConfig_set_walkableClimb_1=function(){return(tP=e._emscripten_bind_rcConfig_set_walkableClimb_1=e.asm.L).apply(null,arguments)},tj=e._emscripten_bind_rcConfig_get_walkableRadius_0=function(){return(tj=e._emscripten_bind_rcConfig_get_walkableRadius_0=e.asm.M).apply(null,arguments)},tx=e._emscripten_bind_rcConfig_set_walkableRadius_1=function(){return(tx=e._emscripten_bind_rcConfig_set_walkableRadius_1=e.asm.N).apply(null,arguments)},tD=e._emscripten_bind_rcConfig_get_maxEdgeLen_0=function(){return(tD=e._emscripten_bind_rcConfig_get_maxEdgeLen_0=e.asm.O).apply(null,arguments)},tS=e._emscripten_bind_rcConfig_set_maxEdgeLen_1=function(){return(tS=e._emscripten_bind_rcConfig_set_maxEdgeLen_1=e.asm.P).apply(null,arguments)},tM=e._emscripten_bind_rcConfig_get_maxSimplificationError_0=function(){return(tM=e._emscripten_bind_rcConfig_get_maxSimplificationError_0=e.asm.Q).apply(null,arguments)},tN=e._emscripten_bind_rcConfig_set_maxSimplificationError_1=function(){return(tN=e._emscripten_bind_rcConfig_set_maxSimplificationError_1=e.asm.R).apply(null,arguments)},tO=e._emscripten_bind_rcConfig_get_minRegionArea_0=function(){return(tO=e._emscripten_bind_rcConfig_get_minRegionArea_0=e.asm.S).apply(null,arguments)},tR=e._emscripten_bind_rcConfig_set_minRegionArea_1=function(){return(tR=e._emscripten_bind_rcConfig_set_minRegionArea_1=e.asm.T).apply(null,arguments)},tE=e._emscripten_bind_rcConfig_get_mergeRegionArea_0=function(){return(tE=e._emscripten_bind_rcConfig_get_mergeRegionArea_0=e.asm.U).apply(null,arguments)},tT=e._emscripten_bind_rcConfig_set_mergeRegionArea_1=function(){return(tT=e._emscripten_bind_rcConfig_set_mergeRegionArea_1=e.asm.V).apply(null,arguments)},tz=e._emscripten_bind_rcConfig_get_maxVertsPerPoly_0=function(){return(tz=e._emscripten_bind_rcConfig_get_maxVertsPerPoly_0=e.asm.W).apply(null,arguments)},tF=e._emscripten_bind_rcConfig_set_maxVertsPerPoly_1=function(){return(tF=e._emscripten_bind_rcConfig_set_maxVertsPerPoly_1=e.asm.X).apply(null,arguments)},tk=e._emscripten_bind_rcConfig_get_detailSampleDist_0=function(){return(tk=e._emscripten_bind_rcConfig_get_detailSampleDist_0=e.asm.Y).apply(null,arguments)},tV=e._emscripten_bind_rcConfig_set_detailSampleDist_1=function(){return(tV=e._emscripten_bind_rcConfig_set_detailSampleDist_1=e.asm.Z).apply(null,arguments)},tW=e._emscripten_bind_rcConfig_get_detailSampleMaxError_0=function(){return(tW=e._emscripten_bind_rcConfig_get_detailSampleMaxError_0=e.asm._).apply(null,arguments)},tH=e._emscripten_bind_rcConfig_set_detailSampleMaxError_1=function(){return(tH=e._emscripten_bind_rcConfig_set_detailSampleMaxError_1=e.asm.$).apply(null,arguments)},tI=e._emscripten_bind_rcConfig___destroy___0=function(){return(tI=e._emscripten_bind_rcConfig___destroy___0=e.asm.aa).apply(null,arguments)},tL=e._emscripten_bind_Vec3_Vec3_0=function(){return(tL=e._emscripten_bind_Vec3_Vec3_0=e.asm.ba).apply(null,arguments)},tQ=e._emscripten_bind_Vec3_Vec3_3=function(){return(tQ=e._emscripten_bind_Vec3_Vec3_3=e.asm.ca).apply(null,arguments)},tY=e._emscripten_bind_Vec3_get_x_0=function(){return(tY=e._emscripten_bind_Vec3_get_x_0=e.asm.da).apply(null,arguments)},tU=e._emscripten_bind_Vec3_set_x_1=function(){return(tU=e._emscripten_bind_Vec3_set_x_1=e.asm.ea).apply(null,arguments)},tq=e._emscripten_bind_Vec3_get_y_0=function(){return(tq=e._emscripten_bind_Vec3_get_y_0=e.asm.fa).apply(null,arguments)},tB=e._emscripten_bind_Vec3_set_y_1=function(){return(tB=e._emscripten_bind_Vec3_set_y_1=e.asm.ga).apply(null,arguments)},tG=e._emscripten_bind_Vec3_get_z_0=function(){return(tG=e._emscripten_bind_Vec3_get_z_0=e.asm.ha).apply(null,arguments)},tJ=e._emscripten_bind_Vec3_set_z_1=function(){return(tJ=e._emscripten_bind_Vec3_set_z_1=e.asm.ia).apply(null,arguments)},tX=e._emscripten_bind_Vec3___destroy___0=function(){return(tX=e._emscripten_bind_Vec3___destroy___0=e.asm.ja).apply(null,arguments)},tK=e._emscripten_bind_Triangle_Triangle_0=function(){return(tK=e._emscripten_bind_Triangle_Triangle_0=e.asm.ka).apply(null,arguments)},tZ=e._emscripten_bind_Triangle_getPoint_1=function(){return(tZ=e._emscripten_bind_Triangle_getPoint_1=e.asm.la).apply(null,arguments)},t$=e._emscripten_bind_Triangle___destroy___0=function(){return(t$=e._emscripten_bind_Triangle___destroy___0=e.asm.ma).apply(null,arguments)},t0=e._emscripten_bind_DebugNavMesh_DebugNavMesh_0=function(){return(t0=e._emscripten_bind_DebugNavMesh_DebugNavMesh_0=e.asm.na).apply(null,arguments)},t1=e._emscripten_bind_DebugNavMesh_getTriangleCount_0=function(){return(t1=e._emscripten_bind_DebugNavMesh_getTriangleCount_0=e.asm.oa).apply(null,arguments)},t2=e._emscripten_bind_DebugNavMesh_getTriangle_1=function(){return(t2=e._emscripten_bind_DebugNavMesh_getTriangle_1=e.asm.pa).apply(null,arguments)},t3=e._emscripten_bind_DebugNavMesh___destroy___0=function(){return(t3=e._emscripten_bind_DebugNavMesh___destroy___0=e.asm.qa).apply(null,arguments)},t6=e._emscripten_bind_dtNavMesh___destroy___0=function(){return(t6=e._emscripten_bind_dtNavMesh___destroy___0=e.asm.ra).apply(null,arguments)},t5=e._emscripten_bind_NavmeshData_NavmeshData_0=function(){return(t5=e._emscripten_bind_NavmeshData_NavmeshData_0=e.asm.sa).apply(null,arguments)},t4=e._emscripten_bind_NavmeshData_get_dataPointer_0=function(){return(t4=e._emscripten_bind_NavmeshData_get_dataPointer_0=e.asm.ta).apply(null,arguments)},t8=e._emscripten_bind_NavmeshData_set_dataPointer_1=function(){return(t8=e._emscripten_bind_NavmeshData_set_dataPointer_1=e.asm.ua).apply(null,arguments)},t9=e._emscripten_bind_NavmeshData_get_size_0=function(){return(t9=e._emscripten_bind_NavmeshData_get_size_0=e.asm.va).apply(null,arguments)},t7=e._emscripten_bind_NavmeshData_set_size_1=function(){return(t7=e._emscripten_bind_NavmeshData_set_size_1=e.asm.wa).apply(null,arguments)},et=e._emscripten_bind_NavmeshData___destroy___0=function(){return(et=e._emscripten_bind_NavmeshData___destroy___0=e.asm.xa).apply(null,arguments)},ee=e._emscripten_bind_NavPath_getPointCount_0=function(){return(ee=e._emscripten_bind_NavPath_getPointCount_0=e.asm.ya).apply(null,arguments)},er=e._emscripten_bind_NavPath_getPoint_1=function(){return(er=e._emscripten_bind_NavPath_getPoint_1=e.asm.za).apply(null,arguments)},en=e._emscripten_bind_NavPath___destroy___0=function(){return(en=e._emscripten_bind_NavPath___destroy___0=e.asm.Aa).apply(null,arguments)},eo=e._emscripten_bind_dtObstacleRef___destroy___0=function(){return(eo=e._emscripten_bind_dtObstacleRef___destroy___0=e.asm.Ba).apply(null,arguments)},e_=e._emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0=function(){return(e_=e._emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0=e.asm.Ca).apply(null,arguments)},ep=e._emscripten_bind_dtCrowdAgentParams_get_radius_0=function(){return(ep=e._emscripten_bind_dtCrowdAgentParams_get_radius_0=e.asm.Da).apply(null,arguments)},ei=e._emscripten_bind_dtCrowdAgentParams_set_radius_1=function(){return(ei=e._emscripten_bind_dtCrowdAgentParams_set_radius_1=e.asm.Ea).apply(null,arguments)},ea=e._emscripten_bind_dtCrowdAgentParams_get_height_0=function(){return(ea=e._emscripten_bind_dtCrowdAgentParams_get_height_0=e.asm.Fa).apply(null,arguments)},es=e._emscripten_bind_dtCrowdAgentParams_set_height_1=function(){return(es=e._emscripten_bind_dtCrowdAgentParams_set_height_1=e.asm.Ga).apply(null,arguments)},ec=e._emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0=function(){return(ec=e._emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0=e.asm.Ha).apply(null,arguments)},eu=e._emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1=function(){return(eu=e._emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1=e.asm.Ia).apply(null,arguments)},el=e._emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0=function(){return(el=e._emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0=e.asm.Ja).apply(null,arguments)},ey=e._emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1=function(){return(ey=e._emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1=e.asm.Ka).apply(null,arguments)},em=e._emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0=function(){return(em=e._emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0=e.asm.La).apply(null,arguments)},ed=e._emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1=function(){return(ed=e._emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1=e.asm.Ma).apply(null,arguments)},ef=e._emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0=function(){return(ef=e._emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0=e.asm.Na).apply(null,arguments)},eg=e._emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1=function(){return(eg=e._emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1=e.asm.Oa).apply(null,arguments)},eb=e._emscripten_bind_dtCrowdAgentParams_get_separationWeight_0=function(){return(eb=e._emscripten_bind_dtCrowdAgentParams_get_separationWeight_0=e.asm.Pa).apply(null,arguments)},eh=e._emscripten_bind_dtCrowdAgentParams_set_separationWeight_1=function(){return(eh=e._emscripten_bind_dtCrowdAgentParams_set_separationWeight_1=e.asm.Qa).apply(null,arguments)},ev=e._emscripten_bind_dtCrowdAgentParams_get_updateFlags_0=function(){return(ev=e._emscripten_bind_dtCrowdAgentParams_get_updateFlags_0=e.asm.Ra).apply(null,arguments)},eC=e._emscripten_bind_dtCrowdAgentParams_set_updateFlags_1=function(){return(eC=e._emscripten_bind_dtCrowdAgentParams_set_updateFlags_1=e.asm.Sa).apply(null,arguments)},ew=e._emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0=function(){return(ew=e._emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0=e.asm.Ta).apply(null,arguments)},eA=e._emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1=function(){return(eA=e._emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1=e.asm.Ua).apply(null,arguments)},eP=e._emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0=function(){return(eP=e._emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0=e.asm.Va).apply(null,arguments)},ej=e._emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1=function(){return(ej=e._emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1=e.asm.Wa).apply(null,arguments)},ex=e._emscripten_bind_dtCrowdAgentParams_get_userData_0=function(){return(ex=e._emscripten_bind_dtCrowdAgentParams_get_userData_0=e.asm.Xa).apply(null,arguments)},eD=e._emscripten_bind_dtCrowdAgentParams_set_userData_1=function(){return(eD=e._emscripten_bind_dtCrowdAgentParams_set_userData_1=e.asm.Ya).apply(null,arguments)},eS=e._emscripten_bind_dtCrowdAgentParams___destroy___0=function(){return(eS=e._emscripten_bind_dtCrowdAgentParams___destroy___0=e.asm.Za).apply(null,arguments)},eM=e._emscripten_bind_NavMesh_NavMesh_0=function(){return(eM=e._emscripten_bind_NavMesh_NavMesh_0=e.asm._a).apply(null,arguments)},eN=e._emscripten_bind_NavMesh_destroy_0=function(){return(eN=e._emscripten_bind_NavMesh_destroy_0=e.asm.$a).apply(null,arguments)},eO=e._emscripten_bind_NavMesh_build_5=function(){return(eO=e._emscripten_bind_NavMesh_build_5=e.asm.ab).apply(null,arguments)},eR=e._emscripten_bind_NavMesh_buildFromNavmeshData_1=function(){return(eR=e._emscripten_bind_NavMesh_buildFromNavmeshData_1=e.asm.bb).apply(null,arguments)},eE=e._emscripten_bind_NavMesh_getNavmeshData_0=function(){return(eE=e._emscripten_bind_NavMesh_getNavmeshData_0=e.asm.cb).apply(null,arguments)},eT=e._emscripten_bind_NavMesh_freeNavmeshData_1=function(){return(eT=e._emscripten_bind_NavMesh_freeNavmeshData_1=e.asm.db).apply(null,arguments)},ez=e._emscripten_bind_NavMesh_getDebugNavMesh_0=function(){return(ez=e._emscripten_bind_NavMesh_getDebugNavMesh_0=e.asm.eb).apply(null,arguments)},eF=e._emscripten_bind_NavMesh_getClosestPoint_1=function(){return(eF=e._emscripten_bind_NavMesh_getClosestPoint_1=e.asm.fb).apply(null,arguments)},ek=e._emscripten_bind_NavMesh_getRandomPointAround_2=function(){return(ek=e._emscripten_bind_NavMesh_getRandomPointAround_2=e.asm.gb).apply(null,arguments)},eV=e._emscripten_bind_NavMesh_moveAlong_2=function(){return(eV=e._emscripten_bind_NavMesh_moveAlong_2=e.asm.hb).apply(null,arguments)},eW=e._emscripten_bind_NavMesh_getNavMesh_0=function(){return(eW=e._emscripten_bind_NavMesh_getNavMesh_0=e.asm.ib).apply(null,arguments)},eH=e._emscripten_bind_NavMesh_computePath_2=function(){return(eH=e._emscripten_bind_NavMesh_computePath_2=e.asm.jb).apply(null,arguments)},eI=e._emscripten_bind_NavMesh_setDefaultQueryExtent_1=function(){return(eI=e._emscripten_bind_NavMesh_setDefaultQueryExtent_1=e.asm.kb).apply(null,arguments)},eL=e._emscripten_bind_NavMesh_getDefaultQueryExtent_0=function(){return(eL=e._emscripten_bind_NavMesh_getDefaultQueryExtent_0=e.asm.lb).apply(null,arguments)},eQ=e._emscripten_bind_NavMesh_addCylinderObstacle_3=function(){return(eQ=e._emscripten_bind_NavMesh_addCylinderObstacle_3=e.asm.mb).apply(null,arguments)},eY=e._emscripten_bind_NavMesh_addBoxObstacle_3=function(){return(eY=e._emscripten_bind_NavMesh_addBoxObstacle_3=e.asm.nb).apply(null,arguments)},eU=e._emscripten_bind_NavMesh_removeObstacle_1=function(){return(eU=e._emscripten_bind_NavMesh_removeObstacle_1=e.asm.ob).apply(null,arguments)},eq=e._emscripten_bind_NavMesh_update_0=function(){return(eq=e._emscripten_bind_NavMesh_update_0=e.asm.pb).apply(null,arguments)},eB=e._emscripten_bind_NavMesh___destroy___0=function(){return(eB=e._emscripten_bind_NavMesh___destroy___0=e.asm.qb).apply(null,arguments)},eG=e._emscripten_bind_Crowd_Crowd_3=function(){return(eG=e._emscripten_bind_Crowd_Crowd_3=e.asm.rb).apply(null,arguments)},eJ=e._emscripten_bind_Crowd_destroy_0=function(){return(eJ=e._emscripten_bind_Crowd_destroy_0=e.asm.sb).apply(null,arguments)},eX=e._emscripten_bind_Crowd_addAgent_2=function(){return(eX=e._emscripten_bind_Crowd_addAgent_2=e.asm.tb).apply(null,arguments)},eK=e._emscripten_bind_Crowd_removeAgent_1=function(){return(eK=e._emscripten_bind_Crowd_removeAgent_1=e.asm.ub).apply(null,arguments)},eZ=e._emscripten_bind_Crowd_update_1=function(){return(eZ=e._emscripten_bind_Crowd_update_1=e.asm.vb).apply(null,arguments)},e$=e._emscripten_bind_Crowd_getAgentPosition_1=function(){return(e$=e._emscripten_bind_Crowd_getAgentPosition_1=e.asm.wb).apply(null,arguments)},e0=e._emscripten_bind_Crowd_getAgentVelocity_1=function(){return(e0=e._emscripten_bind_Crowd_getAgentVelocity_1=e.asm.xb).apply(null,arguments)},e1=e._emscripten_bind_Crowd_getAgentNextTargetPath_1=function(){return(e1=e._emscripten_bind_Crowd_getAgentNextTargetPath_1=e.asm.yb).apply(null,arguments)},e2=e._emscripten_bind_Crowd_getAgentState_1=function(){return(e2=e._emscripten_bind_Crowd_getAgentState_1=e.asm.zb).apply(null,arguments)},e3=e._emscripten_bind_Crowd_overOffmeshConnection_1=function(){return(e3=e._emscripten_bind_Crowd_overOffmeshConnection_1=e.asm.Ab).apply(null,arguments)},e6=e._emscripten_bind_Crowd_agentGoto_2=function(){return(e6=e._emscripten_bind_Crowd_agentGoto_2=e.asm.Bb).apply(null,arguments)},e5=e._emscripten_bind_Crowd_agentTeleport_2=function(){return(e5=e._emscripten_bind_Crowd_agentTeleport_2=e.asm.Cb).apply(null,arguments)},e4=e._emscripten_bind_Crowd_getAgentParameters_1=function(){return(e4=e._emscripten_bind_Crowd_getAgentParameters_1=e.asm.Db).apply(null,arguments)},e8=e._emscripten_bind_Crowd_setAgentParameters_2=function(){return(e8=e._emscripten_bind_Crowd_setAgentParameters_2=e.asm.Eb).apply(null,arguments)},e9=e._emscripten_bind_Crowd_setDefaultQueryExtent_1=function(){return(e9=e._emscripten_bind_Crowd_setDefaultQueryExtent_1=e.asm.Fb).apply(null,arguments)},e7=e._emscripten_bind_Crowd_getDefaultQueryExtent_0=function(){return(e7=e._emscripten_bind_Crowd_getDefaultQueryExtent_0=e.asm.Gb).apply(null,arguments)},rt=e._emscripten_bind_Crowd_getCorners_1=function(){return(rt=e._emscripten_bind_Crowd_getCorners_1=e.asm.Hb).apply(null,arguments)},re=e._emscripten_bind_Crowd___destroy___0=function(){return(re=e._emscripten_bind_Crowd___destroy___0=e.asm.Ib).apply(null,arguments)},rr=e._malloc=function(){return(rr=e._malloc=e.asm.Kb).apply(null,arguments)};function rn(t){if(t=t||h,!(T>0)){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;){var r;r=e.preRun.shift(),O.unshift(r)}H(O),T>0||(e.setStatus?(e.setStatus("Running..."),setTimeout(function(){setTimeout(function(){e.setStatus("")},1),o()},1)):o())}function o(){if(!g&&(g=!0,e.calledRun=!0,!j)){if(H(R),n(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;){var t;t=e.postRun.shift(),E.unshift(t)}H(E)}}}if(e._free=function(){return(e._free=e.asm.Lb).apply(null,arguments)},e.UTF8ToString=M,e.addFunction=function(t,e){if(!i){i=new WeakMap;for(var r=d.length,n=0;n<0+r;n++){var o=L(n);o&&i.set(o,n)}}if(i.has(t))return i.get(t);var _=function(){if(P.length)return P.pop();try{d.grow(1)}catch(t){if(!(t instanceof RangeError))throw t;throw"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH."}return d.length-1}();try{Q(_,t)}catch(r){if(!(r instanceof TypeError))throw r;Q(_,function(t,e){if("function"==typeof WebAssembly.Function){for(var r={i:"i32",j:"i64",f:"f32",d:"f64"},n={parameters:[],results:"v"==e[0]?[]:[r[e[0]]]},o=1;o<e.length;++o)n.parameters.push(r[e[o]]);return new WebAssembly.Function(n,t)}var _=[1,0,1,96],p=e.slice(0,1),i=e.slice(1),a={i:127,j:126,f:125,d:124};_.push(i.length);for(var o=0;o<i.length;++o)_.push(a[i[o]]);"v"==p?_.push(0):_=_.concat([1,a[p]]),_[1]=_.length-2;var s=new Uint8Array([0,97,115,109,1,0,0,0].concat(_,[2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0])),c=new WebAssembly.Module(s);return new WebAssembly.Instance(c,{e:{f:t}}).exports.f}(t,e))}return i.set(t,_),_},F=function t(){g||rn(),g||(F=t)},e.run=rn,e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();function ro(){}function r_(t){return(t||ro).__cache__}function rp(t,e){var r=r_(e),n=r[t];return n||((n=Object.create((e||ro).prototype)).ptr=t,r[t]=n)}rn(),ro.prototype=Object.create(ro.prototype),ro.prototype.constructor=ro,ro.prototype.__class__=ro,ro.__cache__={},e.WrapperObject=ro,e.getCache=r_,e.wrapPointer=rp,e.castObject=function(t,e){return rp(t.ptr,e)},e.NULL=rp(0),e.destroy=function(t){if(!t.__destroy__)throw"Error: Cannot destroy object. (Did you create it yourself?)";t.__destroy__(),delete r_(t.__class__)[t.ptr]},e.compare=function(t,e){return t.ptr===e.ptr},e.getPointer=function(t){return t.ptr},e.getClass=function(t){return t.__class__};var ri={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(ri.needed){for(var t=0;t<ri.temps.length;t++)e._free(ri.temps[t]);ri.temps.length=0,e._free(ri.buffer),ri.buffer=0,ri.size+=ri.needed,ri.needed=0}ri.buffer||(ri.size+=128,ri.buffer=e._malloc(ri.size),x(ri.buffer)),ri.pos=0},alloc:function(t,r){x(ri.buffer);var n,o=r.BYTES_PER_ELEMENT,_=t.length*o;return _=_+7&-8,ri.pos+_>=ri.size?(x(_>0),ri.needed+=_,n=e._malloc(_),ri.temps.push(n)):(n=ri.buffer+ri.pos,ri.pos+=_),n},copy:function(t,e,r){switch(r>>>=0,e.BYTES_PER_ELEMENT){case 2:r>>>=1;break;case 4:r>>>=2;break;case 8:r>>>=3}for(var n=0;n<t.length;n++)e[r+n]=t[n]}};function ra(){throw"cannot construct a VoidPtr, no constructor in IDL"}function rs(){this.ptr=tr(),r_(rs)[this.ptr]=this}function rc(t,e,r){if(t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),r&&"object"==typeof r&&(r=r.ptr),void 0===t){this.ptr=tL(),r_(rc)[this.ptr]=this;return}if(void 0===e){this.ptr=_emscripten_bind_Vec3_Vec3_1(t),r_(rc)[this.ptr]=this;return}if(void 0===r){this.ptr=_emscripten_bind_Vec3_Vec3_2(t,e),r_(rc)[this.ptr]=this;return}this.ptr=tQ(t,e,r),r_(rc)[this.ptr]=this}function ru(){this.ptr=tK(),r_(ru)[this.ptr]=this}function rl(){this.ptr=t0(),r_(rl)[this.ptr]=this}function ry(){throw"cannot construct a dtNavMesh, no constructor in IDL"}function rm(){this.ptr=t5(),r_(rm)[this.ptr]=this}function rd(){throw"cannot construct a NavPath, no constructor in IDL"}function rf(){throw"cannot construct a dtObstacleRef, no constructor in IDL"}function rg(){this.ptr=e_(),r_(rg)[this.ptr]=this}function rb(){this.ptr=eM(),r_(rb)[this.ptr]=this}function rh(t,e,r){t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),r&&"object"==typeof r&&(r=r.ptr),this.ptr=eG(t,e,r),r_(rh)[this.ptr]=this}return ra.prototype=Object.create(ro.prototype),ra.prototype.constructor=ra,ra.prototype.__class__=ra,ra.__cache__={},e.VoidPtr=ra,ra.prototype.__destroy__=ra.prototype.__destroy__=function(){var t=this.ptr;te(t)},rs.prototype=Object.create(ro.prototype),rs.prototype.constructor=rs,rs.prototype.__class__=rs,rs.__cache__={},e.rcConfig=rs,rs.prototype.get_width=rs.prototype.get_width=function(){var t=this.ptr;return tn(t)},rs.prototype.set_width=rs.prototype.set_width=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),to(e,t)},Object.defineProperty(rs.prototype,"width",{get:rs.prototype.get_width,set:rs.prototype.set_width}),rs.prototype.get_height=rs.prototype.get_height=function(){var t=this.ptr;return t_(t)},rs.prototype.set_height=rs.prototype.set_height=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tp(e,t)},Object.defineProperty(rs.prototype,"height",{get:rs.prototype.get_height,set:rs.prototype.set_height}),rs.prototype.get_tileSize=rs.prototype.get_tileSize=function(){var t=this.ptr;return ti(t)},rs.prototype.set_tileSize=rs.prototype.set_tileSize=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),ta(e,t)},Object.defineProperty(rs.prototype,"tileSize",{get:rs.prototype.get_tileSize,set:rs.prototype.set_tileSize}),rs.prototype.get_borderSize=rs.prototype.get_borderSize=function(){var t=this.ptr;return ts(t)},rs.prototype.set_borderSize=rs.prototype.set_borderSize=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tc(e,t)},Object.defineProperty(rs.prototype,"borderSize",{get:rs.prototype.get_borderSize,set:rs.prototype.set_borderSize}),rs.prototype.get_cs=rs.prototype.get_cs=function(){var t=this.ptr;return tu(t)},rs.prototype.set_cs=rs.prototype.set_cs=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tl(e,t)},Object.defineProperty(rs.prototype,"cs",{get:rs.prototype.get_cs,set:rs.prototype.set_cs}),rs.prototype.get_ch=rs.prototype.get_ch=function(){var t=this.ptr;return ty(t)},rs.prototype.set_ch=rs.prototype.set_ch=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tm(e,t)},Object.defineProperty(rs.prototype,"ch",{get:rs.prototype.get_ch,set:rs.prototype.set_ch}),rs.prototype.get_bmin=rs.prototype.get_bmin=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),td(e,t)},rs.prototype.set_bmin=rs.prototype.set_bmin=function(t,e){var r=this.ptr;ri.prepare(),t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),tf(r,t,e)},Object.defineProperty(rs.prototype,"bmin",{get:rs.prototype.get_bmin,set:rs.prototype.set_bmin}),rs.prototype.get_bmax=rs.prototype.get_bmax=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),tg(e,t)},rs.prototype.set_bmax=rs.prototype.set_bmax=function(t,e){var r=this.ptr;ri.prepare(),t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),tb(r,t,e)},Object.defineProperty(rs.prototype,"bmax",{get:rs.prototype.get_bmax,set:rs.prototype.set_bmax}),rs.prototype.get_walkableSlopeAngle=rs.prototype.get_walkableSlopeAngle=function(){var t=this.ptr;return th(t)},rs.prototype.set_walkableSlopeAngle=rs.prototype.set_walkableSlopeAngle=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tv(e,t)},Object.defineProperty(rs.prototype,"walkableSlopeAngle",{get:rs.prototype.get_walkableSlopeAngle,set:rs.prototype.set_walkableSlopeAngle}),rs.prototype.get_walkableHeight=rs.prototype.get_walkableHeight=function(){var t=this.ptr;return tC(t)},rs.prototype.set_walkableHeight=rs.prototype.set_walkableHeight=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tw(e,t)},Object.defineProperty(rs.prototype,"walkableHeight",{get:rs.prototype.get_walkableHeight,set:rs.prototype.set_walkableHeight}),rs.prototype.get_walkableClimb=rs.prototype.get_walkableClimb=function(){var t=this.ptr;return tA(t)},rs.prototype.set_walkableClimb=rs.prototype.set_walkableClimb=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tP(e,t)},Object.defineProperty(rs.prototype,"walkableClimb",{get:rs.prototype.get_walkableClimb,set:rs.prototype.set_walkableClimb}),rs.prototype.get_walkableRadius=rs.prototype.get_walkableRadius=function(){var t=this.ptr;return tj(t)},rs.prototype.set_walkableRadius=rs.prototype.set_walkableRadius=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tx(e,t)},Object.defineProperty(rs.prototype,"walkableRadius",{get:rs.prototype.get_walkableRadius,set:rs.prototype.set_walkableRadius}),rs.prototype.get_maxEdgeLen=rs.prototype.get_maxEdgeLen=function(){var t=this.ptr;return tD(t)},rs.prototype.set_maxEdgeLen=rs.prototype.set_maxEdgeLen=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tS(e,t)},Object.defineProperty(rs.prototype,"maxEdgeLen",{get:rs.prototype.get_maxEdgeLen,set:rs.prototype.set_maxEdgeLen}),rs.prototype.get_maxSimplificationError=rs.prototype.get_maxSimplificationError=function(){var t=this.ptr;return tM(t)},rs.prototype.set_maxSimplificationError=rs.prototype.set_maxSimplificationError=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tN(e,t)},Object.defineProperty(rs.prototype,"maxSimplificationError",{get:rs.prototype.get_maxSimplificationError,set:rs.prototype.set_maxSimplificationError}),rs.prototype.get_minRegionArea=rs.prototype.get_minRegionArea=function(){var t=this.ptr;return tO(t)},rs.prototype.set_minRegionArea=rs.prototype.set_minRegionArea=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tR(e,t)},Object.defineProperty(rs.prototype,"minRegionArea",{get:rs.prototype.get_minRegionArea,set:rs.prototype.set_minRegionArea}),rs.prototype.get_mergeRegionArea=rs.prototype.get_mergeRegionArea=function(){var t=this.ptr;return tE(t)},rs.prototype.set_mergeRegionArea=rs.prototype.set_mergeRegionArea=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tT(e,t)},Object.defineProperty(rs.prototype,"mergeRegionArea",{get:rs.prototype.get_mergeRegionArea,set:rs.prototype.set_mergeRegionArea}),rs.prototype.get_maxVertsPerPoly=rs.prototype.get_maxVertsPerPoly=function(){var t=this.ptr;return tz(t)},rs.prototype.set_maxVertsPerPoly=rs.prototype.set_maxVertsPerPoly=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tF(e,t)},Object.defineProperty(rs.prototype,"maxVertsPerPoly",{get:rs.prototype.get_maxVertsPerPoly,set:rs.prototype.set_maxVertsPerPoly}),rs.prototype.get_detailSampleDist=rs.prototype.get_detailSampleDist=function(){var t=this.ptr;return tk(t)},rs.prototype.set_detailSampleDist=rs.prototype.set_detailSampleDist=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tV(e,t)},Object.defineProperty(rs.prototype,"detailSampleDist",{get:rs.prototype.get_detailSampleDist,set:rs.prototype.set_detailSampleDist}),rs.prototype.get_detailSampleMaxError=rs.prototype.get_detailSampleMaxError=function(){var t=this.ptr;return tW(t)},rs.prototype.set_detailSampleMaxError=rs.prototype.set_detailSampleMaxError=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tH(e,t)},Object.defineProperty(rs.prototype,"detailSampleMaxError",{get:rs.prototype.get_detailSampleMaxError,set:rs.prototype.set_detailSampleMaxError}),rs.prototype.__destroy__=rs.prototype.__destroy__=function(){var t=this.ptr;tI(t)},rc.prototype=Object.create(ro.prototype),rc.prototype.constructor=rc,rc.prototype.__class__=rc,rc.__cache__={},e.Vec3=rc,rc.prototype.get_x=rc.prototype.get_x=function(){var t=this.ptr;return tY(t)},rc.prototype.set_x=rc.prototype.set_x=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tU(e,t)},Object.defineProperty(rc.prototype,"x",{get:rc.prototype.get_x,set:rc.prototype.set_x}),rc.prototype.get_y=rc.prototype.get_y=function(){var t=this.ptr;return tq(t)},rc.prototype.set_y=rc.prototype.set_y=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tB(e,t)},Object.defineProperty(rc.prototype,"y",{get:rc.prototype.get_y,set:rc.prototype.set_y}),rc.prototype.get_z=rc.prototype.get_z=function(){var t=this.ptr;return tG(t)},rc.prototype.set_z=rc.prototype.set_z=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),tJ(e,t)},Object.defineProperty(rc.prototype,"z",{get:rc.prototype.get_z,set:rc.prototype.set_z}),rc.prototype.__destroy__=rc.prototype.__destroy__=function(){var t=this.ptr;tX(t)},ru.prototype=Object.create(ro.prototype),ru.prototype.constructor=ru,ru.prototype.__class__=ru,ru.__cache__={},e.Triangle=ru,ru.prototype.getPoint=ru.prototype.getPoint=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),rp(tZ(e,t),rc)},ru.prototype.__destroy__=ru.prototype.__destroy__=function(){var t=this.ptr;t$(t)},rl.prototype=Object.create(ro.prototype),rl.prototype.constructor=rl,rl.prototype.__class__=rl,rl.__cache__={},e.DebugNavMesh=rl,rl.prototype.getTriangleCount=rl.prototype.getTriangleCount=function(){var t=this.ptr;return t1(t)},rl.prototype.getTriangle=rl.prototype.getTriangle=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),rp(t2(e,t),ru)},rl.prototype.__destroy__=rl.prototype.__destroy__=function(){var t=this.ptr;t3(t)},ry.prototype=Object.create(ro.prototype),ry.prototype.constructor=ry,ry.prototype.__class__=ry,ry.__cache__={},e.dtNavMesh=ry,ry.prototype.__destroy__=ry.prototype.__destroy__=function(){var t=this.ptr;t6(t)},rm.prototype=Object.create(ro.prototype),rm.prototype.constructor=rm,rm.prototype.__class__=rm,rm.__cache__={},e.NavmeshData=rm,rm.prototype.get_dataPointer=rm.prototype.get_dataPointer=function(){var t=this.ptr;return t4(t)},rm.prototype.set_dataPointer=rm.prototype.set_dataPointer=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),t8(e,t)},Object.defineProperty(rm.prototype,"dataPointer",{get:rm.prototype.get_dataPointer,set:rm.prototype.set_dataPointer}),rm.prototype.get_size=rm.prototype.get_size=function(){var t=this.ptr;return t9(t)},rm.prototype.set_size=rm.prototype.set_size=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),t7(e,t)},Object.defineProperty(rm.prototype,"size",{get:rm.prototype.get_size,set:rm.prototype.set_size}),rm.prototype.__destroy__=rm.prototype.__destroy__=function(){var t=this.ptr;et(t)},rd.prototype=Object.create(ro.prototype),rd.prototype.constructor=rd,rd.prototype.__class__=rd,rd.__cache__={},e.NavPath=rd,rd.prototype.getPointCount=rd.prototype.getPointCount=function(){var t=this.ptr;return ee(t)},rd.prototype.getPoint=rd.prototype.getPoint=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),rp(er(e,t),rc)},rd.prototype.__destroy__=rd.prototype.__destroy__=function(){var t=this.ptr;en(t)},rf.prototype=Object.create(ro.prototype),rf.prototype.constructor=rf,rf.prototype.__class__=rf,rf.__cache__={},e.dtObstacleRef=rf,rf.prototype.__destroy__=rf.prototype.__destroy__=function(){var t=this.ptr;eo(t)},rg.prototype=Object.create(ro.prototype),rg.prototype.constructor=rg,rg.prototype.__class__=rg,rg.__cache__={},e.dtCrowdAgentParams=rg,rg.prototype.get_radius=rg.prototype.get_radius=function(){var t=this.ptr;return ep(t)},rg.prototype.set_radius=rg.prototype.set_radius=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),ei(e,t)},Object.defineProperty(rg.prototype,"radius",{get:rg.prototype.get_radius,set:rg.prototype.set_radius}),rg.prototype.get_height=rg.prototype.get_height=function(){var t=this.ptr;return ea(t)},rg.prototype.set_height=rg.prototype.set_height=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),es(e,t)},Object.defineProperty(rg.prototype,"height",{get:rg.prototype.get_height,set:rg.prototype.set_height}),rg.prototype.get_maxAcceleration=rg.prototype.get_maxAcceleration=function(){var t=this.ptr;return ec(t)},rg.prototype.set_maxAcceleration=rg.prototype.set_maxAcceleration=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),eu(e,t)},Object.defineProperty(rg.prototype,"maxAcceleration",{get:rg.prototype.get_maxAcceleration,set:rg.prototype.set_maxAcceleration}),rg.prototype.get_maxSpeed=rg.prototype.get_maxSpeed=function(){var t=this.ptr;return el(t)},rg.prototype.set_maxSpeed=rg.prototype.set_maxSpeed=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),ey(e,t)},Object.defineProperty(rg.prototype,"maxSpeed",{get:rg.prototype.get_maxSpeed,set:rg.prototype.set_maxSpeed}),rg.prototype.get_collisionQueryRange=rg.prototype.get_collisionQueryRange=function(){var t=this.ptr;return em(t)},rg.prototype.set_collisionQueryRange=rg.prototype.set_collisionQueryRange=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),ed(e,t)},Object.defineProperty(rg.prototype,"collisionQueryRange",{get:rg.prototype.get_collisionQueryRange,set:rg.prototype.set_collisionQueryRange}),rg.prototype.get_pathOptimizationRange=rg.prototype.get_pathOptimizationRange=function(){var t=this.ptr;return ef(t)},rg.prototype.set_pathOptimizationRange=rg.prototype.set_pathOptimizationRange=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),eg(e,t)},Object.defineProperty(rg.prototype,"pathOptimizationRange",{get:rg.prototype.get_pathOptimizationRange,set:rg.prototype.set_pathOptimizationRange}),rg.prototype.get_separationWeight=rg.prototype.get_separationWeight=function(){var t=this.ptr;return eb(t)},rg.prototype.set_separationWeight=rg.prototype.set_separationWeight=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),eh(e,t)},Object.defineProperty(rg.prototype,"separationWeight",{get:rg.prototype.get_separationWeight,set:rg.prototype.set_separationWeight}),rg.prototype.get_updateFlags=rg.prototype.get_updateFlags=function(){var t=this.ptr;return ev(t)},rg.prototype.set_updateFlags=rg.prototype.set_updateFlags=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),eC(e,t)},Object.defineProperty(rg.prototype,"updateFlags",{get:rg.prototype.get_updateFlags,set:rg.prototype.set_updateFlags}),rg.prototype.get_obstacleAvoidanceType=rg.prototype.get_obstacleAvoidanceType=function(){var t=this.ptr;return ew(t)},rg.prototype.set_obstacleAvoidanceType=rg.prototype.set_obstacleAvoidanceType=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),eA(e,t)},Object.defineProperty(rg.prototype,"obstacleAvoidanceType",{get:rg.prototype.get_obstacleAvoidanceType,set:rg.prototype.set_obstacleAvoidanceType}),rg.prototype.get_queryFilterType=rg.prototype.get_queryFilterType=function(){var t=this.ptr;return eP(t)},rg.prototype.set_queryFilterType=rg.prototype.set_queryFilterType=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),ej(e,t)},Object.defineProperty(rg.prototype,"queryFilterType",{get:rg.prototype.get_queryFilterType,set:rg.prototype.set_queryFilterType}),rg.prototype.get_userData=rg.prototype.get_userData=function(){var t=this.ptr;return rp(ex(t),ra)},rg.prototype.set_userData=rg.prototype.set_userData=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),eD(e,t)},Object.defineProperty(rg.prototype,"userData",{get:rg.prototype.get_userData,set:rg.prototype.set_userData}),rg.prototype.__destroy__=rg.prototype.__destroy__=function(){var t=this.ptr;eS(t)},rb.prototype=Object.create(ro.prototype),rb.prototype.constructor=rb,rb.prototype.__class__=rb,rb.__cache__={},e.NavMesh=rb,rb.prototype.destroy=rb.prototype.destroy=function(){var t=this.ptr;eN(t)},rb.prototype.build=rb.prototype.build=function(t,e,r,n,o){var _=this.ptr;ri.prepare(),"object"==typeof t&&(t=function(t){if("object"==typeof t){var e=ri.alloc(t,m);return ri.copy(t,m,e),e}return t}(t)),e&&"object"==typeof e&&(e=e.ptr),"object"==typeof r&&(r=function(t){if("object"==typeof t){var e=ri.alloc(t,y);return ri.copy(t,y,e),e}return t}(r)),n&&"object"==typeof n&&(n=n.ptr),o&&"object"==typeof o&&(o=o.ptr),eO(_,t,e,r,n,o)},rb.prototype.buildFromNavmeshData=rb.prototype.buildFromNavmeshData=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),eR(e,t)},rb.prototype.getNavmeshData=rb.prototype.getNavmeshData=function(){var t=this.ptr;return rp(eE(t),rm)},rb.prototype.freeNavmeshData=rb.prototype.freeNavmeshData=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),eT(e,t)},rb.prototype.getDebugNavMesh=rb.prototype.getDebugNavMesh=function(){var t=this.ptr;return rp(ez(t),rl)},rb.prototype.getClosestPoint=rb.prototype.getClosestPoint=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),rp(eF(e,t),rc)},rb.prototype.getRandomPointAround=rb.prototype.getRandomPointAround=function(t,e){var r=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),rp(ek(r,t,e),rc)},rb.prototype.moveAlong=rb.prototype.moveAlong=function(t,e){var r=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),rp(eV(r,t,e),rc)},rb.prototype.getNavMesh=rb.prototype.getNavMesh=function(){var t=this.ptr;return rp(eW(t),ry)},rb.prototype.computePath=rb.prototype.computePath=function(t,e){var r=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),rp(eH(r,t,e),rd)},rb.prototype.setDefaultQueryExtent=rb.prototype.setDefaultQueryExtent=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),eI(e,t)},rb.prototype.getDefaultQueryExtent=rb.prototype.getDefaultQueryExtent=function(){var t=this.ptr;return rp(eL(t),rc)},rb.prototype.addCylinderObstacle=rb.prototype.addCylinderObstacle=function(t,e,r){var n=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),r&&"object"==typeof r&&(r=r.ptr),rp(eQ(n,t,e,r),rf)},rb.prototype.addBoxObstacle=rb.prototype.addBoxObstacle=function(t,e,r){var n=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),r&&"object"==typeof r&&(r=r.ptr),rp(eY(n,t,e,r),rf)},rb.prototype.removeObstacle=rb.prototype.removeObstacle=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),eU(e,t)},rb.prototype.update=rb.prototype.update=function(){var t=this.ptr;eq(t)},rb.prototype.__destroy__=rb.prototype.__destroy__=function(){var t=this.ptr;eB(t)},rh.prototype=Object.create(ro.prototype),rh.prototype.constructor=rh,rh.prototype.__class__=rh,rh.__cache__={},e.Crowd=rh,rh.prototype.destroy=rh.prototype.destroy=function(){var t=this.ptr;eJ(t)},rh.prototype.addAgent=rh.prototype.addAgent=function(t,e){var r=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),eX(r,t,e)},rh.prototype.removeAgent=rh.prototype.removeAgent=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),eK(e,t)},rh.prototype.update=rh.prototype.update=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),eZ(e,t)},rh.prototype.getAgentPosition=rh.prototype.getAgentPosition=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),rp(e$(e,t),rc)},rh.prototype.getAgentVelocity=rh.prototype.getAgentVelocity=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),rp(e0(e,t),rc)},rh.prototype.getAgentNextTargetPath=rh.prototype.getAgentNextTargetPath=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),rp(e1(e,t),rc)},rh.prototype.getAgentState=rh.prototype.getAgentState=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),e2(e,t)},rh.prototype.overOffmeshConnection=rh.prototype.overOffmeshConnection=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),!!e3(e,t)},rh.prototype.agentGoto=rh.prototype.agentGoto=function(t,e){var r=this.ptr;t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),e6(r,t,e)},rh.prototype.agentTeleport=rh.prototype.agentTeleport=function(t,e){var r=this.ptr;t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),e5(r,t,e)},rh.prototype.getAgentParameters=rh.prototype.getAgentParameters=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),rp(e4(e,t),rg)},rh.prototype.setAgentParameters=rh.prototype.setAgentParameters=function(t,e){var r=this.ptr;t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),e8(r,t,e)},rh.prototype.setDefaultQueryExtent=rh.prototype.setDefaultQueryExtent=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),e9(e,t)},rh.prototype.getDefaultQueryExtent=rh.prototype.getDefaultQueryExtent=function(){var t=this.ptr;return rp(e7(t),rc)},rh.prototype.getCorners=rh.prototype.getCorners=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),rp(rt(e,t),rd)},rh.prototype.__destroy__=rh.prototype.__destroy__=function(){var t=this.ptr;re(t)},e.ready}}()}};