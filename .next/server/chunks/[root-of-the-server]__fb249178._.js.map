{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/api/github/profile/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\n\n// Rate limiting store (in production, use Redis or similar)\nconst rateLimitStore = new Map<string, { count: number; resetTime: number }>();\n\n// Rate limiting configuration\nconst RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute\nconst RATE_LIMIT_MAX_REQUESTS = 10; // 10 requests per minute\n\nfunction isRateLimited(ip: string): boolean {\n  const now = Date.now();\n  const userLimit = rateLimitStore.get(ip);\n\n  if (!userLimit || now > userLimit.resetTime) {\n    // Reset or create new limit\n    rateLimitStore.set(ip, {\n      count: 1,\n      resetTime: now + RATE_LIMIT_WINDOW\n    });\n    return false;\n  }\n\n  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {\n    return true;\n  }\n\n  userLimit.count++;\n  return false;\n}\n\nexport async function GET(request: Request) {\n  try {\n    // Basic rate limiting\n    const ip = request.headers.get('x-forwarded-for') || 'unknown';\n    if (isRateLimited(ip)) {\n      return NextResponse.json(\n        { error: 'Rate limit exceeded. Please try again later.' },\n        { status: 429 }\n      );\n    }\n\n    const githubToken = process.env.GITHUB_TOKEN;\n    const githubUsername = process.env.GITHUB_USERNAME || 'GreenHacker420';\n\n    if (!githubToken) {\n      console.warn('GitHub token not configured, using mock data');\n      return getMockProfile();\n    }\n\n    // Fetch real GitHub profile data\n    const response = await fetch(`https://api.github.com/users/${githubUsername}`, {\n      headers: {\n        'Authorization': `Bearer ${githubToken}`,\n        'Accept': 'application/vnd.github.v3+json',\n        'User-Agent': 'Portfolio-App'\n      },\n      next: { revalidate: 3600 } // Cache for 1 hour\n    });\n\n    if (!response.ok) {\n      console.error('GitHub API error:', response.status, response.statusText);\n      \n      // Check if it's a rate limit error\n      if (response.status === 403) {\n        const rateLimitRemaining = response.headers.get('x-ratelimit-remaining');\n        const rateLimitReset = response.headers.get('x-ratelimit-reset');\n        \n        console.error('GitHub API rate limit exceeded:', {\n          remaining: rateLimitRemaining,\n          reset: rateLimitReset\n        });\n      }\n      \n      return getMockProfile();\n    }\n\n    const userData = await response.json();\n\n    const profileData = {\n      login: userData.login,\n      name: userData.name || userData.login,\n      bio: userData.bio || 'Full-stack developer passionate about AI and open source',\n      avatar_url: userData.avatar_url,\n      html_url: userData.html_url,\n      public_repos: userData.public_repos,\n      followers: userData.followers,\n      following: userData.following,\n      created_at: userData.created_at,\n      updated_at: userData.updated_at,\n      location: userData.location,\n      blog: userData.blog,\n      twitter_username: userData.twitter_username,\n      company: userData.company,\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: profileData,\n      cached: false,\n      timestamp: new Date().toISOString()\n    });\n\n  } catch (error) {\n    console.error('GitHub profile API error:', error);\n    return getMockProfile();\n  }\n}\n\nfunction getMockProfile() {\n  const mockProfile = {\n    login: 'GreenHacker420',\n    name: 'Green Hacker',\n    bio: 'Full-stack developer passionate about AI, machine learning, and creating innovative web experiences. Always learning, always building.',\n    avatar_url: 'https://avatars.githubusercontent.com/u/placeholder',\n    html_url: 'https://github.com/GreenHacker420',\n    public_repos: 25,\n    followers: 150,\n    following: 75,\n    created_at: '2020-01-15T00:00:00Z',\n    updated_at: new Date().toISOString(),\n    location: 'San Francisco, CA',\n    blog: 'https://greenhacker420.dev',\n    twitter_username: 'greenhacker420',\n    company: 'Independent Developer',\n  };\n\n  return NextResponse.json({\n    success: true,\n    data: mockProfile,\n    cached: false,\n    mock: true,\n    timestamp: new Date().toISOString()\n  });\n}\n\nexport async function POST() {\n  return NextResponse.json(\n    { error: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,4DAA4D;AAC5D,MAAM,iBAAiB,IAAI;AAE3B,8BAA8B;AAC9B,MAAM,oBAAoB,KAAK,MAAM,WAAW;AAChD,MAAM,0BAA0B,IAAI,yBAAyB;AAE7D,SAAS,cAAc,EAAU;IAC/B,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,eAAe,GAAG,CAAC;IAErC,IAAI,CAAC,aAAa,MAAM,UAAU,SAAS,EAAE;QAC3C,4BAA4B;QAC5B,eAAe,GAAG,CAAC,IAAI;YACrB,OAAO;YACP,WAAW,MAAM;QACnB;QACA,OAAO;IACT;IAEA,IAAI,UAAU,KAAK,IAAI,yBAAyB;QAC9C,OAAO;IACT;IAEA,UAAU,KAAK;IACf,OAAO;AACT;AAEO,eAAe,IAAI,OAAgB;IACxC,IAAI;QACF,sBAAsB;QACtB,MAAM,KAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;QACrD,IAAI,cAAc,KAAK;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+C,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,YAAY;QAC5C,MAAM,iBAAiB,QAAQ,GAAG,CAAC,eAAe,IAAI;QAEtD,IAAI,CAAC,aAAa;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,iCAAiC;QACjC,MAAM,WAAW,MAAM,MAAM,CAAC,6BAA6B,EAAE,gBAAgB,EAAE;YAC7E,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa;gBACxC,UAAU;gBACV,cAAc;YAChB;YACA,MAAM;gBAAE,YAAY;YAAK,EAAE,mBAAmB;QAChD;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,qBAAqB,SAAS,MAAM,EAAE,SAAS,UAAU;YAEvE,mCAAmC;YACnC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,MAAM,qBAAqB,SAAS,OAAO,CAAC,GAAG,CAAC;gBAChD,MAAM,iBAAiB,SAAS,OAAO,CAAC,GAAG,CAAC;gBAE5C,QAAQ,KAAK,CAAC,mCAAmC;oBAC/C,WAAW;oBACX,OAAO;gBACT;YACF;YAEA,OAAO;QACT;QAEA,MAAM,WAAW,MAAM,SAAS,IAAI;QAEpC,MAAM,cAAc;YAClB,OAAO,SAAS,KAAK;YACrB,MAAM,SAAS,IAAI,IAAI,SAAS,KAAK;YACrC,KAAK,SAAS,GAAG,IAAI;YACrB,YAAY,SAAS,UAAU;YAC/B,UAAU,SAAS,QAAQ;YAC3B,cAAc,SAAS,YAAY;YACnC,WAAW,SAAS,SAAS;YAC7B,WAAW,SAAS,SAAS;YAC7B,YAAY,SAAS,UAAU;YAC/B,YAAY,SAAS,UAAU;YAC/B,UAAU,SAAS,QAAQ;YAC3B,MAAM,SAAS,IAAI;YACnB,kBAAkB,SAAS,gBAAgB;YAC3C,SAAS,SAAS,OAAO;QAC3B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;QACnC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEA,SAAS;IACP,MAAM,cAAc;QAClB,OAAO;QACP,MAAM;QACN,KAAK;QACL,YAAY;QACZ,UAAU;QACV,cAAc;QACd,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,OAAO,WAAW;QAClC,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,SAAS;IACX;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,MAAM;QACN,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,OAAO;IAAqB,GAC9B;QAAE,QAAQ;IAAI;AAElB", "debugId": null}}]}