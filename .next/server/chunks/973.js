exports.id=973,exports.ids=[973],exports.modules={443:(e,t,r)=>{"use strict";r.d(t,{f:()=>n,z:()=>i});let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},1765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTTPAccessErrorFallback",{enumerable:!0,get:function(){return o}}),r(72639);let n=r(37413);r(61120);let i={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{display:"inline-block"},h1:{display:"inline-block",margin:"0 20px 0 0",padding:"0 23px 0 0",fontSize:24,fontWeight:500,verticalAlign:"top",lineHeight:"49px"},h2:{fontSize:14,fontWeight:400,lineHeight:"49px",margin:0}};function o(e){let{status:t,message:r}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("title",{children:t+": "+r}),(0,n.jsx)("div",{style:i.error,children:(0,n.jsxs)("div",{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}),(0,n.jsx)("h1",{className:"next-error-h1",style:i.h1,children:t}),(0,n.jsx)("div",{style:i.desc,children:(0,n.jsx)("h2",{style:i.h2,children:r})})]})})]})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],i=r[0];if(Array.isArray(n)&&Array.isArray(i)){if(n[0]!==i[0]||n[2]!==i[2])return!0}else if(n!==i)return!0;if(t[4])return!r[4];if(r[4])return!0;let o=Object.values(t[1])[0],a=Object.values(r[1])[0];return!o||!a||e(o,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let n=r(19169);function i(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},4871:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{IconKeys:function(){return n},ViewportMetaKeys:function(){return r}});let r={width:"width",height:"height",initialScale:"initial-scale",minimumScale:"minimum-scale",maximumScale:"maximum-scale",viewportFit:"viewport-fit",userScalable:"user-scalable",interactiveWidget:"interactive-widget"},n=["icon","shortcut","apple","other"]},5144:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let n=r(51550),i=r(59656);var o=i._("_maxConcurrency"),a=i._("_runningCount"),s=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,r,i=new Promise((e,n)=>{t=e,r=n}),o=async()=>{try{n._(this,a)[a]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,a)[a]--,n._(this,l)[l]()}};return n._(this,s)[s].push({promiseFn:i,task:o}),n._(this,l)[l](),i}bump(e){let t=n._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,s)[s].splice(t,1)[0];n._(this,s)[s].unshift(e),n._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),n._(this,o)[o]=e,n._(this,a)[a]=0,n._(this,s)[s]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,a)[a]<n._(this,o)[o]||e)&&n._(this,s)[s].length>0){var t;null==(t=n._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DYNAMIC_STALETIME_MS:function(){return f},STATIC_STALETIME_MS:function(){return p},createSeededPrefetchCacheEntry:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return d}});let n=r(59008),i=r(59154),o=r(75076);function a(e,t,r){let n=e.pathname;return(t&&(n+=e.search),r)?""+r+"%"+n:n}function s(e,t,r){return a(e,t===i.PrefetchKind.FULL,r)}function l(e){let{url:t,nextUrl:r,tree:n,prefetchCache:o,kind:s,allowAliasing:l=!0}=e,u=function(e,t,r,n,o){for(let s of(void 0===t&&(t=i.PrefetchKind.TEMPORARY),[r,null])){let r=a(e,!0,s),l=a(e,!1,s),u=e.search?r:l,c=n.get(u);if(c&&o){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let d=n.get(l);if(o&&e.search&&t!==i.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==i.PrefetchKind.FULL&&o){for(let t of n.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,s,r,o,l);return u?(u.status=h(u),u.kind!==i.PrefetchKind.FULL&&s===i.PrefetchKind.FULL&&u.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:n,url:t,nextUrl:r,prefetchCache:o,kind:null!=s?s:i.PrefetchKind.TEMPORARY})}),s&&u.kind===i.PrefetchKind.TEMPORARY&&(u.kind=s),u):c({tree:n,url:t,nextUrl:r,prefetchCache:o,kind:s||i.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:r,prefetchCache:n,url:o,data:a,kind:l}=e,u=a.couldBeIntercepted?s(o,l,t):s(o,l),c={treeAtTimeOfPrefetch:r,data:Promise.resolve(a),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:u,status:i.PrefetchCacheEntryStatus.fresh,url:o};return n.set(u,c),c}function c(e){let{url:t,kind:r,tree:a,nextUrl:l,prefetchCache:u}=e,c=s(t,r),d=o.prefetchQueue.enqueue(()=>(0,n.fetchServerResponse)(t,{flightRouterState:a,nextUrl:l,prefetchKind:r}).then(e=>{let r;if(e.couldBeIntercepted&&(r=function(e){let{url:t,nextUrl:r,prefetchCache:n,existingCacheKey:i}=e,o=n.get(i);if(!o)return;let a=s(t,o.kind,r);return n.set(a,{...o,key:a}),n.delete(i),a}({url:t,existingCacheKey:c,nextUrl:l,prefetchCache:u})),e.prerendered){let t=u.get(null!=r?r:c);t&&(t.kind=i.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),f={treeAtTimeOfPrefetch:a,data:d,kind:r,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:i.PrefetchCacheEntryStatus.fresh,url:t};return u.set(c,f),f}function d(e){for(let[t,r]of e)h(r)===i.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("0"),p=1e3*Number("300");function h(e){let{kind:t,prefetchTime:r,lastUsedTime:n,staleTime:o}=e;return -1!==o?Date.now()<r+o?i.PrefetchCacheEntryStatus.fresh:i.PrefetchCacheEntryStatus.stale:Date.now()<(null!=n?n:r)+f?n?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.fresh:t===i.PrefetchKind.AUTO&&Date.now()<r+p?i.PrefetchCacheEntryStatus.stale:t===i.PrefetchKind.FULL&&Date.now()<r+p?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5927:(e,t,r)=>{"use strict";r.d(t,{S:()=>n});let n=e=>!!(e&&e.getVelocity)},6255:(e,t)=>{"use strict";function r(e){return e.default||e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interopDefault",{enumerable:!0,get:function(){return r}})},6361:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return i}});let n=r(96127);function i(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7044:(e,t,r)=>{"use strict";r.d(t,{B:()=>n});let n="undefined"!=typeof window},7236:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,q:()=>a});var n=r(68762);let i=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,o=(e,t)=>r=>!!("string"==typeof r&&i.test(r)&&r.startsWith(e)||t&&null!=r&&Object.prototype.hasOwnProperty.call(r,t)),a=(e,t,r)=>i=>{if("string"!=typeof i)return i;let[o,a,s,l]=i.match(n.S);return{[e]:parseFloat(o),[t]:parseFloat(a),[r]:parseFloat(s),alpha:void 0!==l?parseFloat(l):1}}},7308:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatServerError:function(){return o},getStackWithoutErrorMessage:function(){return i}});let r=["useDeferredValue","useEffect","useImperativeHandle","useInsertionEffect","useLayoutEffect","useReducer","useRef","useState","useSyncExternalStore","useTransition","experimental_useOptimistic","useOptimistic"];function n(e,t){if(e.message=t,e.stack){let r=e.stack.split("\n");r[0]=t,e.stack=r.join("\n")}}function i(e){let t=e.stack;return t?t.replace(/^[^\n]*\n/,""):""}function o(e){if("string"==typeof(null==e?void 0:e.message)){if(e.message.includes("Class extends value undefined is not a constructor or null")){let t="This might be caused by a React Class Component being rendered in a Server Component, React Class Components only works in Client Components. Read more: https://nextjs.org/docs/messages/class-component-in-server-component";if(e.message.includes(t))return;n(e,`${e.message}

${t}`);return}if(e.message.includes("createContext is not a function"))return void n(e,'createContext only works in Client Components. Add the "use client" directive at the top of the file to use it. Read more: https://nextjs.org/docs/messages/context-in-server-component');for(let t of r)if(RegExp(`\\b${t}\\b.*is not a function`).test(e.message))return void n(e,`${t} only works in Client Components. Add the "use client" directive at the top of the file to use it. Read more: https://nextjs.org/docs/messages/react-client-hook-in-server-component`)}}},7339:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},7504:(e,t,r)=>{"use strict";r.d(t,{y:()=>a});var n=r(73063),i=r(12742),o=r(21874);let a={test:e=>o.B.test(e)||n.u.test(e)||i.V.test(e),parse:e=>o.B.test(e)?o.B.parse(e):i.V.test(e)?i.V.parse(e):n.u.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?o.B.transform(e):i.V.transform(e)}},7797:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{StaticGenBailoutError:function(){return n},isStaticGenBailoutError:function(){return i}});let r="NEXT_STATIC_GEN_BAILOUT";class n extends Error{constructor(...e){super(...e),this.code=r}}function i(e){return"object"==typeof e&&null!==e&&"code"in e&&e.code===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8670:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ROOT_SEGMENT_KEY:function(){return o},convertSegmentPathToStaticExportFilename:function(){return u},encodeChildSegmentKey:function(){return a},encodeSegment:function(){return i}});let n=r(35499);function i(e){if("string"==typeof e)return e.startsWith(n.PAGE_SEGMENT_KEY)?n.PAGE_SEGMENT_KEY:"/_not-found"===e?"_not-found":l(e);let t=e[0],r=e[1],i=e[2],o=l(t);return"$"+i+"$"+o+"$"+l(r)}let o="";function a(e,t,r){return e+"/"+("children"===t?r:"@"+l(t)+"/"+r)}let s=/^[a-zA-Z0-9\-_@]+$/;function l(e){return s.test(e)?e:"!"+btoa(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}function u(e){return"__next"+e.replace(/\//g,".")+".txt"}},8681:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isRequestAPICallableInsideAfter:function(){return l},throwForSearchParamsAccessInUseCache:function(){return s},throwWithStaticGenerationBailoutError:function(){return o},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return a}});let n=r(7797),i=r(3295);function o(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function a(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function s(e){let t=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=t,t}function l(){let e=i.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},8693:(e,t,r)=>{"use strict";r.d(t,{Ht:()=>a});var n=r(43210),i=r(60687),o=n.createContext(void 0),a=({client:e,children:t})=>(n.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,i.jsx)(o.Provider,{value:e,children:t}))},8704:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return i},getAccessFallbackErrorTypeByStatus:function(){return s},getAccessFallbackHTTPStatus:function(){return a},isHTTPAccessFallbackError:function(){return o}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),i="NEXT_HTTP_ERROR_FALLBACK";function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}function a(e){return Number(e.digest.split(";")[1])}function s(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8730:(e,t,r)=>{"use strict";r.d(t,{DX:()=>s,Dc:()=>u,TL:()=>a});var n=r(43210),i=r(98599),o=r(60687);function a(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var a;let e,s,l=(a=r,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),u=function(e,t){let r={...t};for(let n in t){let i=e[n],o=t[n];/^on[A-Z]/.test(n)?i&&o?r[n]=(...e)=>{let t=o(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...o}:"className"===n&&(r[n]=[i,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,i.t)(t,l):l),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...a}=e,s=n.Children.toArray(i),l=s.find(c);if(l){let e=l.props.children,i=s.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...a,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}var s=a("Slot"),l=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=l,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},8830:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(59154),r(25232),r(29651),r(28627),r(78866),r(75076),r(97936),r(35429);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9221:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createPrerenderSearchParamsForClientPage:function(){return h},createSearchParamsFromClient:function(){return d},createServerSearchParamsForMetadata:function(){return f},createServerSearchParamsForServerPage:function(){return p},makeErroringExoticSearchParamsForUseCache:function(){return b}});let n=r(83717),i=r(54717),o=r(63033),a=r(75539),s=r(18238),l=r(14768),u=r(84627),c=r(8681);function d(e,t){let r=o.workUnitAsyncStorage.getStore();if(r)switch(r.type){case"prerender":case"prerender-ppr":case"prerender-legacy":return m(t,r)}return y(e,t)}r(52825);let f=p;function p(e,t){let r=o.workUnitAsyncStorage.getStore();if(r)switch(r.type){case"prerender":case"prerender-ppr":case"prerender-legacy":return m(t,r)}return y(e,t)}function h(e){if(e.forceStatic)return Promise.resolve({});let t=o.workUnitAsyncStorage.getStore();return t&&"prerender"===t.type?(0,s.makeHangingPromise)(t.renderSignal,"`searchParams`"):Promise.resolve({})}function m(e,t){return e.forceStatic?Promise.resolve({}):"prerender"===t.type?function(e,t){let r=g.get(t);if(r)return r;let o=(0,s.makeHangingPromise)(t.renderSignal,"`searchParams`"),a=new Proxy(o,{get(r,a,s){if(Object.hasOwn(o,a))return n.ReflectAdapter.get(r,a,s);switch(a){case"then":return(0,i.annotateDynamicAccess)("`await searchParams`, `searchParams.then`, or similar",t),n.ReflectAdapter.get(r,a,s);case"status":return(0,i.annotateDynamicAccess)("`use(searchParams)`, `searchParams.status`, or similar",t),n.ReflectAdapter.get(r,a,s);default:if("string"==typeof a&&!u.wellKnownProperties.has(a)){let r=(0,u.describeStringPropertyAccess)("searchParams",a),n=E(e,r);(0,i.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}return n.ReflectAdapter.get(r,a,s)}},has(r,o){if("string"==typeof o){let r=(0,u.describeHasCheckingStringProperty)("searchParams",o),n=E(e,r);(0,i.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}return n.ReflectAdapter.has(r,o)},ownKeys(){let r="`{...searchParams}`, `Object.keys(searchParams)`, or similar",n=E(e,r);(0,i.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}});return g.set(t,a),a}(e.route,t):function(e,t){let r=g.get(e);if(r)return r;let o=Promise.resolve({}),a=new Proxy(o,{get(r,a,s){if(Object.hasOwn(o,a))return n.ReflectAdapter.get(r,a,s);switch(a){case"then":{let r="`await searchParams`, `searchParams.then`, or similar";e.dynamicShouldError?(0,c.throwWithStaticGenerationBailoutErrorWithDynamicError)(e.route,r):"prerender-ppr"===t.type?(0,i.postponeWithTracking)(e.route,r,t.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(r,e,t);return}case"status":{let r="`use(searchParams)`, `searchParams.status`, or similar";e.dynamicShouldError?(0,c.throwWithStaticGenerationBailoutErrorWithDynamicError)(e.route,r):"prerender-ppr"===t.type?(0,i.postponeWithTracking)(e.route,r,t.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(r,e,t);return}default:if("string"==typeof a&&!u.wellKnownProperties.has(a)){let r=(0,u.describeStringPropertyAccess)("searchParams",a);e.dynamicShouldError?(0,c.throwWithStaticGenerationBailoutErrorWithDynamicError)(e.route,r):"prerender-ppr"===t.type?(0,i.postponeWithTracking)(e.route,r,t.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(r,e,t)}return n.ReflectAdapter.get(r,a,s)}},has(r,o){if("string"==typeof o){let r=(0,u.describeHasCheckingStringProperty)("searchParams",o);return e.dynamicShouldError?(0,c.throwWithStaticGenerationBailoutErrorWithDynamicError)(e.route,r):"prerender-ppr"===t.type?(0,i.postponeWithTracking)(e.route,r,t.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(r,e,t),!1}return n.ReflectAdapter.has(r,o)},ownKeys(){let r="`{...searchParams}`, `Object.keys(searchParams)`, or similar";e.dynamicShouldError?(0,c.throwWithStaticGenerationBailoutErrorWithDynamicError)(e.route,r):"prerender-ppr"===t.type?(0,i.postponeWithTracking)(e.route,r,t.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(r,e,t)}});return g.set(e,a),a}(e,t)}function y(e,t){return t.forceStatic?Promise.resolve({}):function(e,t){let r=g.get(e);if(r)return r;let n=Promise.resolve(e);return g.set(e,n),Object.keys(e).forEach(r=>{u.wellKnownProperties.has(r)||Object.defineProperty(n,r,{get(){let n=o.workUnitAsyncStorage.getStore();return(0,i.trackDynamicDataInDynamicRender)(t,n),e[r]},set(e){Object.defineProperty(n,r,{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0})}),n}(e,t)}let g=new WeakMap,v=new WeakMap;function b(e){let t=v.get(e);if(t)return t;let r=Promise.resolve({}),i=new Proxy(r,{get:(t,i,o)=>(Object.hasOwn(r,i)||"string"!=typeof i||"then"!==i&&u.wellKnownProperties.has(i)||(0,c.throwForSearchParamsAccessInUseCache)(e),n.ReflectAdapter.get(t,i,o)),has:(t,r)=>("string"!=typeof r||"then"!==r&&u.wellKnownProperties.has(r)||(0,c.throwForSearchParamsAccessInUseCache)(e),n.ReflectAdapter.has(t,r)),ownKeys(){(0,c.throwForSearchParamsAccessInUseCache)(e)}});return v.set(e,i),i}let w=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(E),x=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t,r){let n=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${n}used ${t}. \`searchParams\` should be awaited before using its properties. The following properties were not available through enumeration because they conflict with builtin or well-known property names: ${function(e){switch(e.length){case 0:throw Object.defineProperty(new a.InvariantError("Expected describeListOfPropertyNames to be called with a non-empty list of strings."),"__NEXT_ERROR_CODE",{value:"E531",enumerable:!1,configurable:!0});case 1:return`\`${e[0]}\``;case 2:return`\`${e[0]}\` and \`${e[1]}\``;default:{let t="";for(let r=0;r<e.length-1;r++)t+=`\`${e[r]}\`, `;return t+`, and \`${e[e.length-1]}\``}}}(r)}. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E2",enumerable:!1,configurable:!0})});function E(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`searchParams\` should be awaited before using its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E249",enumerable:!1,configurable:!0})}},9510:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(43210),i=r(11273),o=r(98599),a=r(8730),s=r(60687);function l(e){let t=e+"CollectionProvider",[r,l]=(0,i.A)(t),[u,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,i=n.useRef(null),o=n.useRef(new Map).current;return(0,s.jsx)(u,{scope:t,itemMap:o,collectionRef:i,children:r})};d.displayName=t;let f=e+"CollectionSlot",p=(0,a.TL)(f),h=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=c(f,r),a=(0,o.s)(t,i.collectionRef);return(0,s.jsx)(p,{ref:a,children:n})});h.displayName=f;let m=e+"CollectionItemSlot",y="data-radix-collection-item",g=(0,a.TL)(m),v=n.forwardRef((e,t)=>{let{scope:r,children:i,...a}=e,l=n.useRef(null),u=(0,o.s)(t,l),d=c(m,r);return n.useEffect(()=>(d.itemMap.set(l,{ref:l,...a}),()=>void d.itemMap.delete(l))),(0,s.jsx)(g,{...{[y]:""},ref:u,children:i})});return v.displayName=m,[{Provider:d,Slot:h,ItemSlot:v},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${y}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}var u=new WeakMap;function c(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=d(t),i=n>=0?n:r+n;return i<0||i>=r?-1:i}(e,t);return -1===r?void 0:e[r]}function d(e){return e!=e||0===e?0:Math.trunc(e)}},9608:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bailoutToClientRendering",{enumerable:!0,get:function(){return o}});let n=r(81208),i=r(29294);function o(e){let t=i.workAsyncStorage.getStore();if((null==t||!t.forceStatic)&&(null==t?void 0:t.isStaticGeneration))throw Object.defineProperty(new n.BailoutToCSRError(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9707:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return c}});let n=r(83913),i=r(89752),o=r(86770),a=r(57391),s=r(33123),l=r(33898),u=r(59435);function c(e,t,r,c,f){let p,h=t.tree,m=t.cache,y=(0,a.createHrefFromUrl)(c);if("string"==typeof r)return!1;for(let t of r){if(!function e(t){if(!t)return!1;let r=t[2];if(t[3])return!0;for(let t in r)if(e(r[t]))return!0;return!1}(t.seedData))continue;let r=t.tree;r=d(r,Object.fromEntries(c.searchParams));let{seedData:a,isRootRender:u,pathToSegment:f}=t,g=["",...f];r=d(r,Object.fromEntries(c.searchParams));let v=(0,o.applyRouterStatePatchToTree)(g,h,r,y),b=(0,i.createEmptyCacheNode)();if(u&&a){let t=a[1];b.loading=a[3],b.rsc=t,function e(t,r,i,o,a){if(0!==Object.keys(o[1]).length)for(let l in o[1]){let u,c=o[1][l],d=c[0],f=(0,s.createRouterCacheKey)(d),p=null!==a&&void 0!==a[2][l]?a[2][l]:null;if(null!==p){let e=p[1],r=p[3];u={lazyData:null,rsc:d.includes(n.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else u={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let h=r.parallelRoutes.get(l);h?h.set(f,u):r.parallelRoutes.set(l,new Map([[f,u]])),e(t,u,i,c,p)}}(e,b,m,r,a)}else b.rsc=m.rsc,b.prefetchRsc=m.prefetchRsc,b.loading=m.loading,b.parallelRoutes=new Map(m.parallelRoutes),(0,l.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,m,t);v&&(h=v,m=b,p=!0)}return!!p&&(f.patchedTree=h,f.cache=m,f.canonicalUrl=y,f.hashFragment=c.hash,(0,u.handleMutable)(t,f))}function d(e,t){let[r,i,...o]=e;if(r.includes(n.PAGE_SEGMENT_KEY))return[(0,n.addSearchParamsIfPageSegment)(r,t),i,...o];let a={};for(let[e,r]of Object.entries(i))a[e]=d(r,t);return[r,a,...o]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9977:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_HEADER:function(){return n},FLIGHT_HEADERS:function(){return d},NEXT_DID_POSTPONE_HEADER:function(){return h},NEXT_HMR_REFRESH_HASH_COOKIE:function(){return l},NEXT_HMR_REFRESH_HEADER:function(){return s},NEXT_IS_PRERENDER_HEADER:function(){return g},NEXT_REWRITTEN_PATH_HEADER:function(){return m},NEXT_REWRITTEN_QUERY_HEADER:function(){return y},NEXT_ROUTER_PREFETCH_HEADER:function(){return o},NEXT_ROUTER_SEGMENT_PREFETCH_HEADER:function(){return a},NEXT_ROUTER_STALE_TIME_HEADER:function(){return p},NEXT_ROUTER_STATE_TREE_HEADER:function(){return i},NEXT_RSC_UNION_QUERY:function(){return f},NEXT_URL:function(){return u},RSC_CONTENT_TYPE_HEADER:function(){return c},RSC_HEADER:function(){return r}});let r="RSC",n="Next-Action",i="Next-Router-State-Tree",o="Next-Router-Prefetch",a="Next-Router-Segment-Prefetch",s="Next-HMR-Refresh",l="__next_hmr_refresh_hash__",u="Next-Url",c="text/x-component",d=[r,i,o,s,a],f="_rsc",p="x-nextjs-stale-time",h="x-nextjs-postponed",m="x-nextjs-rewritten-path",y="x-nextjs-rewritten-query",g="x-nextjs-prerender";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10218:(e,t,r)=>{"use strict";r.d(t,{D:()=>s});var n=r(43210),i="(prefers-color-scheme: dark)",o=n.createContext(void 0),a={setTheme:e=>{},themes:[]},s=()=>{var e;return null!=(e=n.useContext(o))?e:a},l=null,u=null,c=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},d=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},f=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},10449:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.HooksClientContext},11264:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"callServer",{enumerable:!0,get:function(){return a}});let n=r(43210),i=r(59154),o=r(19129);async function a(e,t){return new Promise((r,a)=>{(0,n.startTransition)(()=>{(0,o.dispatchAppRouterAction)({type:i.ACTION_SERVER_ACTION,actionId:e,actionArgs:t,resolve:r,reject:a})})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},11273:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,q:()=>o});var n=r(43210),i=r(60687);function o(e,t){let r=n.createContext(t),o=e=>{let{children:t,...o}=e,a=n.useMemo(()=>o,Object.values(o));return(0,i.jsx)(r.Provider,{value:a,children:t})};return o.displayName=e+"Provider",[o,function(i){let o=n.useContext(r);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function a(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let i=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return o.scopeName=e,[function(t,o){let a=n.createContext(o),s=r.length;r=[...r,o];let l=t=>{let{scope:r,children:o,...l}=t,u=r?.[e]?.[s]||a,c=n.useMemo(()=>l,Object.values(l));return(0,i.jsx)(u.Provider,{value:c,children:o})};return l.displayName=t+"Provider",[l,function(r,i){let l=i?.[e]?.[s]||a,u=n.useContext(l);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(o,...t)]}},11448:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findSourceMapURL",{enumerable:!0,get:function(){return r}});let r=void 0;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},11804:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AppLinksMeta:function(){return s},OpenGraphMetadata:function(){return i},TwitterMetadata:function(){return a}});let n=r(80407);function i({openGraph:e}){var t,r,i,o,a,s,l;let u;if(!e)return null;if("type"in e){let t=e.type;switch(t){case"website":u=[(0,n.Meta)({property:"og:type",content:"website"})];break;case"article":u=[(0,n.Meta)({property:"og:type",content:"article"}),(0,n.Meta)({property:"article:published_time",content:null==(o=e.publishedTime)?void 0:o.toString()}),(0,n.Meta)({property:"article:modified_time",content:null==(a=e.modifiedTime)?void 0:a.toString()}),(0,n.Meta)({property:"article:expiration_time",content:null==(s=e.expirationTime)?void 0:s.toString()}),(0,n.MultiMeta)({propertyPrefix:"article:author",contents:e.authors}),(0,n.Meta)({property:"article:section",content:e.section}),(0,n.MultiMeta)({propertyPrefix:"article:tag",contents:e.tags})];break;case"book":u=[(0,n.Meta)({property:"og:type",content:"book"}),(0,n.Meta)({property:"book:isbn",content:e.isbn}),(0,n.Meta)({property:"book:release_date",content:e.releaseDate}),(0,n.MultiMeta)({propertyPrefix:"book:author",contents:e.authors}),(0,n.MultiMeta)({propertyPrefix:"book:tag",contents:e.tags})];break;case"profile":u=[(0,n.Meta)({property:"og:type",content:"profile"}),(0,n.Meta)({property:"profile:first_name",content:e.firstName}),(0,n.Meta)({property:"profile:last_name",content:e.lastName}),(0,n.Meta)({property:"profile:username",content:e.username}),(0,n.Meta)({property:"profile:gender",content:e.gender})];break;case"music.song":u=[(0,n.Meta)({property:"og:type",content:"music.song"}),(0,n.Meta)({property:"music:duration",content:null==(l=e.duration)?void 0:l.toString()}),(0,n.MultiMeta)({propertyPrefix:"music:album",contents:e.albums}),(0,n.MultiMeta)({propertyPrefix:"music:musician",contents:e.musicians})];break;case"music.album":u=[(0,n.Meta)({property:"og:type",content:"music.album"}),(0,n.MultiMeta)({propertyPrefix:"music:song",contents:e.songs}),(0,n.MultiMeta)({propertyPrefix:"music:musician",contents:e.musicians}),(0,n.Meta)({property:"music:release_date",content:e.releaseDate})];break;case"music.playlist":u=[(0,n.Meta)({property:"og:type",content:"music.playlist"}),(0,n.MultiMeta)({propertyPrefix:"music:song",contents:e.songs}),(0,n.MultiMeta)({propertyPrefix:"music:creator",contents:e.creators})];break;case"music.radio_station":u=[(0,n.Meta)({property:"og:type",content:"music.radio_station"}),(0,n.MultiMeta)({propertyPrefix:"music:creator",contents:e.creators})];break;case"video.movie":u=[(0,n.Meta)({property:"og:type",content:"video.movie"}),(0,n.MultiMeta)({propertyPrefix:"video:actor",contents:e.actors}),(0,n.MultiMeta)({propertyPrefix:"video:director",contents:e.directors}),(0,n.MultiMeta)({propertyPrefix:"video:writer",contents:e.writers}),(0,n.Meta)({property:"video:duration",content:e.duration}),(0,n.Meta)({property:"video:release_date",content:e.releaseDate}),(0,n.MultiMeta)({propertyPrefix:"video:tag",contents:e.tags})];break;case"video.episode":u=[(0,n.Meta)({property:"og:type",content:"video.episode"}),(0,n.MultiMeta)({propertyPrefix:"video:actor",contents:e.actors}),(0,n.MultiMeta)({propertyPrefix:"video:director",contents:e.directors}),(0,n.MultiMeta)({propertyPrefix:"video:writer",contents:e.writers}),(0,n.Meta)({property:"video:duration",content:e.duration}),(0,n.Meta)({property:"video:release_date",content:e.releaseDate}),(0,n.MultiMeta)({propertyPrefix:"video:tag",contents:e.tags}),(0,n.Meta)({property:"video:series",content:e.series})];break;case"video.tv_show":u=[(0,n.Meta)({property:"og:type",content:"video.tv_show"})];break;case"video.other":u=[(0,n.Meta)({property:"og:type",content:"video.other"})];break;default:throw Object.defineProperty(Error(`Invalid OpenGraph type: ${t}`),"__NEXT_ERROR_CODE",{value:"E237",enumerable:!1,configurable:!0})}}return(0,n.MetaFilter)([(0,n.Meta)({property:"og:determiner",content:e.determiner}),(0,n.Meta)({property:"og:title",content:null==(t=e.title)?void 0:t.absolute}),(0,n.Meta)({property:"og:description",content:e.description}),(0,n.Meta)({property:"og:url",content:null==(r=e.url)?void 0:r.toString()}),(0,n.Meta)({property:"og:site_name",content:e.siteName}),(0,n.Meta)({property:"og:locale",content:e.locale}),(0,n.Meta)({property:"og:country_name",content:e.countryName}),(0,n.Meta)({property:"og:ttl",content:null==(i=e.ttl)?void 0:i.toString()}),(0,n.MultiMeta)({propertyPrefix:"og:image",contents:e.images}),(0,n.MultiMeta)({propertyPrefix:"og:video",contents:e.videos}),(0,n.MultiMeta)({propertyPrefix:"og:audio",contents:e.audio}),(0,n.MultiMeta)({propertyPrefix:"og:email",contents:e.emails}),(0,n.MultiMeta)({propertyPrefix:"og:phone_number",contents:e.phoneNumbers}),(0,n.MultiMeta)({propertyPrefix:"og:fax_number",contents:e.faxNumbers}),(0,n.MultiMeta)({propertyPrefix:"og:locale:alternate",contents:e.alternateLocale}),...u||[]])}function o({app:e,type:t}){var r,i;return[(0,n.Meta)({name:`twitter:app:name:${t}`,content:e.name}),(0,n.Meta)({name:`twitter:app:id:${t}`,content:e.id[t]}),(0,n.Meta)({name:`twitter:app:url:${t}`,content:null==(i=e.url)||null==(r=i[t])?void 0:r.toString()})]}function a({twitter:e}){var t;if(!e)return null;let{card:r}=e;return(0,n.MetaFilter)([(0,n.Meta)({name:"twitter:card",content:r}),(0,n.Meta)({name:"twitter:site",content:e.site}),(0,n.Meta)({name:"twitter:site:id",content:e.siteId}),(0,n.Meta)({name:"twitter:creator",content:e.creator}),(0,n.Meta)({name:"twitter:creator:id",content:e.creatorId}),(0,n.Meta)({name:"twitter:title",content:null==(t=e.title)?void 0:t.absolute}),(0,n.Meta)({name:"twitter:description",content:e.description}),(0,n.MultiMeta)({namePrefix:"twitter:image",contents:e.images}),..."player"===r?e.players.flatMap(e=>[(0,n.Meta)({name:"twitter:player",content:e.playerUrl.toString()}),(0,n.Meta)({name:"twitter:player:stream",content:e.streamUrl.toString()}),(0,n.Meta)({name:"twitter:player:width",content:e.width}),(0,n.Meta)({name:"twitter:player:height",content:e.height})]):[],..."app"===r?[o({app:e.app,type:"iphone"}),o({app:e.app,type:"ipad"}),o({app:e.app,type:"googleplay"})]:[]])}function s({appLinks:e}){return e?(0,n.MetaFilter)([(0,n.MultiMeta)({propertyPrefix:"al:ios",contents:e.ios}),(0,n.MultiMeta)({propertyPrefix:"al:iphone",contents:e.iphone}),(0,n.MultiMeta)({propertyPrefix:"al:ipad",contents:e.ipad}),(0,n.MultiMeta)({propertyPrefix:"al:android",contents:e.android}),(0,n.MultiMeta)({propertyPrefix:"al:windows_phone",contents:e.windows_phone}),(0,n.MultiMeta)({propertyPrefix:"al:windows",contents:e.windows}),(0,n.MultiMeta)({propertyPrefix:"al:windows_universal",contents:e.windows_universal}),(0,n.MultiMeta)({propertyPrefix:"al:web",contents:e.web})]):null}},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41552).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12089:(e,t,r)=>{let{createProxy:n}=r(39844);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/metadata/async-metadata.js")},12157:(e,t,r)=>{"use strict";r.d(t,{L:()=>n});let n=(0,r(43210).createContext)({})},12441:(e,t,r)=>{"use strict";r.d(t,{V:()=>n});let n=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2},12742:(e,t,r)=>{"use strict";r.d(t,{V:()=>s});var n=r(95444),i=r(32874),o=r(97095),a=r(7236);let s={test:(0,a.$)("hsl","hue"),parse:(0,a.q)("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:a=1})=>"hsla("+Math.round(e)+", "+i.KN.transform((0,o.a)(t))+", "+i.KN.transform((0,o.a)(r))+", "+(0,o.a)(n.X4.transform(a))+")"}},12776:(e,t,r)=>{"use strict";function n(e){return!1}function i(){}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleHardNavError:function(){return n},useNavFailureHandler:function(){return i}}),r(43210),r(57391),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},12907:(e,t,r)=>{"use strict";e.exports=r(65239).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},13495:(e,t,r)=>{"use strict";r.d(t,{c:()=>i});var n=r(43210);function i(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},14077:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"matchSegment",{enumerable:!0,get:function(){return r}});let r=(e,t)=>"string"==typeof e?"string"==typeof t&&e===t:"string"!=typeof t&&e[0]===t[0]&&e[1]===t[1];("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},14114:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"IconsMetadata",{enumerable:!0,get:function(){return s}});let n=r(37413),i=r(80407);function o({icon:e}){let{url:t,rel:r="icon",...i}=e;return(0,n.jsx)("link",{rel:r,href:t.toString(),...i})}function a({rel:e,icon:t}){if("object"==typeof t&&!(t instanceof URL))return!t.rel&&e&&(t.rel=e),o({icon:t});{let r=t.toString();return(0,n.jsx)("link",{rel:e,href:r})}}function s({icons:e}){if(!e)return null;let t=e.shortcut,r=e.icon,n=e.apple,s=e.other;return(0,i.MetaFilter)([t?t.map(e=>a({rel:"shortcut icon",icon:e})):null,r?r.map(e=>a({rel:"icon",icon:e})):null,n?n.map(e=>a({rel:"apple-touch-icon",icon:e})):null,s?s.map(e=>o({icon:e})):null])}},14163:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>s});var n=r(43210),i=r(51215),o=r(8730),a=r(60687),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.TL)(`Primitive.${t}`),i=n.forwardRef((e,n)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?r:t,{...o,ref:n})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},14296:(e,t,r)=>{"use strict";r.d(t,{v:()=>i});var n=r(87556);class i{constructor(){this.subscriptions=[]}add(e){return(0,n.Kq)(this.subscriptions,e),()=>(0,n.Ai)(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(e,t,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},14768:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(43210));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}let o={current:null},a="function"==typeof n.cache?n.cache:e=>e,s=console.warn;function l(e){return function(...t){s(e(...t))}}a(e=>{try{s(o.current)}finally{o.current=null}})},14985:(e,t,r)=>{"use strict";function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n})},15102:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},15124:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(43210);let i=r(7044).B?n.useLayoutEffect:n.useEffect},15547:(e,t,r)=>{"use strict";function n(e,t){return t?1e3/t*e:0}r.d(t,{f:()=>n})},16042:(e,t,r)=>{let{createProxy:n}=r(39844);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/client-segment.js")},16444:(e,t,r)=>{let{createProxy:n}=r(39844);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/client-page.js")},17135:(e,t,r)=>{"use strict";r.d(t,{d:()=>s});var n=r(24342),i=r(43210),o=r(32582),a=r(72789);function s(e){let t=(0,a.M)(()=>(0,n.OQ)(e)),{isStatic:r}=(0,i.useContext)(o.Q);if(r){let[,r]=(0,i.useState)(e);(0,i.useEffect)(()=>t.on("change",r),[])}return t}},17388:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17974:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},18171:(e,t,r)=>{"use strict";r.d(t,{s:()=>i});var n=r(74479);function i(e){return(0,n.G)(e)&&"offsetHeight"in e}},18238:(e,t)=>{"use strict";function r(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isHangingPromiseRejectionError:function(){return r},makeHangingPromise:function(){return a}});let n="HANGING_PROMISE_REJECTION";class i extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest=n}}let o=new WeakMap;function a(e,t){if(e.aborted)return Promise.reject(new i(t));{let r=new Promise((r,n)=>{let a=n.bind(null,new i(t)),s=o.get(e);if(s)s.push(a);else{let t=[a];o.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return r.catch(s),r}}function s(){}},18468:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,o){let a=o.length<=2,[s,l]=o,u=(0,n.createRouterCacheKey)(l),c=r.parallelRoutes.get(s);if(!c)return;let d=t.parallelRoutes.get(s);if(d&&d!==c||(d=new Map(c),t.parallelRoutes.set(s,d)),a)return void d.delete(u);let f=c.get(u),p=d.get(u);p&&f&&(p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},d.set(u,p)),e(p,f,(0,i.getNextFlightSegmentPath)(o)))}}});let n=r(33123),i=r(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19129:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{dispatchAppRouterAction:function(){return a},useActionQueue:function(){return s}});let n=r(40740)._(r(43210)),i=r(91992),o=null;function a(e){if(null===o)throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0});o(e)}function s(e){let[t,r]=n.default.useState(e.state);return o=t=>e.dispatch(t,r),(0,i.isThenable)(t)?(0,n.use)(t):t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19169:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},19331:(e,t,r)=>{"use strict";r.d(t,{G:()=>c});var n=r(97819),i=r(83361),o=r(78205),a=r(66244),s=r(64068),l=r(97758),u=r(91955);function c(e,t,{clamp:r=!0,ease:d,mixer:f}={}){let p=e.length;if((0,a.V)(p===t.length,"Both input and output ranges must be the same length"),1===p)return()=>t[0];if(2===p&&t[0]===t[1])return()=>t[1];let h=e[0]===e[1];e[0]>e[p-1]&&(e=[...e].reverse(),t=[...t].reverse());let m=function(e,t,r){let a=[],s=r||n.W.mix||u.j,l=e.length-1;for(let r=0;r<l;r++){let n=s(e[r],e[r+1]);if(t){let e=Array.isArray(t)?t[r]||i.l:t;n=(0,o.F)(e,n)}a.push(n)}return a}(t,d,f),y=m.length,g=r=>{if(h&&r<e[0])return t[0];let n=0;if(y>1)for(;n<e.length-2&&!(r<e[n+1]);n++);let i=(0,s.q)(e[n],e[n+1],r);return m[n](i)};return r?t=>g((0,l.q)(e[0],e[p-1],t)):g}},19357:(e,t,r)=>{"use strict";e.exports=r(94041).vendored["react-ssr"].ReactServerDOMWebpackClientEdge},20592:(e,t,r)=>{"use strict";r.d(t,{UC:()=>tv,Kq:()=>tm,bL:()=>ty,l9:()=>tg});var n=r(43210),i=r(70569),o=r(98599),a=r(11273),s=r(31355),l=r(96963);let u=["top","right","bottom","left"],c=Math.min,d=Math.max,f=Math.round,p=Math.floor,h=e=>({x:e,y:e}),m={left:"right",right:"left",bottom:"top",top:"bottom"},y={start:"end",end:"start"};function g(e,t){return"function"==typeof e?e(t):e}function v(e){return e.split("-")[0]}function b(e){return e.split("-")[1]}function w(e){return"x"===e?"y":"x"}function x(e){return"y"===e?"height":"width"}function E(e){return["top","bottom"].includes(v(e))?"y":"x"}function _(e){return e.replace(/start|end/g,e=>y[e])}function P(e){return e.replace(/left|right|bottom|top/g,e=>m[e])}function R(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function S(e){let{x:t,y:r,width:n,height:i}=e;return{width:n,height:i,top:r,left:t,right:t+n,bottom:r+i,x:t,y:r}}function O(e,t,r){let n,{reference:i,floating:o}=e,a=E(t),s=w(E(t)),l=x(s),u=v(t),c="y"===a,d=i.x+i.width/2-o.width/2,f=i.y+i.height/2-o.height/2,p=i[l]/2-o[l]/2;switch(u){case"top":n={x:d,y:i.y-o.height};break;case"bottom":n={x:d,y:i.y+i.height};break;case"right":n={x:i.x+i.width,y:f};break;case"left":n={x:i.x-o.width,y:f};break;default:n={x:i.x,y:i.y}}switch(b(t)){case"start":n[s]-=p*(r&&c?-1:1);break;case"end":n[s]+=p*(r&&c?-1:1)}return n}let T=async(e,t,r)=>{let{placement:n="bottom",strategy:i="absolute",middleware:o=[],platform:a}=r,s=o.filter(Boolean),l=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:d}=O(u,n,l),f=n,p={},h=0;for(let r=0;r<s.length;r++){let{name:o,fn:m}=s[r],{x:y,y:g,data:v,reset:b}=await m({x:c,y:d,initialPlacement:n,placement:f,strategy:i,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=y?y:c,d=null!=g?g:d,p={...p,[o]:{...p[o],...v}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:i}):b.rects),{x:c,y:d}=O(u,f,l)),r=-1)}return{x:c,y:d,placement:f,strategy:i,middlewareData:p}};async function j(e,t){var r;void 0===t&&(t={});let{x:n,y:i,platform:o,rects:a,elements:s,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=g(t,e),h=R(p),m=s[f?"floating"===d?"reference":"floating":d],y=S(await o.getClippingRect({element:null==(r=await (null==o.isElement?void 0:o.isElement(m)))||r?m:m.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(s.floating)),boundary:u,rootBoundary:c,strategy:l})),v="floating"===d?{x:n,y:i,width:a.floating.width,height:a.floating.height}:a.reference,b=await (null==o.getOffsetParent?void 0:o.getOffsetParent(s.floating)),w=await (null==o.isElement?void 0:o.isElement(b))&&await (null==o.getScale?void 0:o.getScale(b))||{x:1,y:1},x=S(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:v,offsetParent:b,strategy:l}):v);return{top:(y.top-x.top+h.top)/w.y,bottom:(x.bottom-y.bottom+h.bottom)/w.y,left:(y.left-x.left+h.left)/w.x,right:(x.right-y.right+h.right)/w.x}}function M(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function A(e){return u.some(t=>e[t]>=0)}async function C(e,t){let{placement:r,platform:n,elements:i}=e,o=await (null==n.isRTL?void 0:n.isRTL(i.floating)),a=v(r),s=b(r),l="y"===E(r),u=["left","top"].includes(a)?-1:1,c=o&&l?-1:1,d=g(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof h&&(p="end"===s?-1*h:h),l?{x:p*c,y:f*u}:{x:f*u,y:p*c}}function k(){return"undefined"!=typeof window}function D(e){return F(e)?(e.nodeName||"").toLowerCase():"#document"}function N(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function L(e){var t;return null==(t=(F(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function F(e){return!!k()&&(e instanceof Node||e instanceof N(e).Node)}function I(e){return!!k()&&(e instanceof Element||e instanceof N(e).Element)}function U(e){return!!k()&&(e instanceof HTMLElement||e instanceof N(e).HTMLElement)}function B(e){return!!k()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof N(e).ShadowRoot)}function V(e){let{overflow:t,overflowX:r,overflowY:n,display:i}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(i)}function $(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function H(e){let t=W(),r=I(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function W(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function G(e){return["html","body","#document"].includes(D(e))}function z(e){return N(e).getComputedStyle(e)}function q(e){return I(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function K(e){if("html"===D(e))return e;let t=e.assignedSlot||e.parentNode||B(e)&&e.host||L(e);return B(t)?t.host:t}function X(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let i=function e(t){let r=K(t);return G(r)?t.ownerDocument?t.ownerDocument.body:t.body:U(r)&&V(r)?r:e(r)}(e),o=i===(null==(n=e.ownerDocument)?void 0:n.body),a=N(i);if(o){let e=Y(a);return t.concat(a,a.visualViewport||[],V(i)?i:[],e&&r?X(e):[])}return t.concat(i,X(i,[],r))}function Y(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Q(e){let t=z(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,i=U(e),o=i?e.offsetWidth:r,a=i?e.offsetHeight:n,s=f(r)!==o||f(n)!==a;return s&&(r=o,n=a),{width:r,height:n,$:s}}function J(e){return I(e)?e:e.contextElement}function Z(e){let t=J(e);if(!U(t))return h(1);let r=t.getBoundingClientRect(),{width:n,height:i,$:o}=Q(t),a=(o?f(r.width):r.width)/n,s=(o?f(r.height):r.height)/i;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}let ee=h(0);function et(e){let t=N(e);return W()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ee}function er(e,t,r,n){var i;void 0===t&&(t=!1),void 0===r&&(r=!1);let o=e.getBoundingClientRect(),a=J(e),s=h(1);t&&(n?I(n)&&(s=Z(n)):s=Z(e));let l=(void 0===(i=r)&&(i=!1),n&&(!i||n===N(a))&&i)?et(a):h(0),u=(o.left+l.x)/s.x,c=(o.top+l.y)/s.y,d=o.width/s.x,f=o.height/s.y;if(a){let e=N(a),t=n&&I(n)?N(n):n,r=e,i=Y(r);for(;i&&n&&t!==r;){let e=Z(i),t=i.getBoundingClientRect(),n=z(i),o=t.left+(i.clientLeft+parseFloat(n.paddingLeft))*e.x,a=t.top+(i.clientTop+parseFloat(n.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,f*=e.y,u+=o,c+=a,i=Y(r=N(i))}}return S({width:d,height:f,x:u,y:c})}function en(e,t){let r=q(e).scrollLeft;return t?t.left+r:er(L(e)).left+r}function ei(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:en(e,n)),y:n.top+t.scrollTop}}function eo(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=N(e),n=L(e),i=r.visualViewport,o=n.clientWidth,a=n.clientHeight,s=0,l=0;if(i){o=i.width,a=i.height;let e=W();(!e||e&&"fixed"===t)&&(s=i.offsetLeft,l=i.offsetTop)}return{width:o,height:a,x:s,y:l}}(e,r);else if("document"===t)n=function(e){let t=L(e),r=q(e),n=e.ownerDocument.body,i=d(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),o=d(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),a=-r.scrollLeft+en(e),s=-r.scrollTop;return"rtl"===z(n).direction&&(a+=d(t.clientWidth,n.clientWidth)-i),{width:i,height:o,x:a,y:s}}(L(e));else if(I(t))n=function(e,t){let r=er(e,!0,"fixed"===t),n=r.top+e.clientTop,i=r.left+e.clientLeft,o=U(e)?Z(e):h(1),a=e.clientWidth*o.x,s=e.clientHeight*o.y;return{width:a,height:s,x:i*o.x,y:n*o.y}}(t,r);else{let r=et(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return S(n)}function ea(e){return"static"===z(e).position}function es(e,t){if(!U(e)||"fixed"===z(e).position)return null;if(t)return t(e);let r=e.offsetParent;return L(e)===r&&(r=r.ownerDocument.body),r}function el(e,t){let r=N(e);if($(e))return r;if(!U(e)){let t=K(e);for(;t&&!G(t);){if(I(t)&&!ea(t))return t;t=K(t)}return r}let n=es(e,t);for(;n&&["table","td","th"].includes(D(n))&&ea(n);)n=es(n,t);return n&&G(n)&&ea(n)&&!H(n)?r:n||function(e){let t=K(e);for(;U(t)&&!G(t);){if(H(t))return t;if($(t))break;t=K(t)}return null}(e)||r}let eu=async function(e){let t=this.getOffsetParent||el,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=U(t),i=L(t),o="fixed"===r,a=er(e,!0,o,t),s={scrollLeft:0,scrollTop:0},l=h(0);if(n||!n&&!o)if(("body"!==D(t)||V(i))&&(s=q(t)),n){let e=er(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=en(i));o&&!n&&i&&(l.x=en(i));let u=!i||n||o?h(0):ei(i,s);return{x:a.left+s.scrollLeft-l.x-u.x,y:a.top+s.scrollTop-l.y-u.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},ec={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:i}=e,o="fixed"===i,a=L(n),s=!!t&&$(t.floating);if(n===a||s&&o)return r;let l={scrollLeft:0,scrollTop:0},u=h(1),c=h(0),d=U(n);if((d||!d&&!o)&&(("body"!==D(n)||V(a))&&(l=q(n)),U(n))){let e=er(n);u=Z(n),c.x=e.x+n.clientLeft,c.y=e.y+n.clientTop}let f=!a||d||o?h(0):ei(a,l,!0);return{width:r.width*u.x,height:r.height*u.y,x:r.x*u.x-l.scrollLeft*u.x+c.x+f.x,y:r.y*u.y-l.scrollTop*u.y+c.y+f.y}},getDocumentElement:L,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:i}=e,o=[..."clippingAncestors"===r?$(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=X(e,[],!1).filter(e=>I(e)&&"body"!==D(e)),i=null,o="fixed"===z(e).position,a=o?K(e):e;for(;I(a)&&!G(a);){let t=z(a),r=H(a);r||"fixed"!==t.position||(i=null),(o?!r&&!i:!r&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||V(a)&&!r&&function e(t,r){let n=K(t);return!(n===r||!I(n)||G(n))&&("fixed"===z(n).position||e(n,r))}(e,a))?n=n.filter(e=>e!==a):i=t,a=K(a)}return t.set(e,n),n}(t,this._c):[].concat(r),n],a=o[0],s=o.reduce((e,r)=>{let n=eo(t,r,i);return e.top=d(n.top,e.top),e.right=c(n.right,e.right),e.bottom=c(n.bottom,e.bottom),e.left=d(n.left,e.left),e},eo(t,a,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:el,getElementRects:eu,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=Q(e);return{width:t,height:r}},getScale:Z,isElement:I,isRTL:function(e){return"rtl"===z(e).direction}};function ed(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ef=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:i,rects:o,platform:a,elements:s,middlewareData:l}=t,{element:u,padding:f=0}=g(e,t)||{};if(null==u)return{};let p=R(f),h={x:r,y:n},m=w(E(i)),y=x(m),v=await a.getDimensions(u),_="y"===m,P=_?"clientHeight":"clientWidth",S=o.reference[y]+o.reference[m]-h[m]-o.floating[y],O=h[m]-o.reference[m],T=await (null==a.getOffsetParent?void 0:a.getOffsetParent(u)),j=T?T[P]:0;j&&await (null==a.isElement?void 0:a.isElement(T))||(j=s.floating[P]||o.floating[y]);let M=j/2-v[y]/2-1,A=c(p[_?"top":"left"],M),C=c(p[_?"bottom":"right"],M),k=j-v[y]-C,D=j/2-v[y]/2+(S/2-O/2),N=d(A,c(D,k)),L=!l.arrow&&null!=b(i)&&D!==N&&o.reference[y]/2-(D<A?A:C)-v[y]/2<0,F=L?D<A?D-A:D-k:0;return{[m]:h[m]+F,data:{[m]:N,centerOffset:D-N-F,...L&&{alignmentOffset:F}},reset:L}}}),ep=(e,t,r)=>{let n=new Map,i={platform:ec,...r},o={...i.platform,_c:n};return T(e,t,{...i,platform:o})};var eh=r(51215),em="undefined"!=typeof document?n.useLayoutEffect:n.useEffect;function ey(e,t){let r,n,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!ey(e[n],t[n]))return!1;return!0}if((r=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,i[n]))return!1;for(n=r;0!=n--;){let r=i[n];if(("_owner"!==r||!e.$$typeof)&&!ey(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eg(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ev(e,t){let r=eg(e);return Math.round(t*r)/r}function eb(e){let t=n.useRef(e);return em(()=>{t.current=e}),t}let ew=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ef({element:r.current,padding:n}).fn(t):{}:r?ef({element:r,padding:n}).fn(t):{}}}),ex=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:i,y:o,placement:a,middlewareData:s}=t,l=await C(t,e);return a===(null==(r=s.offset)?void 0:r.placement)&&null!=(n=s.arrow)&&n.alignmentOffset?{}:{x:i+l.x,y:o+l.y,data:{...l,placement:a}}}}}(e),options:[e,t]}),eE=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:i}=t,{mainAxis:o=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...l}=g(e,t),u={x:r,y:n},f=await j(t,l),p=E(v(i)),h=w(p),m=u[h],y=u[p];if(o){let e="y"===h?"top":"left",t="y"===h?"bottom":"right",r=m+f[e],n=m-f[t];m=d(r,c(m,n))}if(a){let e="y"===p?"top":"left",t="y"===p?"bottom":"right",r=y+f[e],n=y-f[t];y=d(r,c(y,n))}let b=s.fn({...t,[h]:m,[p]:y});return{...b,data:{x:b.x-r,y:b.y-n,enabled:{[h]:o,[p]:a}}}}}}(e),options:[e,t]}),e_=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:i,rects:o,middlewareData:a}=t,{offset:s=0,mainAxis:l=!0,crossAxis:u=!0}=g(e,t),c={x:r,y:n},d=E(i),f=w(d),p=c[f],h=c[d],m=g(s,t),y="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(l){let e="y"===f?"height":"width",t=o.reference[f]-o.floating[e]+y.mainAxis,r=o.reference[f]+o.reference[e]-y.mainAxis;p<t?p=t:p>r&&(p=r)}if(u){var b,x;let e="y"===f?"width":"height",t=["top","left"].includes(v(i)),r=o.reference[d]-o.floating[e]+(t&&(null==(b=a.offset)?void 0:b[d])||0)+(t?0:y.crossAxis),n=o.reference[d]+o.reference[e]+(t?0:(null==(x=a.offset)?void 0:x[d])||0)-(t?y.crossAxis:0);h<r?h=r:h>n&&(h=n)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,i,o,a,s;let{placement:l,middlewareData:u,rects:c,initialPlacement:d,platform:f,elements:p}=t,{mainAxis:h=!0,crossAxis:m=!0,fallbackPlacements:y,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:O=!0,...T}=g(e,t);if(null!=(r=u.arrow)&&r.alignmentOffset)return{};let M=v(l),A=E(d),C=v(d)===d,k=await (null==f.isRTL?void 0:f.isRTL(p.floating)),D=y||(C||!O?[P(d)]:function(e){let t=P(e);return[_(e),t,_(t)]}(d)),N="none"!==S;!y&&N&&D.push(...function(e,t,r,n){let i=b(e),o=function(e,t,r){let n=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(r)return t?i:n;return t?n:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(v(e),"start"===r,n);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(_)))),o}(d,O,S,k));let L=[d,...D],F=await j(t,T),I=[],U=(null==(n=u.flip)?void 0:n.overflows)||[];if(h&&I.push(F[M]),m){let e=function(e,t,r){void 0===r&&(r=!1);let n=b(e),i=w(E(e)),o=x(i),a="x"===i?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[o]>t.floating[o]&&(a=P(a)),[a,P(a)]}(l,c,k);I.push(F[e[0]],F[e[1]])}if(U=[...U,{placement:l,overflows:I}],!I.every(e=>e<=0)){let e=((null==(i=u.flip)?void 0:i.index)||0)+1,t=L[e];if(t){let r="alignment"===m&&A!==E(t),n=(null==(a=U[0])?void 0:a.overflows[0])>0;if(!r||n)return{data:{index:e,overflows:U},reset:{placement:t}}}let r=null==(o=U.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!r)switch(R){case"bestFit":{let e=null==(s=U.filter(e=>{if(N){let t=E(e.placement);return t===A||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:s[0];e&&(r=e);break}case"initialPlacement":r=d}if(l!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),eR=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let i,o,{placement:a,rects:s,platform:l,elements:u}=t,{apply:f=()=>{},...p}=g(e,t),h=await j(t,p),m=v(a),y=b(a),w="y"===E(a),{width:x,height:_}=s.floating;"top"===m||"bottom"===m?(i=m,o=y===(await (null==l.isRTL?void 0:l.isRTL(u.floating))?"start":"end")?"left":"right"):(o=m,i="end"===y?"top":"bottom");let P=_-h.top-h.bottom,R=x-h.left-h.right,S=c(_-h[i],P),O=c(x-h[o],R),T=!t.middlewareData.shift,M=S,A=O;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(A=R),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(M=P),T&&!y){let e=d(h.left,0),t=d(h.right,0),r=d(h.top,0),n=d(h.bottom,0);w?A=x-2*(0!==e||0!==t?e+t:d(h.left,h.right)):M=_-2*(0!==r||0!==n?r+n:d(h.top,h.bottom))}await f({...t,availableWidth:A,availableHeight:M});let C=await l.getDimensions(u.floating);return x!==C.width||_!==C.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eS=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...i}=g(e,t);switch(n){case"referenceHidden":{let e=M(await j(t,{...i,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:A(e)}}}case"escaped":{let e=M(await j(t,{...i,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:A(e)}}}default:return{}}}}}(e),options:[e,t]}),eO=(e,t)=>({...ew(e),options:[e,t]});var eT=r(14163),ej=r(60687),eM=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:i=5,...o}=e;return(0,ej.jsx)(eT.sG.svg,{...o,ref:t,width:n,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,ej.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eM.displayName="Arrow";var eA=r(13495),eC=r(66156),ek="Popper",[eD,eN]=(0,a.A)(ek),[eL,eF]=eD(ek),eI=e=>{let{__scopePopper:t,children:r}=e,[i,o]=n.useState(null);return(0,ej.jsx)(eL,{scope:t,anchor:i,onAnchorChange:o,children:r})};eI.displayName=ek;var eU="PopperAnchor",eB=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:i,...a}=e,s=eF(eU,r),l=n.useRef(null),u=(0,o.s)(t,l);return n.useEffect(()=>{s.onAnchorChange(i?.current||l.current)}),i?null:(0,ej.jsx)(eT.sG.div,{...a,ref:u})});eB.displayName=eU;var eV="PopperContent",[e$,eH]=eD(eV),eW=n.forwardRef((e,t)=>{let{__scopePopper:r,side:i="bottom",sideOffset:a=0,align:s="center",alignOffset:l=0,arrowPadding:u=0,avoidCollisions:f=!0,collisionBoundary:h=[],collisionPadding:m=0,sticky:y="partial",hideWhenDetached:g=!1,updatePositionStrategy:v="optimized",onPlaced:b,...w}=e,x=eF(eV,r),[E,_]=n.useState(null),P=(0,o.s)(t,e=>_(e)),[R,S]=n.useState(null),O=function(e){let[t,r]=n.useState(void 0);return(0,eC.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,i=t.blockSize}else n=e.offsetWidth,i=e.offsetHeight;r({width:n,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(R),T=O?.width??0,j=O?.height??0,M="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},A=Array.isArray(h)?h:[h],C=A.length>0,k={padding:M,boundary:A.filter(eK),altBoundary:C},{refs:D,floatingStyles:N,placement:F,isPositioned:I,middlewareData:U}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:i=[],platform:o,elements:{reference:a,floating:s}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,f]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=n.useState(i);ey(p,i)||h(i);let[m,y]=n.useState(null),[g,v]=n.useState(null),b=n.useCallback(e=>{e!==_.current&&(_.current=e,y(e))},[]),w=n.useCallback(e=>{e!==P.current&&(P.current=e,v(e))},[]),x=a||m,E=s||g,_=n.useRef(null),P=n.useRef(null),R=n.useRef(d),S=null!=u,O=eb(u),T=eb(o),j=eb(c),M=n.useCallback(()=>{if(!_.current||!P.current)return;let e={placement:t,strategy:r,middleware:p};T.current&&(e.platform=T.current),ep(_.current,P.current,e).then(e=>{let t={...e,isPositioned:!1!==j.current};A.current&&!ey(R.current,t)&&(R.current=t,eh.flushSync(()=>{f(t)}))})},[p,t,r,T,j]);em(()=>{!1===c&&R.current.isPositioned&&(R.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let A=n.useRef(!1);em(()=>(A.current=!0,()=>{A.current=!1}),[]),em(()=>{if(x&&(_.current=x),E&&(P.current=E),x&&E){if(O.current)return O.current(x,E,M);M()}},[x,E,M,O,S]);let C=n.useMemo(()=>({reference:_,floating:P,setReference:b,setFloating:w}),[b,w]),k=n.useMemo(()=>({reference:x,floating:E}),[x,E]),D=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!k.floating)return e;let t=ev(k.floating,d.x),n=ev(k.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...eg(k.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,l,k.floating,d.x,d.y]);return n.useMemo(()=>({...d,update:M,refs:C,elements:k,floatingStyles:D}),[d,M,C,k,D])}({strategy:"fixed",placement:i+("center"!==s?"-"+s:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let i;void 0===n&&(n={});let{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:u=!1}=n,f=J(e),h=o||a?[...f?X(f):[],...X(t)]:[];h.forEach(e=>{o&&e.addEventListener("scroll",r,{passive:!0}),a&&e.addEventListener("resize",r)});let m=f&&l?function(e,t){let r,n=null,i=L(e);function o(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function a(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),o();let u=e.getBoundingClientRect(),{left:f,top:h,width:m,height:y}=u;if(s||t(),!m||!y)return;let g=p(h),v=p(i.clientWidth-(f+m)),b={rootMargin:-g+"px "+-v+"px "+-p(i.clientHeight-(h+y))+"px "+-p(f)+"px",threshold:d(0,c(1,l))||1},w=!0;function x(t){let n=t[0].intersectionRatio;if(n!==l){if(!w)return a();n?a(!1,n):r=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==n||ed(u,e.getBoundingClientRect())||a(),w=!1}try{n=new IntersectionObserver(x,{...b,root:i.ownerDocument})}catch(e){n=new IntersectionObserver(x,b)}n.observe(e)}(!0),o}(f,r):null,y=-1,g=null;s&&(g=new ResizeObserver(e=>{let[n]=e;n&&n.target===f&&g&&(g.unobserve(t),cancelAnimationFrame(y),y=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),r()}),f&&!u&&g.observe(f),g.observe(t));let v=u?er(e):null;return u&&function t(){let n=er(e);v&&!ed(v,n)&&r(),v=n,i=requestAnimationFrame(t)}(),r(),()=>{var e;h.forEach(e=>{o&&e.removeEventListener("scroll",r),a&&e.removeEventListener("resize",r)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,u&&cancelAnimationFrame(i)}})(...e,{animationFrame:"always"===v}),elements:{reference:x.anchor},middleware:[ex({mainAxis:a+j,alignmentAxis:l}),f&&eE({mainAxis:!0,crossAxis:!1,limiter:"partial"===y?e_():void 0,...k}),f&&eP({...k}),eR({...k,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:i,height:o}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${r}px`),a.setProperty("--radix-popper-available-height",`${n}px`),a.setProperty("--radix-popper-anchor-width",`${i}px`),a.setProperty("--radix-popper-anchor-height",`${o}px`)}}),R&&eO({element:R,padding:u}),eX({arrowWidth:T,arrowHeight:j}),g&&eS({strategy:"referenceHidden",...k})]}),[B,V]=eY(F),$=(0,eA.c)(b);(0,eC.N)(()=>{I&&$?.()},[I,$]);let H=U.arrow?.x,W=U.arrow?.y,G=U.arrow?.centerOffset!==0,[z,q]=n.useState();return(0,eC.N)(()=>{E&&q(window.getComputedStyle(E).zIndex)},[E]),(0,ej.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...N,transform:I?N.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:z,"--radix-popper-transform-origin":[U.transformOrigin?.x,U.transformOrigin?.y].join(" "),...U.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,ej.jsx)(e$,{scope:r,placedSide:B,onArrowChange:S,arrowX:H,arrowY:W,shouldHideArrow:G,children:(0,ej.jsx)(eT.sG.div,{"data-side":B,"data-align":V,...w,ref:P,style:{...w.style,animation:I?void 0:"none"}})})})});eW.displayName=eV;var eG="PopperArrow",ez={top:"bottom",right:"left",bottom:"top",left:"right"},eq=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,i=eH(eG,r),o=ez[i.placedSide];return(0,ej.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,ej.jsx)(eM,{...n,ref:t,style:{...n.style,display:"block"}})})});function eK(e){return null!==e}eq.displayName=eG;var eX=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:i}=t,o=i.arrow?.centerOffset!==0,a=o?0:e.arrowWidth,s=o?0:e.arrowHeight,[l,u]=eY(r),c={start:"0%",center:"50%",end:"100%"}[u],d=(i.arrow?.x??0)+a/2,f=(i.arrow?.y??0)+s/2,p="",h="";return"bottom"===l?(p=o?c:`${d}px`,h=`${-s}px`):"top"===l?(p=o?c:`${d}px`,h=`${n.floating.height+s}px`):"right"===l?(p=`${-s}px`,h=o?c:`${f}px`):"left"===l&&(p=`${n.floating.width+s}px`,h=o?c:`${f}px`),{data:{x:p,y:h}}}});function eY(e){let[t,r="center"]=e.split("-");return[t,r]}r(25028);var eQ=r(46059),eJ=r(8730),eZ=r(65551),e0=r(69024),[e1,e2]=(0,a.A)("Tooltip",[eN]),e3=eN(),e4="TooltipProvider",e5="tooltip.open",[e9,e7]=e1(e4),e6=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:i=300,disableHoverableContent:o=!1,children:a}=e,s=n.useRef(!0),l=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,ej.jsx)(e9,{scope:t,isOpenDelayedRef:s,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),s.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>s.current=!0,i)},[i]),isPointerInTransitRef:l,onPointerInTransitChange:n.useCallback(e=>{l.current=e},[]),disableHoverableContent:o,children:a})};e6.displayName=e4;var e8="Tooltip",[te,tt]=e1(e8),tr=e=>{let{__scopeTooltip:t,children:r,open:i,defaultOpen:o,onOpenChange:a,disableHoverableContent:s,delayDuration:u}=e,c=e7(e8,e.__scopeTooltip),d=e3(t),[f,p]=n.useState(null),h=(0,l.B)(),m=n.useRef(0),y=s??c.disableHoverableContent,g=u??c.delayDuration,v=n.useRef(!1),[b,w]=(0,eZ.i)({prop:i,defaultProp:o??!1,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(e5))):c.onClose(),a?.(e)},caller:e8}),x=n.useMemo(()=>b?v.current?"delayed-open":"instant-open":"closed",[b]),E=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,v.current=!1,w(!0)},[w]),_=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,w(!1)},[w]),P=n.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{v.current=!0,w(!0),m.current=0},g)},[g,w]);return n.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,ej.jsx)(eI,{...d,children:(0,ej.jsx)(te,{scope:t,contentId:h,open:b,stateAttribute:x,trigger:f,onTriggerChange:p,onTriggerEnter:n.useCallback(()=>{c.isOpenDelayedRef.current?P():E()},[c.isOpenDelayedRef,P,E]),onTriggerLeave:n.useCallback(()=>{y?_():(window.clearTimeout(m.current),m.current=0)},[_,y]),onOpen:E,onClose:_,disableHoverableContent:y,children:r})})};tr.displayName=e8;var tn="TooltipTrigger",ti=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...a}=e,s=tt(tn,r),l=e7(tn,r),u=e3(r),c=n.useRef(null),d=(0,o.s)(t,c,s.onTriggerChange),f=n.useRef(!1),p=n.useRef(!1),h=n.useCallback(()=>f.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),(0,ej.jsx)(eB,{asChild:!0,...u,children:(0,ej.jsx)(eT.sG.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...a,ref:d,onPointerMove:(0,i.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(p.current||l.isPointerInTransitRef.current||(s.onTriggerEnter(),p.current=!0))}),onPointerLeave:(0,i.m)(e.onPointerLeave,()=>{s.onTriggerLeave(),p.current=!1}),onPointerDown:(0,i.m)(e.onPointerDown,()=>{s.open&&s.onClose(),f.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:(0,i.m)(e.onFocus,()=>{f.current||s.onOpen()}),onBlur:(0,i.m)(e.onBlur,s.onClose),onClick:(0,i.m)(e.onClick,s.onClose)})})});ti.displayName=tn;var[to,ta]=e1("TooltipPortal",{forceMount:void 0}),ts="TooltipContent",tl=n.forwardRef((e,t)=>{let r=ta(ts,e.__scopeTooltip),{forceMount:n=r.forceMount,side:i="top",...o}=e,a=tt(ts,e.__scopeTooltip);return(0,ej.jsx)(eQ.C,{present:n||a.open,children:a.disableHoverableContent?(0,ej.jsx)(tp,{side:i,...o,ref:t}):(0,ej.jsx)(tu,{side:i,...o,ref:t})})}),tu=n.forwardRef((e,t)=>{let r=tt(ts,e.__scopeTooltip),i=e7(ts,e.__scopeTooltip),a=n.useRef(null),s=(0,o.s)(t,a),[l,u]=n.useState(null),{trigger:c,onClose:d}=r,f=a.current,{onPointerInTransitChange:p}=i,h=n.useCallback(()=>{u(null),p(!1)},[p]),m=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},i=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(r,n,i,o)){case o:return"left";case i:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,i),...function(e){let{top:t,right:r,bottom:n,left:i}=e;return[{x:i,y:t},{x:r,y:t},{x:r,y:n},{x:i,y:n}]}(t.getBoundingClientRect())])),p(!0)},[p]);return n.useEffect(()=>()=>h(),[h]),n.useEffect(()=>{if(c&&f){let e=e=>m(e,f),t=e=>m(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,m,h]),n.useEffect(()=>{if(l){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=c?.contains(t)||f?.contains(t),i=!function(e,t){let{x:r,y:n}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let a=t[e],s=t[o],l=a.x,u=a.y,c=s.x,d=s.y;u>n!=d>n&&r<(c-l)*(n-u)/(d-u)+l&&(i=!i)}return i}(r,l);n?h():i&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,l,d,h]),(0,ej.jsx)(tp,{...e,ref:s})}),[tc,td]=e1(e8,{isInside:!1}),tf=(0,eJ.Dc)("TooltipContent"),tp=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:i,"aria-label":o,onEscapeKeyDown:a,onPointerDownOutside:l,...u}=e,c=tt(ts,r),d=e3(r),{onClose:f}=c;return n.useEffect(()=>(document.addEventListener(e5,f),()=>document.removeEventListener(e5,f)),[f]),n.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;t?.contains(c.trigger)&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,f]),(0,ej.jsx)(s.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,ej.jsxs)(eW,{"data-state":c.stateAttribute,...d,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,ej.jsx)(tf,{children:i}),(0,ej.jsx)(tc,{scope:r,isInside:!0,children:(0,ej.jsx)(e0.bL,{id:c.contentId,role:"tooltip",children:o||i})})]})})});tl.displayName=ts;var th="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,i=e3(r);return td(th,r).isInside?null:(0,ej.jsx)(eq,{...i,...n,ref:t})}).displayName=th;var tm=e6,ty=tr,tg=ti,tv=tl},20884:(e,t,r)=>{"use strict";var n=r(46033),i={stream:!0},o=new Map;function a(e){var t=globalThis.__next_require__(e);return"function"!=typeof t.then||"fulfilled"===t.status?null:(t.then(function(e){t.status="fulfilled",t.value=e},function(e){t.status="rejected",t.reason=e}),t)}function s(){}function l(e){for(var t=e[1],n=[],i=0;i<t.length;){var l=t[i++];t[i++];var u=o.get(l);if(void 0===u){u=r.e(l),n.push(u);var c=o.set.bind(o,l,null);u.then(c,s),o.set(l,u)}else null!==u&&n.push(u)}return 4===e.length?0===n.length?a(e[0]):Promise.all(n).then(function(){return a(e[0])}):0<n.length?Promise.all(n):null}function u(e){var t=globalThis.__next_require__(e[0]);if(4===e.length&&"function"==typeof t.then)if("fulfilled"===t.status)t=t.value;else throw t.reason;return"*"===e[2]?t:""===e[2]?t.__esModule?t.default:t:t[e[2]]}var c=n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,d=Symbol.for("react.transitional.element"),f=Symbol.for("react.lazy"),p=Symbol.iterator,h=Symbol.asyncIterator,m=Array.isArray,y=Object.getPrototypeOf,g=Object.prototype,v=new WeakMap;function b(e,t,r,n,i){function o(e,r){r=new Blob([new Uint8Array(r.buffer,r.byteOffset,r.byteLength)]);var n=l++;return null===c&&(c=new FormData),c.append(t+n,r),"$"+e+n.toString(16)}function a(e,x){if(null===x)return null;if("object"==typeof x){switch(x.$$typeof){case d:if(void 0!==r&&-1===e.indexOf(":")){var E,_,P,R,S,O=b.get(this);if(void 0!==O)return r.set(O+":"+e,x),"$T"}throw Error("React Element cannot be passed to Server Functions from the Client without a temporary reference set. Pass a TemporaryReferenceSet to the options.");case f:O=x._payload;var T=x._init;null===c&&(c=new FormData),u++;try{var j=T(O),M=l++,A=s(j,M);return c.append(t+M,A),"$"+M.toString(16)}catch(e){if("object"==typeof e&&null!==e&&"function"==typeof e.then){u++;var C=l++;return O=function(){try{var e=s(x,C),r=c;r.append(t+C,e),u--,0===u&&n(r)}catch(e){i(e)}},e.then(O,O),"$"+C.toString(16)}return i(e),null}finally{u--}}if("function"==typeof x.then){null===c&&(c=new FormData),u++;var k=l++;return x.then(function(e){try{var r=s(e,k);(e=c).append(t+k,r),u--,0===u&&n(e)}catch(e){i(e)}},i),"$@"+k.toString(16)}if(void 0!==(O=b.get(x)))if(w!==x)return O;else w=null;else -1===e.indexOf(":")&&void 0!==(O=b.get(this))&&(e=O+":"+e,b.set(x,e),void 0!==r&&r.set(e,x));if(m(x))return x;if(x instanceof FormData){null===c&&(c=new FormData);var D=c,N=t+(e=l++)+"_";return x.forEach(function(e,t){D.append(N+t,e)}),"$K"+e.toString(16)}if(x instanceof Map)return e=l++,O=s(Array.from(x),e),null===c&&(c=new FormData),c.append(t+e,O),"$Q"+e.toString(16);if(x instanceof Set)return e=l++,O=s(Array.from(x),e),null===c&&(c=new FormData),c.append(t+e,O),"$W"+e.toString(16);if(x instanceof ArrayBuffer)return e=new Blob([x]),O=l++,null===c&&(c=new FormData),c.append(t+O,e),"$A"+O.toString(16);if(x instanceof Int8Array)return o("O",x);if(x instanceof Uint8Array)return o("o",x);if(x instanceof Uint8ClampedArray)return o("U",x);if(x instanceof Int16Array)return o("S",x);if(x instanceof Uint16Array)return o("s",x);if(x instanceof Int32Array)return o("L",x);if(x instanceof Uint32Array)return o("l",x);if(x instanceof Float32Array)return o("G",x);if(x instanceof Float64Array)return o("g",x);if(x instanceof BigInt64Array)return o("M",x);if(x instanceof BigUint64Array)return o("m",x);if(x instanceof DataView)return o("V",x);if("function"==typeof Blob&&x instanceof Blob)return null===c&&(c=new FormData),e=l++,c.append(t+e,x),"$B"+e.toString(16);if(e=null===(E=x)||"object"!=typeof E?null:"function"==typeof(E=p&&E[p]||E["@@iterator"])?E:null)return(O=e.call(x))===x?(e=l++,O=s(Array.from(O),e),null===c&&(c=new FormData),c.append(t+e,O),"$i"+e.toString(16)):Array.from(O);if("function"==typeof ReadableStream&&x instanceof ReadableStream)return function(e){try{var r,o,s,d,f,p,h,m=e.getReader({mode:"byob"})}catch(d){return r=e.getReader(),null===c&&(c=new FormData),o=c,u++,s=l++,r.read().then(function e(l){if(l.done)o.append(t+s,"C"),0==--u&&n(o);else try{var c=JSON.stringify(l.value,a);o.append(t+s,c),r.read().then(e,i)}catch(e){i(e)}},i),"$R"+s.toString(16)}return d=m,null===c&&(c=new FormData),f=c,u++,p=l++,h=[],d.read(new Uint8Array(1024)).then(function e(r){r.done?(r=l++,f.append(t+r,new Blob(h)),f.append(t+p,'"$o'+r.toString(16)+'"'),f.append(t+p,"C"),0==--u&&n(f)):(h.push(r.value),d.read(new Uint8Array(1024)).then(e,i))},i),"$r"+p.toString(16)}(x);if("function"==typeof(e=x[h]))return _=x,P=e.call(x),null===c&&(c=new FormData),R=c,u++,S=l++,_=_===P,P.next().then(function e(r){if(r.done){if(void 0===r.value)R.append(t+S,"C");else try{var o=JSON.stringify(r.value,a);R.append(t+S,"C"+o)}catch(e){i(e);return}0==--u&&n(R)}else try{var s=JSON.stringify(r.value,a);R.append(t+S,s),P.next().then(e,i)}catch(e){i(e)}},i),"$"+(_?"x":"X")+S.toString(16);if((e=y(x))!==g&&(null===e||null!==y(e))){if(void 0===r)throw Error("Only plain objects, and a few built-ins, can be passed to Server Functions. Classes or null prototypes are not supported.");return"$T"}return x}if("string"==typeof x)return"Z"===x[x.length-1]&&this[e]instanceof Date?"$D"+x:e="$"===x[0]?"$"+x:x;if("boolean"==typeof x)return x;if("number"==typeof x)return Number.isFinite(x)?0===x&&-1/0==1/x?"$-0":x:1/0===x?"$Infinity":-1/0===x?"$-Infinity":"$NaN";if(void 0===x)return"$undefined";if("function"==typeof x){if(void 0!==(O=v.get(x)))return e=JSON.stringify({id:O.id,bound:O.bound},a),null===c&&(c=new FormData),O=l++,c.set(t+O,e),"$F"+O.toString(16);if(void 0!==r&&-1===e.indexOf(":")&&void 0!==(O=b.get(this)))return r.set(O+":"+e,x),"$T";throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.")}if("symbol"==typeof x){if(void 0!==r&&-1===e.indexOf(":")&&void 0!==(O=b.get(this)))return r.set(O+":"+e,x),"$T";throw Error("Symbols cannot be passed to a Server Function without a temporary reference set. Pass a TemporaryReferenceSet to the options.")}if("bigint"==typeof x)return"$n"+x.toString(10);throw Error("Type "+typeof x+" is not supported as an argument to a Server Function.")}function s(e,t){return"object"==typeof e&&null!==e&&(t="$"+t.toString(16),b.set(e,t),void 0!==r&&r.set(t,e)),w=e,JSON.stringify(e,a)}var l=1,u=0,c=null,b=new WeakMap,w=e,x=s(e,0);return null===c?n(x):(c.set(t+"0",x),0===u&&n(c)),function(){0<u&&(u=0,null===c?n(x):n(c))}}var w=new WeakMap;function x(e){var t=v.get(this);if(!t)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var r=null;if(null!==t.bound){if((r=w.get(t))||(n={id:t.id,bound:t.bound},a=new Promise(function(e,t){i=e,o=t}),b(n,"",void 0,function(e){if("string"==typeof e){var t=new FormData;t.append("0",e),e=t}a.status="fulfilled",a.value=e,i(e)},function(e){a.status="rejected",a.reason=e,o(e)}),r=a,w.set(t,r)),"rejected"===r.status)throw r.reason;if("fulfilled"!==r.status)throw r;t=r.value;var n,i,o,a,s=new FormData;t.forEach(function(t,r){s.append("$ACTION_"+e+":"+r,t)}),r=s,t="$ACTION_REF_"+e}else t="$ACTION_ID_"+t.id;return{name:t,method:"POST",encType:"multipart/form-data",data:r}}function E(e,t){var r=v.get(this);if(!r)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(r.id!==e)return!1;var n=r.bound;if(null===n)return 0===t;switch(n.status){case"fulfilled":return n.value.length===t;case"pending":throw n;case"rejected":throw n.reason;default:throw"string"!=typeof n.status&&(n.status="pending",n.then(function(e){n.status="fulfilled",n.value=e},function(e){n.status="rejected",n.reason=e})),n}}function _(e,t,r,n){v.has(e)||(v.set(e,{id:t,originalBind:e.bind,bound:r}),Object.defineProperties(e,{$$FORM_ACTION:{value:void 0===n?x:function(){var e=v.get(this);if(!e)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var t=e.bound;return null===t&&(t=Promise.resolve([])),n(e.id,t)}},$$IS_SIGNATURE_EQUAL:{value:E},bind:{value:S}}))}var P=Function.prototype.bind,R=Array.prototype.slice;function S(){var e=v.get(this);if(!e)return P.apply(this,arguments);var t=e.originalBind.apply(this,arguments),r=R.call(arguments,1),n=null;return n=null!==e.bound?Promise.resolve(e.bound).then(function(e){return e.concat(r)}):Promise.resolve(r),v.set(t,{id:e.id,originalBind:t.bind,bound:n}),Object.defineProperties(t,{$$FORM_ACTION:{value:this.$$FORM_ACTION},$$IS_SIGNATURE_EQUAL:{value:E},bind:{value:S}}),t}function O(e,t,r,n){this.status=e,this.value=t,this.reason=r,this._response=n}function T(e){switch(e.status){case"resolved_model":I(e);break;case"resolved_module":U(e)}switch(e.status){case"fulfilled":return e.value;case"pending":case"blocked":throw e;default:throw e.reason}}function j(e){return new O("pending",null,null,e)}function M(e,t){for(var r=0;r<e.length;r++)(0,e[r])(t)}function A(e,t,r){switch(e.status){case"fulfilled":M(t,e.value);break;case"pending":case"blocked":if(e.value)for(var n=0;n<t.length;n++)e.value.push(t[n]);else e.value=t;if(e.reason){if(r)for(t=0;t<r.length;t++)e.reason.push(r[t])}else e.reason=r;break;case"rejected":r&&M(r,e.reason)}}function C(e,t){if("pending"!==e.status&&"blocked"!==e.status)e.reason.error(t);else{var r=e.reason;e.status="rejected",e.reason=t,null!==r&&M(r,t)}}function k(e,t,r){return new O("resolved_model",(r?'{"done":true,"value":':'{"done":false,"value":')+t+"}",null,e)}function D(e,t,r){N(e,(r?'{"done":true,"value":':'{"done":false,"value":')+t+"}")}function N(e,t){if("pending"!==e.status)e.reason.enqueueModel(t);else{var r=e.value,n=e.reason;e.status="resolved_model",e.value=t,null!==r&&(I(e),A(e,r,n))}}function L(e,t){if("pending"===e.status||"blocked"===e.status){var r=e.value,n=e.reason;e.status="resolved_module",e.value=t,null!==r&&(U(e),A(e,r,n))}}O.prototype=Object.create(Promise.prototype),O.prototype.then=function(e,t){switch(this.status){case"resolved_model":I(this);break;case"resolved_module":U(this)}switch(this.status){case"fulfilled":e(this.value);break;case"pending":case"blocked":e&&(null===this.value&&(this.value=[]),this.value.push(e)),t&&(null===this.reason&&(this.reason=[]),this.reason.push(t));break;default:t&&t(this.reason)}};var F=null;function I(e){var t=F;F=null;var r=e.value;e.status="blocked",e.value=null,e.reason=null;try{var n=JSON.parse(r,e._response._fromJSON),i=e.value;if(null!==i&&(e.value=null,e.reason=null,M(i,n)),null!==F){if(F.errored)throw F.value;if(0<F.deps){F.value=n,F.chunk=e;return}}e.status="fulfilled",e.value=n}catch(t){e.status="rejected",e.reason=t}finally{F=t}}function U(e){try{var t=u(e.value);e.status="fulfilled",e.value=t}catch(t){e.status="rejected",e.reason=t}}function B(e,t){e._closed=!0,e._closedReason=t,e._chunks.forEach(function(e){"pending"===e.status&&C(e,t)})}function V(e){return{$$typeof:f,_payload:e,_init:T}}function $(e,t){var r=e._chunks,n=r.get(t);return n||(n=e._closed?new O("rejected",null,e._closedReason,e):j(e),r.set(t,n)),n}function H(e,t,r,n,i,o){function a(e){if(!s.errored){s.errored=!0,s.value=e;var t=s.chunk;null!==t&&"blocked"===t.status&&C(t,e)}}if(F){var s=F;s.deps++}else s=F={parent:null,chunk:null,value:null,deps:1,errored:!1};return e.then(function e(l){for(var u=1;u<o.length;u++){for(;l.$$typeof===f;)if((l=l._payload)===s.chunk)l=s.value;else if("fulfilled"===l.status)l=l.value;else{o.splice(0,u-1),l.then(e,a);return}l=l[o[u]]}u=i(n,l,t,r),t[r]=u,""===r&&null===s.value&&(s.value=u),t[0]===d&&"object"==typeof s.value&&null!==s.value&&s.value.$$typeof===d&&(l=s.value,"3"===r)&&(l.props=u),s.deps--,0===s.deps&&null!==(u=s.chunk)&&"blocked"===u.status&&(l=u.value,u.status="fulfilled",u.value=s.value,null!==l&&M(l,s.value))},a),null}function W(e,t,r,n){if(!e._serverReferenceConfig)return function(e,t,r){function n(){var e=Array.prototype.slice.call(arguments);return o?"fulfilled"===o.status?t(i,o.value.concat(e)):Promise.resolve(o).then(function(r){return t(i,r.concat(e))}):t(i,e)}var i=e.id,o=e.bound;return _(n,i,o,r),n}(t,e._callServer,e._encodeFormAction);var i=function(e,t){var r="",n=e[t];if(n)r=n.name;else{var i=t.lastIndexOf("#");if(-1!==i&&(r=t.slice(i+1),n=e[t.slice(0,i)]),!n)throw Error('Could not find the module "'+t+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.')}return n.async?[n.id,n.chunks,r,1]:[n.id,n.chunks,r]}(e._serverReferenceConfig,t.id),o=l(i);if(o)t.bound&&(o=Promise.all([o,t.bound]));else{if(!t.bound)return _(o=u(i),t.id,t.bound,e._encodeFormAction),o;o=Promise.resolve(t.bound)}if(F){var a=F;a.deps++}else a=F={parent:null,chunk:null,value:null,deps:1,errored:!1};return o.then(function(){var o=u(i);if(t.bound){var s=t.bound.value.slice(0);s.unshift(null),o=o.bind.apply(o,s)}_(o,t.id,t.bound,e._encodeFormAction),r[n]=o,""===n&&null===a.value&&(a.value=o),r[0]===d&&"object"==typeof a.value&&null!==a.value&&a.value.$$typeof===d&&(s=a.value,"3"===n)&&(s.props=o),a.deps--,0===a.deps&&null!==(o=a.chunk)&&"blocked"===o.status&&(s=o.value,o.status="fulfilled",o.value=a.value,null!==s&&M(s,a.value))},function(e){if(!a.errored){a.errored=!0,a.value=e;var t=a.chunk;null!==t&&"blocked"===t.status&&C(t,e)}}),null}function G(e,t,r,n,i){var o=parseInt((t=t.split(":"))[0],16);switch((o=$(e,o)).status){case"resolved_model":I(o);break;case"resolved_module":U(o)}switch(o.status){case"fulfilled":var a=o.value;for(o=1;o<t.length;o++){for(;a.$$typeof===f;)if("fulfilled"!==(a=a._payload).status)return H(a,r,n,e,i,t.slice(o-1));else a=a.value;a=a[t[o]]}return i(e,a,r,n);case"pending":case"blocked":return H(o,r,n,e,i,t);default:return F?(F.errored=!0,F.value=o.reason):F={parent:null,chunk:null,value:o.reason,deps:0,errored:!0},null}}function z(e,t){return new Map(t)}function q(e,t){return new Set(t)}function K(e,t){return new Blob(t.slice(1),{type:t[0]})}function X(e,t){e=new FormData;for(var r=0;r<t.length;r++)e.append(t[r][0],t[r][1]);return e}function Y(e,t){return t[Symbol.iterator]()}function Q(e,t){return t}function J(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.')}function Z(e,t,r,n,i,o,a){var s,l=new Map;this._bundlerConfig=e,this._serverReferenceConfig=t,this._moduleLoading=r,this._callServer=void 0!==n?n:J,this._encodeFormAction=i,this._nonce=o,this._chunks=l,this._stringDecoder=new TextDecoder,this._fromJSON=null,this._rowLength=this._rowTag=this._rowID=this._rowState=0,this._buffer=[],this._closed=!1,this._closedReason=null,this._tempRefs=a,this._fromJSON=(s=this,function(e,t){if("string"==typeof t){var r=s,n=this,i=e,o=t;if("$"===o[0]){if("$"===o)return null!==F&&"0"===i&&(F={parent:F,chunk:null,value:null,deps:0,errored:!1}),d;switch(o[1]){case"$":return o.slice(1);case"L":return V(r=$(r,n=parseInt(o.slice(2),16)));case"@":if(2===o.length)return new Promise(function(){});return $(r,n=parseInt(o.slice(2),16));case"S":return Symbol.for(o.slice(2));case"F":return G(r,o=o.slice(2),n,i,W);case"T":if(n="$"+o.slice(2),null==(r=r._tempRefs))throw Error("Missing a temporary reference set but the RSC response returned a temporary reference. Pass a temporaryReference option with the set that was used with the reply.");return r.get(n);case"Q":return G(r,o=o.slice(2),n,i,z);case"W":return G(r,o=o.slice(2),n,i,q);case"B":return G(r,o=o.slice(2),n,i,K);case"K":return G(r,o=o.slice(2),n,i,X);case"Z":return eo();case"i":return G(r,o=o.slice(2),n,i,Y);case"I":return 1/0;case"-":return"$-0"===o?-0:-1/0;case"N":return NaN;case"u":return;case"D":return new Date(Date.parse(o.slice(2)));case"n":return BigInt(o.slice(2));default:return G(r,o=o.slice(1),n,i,Q)}}return o}if("object"==typeof t&&null!==t){if(t[0]===d){if(e={$$typeof:d,type:t[1],key:t[2],ref:null,props:t[3]},null!==F){if(F=(t=F).parent,t.errored)e=V(e=new O("rejected",null,t.value,s));else if(0<t.deps){var a=new O("blocked",null,null,s);t.value=e,t.chunk=a,e=V(a)}}}else e=t;return e}return t})}function ee(e,t,r){var n=e._chunks,i=n.get(t);i&&"pending"!==i.status?i.reason.enqueueValue(r):n.set(t,new O("fulfilled",r,null,e))}function et(e,t,r,n){var i=e._chunks,o=i.get(t);o?"pending"===o.status&&(e=o.value,o.status="fulfilled",o.value=r,o.reason=n,null!==e&&M(e,o.value)):i.set(t,new O("fulfilled",r,n,e))}function er(e,t,r){var n=null;r=new ReadableStream({type:r,start:function(e){n=e}});var i=null;et(e,t,r,{enqueueValue:function(e){null===i?n.enqueue(e):i.then(function(){n.enqueue(e)})},enqueueModel:function(t){if(null===i){var r=new O("resolved_model",t,null,e);I(r),"fulfilled"===r.status?n.enqueue(r.value):(r.then(function(e){return n.enqueue(e)},function(e){return n.error(e)}),i=r)}else{r=i;var o=j(e);o.then(function(e){return n.enqueue(e)},function(e){return n.error(e)}),i=o,r.then(function(){i===o&&(i=null),N(o,t)})}},close:function(){if(null===i)n.close();else{var e=i;i=null,e.then(function(){return n.close()})}},error:function(e){if(null===i)n.error(e);else{var t=i;i=null,t.then(function(){return n.error(e)})}}})}function en(){return this}function ei(e,t,r){var n=[],i=!1,o=0,a={};a[h]=function(){var t,r=0;return(t={next:t=function(t){if(void 0!==t)throw Error("Values cannot be passed to next() of AsyncIterables passed to Client Components.");if(r===n.length){if(i)return new O("fulfilled",{done:!0,value:void 0},null,e);n[r]=j(e)}return n[r++]}})[h]=en,t},et(e,t,r?a[h]():a,{enqueueValue:function(t){if(o===n.length)n[o]=new O("fulfilled",{done:!1,value:t},null,e);else{var r=n[o],i=r.value,a=r.reason;r.status="fulfilled",r.value={done:!1,value:t},null!==i&&A(r,i,a)}o++},enqueueModel:function(t){o===n.length?n[o]=k(e,t,!1):D(n[o],t,!1),o++},close:function(t){for(i=!0,o===n.length?n[o]=k(e,t,!0):D(n[o],t,!0),o++;o<n.length;)D(n[o++],'"$undefined"',!0)},error:function(t){for(i=!0,o===n.length&&(n[o]=j(e));o<n.length;)C(n[o++],t)}})}function eo(){var e=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");return e.stack="Error: "+e.message,e}function ea(e,t){for(var r=e.length,n=t.length,i=0;i<r;i++)n+=e[i].byteLength;n=new Uint8Array(n);for(var o=i=0;o<r;o++){var a=e[o];n.set(a,i),i+=a.byteLength}return n.set(t,i),n}function es(e,t,r,n,i,o){ee(e,t,i=new i((r=0===r.length&&0==n.byteOffset%o?n:ea(r,n)).buffer,r.byteOffset,r.byteLength/o))}function el(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.")}function eu(e){return new Z(e.serverConsumerManifest.moduleMap,e.serverConsumerManifest.serverModuleMap,e.serverConsumerManifest.moduleLoading,el,e.encodeFormAction,"string"==typeof e.nonce?e.nonce:void 0,e&&e.temporaryReferences?e.temporaryReferences:void 0)}function ec(e,t){function r(t){B(e,t)}var n=t.getReader();n.read().then(function t(o){var a=o.value;if(o.done)B(e,Error("Connection closed."));else{var s=0,u=e._rowState;o=e._rowID;for(var d=e._rowTag,f=e._rowLength,p=e._buffer,h=a.length;s<h;){var m=-1;switch(u){case 0:58===(m=a[s++])?u=1:o=o<<4|(96<m?m-87:m-48);continue;case 1:84===(u=a[s])||65===u||79===u||111===u||85===u||83===u||115===u||76===u||108===u||71===u||103===u||77===u||109===u||86===u?(d=u,u=2,s++):64<u&&91>u||35===u||114===u||120===u?(d=u,u=3,s++):(d=0,u=3);continue;case 2:44===(m=a[s++])?u=4:f=f<<4|(96<m?m-87:m-48);continue;case 3:m=a.indexOf(10,s);break;case 4:(m=s+f)>a.length&&(m=-1)}var y=a.byteOffset+s;if(-1<m)(function(e,t,r,n,o){switch(r){case 65:ee(e,t,ea(n,o).buffer);return;case 79:es(e,t,n,o,Int8Array,1);return;case 111:ee(e,t,0===n.length?o:ea(n,o));return;case 85:es(e,t,n,o,Uint8ClampedArray,1);return;case 83:es(e,t,n,o,Int16Array,2);return;case 115:es(e,t,n,o,Uint16Array,2);return;case 76:es(e,t,n,o,Int32Array,4);return;case 108:es(e,t,n,o,Uint32Array,4);return;case 71:es(e,t,n,o,Float32Array,4);return;case 103:es(e,t,n,o,Float64Array,8);return;case 77:es(e,t,n,o,BigInt64Array,8);return;case 109:es(e,t,n,o,BigUint64Array,8);return;case 86:es(e,t,n,o,DataView,1);return}for(var a=e._stringDecoder,s="",u=0;u<n.length;u++)s+=a.decode(n[u],i);switch(n=s+=a.decode(o),r){case 73:var d=e,f=t,p=n,h=d._chunks,m=h.get(f);p=JSON.parse(p,d._fromJSON);var y=function(e,t){if(e){var r=e[t[0]];if(e=r&&r[t[2]])r=e.name;else{if(!(e=r&&r["*"]))throw Error('Could not find the module "'+t[0]+'" in the React Server Consumer Manifest. This is probably a bug in the React Server Components bundler.');r=t[2]}return 4===t.length?[e.id,e.chunks,r,1]:[e.id,e.chunks,r]}return t}(d._bundlerConfig,p);if(!function(e,t,r){if(null!==e)for(var n=1;n<t.length;n+=2){var i=c.d,o=i.X,a=e.prefix+t[n],s=e.crossOrigin;s="string"==typeof s?"use-credentials"===s?s:"":void 0,o.call(i,a,{crossOrigin:s,nonce:r})}}(d._moduleLoading,p[1],d._nonce),p=l(y)){if(m){var g=m;g.status="blocked"}else g=new O("blocked",null,null,d),h.set(f,g);p.then(function(){return L(g,y)},function(e){return C(g,e)})}else m?L(m,y):h.set(f,new O("resolved_module",y,null,d));break;case 72:switch(t=n[0],e=JSON.parse(n=n.slice(1),e._fromJSON),n=c.d,t){case"D":n.D(e);break;case"C":"string"==typeof e?n.C(e):n.C(e[0],e[1]);break;case"L":t=e[0],r=e[1],3===e.length?n.L(t,r,e[2]):n.L(t,r);break;case"m":"string"==typeof e?n.m(e):n.m(e[0],e[1]);break;case"X":"string"==typeof e?n.X(e):n.X(e[0],e[1]);break;case"S":"string"==typeof e?n.S(e):n.S(e[0],0===e[1]?void 0:e[1],3===e.length?e[2]:void 0);break;case"M":"string"==typeof e?n.M(e):n.M(e[0],e[1])}break;case 69:r=JSON.parse(n),(n=eo()).digest=r.digest,(o=(r=e._chunks).get(t))?C(o,n):r.set(t,new O("rejected",null,n,e));break;case 84:(o=(r=e._chunks).get(t))&&"pending"!==o.status?o.reason.enqueueValue(n):r.set(t,new O("fulfilled",n,null,e));break;case 78:case 68:case 87:throw Error("Failed to read a RSC payload created by a development version of React on the server while using a production version on the client. Always use matching versions on the server and the client.");case 82:er(e,t,void 0);break;case 114:er(e,t,"bytes");break;case 88:ei(e,t,!1);break;case 120:ei(e,t,!0);break;case 67:(e=e._chunks.get(t))&&"fulfilled"===e.status&&e.reason.close(""===n?'"$undefined"':n);break;default:(o=(r=e._chunks).get(t))?N(o,n):r.set(t,new O("resolved_model",n,null,e))}})(e,o,d,p,f=new Uint8Array(a.buffer,y,m-s)),s=m,3===u&&s++,f=o=d=u=0,p.length=0;else{a=new Uint8Array(a.buffer,y,a.byteLength-s),p.push(a),f-=a.byteLength;break}}return e._rowState=u,e._rowID=o,e._rowTag=d,e._rowLength=f,n.read().then(t).catch(r)}}).catch(r)}t.createFromFetch=function(e,t){var r=eu(t);return e.then(function(e){ec(r,e.body)},function(e){B(r,e)}),$(r,0)},t.createFromReadableStream=function(e,t){return ec(t=eu(t),e),$(t,0)},t.createServerReference=function(e){function t(){var t=Array.prototype.slice.call(arguments);return el(e,t)}return _(t,e,null,void 0),t},t.createTemporaryReferenceSet=function(){return new Map},t.encodeReply=function(e,t){return new Promise(function(r,n){var i=b(e,"",t&&t.temporaryReferences?t.temporaryReferences:void 0,r,n);if(t&&t.signal){var o=t.signal;if(o.aborted)i(o.reason);else{var a=function(){i(o.reason),o.removeEventListener("abort",a)};o.addEventListener("abort",a)}}})},t.registerServerReference=function(e,t,r){return _(e,t,null,r),e}},21279:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});let n=(0,r(43210).createContext)(null)},21709:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{bootstrap:function(){return l},error:function(){return c},event:function(){return h},info:function(){return p},prefixes:function(){return o},ready:function(){return f},trace:function(){return m},wait:function(){return u},warn:function(){return d},warnOnce:function(){return g}});let n=r(75317),i=r(38522),o={wait:(0,n.white)((0,n.bold)("○")),error:(0,n.red)((0,n.bold)("⨯")),warn:(0,n.yellow)((0,n.bold)("⚠")),ready:"▲",info:(0,n.white)((0,n.bold)(" ")),event:(0,n.green)((0,n.bold)("✓")),trace:(0,n.magenta)((0,n.bold)("\xbb"))},a={log:"log",warn:"warn",error:"error"};function s(e,...t){(""===t[0]||void 0===t[0])&&1===t.length&&t.shift();let r=e in a?a[e]:"log",n=o[e];0===t.length?console[r](""):1===t.length&&"string"==typeof t[0]?console[r](" "+n+" "+t[0]):console[r](" "+n,...t)}function l(...e){console.log("   "+e.join(" "))}function u(...e){s("wait",...e)}function c(...e){s("error",...e)}function d(...e){s("warn",...e)}function f(...e){s("ready",...e)}function p(...e){s("info",...e)}function h(...e){s("event",...e)}function m(...e){s("trace",...e)}let y=new i.LRUCache(1e4,e=>e.length);function g(...e){let t=e.join(" ");y.has(t)||(y.set(t,t),d(...e))}},21874:(e,t,r)=>{"use strict";r.d(t,{B:()=>u});var n=r(97758),i=r(95444),o=r(97095),a=r(7236);let s=e=>(0,n.q)(0,255,e),l={...i.ai,transform:e=>Math.round(s(e))},u={test:(0,a.$)("rgb","red"),parse:(0,a.q)("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+l.transform(e)+", "+l.transform(t)+", "+l.transform(r)+", "+(0,o.a)(i.X4.transform(n))+")"}},22113:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DynamicServerError:function(){return n},isDynamicServerError:function(){return i}});let r="DYNAMIC_SERVER_USAGE";class n extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22142:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.AppRouterContext},22238:(e,t,r)=>{"use strict";r.d(t,{j:()=>i,p:()=>a});let n=e=>t=>"string"==typeof t&&t.startsWith(e),i=n("--"),o=n("var(--"),a=e=>!!o(e)&&s.test(e.split("/*")[0].trim()),s=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},22308:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,i,,a]=t;for(let s in n.includes(o.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=r,t[3]="refresh"),i)e(i[s],r)}},refreshInactiveParallelSegments:function(){return a}});let n=r(56928),i=r(59008),o=r(83913);async function a(e){let t=new Set;await s({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function s(e){let{navigatedAt:t,state:r,updatedTree:o,updatedCache:a,includeNextUrl:l,fetchedSegments:u,rootTree:c=o,canonicalUrl:d}=e,[,f,p,h]=o,m=[];if(p&&p!==d&&"refresh"===h&&!u.has(p)){u.add(p);let e=(0,i.fetchServerResponse)(new URL(p,location.origin),{flightRouterState:[c[0],c[1],c[2],"refetch"],nextUrl:l?r.nextUrl:null}).then(e=>{let{flightData:r}=e;if("string"!=typeof r)for(let e of r)(0,n.applyFlightData)(t,a,a,e)});m.push(e)}for(let e in f){let n=s({navigatedAt:t,state:r,updatedTree:f[e],updatedCache:a,includeNextUrl:l,fetchedSegments:u,rootTree:c,canonicalUrl:d});m.push(n)}await Promise.all(m)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22586:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getComponentTypeModule:function(){return o},getLayoutOrPageModule:function(){return i}});let n=r(35499);async function i(e){let t,r,i,{layout:o,page:a,defaultPage:s}=e[2],l=void 0!==o,u=void 0!==a,c=void 0!==s&&e[0]===n.DEFAULT_SEGMENT_KEY;return l?(t=await o[0](),r="layout",i=o[1]):u?(t=await a[0](),r="page",i=a[1]):c&&(t=await s[0](),r="page",i=s[1]),{mod:t,modType:r,filePath:i}}async function o(e,t){let{[t]:r}=e[2];if(void 0!==r)return await r[0]()}},23671:(e,t,r)=>{"use strict";r.d(t,{Gt:()=>i,PP:()=>s,WG:()=>o,uv:()=>a});var n=r(83361);let{schedule:i,cancel:o,state:a,steps:s}=(0,r(69848).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.l,!0)},24207:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{METADATA_BOUNDARY_NAME:function(){return r},OUTLET_BOUNDARY_NAME:function(){return i},VIEWPORT_BOUNDARY_NAME:function(){return n}});let r="__next_metadata_boundary__",n="__next_viewport_boundary__",i="__next_outlet_boundary__"},24224:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var n=r(49384);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$,a=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:s}=t,l=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let o=i(t)||i(n);return a[e][o]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,l,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...u}[t]):({...s,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},24325:(e,t,r)=>{"use strict";let n;r.d(t,{k:()=>s});var i=r(97819),o=r(23671);function a(){n=void 0}let s={now:()=>(void 0===n&&s.set(o.uv.isProcessing||i.W.useManualTiming?o.uv.timestamp:performance.now()),n),set:e=>{n=e,queueMicrotask(a)}}},24342:(e,t,r)=>{"use strict";r.d(t,{OQ:()=>c,bt:()=>l});var n=r(14296),i=r(15547),o=r(24325),a=r(23671);let s=e=>!isNaN(parseFloat(e)),l={current:void 0};class u{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let r=o.k.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=o.k.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=s(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new n.v);let r=this.events[e].add(t);return"change"===e?()=>{r(),a.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-r}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let e=o.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let t=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,i.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),t)}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function c(e,t){return new u(e,t)}},24366:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41552).A)("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]])},24624:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(43210),i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},o=r(443);let a=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:s="",children:l,iconNode:u,...c},d)=>(0,n.createElement)("svg",{ref:d,...i,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:(0,o.z)("lucide",s),...c},[...u.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(l)?l:[l]]))},24642:(e,t)=>{"use strict";function r(e){let t=parseInt(e.slice(0,2),16),r=t>>1&63,n=Array(6);for(let e=0;e<6;e++){let t=r>>5-e&1;n[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:n,hasRestArgs:1==(1&t)}}function n(e,t){let r=Array(e.length);for(let n=0;n<e.length;n++)(n<6&&t.usedArgs[n]||n>=6&&t.hasRestArgs)&&(r[n]=e[n]);return r}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractInfoFromServerReferenceId:function(){return r},omitUnusedArgs:function(){return n}})},25028:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(43210),i=r(51215),o=r(14163),a=r(66156),s=r(60687),l=n.forwardRef((e,t)=>{let{container:r,...l}=e,[u,c]=n.useState(!1);(0,a.N)(()=>c(!0),[]);let d=r||u&&globalThis?.document?.body;return d?i.createPortal((0,s.jsx)(o.sG.div,{...l,ref:t}),d):null});l.displayName="Portal"},25232:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,r){let{url:x,isExternalUrl:E,navigateType:_,shouldScroll:P,allowAliasing:R}=r,S={},{hash:O}=x,T=(0,i.createHrefFromUrl)(x),j="push"===_;if((0,y.prunePrefetchCache)(t.prefetchCache),S.preserveCustomHistoryState=!1,S.pendingPush=j,E)return b(t,S,x.toString(),j);if(document.getElementById("__next-page-redirect"))return b(t,S,T,j);let M=(0,y.getOrCreatePrefetchCacheEntry)({url:x,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:R}),{treeAtTimeOfPrefetch:A,data:C}=M;return f.prefetchQueue.bump(C),C.then(f=>{let{flightData:y,canonicalUrl:E,postponed:_}=f,R=Date.now(),C=!1;if(M.lastUsedTime||(M.lastUsedTime=R,C=!0),M.aliased){let n=(0,v.handleAliasedPrefetchEntry)(R,t,y,x,S);return!1===n?e(t,{...r,allowAliasing:!1}):n}if("string"==typeof y)return b(t,S,y,j);let k=E?(0,i.createHrefFromUrl)(E):T;if(O&&t.canonicalUrl.split("#",1)[0]===k.split("#",1)[0])return S.onlyHashChange=!0,S.canonicalUrl=k,S.shouldScroll=P,S.hashFragment=O,S.scrollableSegments=[],(0,c.handleMutable)(t,S);let D=t.tree,N=t.cache,L=[];for(let e of y){let{pathToSegment:r,seedData:i,head:c,isHeadPartial:f,isRootRender:y}=e,v=e.tree,E=["",...r],P=(0,a.applyRouterStatePatchToTree)(E,D,v,T);if(null===P&&(P=(0,a.applyRouterStatePatchToTree)(E,A,v,T)),null!==P){if(i&&y&&_){let e=(0,m.startPPRNavigation)(R,N,D,v,i,c,f,!1,L);if(null!==e){if(null===e.route)return b(t,S,T,j);P=e.route;let r=e.node;null!==r&&(S.cache=r);let i=e.dynamicRequestTree;if(null!==i){let r=(0,n.fetchServerResponse)(x,{flightRouterState:i,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,r)}}else P=v}else{if((0,l.isNavigatingToNewRootLayout)(D,P))return b(t,S,T,j);let n=(0,p.createEmptyCacheNode)(),i=!1;for(let t of(M.status!==u.PrefetchCacheEntryStatus.stale||C?i=(0,d.applyFlightData)(R,N,n,e,M):(i=function(e,t,r,n){let i=!1;for(let o of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),w(n).map(e=>[...r,...e])))(0,g.clearCacheNodeDataForSegmentPath)(e,t,o),i=!0;return i}(n,N,r,v),M.lastUsedTime=R),(0,s.shouldHardNavigate)(E,D)?(n.rsc=N.rsc,n.prefetchRsc=N.prefetchRsc,(0,o.invalidateCacheBelowFlightSegmentPath)(n,N,r),S.cache=n):i&&(S.cache=n,N=n),w(v))){let e=[...r,...t];e[e.length-1]!==h.DEFAULT_SEGMENT_KEY&&L.push(e)}}D=P}}return S.patchedTree=D,S.canonicalUrl=k,S.scrollableSegments=L,S.hashFragment=O,S.shouldScroll=P,(0,c.handleMutable)(t,S)},()=>t)}}});let n=r(59008),i=r(57391),o=r(18468),a=r(86770),s=r(65951),l=r(2030),u=r(59154),c=r(59435),d=r(56928),f=r(75076),p=r(89752),h=r(83913),m=r(65956),y=r(5334),g=r(97464),v=r(9707);function b(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function w(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,i]of Object.entries(n))for(let n of w(i))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}r(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25942:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(26736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let n=r(2255);function i(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},27900:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41552).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},27924:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ClientSegmentRoot",{enumerable:!0,get:function(){return o}});let n=r(60687),i=r(75539);function o(e){let{Component:t,slots:o,params:a,promise:s}=e;{let e,{workAsyncStorage:s}=r(29294),l=s.getStore();if(!l)throw Object.defineProperty(new i.InvariantError("Expected workStore to exist when handling params in a client segment such as a Layout or Template."),"__NEXT_ERROR_CODE",{value:"E600",enumerable:!1,configurable:!0});let{createParamsFromClient:u}=r(60824);return e=u(a,l),(0,n.jsx)(t,{...o,params:e})}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41552).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28627:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return o}});let n=r(57391),i=r(70642);function o(e,t){var r;let{url:o,tree:a}=t,s=(0,n.createHrefFromUrl)(o),l=a||e.tree,u=e.cache;return{canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(r=(0,i.extractPathFromFlightRouterState)(l))?r:o.pathname}}r(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28827:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AsyncMetadata:function(){return o},AsyncMetadataOutlet:function(){return s}});let n=r(60687),i=r(43210),o=r(85429).ServerInsertMetadata;function a(e){let{promise:t}=e,{error:r,digest:n}=(0,i.use)(t);if(r)throw n&&(r.digest=n),r;return null}function s(e){let{promise:t}=e;return(0,n.jsx)(i.Suspense,{fallback:null,children:(0,n.jsx)(a,{promise:t})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28830:(e,t,r)=>{"use strict";r.d(t,{G:()=>n});let n=e=>t=>1-e(1-t)},28938:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"collectSegmentData",{enumerable:!0,get:function(){return d}});let n=r(37413),i=r(52513),o=r(93972),a=r(77855),s=r(44523),l=r(8670),u=r(62713);function c(e){let t=(0,u.getDigestForWellKnownError)(e);if(t)return t}async function d(e,t,r,l,u,d){let p=new Map;try{await (0,i.createFromReadableStream)((0,a.streamFromBuffer)(t),{serverConsumerManifest:u}),await (0,s.waitAtLeastOneReactRenderTask)()}catch{}let h=new AbortController,m=async()=>{await (0,s.waitAtLeastOneReactRenderTask)(),h.abort()},y=[],{prelude:g}=await (0,o.unstable_prerender)((0,n.jsx)(f,{shouldAssumePartialData:e,fullPageDataBuffer:t,fallbackRouteParams:d,serverConsumerManifest:u,clientModules:l,staleTime:r,segmentTasks:y,onCompletedProcessingRouteTree:m}),l,{signal:h.signal,onError:c}),v=await (0,a.streamToBuffer)(g);for(let[e,t]of(p.set("/_tree",v),await Promise.all(y)))p.set(e,t);return p}async function f({shouldAssumePartialData:e,fullPageDataBuffer:t,fallbackRouteParams:r,serverConsumerManifest:n,clientModules:o,staleTime:u,segmentTasks:c,onCompletedProcessingRouteTree:d}){let f=await (0,i.createFromReadableStream)(function(e){let t=e.getReader();return new ReadableStream({async pull(e){for(;;){let{done:r,value:n}=await t.read();if(!r){e.enqueue(n);continue}return}}})}((0,a.streamFromBuffer)(t)),{serverConsumerManifest:n}),m=f.b,y=f.f;if(1!==y.length&&3!==y[0].length)return console.error("Internal Next.js error: InitialRSCPayload does not match the expected shape for a prerendered page during segment prefetch generation."),null;let g=y[0][0],v=y[0][1],b=y[0][2],w=function e(t,r,n,i,o,a,u,c,d,f){let h=null,m=r[1],y=null!==i?i[2]:null;for(let r in m){let i=m[r],s=i[0],p=null!==y?y[r]:null,g=(0,l.encodeChildSegmentKey)(d,r,Array.isArray(s)&&null!==o?function(e,t){let r=e[0];if(!t.has(r))return(0,l.encodeSegment)(e);let n=(0,l.encodeSegment)(e),i=n.lastIndexOf("$");return n.substring(0,i+1)+`[${r}]`}(s,o):(0,l.encodeSegment)(s)),v=e(t,i,n,p,o,a,u,c,g,f);null===h&&(h={}),h[r]=v}return null!==i&&f.push((0,s.waitAtLeastOneReactRenderTask)().then(()=>p(t,n,i,d,u))),{segment:r[0],slots:h,isRootLayout:!0===r[4]}}(e,g,m,v,r,t,o,n,l.ROOT_SEGMENT_KEY,c),x=e||await h(b,o);return d(),{buildId:m,tree:w,head:b,isHeadPartial:x,staleTime:u}}async function p(e,t,r,n,i){let u=r[1],d={buildId:t,rsc:u,loading:r[3],isPartial:e||await h(u,i)},f=new AbortController;(0,s.waitAtLeastOneReactRenderTask)().then(()=>f.abort());let{prelude:p}=await (0,o.unstable_prerender)(d,i,{signal:f.signal,onError:c}),m=await (0,a.streamToBuffer)(p);return n===l.ROOT_SEGMENT_KEY?["/_index",m]:[n,m]}async function h(e,t){let r=!1,n=new AbortController;return(0,s.waitAtLeastOneReactRenderTask)().then(()=>{r=!0,n.abort()}),await (0,o.unstable_prerender)(e,t,{signal:n.signal,onError(){}}),r}},29070:(e,t,r)=>{"use strict";r.d(t,{X:()=>i});let n=e=>null!==e;function i(e,{repeat:t,repeatType:r="loop"},o,a=1){let s=e.filter(n),l=a<0||t&&"loop"!==r&&t%2==1?0:s.length-1;return l&&void 0!==o?o:s[l]}},29345:(e,t,r)=>{let{createProxy:n}=r(39844);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/layout-router.js")},29651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let n=r(57391),i=r(86770),o=r(2030),a=r(25232),s=r(56928),l=r(59435),u=r(89752);function c(e,t){let{serverResponse:{flightData:r,canonicalUrl:c},navigatedAt:d}=t,f={};if(f.preserveCustomHistoryState=!1,"string"==typeof r)return(0,a.handleExternalUrl)(e,f,r,e.pushRef.pendingPush);let p=e.tree,h=e.cache;for(let t of r){let{segmentPath:r,tree:l}=t,m=(0,i.applyRouterStatePatchToTree)(["",...r],p,l,e.canonicalUrl);if(null===m)return e;if((0,o.isNavigatingToNewRootLayout)(p,m))return(0,a.handleExternalUrl)(e,f,e.canonicalUrl,e.pushRef.pendingPush);let y=c?(0,n.createHrefFromUrl)(c):void 0;y&&(f.canonicalUrl=y);let g=(0,u.createEmptyCacheNode)();(0,s.applyFlightData)(d,h,g,t),f.patchedTree=m,f.cache=g,h=g,p=m}return(0,l.handleMutable)(e,f)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30195:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return s},urlObjectKeys:function(){return a}});let n=r(40740)._(r(76715)),i=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:r}=e,o=e.protocol||"",a=e.pathname||"",s=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||i.test(o))&&!1!==u?(u="//"+(u||""),a&&"/"!==a[0]&&(a="/"+a)):u||(u=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+o+u+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return o(e)}},30893:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ClientPageRoot:function(){return c.ClientPageRoot},ClientSegmentRoot:function(){return d.ClientSegmentRoot},HTTPAccessFallbackBoundary:function(){return m.HTTPAccessFallbackBoundary},LayoutRouter:function(){return o.default},MetadataBoundary:function(){return v.MetadataBoundary},OutletBoundary:function(){return v.OutletBoundary},Postpone:function(){return w.Postpone},RenderFromTemplateContext:function(){return a.default},ViewportBoundary:function(){return v.ViewportBoundary},actionAsyncStorage:function(){return u.actionAsyncStorage},collectSegmentData:function(){return E.collectSegmentData},createMetadataComponents:function(){return y.createMetadataComponents},createPrerenderParamsForClientSegment:function(){return p.createPrerenderParamsForClientSegment},createPrerenderSearchParamsForClientPage:function(){return f.createPrerenderSearchParamsForClientPage},createServerParamsForServerSegment:function(){return p.createServerParamsForServerSegment},createServerSearchParamsForServerPage:function(){return f.createServerSearchParamsForServerPage},createTemporaryReferenceSet:function(){return n.createTemporaryReferenceSet},decodeAction:function(){return n.decodeAction},decodeFormState:function(){return n.decodeFormState},decodeReply:function(){return n.decodeReply},patchFetch:function(){return R},preconnect:function(){return b.preconnect},preloadFont:function(){return b.preloadFont},preloadStyle:function(){return b.preloadStyle},prerender:function(){return i.unstable_prerender},renderToReadableStream:function(){return n.renderToReadableStream},serverHooks:function(){return h},taintObjectReference:function(){return x.taintObjectReference},workAsyncStorage:function(){return s.workAsyncStorage},workUnitAsyncStorage:function(){return l.workUnitAsyncStorage}});let n=r(12907),i=r(93972),o=_(r(29345)),a=_(r(31307)),s=r(29294),l=r(63033),u=r(19121),c=r(16444),d=r(16042),f=r(83091),p=r(73102),h=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=P(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(98479)),m=r(49477),y=r(59521),g=r(37719);r(88170);let v=r(46577),b=r(72900),w=r(61068),x=r(96844),E=r(28938);function _(e){return e&&e.__esModule?e:{default:e}}function P(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(P=function(e){return e?r:t})(e)}function R(){return(0,g.patchFetch)({workAsyncStorage:s.workAsyncStorage,workUnitAsyncStorage:l.workUnitAsyncStorage})}},31162:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return o}});let n=r(8704),i=r(49026);function o(e){return(0,i.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31307:(e,t,r)=>{let{createProxy:n}=r(39844);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/render-from-template-context.js")},31355:(e,t,r)=>{"use strict";r.d(t,{lg:()=>g,qW:()=>f,bL:()=>y});var n,i=r(43210),o=r(70569),a=r(14163),s=r(98599),l=r(13495),u=r(60687),c="dismissableLayer.update",d=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:y,onInteractOutside:g,onDismiss:v,...b}=e,w=i.useContext(d),[x,E]=i.useState(null),_=x?.ownerDocument??globalThis?.document,[,P]=i.useState({}),R=(0,s.s)(t,e=>E(e)),S=Array.from(w.layers),[O]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),T=S.indexOf(O),j=x?S.indexOf(x):-1,M=w.layersWithOutsidePointerEventsDisabled.size>0,A=j>=T,C=function(e,t=globalThis?.document){let r=(0,l.c)(e),n=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){m("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=n,t.addEventListener("click",o.current,{once:!0})):n()}else t.removeEventListener("click",o.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...w.branches].some(e=>e.contains(t));A&&!r&&(p?.(e),g?.(e),e.defaultPrevented||v?.())},_),k=function(e,t=globalThis?.document){let r=(0,l.c)(e),n=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!n.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;![...w.branches].some(e=>e.contains(t))&&(y?.(e),g?.(e),e.defaultPrevented||v?.())},_);return!function(e,t=globalThis?.document){let r=(0,l.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{j===w.layers.size-1&&(f?.(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},_),i.useEffect(()=>{if(x)return r&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(n=_.body.style.pointerEvents,_.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(x)),w.layers.add(x),h(),()=>{r&&1===w.layersWithOutsidePointerEventsDisabled.size&&(_.body.style.pointerEvents=n)}},[x,_,r,w]),i.useEffect(()=>()=>{x&&(w.layers.delete(x),w.layersWithOutsidePointerEventsDisabled.delete(x),h())},[x,w]),i.useEffect(()=>{let e=()=>P({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(a.sG.div,{...b,ref:R,style:{pointerEvents:M?A?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,k.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,C.onPointerDownCapture)})});f.displayName="DismissableLayer";var p=i.forwardRef((e,t)=>{let r=i.useContext(d),n=i.useRef(null),o=(0,s.s)(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(a.sG.div,{...e,ref:o})});function h(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,r,{discrete:n}){let i=r.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,a.hO)(i,o):i.dispatchEvent(o)}p.displayName="DismissableLayerBranch";var y=f,g=p},32192:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41552).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},32582:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});let n=(0,r(43210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},32708:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},32874:(e,t,r)=>{"use strict";r.d(t,{KN:()=>o,gQ:()=>u,px:()=>a,uj:()=>i,vh:()=>s,vw:()=>l});let n=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),i=n("deg"),o=n("%"),a=n("px"),s=n("vh"),l=n("vw"),u={...o,parse:e=>o.parse(e)/100,transform:e=>o.transform(100*e)}},33123:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createRouterCacheKey",{enumerable:!0,get:function(){return i}});let n=r(83913);function i(e,t){return(void 0===t&&(t=!1),Array.isArray(e))?e[0]+"|"+e[1]+"|"+e[2]:t&&e.startsWith(n.PAGE_SEGMENT_KEY)?n.PAGE_SEGMENT_KEY:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillCacheWithNewSubTreeData:function(){return l},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return u}});let n=r(34400),i=r(41500),o=r(33123),a=r(83913);function s(e,t,r,s,l,u){let{segmentPath:c,seedData:d,tree:f,head:p}=s,h=t,m=r;for(let t=0;t<c.length;t+=2){let r=c[t],s=c[t+1],y=t===c.length-2,g=(0,o.createRouterCacheKey)(s),v=m.parallelRoutes.get(r);if(!v)continue;let b=h.parallelRoutes.get(r);b&&b!==v||(b=new Map(v),h.parallelRoutes.set(r,b));let w=v.get(g),x=b.get(g);if(y){if(d&&(!x||!x.lazyData||x===w)){let t=d[0],r=d[1],o=d[3];x={lazyData:null,rsc:u||t!==a.PAGE_SEGMENT_KEY?r:null,prefetchRsc:null,head:null,prefetchHead:null,loading:o,parallelRoutes:u&&w?new Map(w.parallelRoutes):new Map,navigatedAt:e},w&&u&&(0,n.invalidateCacheByRouterState)(x,w,f),u&&(0,i.fillLazyItemsTillLeafWithHead)(e,x,w,f,d,p,l),b.set(g,x)}continue}x&&w&&(x===w&&(x={lazyData:x.lazyData,rsc:x.rsc,prefetchRsc:x.prefetchRsc,head:x.head,prefetchHead:x.prefetchHead,parallelRoutes:new Map(x.parallelRoutes),loading:x.loading},b.set(g,x)),h=x,m=w)}}function l(e,t,r,n,i){s(e,t,r,n,i,!0)}function u(e,t,r,n,i){s(e,t,r,n,i,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let n=r(33123);function i(e,t,r){for(let i in r[1]){let o=r[1][i][0],a=(0,n.createRouterCacheKey)(o),s=t.parallelRoutes.get(i);if(s){let t=new Map(s);t.delete(a),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34822:()=>{},34948:(e,t,r)=>{"use strict";r.d(t,{Y:()=>n,t:()=>i});let n=2e4;function i(e){let t=0,r=e.next(t);for(;!r.done&&t<n;)t+=50,r=e.next(t);return t>=n?1/0:t}},35416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return o},getBotType:function(){return l},isBot:function(){return s}});let n=r(95796),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,o=n.HTML_LIMITED_BOT_UA_RE.source;function a(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function s(e){return i.test(e)||a(e)}function l(e){return i.test(e)?"dom":a(e)?"html":void 0}},35429:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return j}});let n=r(11264),i=r(11448),o=r(91563),a=r(59154),s=r(6361),l=r(57391),u=r(25232),c=r(86770),d=r(2030),f=r(59435),p=r(41500),h=r(89752),m=r(68214),y=r(96493),g=r(22308),v=r(74007),b=r(36875),w=r(97860),x=r(5334),E=r(25942),_=r(26736),P=r(24642);r(50593);let{createFromFetch:R,createTemporaryReferenceSet:S,encodeReply:O}=r(19357);async function T(e,t,r){let a,l,{actionId:u,actionArgs:c}=r,d=S(),f=(0,P.extractInfoFromServerReferenceId)(u),p="use-cache"===f.type?(0,P.omitUnusedArgs)(c,f):c,h=await O(p,{temporaryReferences:d}),m=await fetch("",{method:"POST",headers:{Accept:o.RSC_CONTENT_TYPE_HEADER,[o.ACTION_HEADER]:u,[o.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[o.NEXT_URL]:t}:{}},body:h}),y=m.headers.get("x-action-redirect"),[g,b]=(null==y?void 0:y.split(";"))||[];switch(b){case"push":a=w.RedirectType.push;break;case"replace":a=w.RedirectType.replace;break;default:a=void 0}let x=!!m.headers.get(o.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let E=g?(0,s.assignLocation)(g,new URL(e.canonicalUrl,window.location.href)):void 0,_=m.headers.get("content-type");if(null==_?void 0:_.startsWith(o.RSC_CONTENT_TYPE_HEADER)){let e=await R(Promise.resolve(m),{callServer:n.callServer,findSourceMapURL:i.findSourceMapURL,temporaryReferences:d});return g?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:E,redirectType:a,revalidatedParts:l,isPrerender:x}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:E,redirectType:a,revalidatedParts:l,isPrerender:x}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===_?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:E,redirectType:a,revalidatedParts:l,isPrerender:x}}function j(e,t){let{resolve:r,reject:n}=t,i={},o=e.tree;i.preserveCustomHistoryState=!1;let s=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,v=Date.now();return T(e,s,t).then(async m=>{let P,{actionResult:R,actionFlightData:S,redirectLocation:O,redirectType:T,isPrerender:j,revalidatedParts:M}=m;if(O&&(T===w.RedirectType.replace?(e.pushRef.pendingPush=!1,i.pendingPush=!1):(e.pushRef.pendingPush=!0,i.pendingPush=!0),i.canonicalUrl=P=(0,l.createHrefFromUrl)(O,!1)),!S)return(r(R),O)?(0,u.handleExternalUrl)(e,i,O.href,e.pushRef.pendingPush):e;if("string"==typeof S)return r(R),(0,u.handleExternalUrl)(e,i,S,e.pushRef.pendingPush);let A=M.paths.length>0||M.tag||M.cookie;for(let n of S){let{tree:a,seedData:l,head:f,isRootRender:m}=n;if(!m)return console.log("SERVER ACTION APPLY FAILED"),r(R),e;let b=(0,c.applyRouterStatePatchToTree)([""],o,a,P||e.canonicalUrl);if(null===b)return r(R),(0,y.handleSegmentMismatch)(e,t,a);if((0,d.isNavigatingToNewRootLayout)(o,b))return r(R),(0,u.handleExternalUrl)(e,i,P||e.canonicalUrl,e.pushRef.pendingPush);if(null!==l){let t=l[1],r=(0,h.createEmptyCacheNode)();r.rsc=t,r.prefetchRsc=null,r.loading=l[3],(0,p.fillLazyItemsTillLeafWithHead)(v,r,void 0,a,l,f,void 0),i.cache=r,i.prefetchCache=new Map,A&&await (0,g.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:b,updatedCache:r,includeNextUrl:!!s,canonicalUrl:i.canonicalUrl||e.canonicalUrl})}i.patchedTree=b,o=b}return O&&P?(A||((0,x.createSeededPrefetchCacheEntry)({url:O,data:{flightData:S,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:j?a.PrefetchKind.FULL:a.PrefetchKind.AUTO}),i.prefetchCache=e.prefetchCache),n((0,b.getRedirectError)((0,_.hasBasePath)(P)?(0,E.removeBasePath)(P):P,T||w.RedirectType.push))):r(R),(0,f.handleMutable)(e,i)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35499:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}function n(e){return e.startsWith("@")&&"@children"!==e}function i(e,t){if(e.includes(o)){let e=JSON.stringify(t);return"{}"!==e?o+"?"+e:o}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_SEGMENT_KEY:function(){return a},PAGE_SEGMENT_KEY:function(){return o},addSearchParamsIfPageSegment:function(){return i},isGroupSegment:function(){return r},isParallelRouteSegment:function(){return n}});let o="__PAGE__",a="__DEFAULT__"},35656:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ErrorBoundary:function(){return h},ErrorBoundaryHandler:function(){return d},GlobalError:function(){return f},default:function(){return p}});let n=r(14985),i=r(60687),o=n._(r(43210)),a=r(93883),s=r(88092);r(12776);let l=r(29294).workAsyncStorage,u={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}};function c(e){let{error:t}=e;if(l){let e=l.getStore();if((null==e?void 0:e.isRevalidate)||(null==e?void 0:e.isStaticGeneration))throw console.error(t),t}return null}class d extends o.default.Component{static getDerivedStateFromError(e){if((0,s.isNextRouterError)(e))throw e;return{error:e}}static getDerivedStateFromProps(e,t){let{error:r}=t;return e.pathname!==t.previousPathname&&t.error?{error:null,previousPathname:e.pathname}:{error:t.error,previousPathname:e.pathname}}render(){return this.state.error?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(c,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,i.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}constructor(e){super(e),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function f(e){let{error:t}=e,r=null==t?void 0:t.digest;return(0,i.jsxs)("html",{id:"__next_error__",children:[(0,i.jsx)("head",{}),(0,i.jsxs)("body",{children:[(0,i.jsx)(c,{error:t}),(0,i.jsx)("div",{style:u.error,children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("h2",{style:u.text,children:["Application error: a ",r?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",r?"server logs":"browser console"," for more information)."]}),r?(0,i.jsx)("p",{style:u.text,children:"Digest: "+r}):null]})})]})]})}let p=f;function h(e){let{errorComponent:t,errorStyles:r,errorScripts:n,children:o}=e,s=(0,a.useUntrackedPathname)();return t?(0,i.jsx)(d,{pathname:s,errorComponent:t,errorStyles:r,errorScripts:n,children:o}):(0,i.jsx)(i.Fragment,{children:o})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35715:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return i},getProperError:function(){return o}});let n=r(69385);function i(e){return"object"==typeof e&&null!==e&&"name"in e&&"message"in e}function o(e){return i(e)?e:Object.defineProperty(Error((0,n.isPlainObject)(e)?function(e){let t=new WeakSet;return JSON.stringify(e,(e,r)=>{if("object"==typeof r&&null!==r){if(t.has(r))return"[Circular]";t.add(r)}return r})}(e):e+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},36070:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AlternatesMetadata",{enumerable:!0,get:function(){return a}});let n=r(37413);r(61120);let i=r(80407);function o({descriptor:e,...t}){return e.url?(0,n.jsx)("link",{...t,...e.title&&{title:e.title},href:e.url.toString()}):null}function a({alternates:e}){if(!e)return null;let{canonical:t,languages:r,media:n,types:a}=e;return(0,i.MetaFilter)([t?o({rel:"canonical",descriptor:t}):null,r?Object.entries(r).flatMap(([e,t])=>null==t?void 0:t.map(t=>o({rel:"alternate",hrefLang:e,descriptor:t}))):null,n?Object.entries(n).flatMap(([e,t])=>null==t?void 0:t.map(t=>o({rel:"alternate",media:e,descriptor:t}))):null,a?Object.entries(a).flatMap(([e,t])=>null==t?void 0:t.map(t=>o({rel:"alternate",type:e,descriptor:t}))):null])}},36536:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{resolveAlternates:function(){return l},resolveAppLinks:function(){return m},resolveAppleWebApp:function(){return h},resolveFacebook:function(){return g},resolveItunes:function(){return y},resolvePagination:function(){return v},resolveRobots:function(){return d},resolveThemeColor:function(){return a},resolveVerification:function(){return p}});let n=r(77341),i=r(96258);function o(e,t,r){if(e instanceof URL){let t=new URL(r.pathname,e);e.searchParams.forEach((e,r)=>t.searchParams.set(r,e)),e=t}return(0,i.resolveAbsoluteUrlWithPathname)(e,t,r)}let a=e=>{var t;if(!e)return null;let r=[];return null==(t=(0,n.resolveAsArrayOrUndefined)(e))||t.forEach(e=>{"string"==typeof e?r.push({color:e}):"object"==typeof e&&r.push({color:e.color,media:e.media})}),r};function s(e,t,r){if(!e)return null;let n={};for(let[i,a]of Object.entries(e))"string"==typeof a||a instanceof URL?n[i]=[{url:o(a,t,r)}]:(n[i]=[],null==a||a.forEach((e,a)=>{let s=o(e.url,t,r);n[i][a]={url:s,title:e.title}}));return n}let l=(e,t,r)=>{if(!e)return null;let n=function(e,t,r){return e?{url:o("string"==typeof e||e instanceof URL?e:e.url,t,r)}:null}(e.canonical,t,r),i=s(e.languages,t,r),a=s(e.media,t,r);return{canonical:n,languages:i,media:a,types:s(e.types,t,r)}},u=["noarchive","nosnippet","noimageindex","nocache","notranslate","indexifembedded","nositelinkssearchbox","unavailable_after","max-video-preview","max-image-preview","max-snippet"],c=e=>{if(!e)return null;if("string"==typeof e)return e;let t=[];for(let r of(e.index?t.push("index"):"boolean"==typeof e.index&&t.push("noindex"),e.follow?t.push("follow"):"boolean"==typeof e.follow&&t.push("nofollow"),u)){let n=e[r];void 0!==n&&!1!==n&&t.push("boolean"==typeof n?r:`${r}:${n}`)}return t.join(", ")},d=e=>e?{basic:c(e),googleBot:"string"!=typeof e?c(e.googleBot):null}:null,f=["google","yahoo","yandex","me","other"],p=e=>{if(!e)return null;let t={};for(let r of f){let i=e[r];if(i)if("other"===r)for(let r in t.other={},e.other){let i=(0,n.resolveAsArrayOrUndefined)(e.other[r]);i&&(t.other[r]=i)}else t[r]=(0,n.resolveAsArrayOrUndefined)(i)}return t},h=e=>{var t;if(!e)return null;if(!0===e)return{capable:!0};let r=e.startupImage?null==(t=(0,n.resolveAsArrayOrUndefined)(e.startupImage))?void 0:t.map(e=>"string"==typeof e?{url:e}:e):null;return{capable:!("capable"in e)||!!e.capable,title:e.title||null,startupImage:r,statusBarStyle:e.statusBarStyle||"default"}},m=e=>{if(!e)return null;for(let t in e)e[t]=(0,n.resolveAsArrayOrUndefined)(e[t]);return e},y=(e,t,r)=>e?{appId:e.appId,appArgument:e.appArgument?o(e.appArgument,t,r):void 0}:null,g=e=>e?{appId:e.appId,admins:(0,n.resolveAsArrayOrUndefined)(e.admins)}:null,v=(e,t,r)=>({previous:(null==e?void 0:e.previous)?o(e.previous,t,r):null,next:(null==e?void 0:e.next)?o(e.next,t,r):null})},36875:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return l},redirect:function(){return s}});let n=r(17974),i=r(97860),o=r(19121).actionAsyncStorage;function a(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let o=Object.defineProperty(Error(i.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return o.digest=i.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",o}function s(e,t){var r;throw null!=t||(t=(null==o||null==(r=o.getStore())?void 0:r.isAction)?i.RedirectType.push:i.RedirectType.replace),a(e,t,n.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=i.RedirectType.replace),a(e,t,n.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,i.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},37413:(e,t,r)=>{"use strict";e.exports=r(65239).vendored["react-rsc"].ReactJsxRuntime},37697:(e,t)=>{"use strict";function r(){return{width:"device-width",initialScale:1,themeColor:null,colorScheme:null}}function n(){return{viewport:null,themeColor:null,colorScheme:null,metadataBase:null,title:null,description:null,applicationName:null,authors:null,generator:null,keywords:null,referrer:null,creator:null,publisher:null,robots:null,manifest:null,alternates:{canonical:null,languages:null,media:null,types:null},icons:null,openGraph:null,twitter:null,verification:{},appleWebApp:null,formatDetection:null,itunes:null,facebook:null,pinterest:null,abstract:null,appLinks:null,archives:null,assets:null,bookmarks:null,category:null,classification:null,pagination:{previous:null,next:null},other:{}}}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createDefaultMetadata:function(){return n},createDefaultViewport:function(){return r}})},38243:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return S}});let n=r(14985),i=r(40740),o=r(60687),a=r(59154),s=i._(r(43210)),l=n._(r(51215)),u=r(22142),c=r(59008),d=r(89330),f=r(35656),p=r(14077),h=r(86719),m=r(67086),y=r(40099),g=r(33123),v=r(68214),b=r(19129);l.default.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;let w=["bottom","height","left","right","top","width","x","y"];function x(e,t){let r=e.getBoundingClientRect();return r.top>=0&&r.top<=t}class E extends s.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...e){super(...e),this.handlePotentialScroll=()=>{let{focusAndScrollRef:e,segmentPath:t}=this.props;if(e.apply){if(0!==e.segmentPaths.length&&!e.segmentPaths.some(e=>t.every((t,r)=>(0,p.matchSegment)(t,e[r]))))return;let r=null,n=e.hashFragment;if(n&&(r=function(e){var t;return"top"===e?document.body:null!=(t=document.getElementById(e))?t:document.getElementsByName(e)[0]}(n)),r||(r=null),!(r instanceof Element))return;for(;!(r instanceof HTMLElement)||function(e){if(["sticky","fixed"].includes(getComputedStyle(e).position))return!0;let t=e.getBoundingClientRect();return w.every(e=>0===t[e])}(r);){if(null===r.nextElementSibling)return;r=r.nextElementSibling}e.apply=!1,e.hashFragment=null,e.segmentPaths=[],(0,h.handleSmoothScroll)(()=>{if(n)return void r.scrollIntoView();let e=document.documentElement,t=e.clientHeight;!x(r,t)&&(e.scrollTop=0,x(r,t)||r.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:e.onlyHashChange}),e.onlyHashChange=!1,r.focus()}}}}function _(e){let{segmentPath:t,children:r}=e,n=(0,s.useContext)(u.GlobalLayoutRouterContext);if(!n)throw Object.defineProperty(Error("invariant global layout router not mounted"),"__NEXT_ERROR_CODE",{value:"E473",enumerable:!1,configurable:!0});return(0,o.jsx)(E,{segmentPath:t,focusAndScrollRef:n.focusAndScrollRef,children:r})}function P(e){let{tree:t,segmentPath:r,cacheNode:n,url:i}=e,l=(0,s.useContext)(u.GlobalLayoutRouterContext);if(!l)throw Object.defineProperty(Error("invariant global layout router not mounted"),"__NEXT_ERROR_CODE",{value:"E473",enumerable:!1,configurable:!0});let{tree:f}=l,h=null!==n.prefetchRsc?n.prefetchRsc:n.rsc,m=(0,s.useDeferredValue)(n.rsc,h),y="object"==typeof m&&null!==m&&"function"==typeof m.then?(0,s.use)(m):m;if(!y){let e=n.lazyData;if(null===e){let t=function e(t,r){if(t){let[n,i]=t,o=2===t.length;if((0,p.matchSegment)(r[0],n)&&r[1].hasOwnProperty(i)){if(o){let t=e(void 0,r[1][i]);return[r[0],{...r[1],[i]:[t[0],t[1],t[2],"refetch"]}]}return[r[0],{...r[1],[i]:e(t.slice(2),r[1][i])}]}}return r}(["",...r],f),o=(0,v.hasInterceptionRouteInCurrentTree)(f),u=Date.now();n.lazyData=e=(0,c.fetchServerResponse)(new URL(i,location.origin),{flightRouterState:t,nextUrl:o?l.nextUrl:null}).then(e=>((0,s.startTransition)(()=>{(0,b.dispatchAppRouterAction)({type:a.ACTION_SERVER_PATCH,previousTree:f,serverResponse:e,navigatedAt:u})}),e)),(0,s.use)(e)}(0,s.use)(d.unresolvedThenable)}return(0,o.jsx)(u.LayoutRouterContext.Provider,{value:{parentTree:t,parentCacheNode:n,parentSegmentPath:r,url:i},children:y})}function R(e){let t,{loading:r,children:n}=e;if(t="object"==typeof r&&null!==r&&"function"==typeof r.then?(0,s.use)(r):r){let e=t[0],r=t[1],i=t[2];return(0,o.jsx)(s.Suspense,{fallback:(0,o.jsxs)(o.Fragment,{children:[r,i,e]}),children:n})}return(0,o.jsx)(o.Fragment,{children:n})}function S(e){let{parallelRouterKey:t,error:r,errorStyles:n,errorScripts:i,templateStyles:a,templateScripts:l,template:c,notFound:d,forbidden:p,unauthorized:h}=e,v=(0,s.useContext)(u.LayoutRouterContext);if(!v)throw Object.defineProperty(Error("invariant expected layout router to be mounted"),"__NEXT_ERROR_CODE",{value:"E56",enumerable:!1,configurable:!0});let{parentTree:b,parentCacheNode:w,parentSegmentPath:x,url:E}=v,S=w.parallelRoutes,O=S.get(t);O||(O=new Map,S.set(t,O));let T=b[0],j=b[1][t],M=j[0],A=null===x?[t]:x.concat([T,t]),C=(0,g.createRouterCacheKey)(M),k=(0,g.createRouterCacheKey)(M,!0),D=O.get(C);if(void 0===D){let e={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};D=e,O.set(C,e)}let N=w.loading;return(0,o.jsxs)(u.TemplateContext.Provider,{value:(0,o.jsx)(_,{segmentPath:A,children:(0,o.jsx)(f.ErrorBoundary,{errorComponent:r,errorStyles:n,errorScripts:i,children:(0,o.jsx)(R,{loading:N,children:(0,o.jsx)(y.HTTPAccessFallbackBoundary,{notFound:d,forbidden:p,unauthorized:h,children:(0,o.jsx)(m.RedirectBoundary,{children:(0,o.jsx)(P,{url:E,tree:j,cacheNode:D,segmentPath:A})})})})})}),children:[a,l,c]},k)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38522:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"LRUCache",{enumerable:!0,get:function(){return r}});class r{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}},38637:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setCacheBustingSearchParam",{enumerable:!0,get:function(){return o}});let n=r(15102),i=r(91563),o=(e,t)=>{let r=(0,n.hexHash)([t[i.NEXT_ROUTER_PREFETCH_HEADER]||"0",t[i.NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]||"0",t[i.NEXT_ROUTER_STATE_TREE_HEADER],t[i.NEXT_URL]].join(",")),o=e.search,a=(o.startsWith("?")?o.slice(1):o).split("&").filter(Boolean);a.push(i.NEXT_RSC_UNION_QUERY+"="+r),e.search=a.length?"?"+a.join("&"):""};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38681:(e,t,r)=>{"use strict";r.d(t,{Y:()=>i});var n=r(15547);function i(e,t,r){let i=Math.max(t-5,0);return(0,n.f)(r-e(i),t-i)}},39444:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return o},normalizeRscURL:function(){return a}});let n=r(46453),i=r(83913);function o(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},39664:(e,t,r)=>{"use strict";r.d(t,{V:()=>c,f:()=>h});var n=r(7504);let i=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var o=r(68762),a=r(97095);let s="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function c(e){let t=e.toString(),r=[],i={color:[],number:[],var:[]},o=[],a=0,c=t.replace(u,e=>(n.y.test(e)?(i.color.push(a),o.push(l),r.push(n.y.parse(e))):e.startsWith("var(")?(i.var.push(a),o.push("var"),r.push(e)):(i.number.push(a),o.push(s),r.push(parseFloat(e))),++a,"${}")).split("${}");return{values:r,split:c,indexes:i,types:o}}function d(e){return c(e).values}function f(e){let{split:t,types:r}=c(e),i=t.length;return e=>{let o="";for(let u=0;u<i;u++)if(o+=t[u],void 0!==e[u]){let t=r[u];t===s?o+=(0,a.a)(e[u]):t===l?o+=n.y.transform(e[u]):o+=e[u]}return o}}let p=e=>"number"==typeof e?0:e,h={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(o.S)?.length||0)+(e.match(i)?.length||0)>0},parse:d,createTransformer:f,getAnimatableNone:function(e){let t=d(e);return f(e)(t.map(p))}}},39695:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.ServerInsertedHtml},39844:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createProxy",{enumerable:!0,get:function(){return n}});let n=r(12907).createClientModuleProxy},40099:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTTPAccessFallbackBoundary",{enumerable:!0,get:function(){return c}});let n=r(40740),i=r(60687),o=n._(r(43210)),a=r(93883),s=r(86358);r(50148);let l=r(22142);class u extends o.default.Component{componentDidCatch(){}static getDerivedStateFromError(e){if((0,s.isHTTPAccessFallbackError)(e))return{triggeredStatus:(0,s.getAccessFallbackHTTPStatus)(e)};throw e}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.triggeredStatus?{triggeredStatus:void 0,previousPathname:e.pathname}:{triggeredStatus:t.triggeredStatus,previousPathname:e.pathname}}render(){let{notFound:e,forbidden:t,unauthorized:r,children:n}=this.props,{triggeredStatus:o}=this.state,a={[s.HTTPAccessErrorStatus.NOT_FOUND]:e,[s.HTTPAccessErrorStatus.FORBIDDEN]:t,[s.HTTPAccessErrorStatus.UNAUTHORIZED]:r};if(o){let l=o===s.HTTPAccessErrorStatus.NOT_FOUND&&e,u=o===s.HTTPAccessErrorStatus.FORBIDDEN&&t,c=o===s.HTTPAccessErrorStatus.UNAUTHORIZED&&r;return l||u||c?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("meta",{name:"robots",content:"noindex"}),!1,a[o]]}):n}return n}constructor(e){super(e),this.state={triggeredStatus:void 0,previousPathname:e.pathname}}}function c(e){let{notFound:t,forbidden:r,unauthorized:n,children:s}=e,c=(0,a.useUntrackedPathname)(),d=(0,o.useContext)(l.MissingSlotContext);return t||r||n?(0,i.jsx)(u,{pathname:c,notFound:t,forbidden:r,unauthorized:n,missingSlots:d,children:s}):(0,i.jsx)(i.Fragment,{children:s})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},40740:(e,t,r)=>{"use strict";function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}function i(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var i={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(i,a,s):i[a]=e[a]}return i.default=e,r&&r.set(e,i),i}r.r(t),r.d(t,{_:()=>i})},41455:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(83361);let i=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function o(e,t,r,o){if(e===t&&r===o)return n.l;let a=t=>(function(e,t,r,n,o){let a,s,l=0;do(a=i(s=t+(r-t)/2,n,o)-e)>0?r=s:t=s;while(Math.abs(a)>1e-7&&++l<12);return s})(t,0,1,e,r);return e=>0===e||1===e?e:i(a(e),t,o)}},41500:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,o,a,s,l,u){if(0===Object.keys(a[1]).length){r.head=l;return}for(let c in a[1]){let d,f=a[1][c],p=f[0],h=(0,n.createRouterCacheKey)(p),m=null!==s&&void 0!==s[2][c]?s[2][c]:null;if(o){let n=o.parallelRoutes.get(c);if(n){let o,a=(null==u?void 0:u.kind)==="auto"&&u.status===i.PrefetchCacheEntryStatus.reusable,s=new Map(n),d=s.get(h);o=null!==m?{lazyData:null,rsc:m[1],prefetchRsc:null,head:null,prefetchHead:null,loading:m[3],parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),navigatedAt:t}:a&&d?{lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes),loading:d.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),loading:null,navigatedAt:t},s.set(h,o),e(t,o,d,f,m||null,l,u),r.parallelRoutes.set(c,s);continue}}if(null!==m){let e=m[1],r=m[3];d={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else d={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let y=r.parallelRoutes.get(c);y?y.set(h,d):r.parallelRoutes.set(c,new Map([[h,d]])),e(t,d,void 0,f,m,l,u)}}}});let n=r(33123),i=r(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41552:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(43210),i=r(443),o=r(24624);let a=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...a},s)=>(0,n.createElement)(o.A,{ref:s,iconNode:t,className:(0,i.z)(`lucide-${(0,i.f)(e)}`,r),...a}));return r.displayName=`${e}`,r}},42292:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,o.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,s.isDynamicPostpone)(t)||(0,i.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(18238),i=r(76299),o=r(81208),a=r(88092),s=r(54717),l=r(22113);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42706:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{accumulateMetadata:function(){return C},accumulateViewport:function(){return k},resolveMetadata:function(){return D},resolveViewport:function(){return N}}),r(34822);let n=r(61120),i=r(37697),o=r(66483),a=r(57373),s=r(77341),l=r(22586),u=r(6255),c=r(36536),d=r(97181),f=r(81289),p=r(14823),h=r(35499),m=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=g(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(21709)),y=r(73102);function g(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(g=function(e){return e?r:t})(e)}function v(e,t,r){if("function"==typeof e.generateViewport){let{route:n}=r;return r=>(0,f.getTracer)().trace(p.ResolveMetadataSpan.generateViewport,{spanName:`generateViewport ${n}`,attributes:{"next.page":n}},()=>e.generateViewport(t,r))}return e.viewport||null}function b(e,t,r){if("function"==typeof e.generateMetadata){let{route:n}=r;return r=>(0,f.getTracer)().trace(p.ResolveMetadataSpan.generateMetadata,{spanName:`generateMetadata ${n}`,attributes:{"next.page":n}},()=>e.generateMetadata(t,r))}return e.metadata||null}async function w(e,t,r){var n;if(!(null==e?void 0:e[r]))return;let i=e[r].map(async e=>(0,u.interopDefault)(await e(t)));return(null==i?void 0:i.length)>0?null==(n=await Promise.all(i))?void 0:n.flat():void 0}async function x(e,t){let{metadata:r}=e;if(!r)return null;let[n,i,o,a]=await Promise.all([w(r,t,"icon"),w(r,t,"apple"),w(r,t,"openGraph"),w(r,t,"twitter")]);return{icon:n,apple:i,openGraph:o,twitter:a,manifest:r.manifest}}async function E({tree:e,metadataItems:t,errorMetadataItem:r,props:n,route:i,errorConvention:o}){let a,s,u=!!(o&&e[2][o]);if(o)a=await (0,l.getComponentTypeModule)(e,"layout"),s=o;else{let{mod:t,modType:r}=await (0,l.getLayoutOrPageModule)(e);a=t,s=r}s&&(i+=`/${s}`);let c=await x(e[2],n),d=a?b(a,n,{route:i}):null;if(t.push([d,c]),u&&o){let t=await (0,l.getComponentTypeModule)(e,o),a=t?b(t,n,{route:i}):null;r[0]=a,r[1]=c}}async function _({tree:e,viewportItems:t,errorViewportItemRef:r,props:n,route:i,errorConvention:o}){let a,s,u=!!(o&&e[2][o]);if(o)a=await (0,l.getComponentTypeModule)(e,"layout"),s=o;else{let{mod:t,modType:r}=await (0,l.getLayoutOrPageModule)(e);a=t,s=r}s&&(i+=`/${s}`);let c=a?v(a,n,{route:i}):null;if(t.push(c),u&&o){let t=await (0,l.getComponentTypeModule)(e,o);r.current=t?v(t,n,{route:i}):null}}let P=(0,n.cache)(async function(e,t,r,n,i){return R([],e,void 0,{},t,r,[null,null],n,i)});async function R(e,t,r,n,i,o,a,s,l){let u,[c,d,{page:f}]=t,p=r&&r.length?[...r,c]:[c],m=s(c),g=n;m&&null!==m.value&&(g={...n,[m.param]:m.value});let v=(0,y.createServerParamsForMetadata)(g,l);for(let r in u=void 0!==f?{params:v,searchParams:i}:{params:v},await E({tree:t,metadataItems:e,errorMetadataItem:a,errorConvention:o,props:u,route:p.filter(e=>e!==h.PAGE_SEGMENT_KEY).join("/")}),d){let t=d[r];await R(e,t,p,g,i,o,a,s,l)}return 0===Object.keys(d).length&&o&&e.push(a),e}let S=(0,n.cache)(async function(e,t,r,n,i){return O([],e,void 0,{},t,r,{current:null},n,i)});async function O(e,t,r,n,i,o,a,s,l){let u,[c,d,{page:f}]=t,p=r&&r.length?[...r,c]:[c],m=s(c),g=n;m&&null!==m.value&&(g={...n,[m.param]:m.value});let v=(0,y.createServerParamsForMetadata)(g,l);for(let r in u=void 0!==f?{params:v,searchParams:i}:{params:v},await _({tree:t,viewportItems:e,errorViewportItemRef:a,errorConvention:o,props:u,route:p.filter(e=>e!==h.PAGE_SEGMENT_KEY).join("/")}),d){let t=d[r];await O(e,t,p,g,i,o,a,s,l)}return 0===Object.keys(d).length&&o&&e.push(a.current),e}let T=e=>!!(null==e?void 0:e.absolute),j=e=>T(null==e?void 0:e.title);function M(e,t){e&&(!j(e)&&j(t)&&(e.title=t.title),!e.description&&t.description&&(e.description=t.description))}function A(e,t){if("function"==typeof t){let r=t(new Promise(t=>e.push(t)));e.push(r),r instanceof Promise&&r.catch(e=>({__nextError:e}))}else"object"==typeof t?e.push(t):e.push(null)}async function C(e,t){let r,n=(0,i.createDefaultMetadata)(),l={title:null,twitter:null,openGraph:null},u={warnings:new Set},f={icon:[],apple:[]},p=function(e){let t=[];for(let r=0;r<e.length;r++)A(t,e[r][0]);return t}(e),h=0;for(let i=0;i<e.length;i++){var y,g,v,b,w,x;let m,E=e[i][1];if(i<=1&&(x=null==E||null==(y=E.icon)?void 0:y[0])&&("/favicon.ico"===x.url||x.url.toString().startsWith("/favicon.ico?"))&&"image/x-icon"===x.type){let e=null==E||null==(g=E.icon)?void 0:g.shift();0===i&&(r=e)}let _=p[h++];if("function"==typeof _){let e=_;_=p[h++],e(n)}!function({source:e,target:t,staticFilesMetadata:r,titleTemplates:n,metadataContext:i,buildState:l,leafSegmentStaticIcons:u}){let f=void 0!==(null==e?void 0:e.metadataBase)?e.metadataBase:t.metadataBase;for(let r in e)switch(r){case"title":t.title=(0,a.resolveTitle)(e.title,n.title);break;case"alternates":t.alternates=(0,c.resolveAlternates)(e.alternates,f,i);break;case"openGraph":t.openGraph=(0,o.resolveOpenGraph)(e.openGraph,f,i,n.openGraph);break;case"twitter":t.twitter=(0,o.resolveTwitter)(e.twitter,f,i,n.twitter);break;case"facebook":t.facebook=(0,c.resolveFacebook)(e.facebook);break;case"verification":t.verification=(0,c.resolveVerification)(e.verification);break;case"icons":t.icons=(0,d.resolveIcons)(e.icons);break;case"appleWebApp":t.appleWebApp=(0,c.resolveAppleWebApp)(e.appleWebApp);break;case"appLinks":t.appLinks=(0,c.resolveAppLinks)(e.appLinks);break;case"robots":t.robots=(0,c.resolveRobots)(e.robots);break;case"archives":case"assets":case"bookmarks":case"keywords":t[r]=(0,s.resolveAsArrayOrUndefined)(e[r]);break;case"authors":t[r]=(0,s.resolveAsArrayOrUndefined)(e.authors);break;case"itunes":t[r]=(0,c.resolveItunes)(e.itunes,f,i);break;case"pagination":t.pagination=(0,c.resolvePagination)(e.pagination,f,i);break;case"applicationName":case"description":case"generator":case"creator":case"publisher":case"category":case"classification":case"referrer":case"formatDetection":case"manifest":case"pinterest":t[r]=e[r]||null;break;case"other":t.other=Object.assign({},t.other,e.other);break;case"metadataBase":t.metadataBase=f;break;default:("viewport"===r||"themeColor"===r||"colorScheme"===r)&&null!=e[r]&&l.warnings.add(`Unsupported metadata ${r} is configured in metadata export in ${i.pathname}. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport`)}!function(e,t,r,n,i,a){var s,l;if(!r)return;let{icon:u,apple:c,openGraph:d,twitter:f,manifest:p}=r;if(u&&(a.icon=u),c&&(a.apple=c),f&&!(null==e||null==(s=e.twitter)?void 0:s.hasOwnProperty("images"))){let e=(0,o.resolveTwitter)({...t.twitter,images:f},t.metadataBase,{...n,isStaticMetadataRouteFile:!0},i.twitter);t.twitter=e}if(d&&!(null==e||null==(l=e.openGraph)?void 0:l.hasOwnProperty("images"))){let e=(0,o.resolveOpenGraph)({...t.openGraph,images:d},t.metadataBase,{...n,isStaticMetadataRouteFile:!0},i.openGraph);t.openGraph=e}p&&(t.manifest=p)}(e,t,r,i,n,u)}({target:n,source:L(_)?await _:_,metadataContext:t,staticFilesMetadata:E,titleTemplates:l,buildState:u,leafSegmentStaticIcons:f}),i<e.length-2&&(l={title:(null==(v=n.title)?void 0:v.template)||null,openGraph:(null==(b=n.openGraph)?void 0:b.title.template)||null,twitter:(null==(w=n.twitter)?void 0:w.title.template)||null})}if((f.icon.length>0||f.apple.length>0)&&!n.icons&&(n.icons={icon:[],apple:[]},f.icon.length>0&&n.icons.icon.unshift(...f.icon),f.apple.length>0&&n.icons.apple.unshift(...f.apple)),u.warnings.size>0)for(let e of u.warnings)m.warn(e);return function(e,t,r,n){let{openGraph:i,twitter:a}=e;if(i){let t={},s=j(a),l=null==a?void 0:a.description,u=!!((null==a?void 0:a.hasOwnProperty("images"))&&a.images);if(!s&&(T(i.title)?t.title=i.title:e.title&&T(e.title)&&(t.title=e.title)),l||(t.description=i.description||e.description||void 0),u||(t.images=i.images),Object.keys(t).length>0){let i=(0,o.resolveTwitter)(t,e.metadataBase,n,r.twitter);e.twitter?e.twitter=Object.assign({},e.twitter,{...!s&&{title:null==i?void 0:i.title},...!l&&{description:null==i?void 0:i.description},...!u&&{images:null==i?void 0:i.images}}):e.twitter=i}}return M(i,e),M(a,e),t&&(e.icons||(e.icons={icon:[],apple:[]}),e.icons.icon.unshift(t)),e}(n,r,l,t)}async function k(e){let t=(0,i.createDefaultViewport)(),r=function(e){let t=[];for(let r=0;r<e.length;r++)A(t,e[r]);return t}(e),n=0;for(;n<r.length;){let e,i=r[n++];if("function"==typeof i){let e=i;i=r[n++],e(t)}!function({target:e,source:t}){if(t)for(let r in t)switch(r){case"themeColor":e.themeColor=(0,c.resolveThemeColor)(t.themeColor);break;case"colorScheme":e.colorScheme=t.colorScheme||null;break;default:e[r]=t[r]}}({target:t,source:L(i)?await i:i})}return t}async function D(e,t,r,n,i,o){return C(await P(e,t,r,n,i),o)}async function N(e,t,r,n,i){return k(await S(e,t,r,n,i))}function L(e){return"object"==typeof e&&null!==e&&"function"==typeof e.then}},43203:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var n=r(5927),i=r(73303),o=r(23671);function a(e){return"number"==typeof e?e:parseFloat(e)}var s=r(43210),l=r(32582),u=r(17135),c=r(53342);function d(e,t={}){let{isStatic:r}=(0,s.useContext)(l.Q),f=()=>(0,n.S)(e)?e.get():e;if(r)return(0,c.G)(f);let p=(0,u.d)(f());return(0,s.useInsertionEffect)(()=>(function(e,t,r){let s,l,u=e.get(),c=null,d=u,f="string"==typeof u?u.replace(/[\d.-]/g,""):void 0,p=()=>{c&&(c.stop(),c=null)},h=()=>{p(),c=new i.s({keyframes:[a(e.get()),a(d)],velocity:e.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...r,onUpdate:s})};return e.attach((t,r)=>(d=t,s=e=>{var t,n;return r((t=e,(n=f)?t+n:t))},o.Gt.postRender(h),e.get()),p),(0,n.S)(t)&&(l=t.on("change",t=>{var r,n;return e.set((r=t,(n=f)?r+n:r))}),e.on("destroy",l)),l})(p,e,t),[p,JSON.stringify(t)]),p}},43210:(e,t,r)=>{"use strict";e.exports=r(94041).vendored["react-ssr"].React},43500:(e,t,r)=>{"use strict";r.d(t,{E:()=>s});var n=r(53532),i=r(57225),o=r(69825);let a={decay:n.B,inertia:n.B,tween:i.i,keyframes:i.i,spring:o.o};function s(e){"string"==typeof e.type&&(e.type=a[e.type])}},44397:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let n=r(33123);function i(e,t){return function e(t,r,i){if(0===Object.keys(r).length)return[t,i];if(r.children){let[o,a]=r.children,s=t.parallelRoutes.get("children");if(s){let t=(0,n.createRouterCacheKey)(o),r=s.get(t);if(r){let n=e(r,a,i+"/"+t);if(n)return n}}}for(let o in r){if("children"===o)continue;let[a,s]=r[o],l=t.parallelRoutes.get(o);if(!l)continue;let u=(0,n.createRouterCacheKey)(a),c=l.get(u);if(!c)continue;let d=e(c,s,i+"/"+u);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},46033:(e,t,r)=>{"use strict";e.exports=r(65239).vendored["react-rsc"].ReactDOM},46059:(e,t,r)=>{"use strict";r.d(t,{C:()=>a});var n=r(43210),i=r(98599),o=r(66156),a=e=>{let{present:t,children:r}=e,a=function(e){var t,r;let[i,a]=n.useState(),l=n.useRef(null),u=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=s(l.current);c.current="mounted"===d?e:"none"},[d]),(0,o.N)(()=>{let t=l.current,r=u.current;if(r!==e){let n=c.current,i=s(t);e?f("MOUNT"):"none"===i||t?.display==="none"?f("UNMOUNT"):r&&n!==i?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,o.N)(()=>{if(i){let e,t=i.ownerDocument.defaultView??window,r=r=>{let n=s(l.current).includes(r.animationName);if(r.target===i&&n&&(f("ANIMATION_END"),!u.current)){let r=i.style.animationFillMode;i.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=r)})}},n=e=>{e.target===i&&(c.current=s(l.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{t.clearTimeout(e),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}f("ANIMATION_END")},[i,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,a(e)},[])}}(t),l="function"==typeof r?r({present:a.isPresent}):n.Children.only(r),u=(0,i.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||a.isPresent?n.cloneElement(l,{ref:u}):null};function s(e){return e?.animationName||"none"}a.displayName="Presence"},46453:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},46577:(e,t,r)=>{let{createProxy:n}=r(39844);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/metadata/metadata-boundary.js")},47313:(e,t,r)=>{"use strict";r.d(t,{Kq:()=>Y,LM:()=>Q,VY:()=>ee,bL:()=>J,bm:()=>er,hE:()=>Z,rc:()=>et});var n=r(43210),i=r(51215),o=r(70569),a=r(98599),s=r(9510),l=r(11273),u=r(31355),c=r(25028),d=r(46059),f=r(14163),p=r(13495),h=r(65551),m=r(66156),y=r(69024),g=r(60687),v="ToastProvider",[b,w,x]=(0,s.N)("Toast"),[E,_]=(0,l.A)("Toast",[x]),[P,R]=E(v),S=e=>{let{__scopeToast:t,label:r="Notification",duration:i=5e3,swipeDirection:o="right",swipeThreshold:a=50,children:s}=e,[l,u]=n.useState(null),[c,d]=n.useState(0),f=n.useRef(!1),p=n.useRef(!1);return r.trim()||console.error(`Invalid prop \`label\` supplied to \`${v}\`. Expected non-empty \`string\`.`),(0,g.jsx)(b.Provider,{scope:t,children:(0,g.jsx)(P,{scope:t,label:r,duration:i,swipeDirection:o,swipeThreshold:a,toastCount:c,viewport:l,onViewportChange:u,onToastAdd:n.useCallback(()=>d(e=>e+1),[]),onToastRemove:n.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:s})})};S.displayName=v;var O="ToastViewport",T=["F8"],j="toast.viewportPause",M="toast.viewportResume",A=n.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:i=T,label:o="Notifications ({hotkey})",...s}=e,l=R(O,r),c=w(r),d=n.useRef(null),p=n.useRef(null),h=n.useRef(null),m=n.useRef(null),y=(0,a.s)(t,m,l.onViewportChange),v=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=l.toastCount>0;n.useEffect(()=>{let e=e=>{0!==i.length&&i.every(t=>e[t]||e.code===t)&&m.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[i]),n.useEffect(()=>{let e=d.current,t=m.current;if(x&&e&&t){let r=()=>{if(!l.isClosePausedRef.current){let e=new CustomEvent(j);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},n=()=>{if(l.isClosePausedRef.current){let e=new CustomEvent(M);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},i=t=>{e.contains(t.relatedTarget)||n()},o=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",i),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",o),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",i),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",o),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[x,l.isClosePausedRef]);let E=n.useCallback(({tabbingDirection:e})=>{let t=c().map(t=>{let r=t.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===e?n:n.reverse()});return("forwards"===e?t.reverse():t).flat()},[c]);return n.useEffect(()=>{let e=m.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){let r=document.activeElement,n=t.shiftKey;if(t.target===e&&n)return void p.current?.focus();let i=E({tabbingDirection:n?"backwards":"forwards"}),o=i.findIndex(e=>e===r);X(i.slice(o+1))?t.preventDefault():n?p.current?.focus():h.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,E]),(0,g.jsxs)(u.lg,{ref:d,role:"region","aria-label":o.replace("{hotkey}",v),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&(0,g.jsx)(k,{ref:p,onFocusFromOutsideViewport:()=>{X(E({tabbingDirection:"forwards"}))}}),(0,g.jsx)(b.Slot,{scope:r,children:(0,g.jsx)(f.sG.ol,{tabIndex:-1,...s,ref:y})}),x&&(0,g.jsx)(k,{ref:h,onFocusFromOutsideViewport:()=>{X(E({tabbingDirection:"backwards"}))}})]})});A.displayName=O;var C="ToastFocusProxy",k=n.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...i}=e,o=R(C,r);return(0,g.jsx)(y.s6,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:e=>{let t=e.relatedTarget;o.viewport?.contains(t)||n()}})});k.displayName=C;var D="Toast",N=n.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:i,onOpenChange:a,...s}=e,[l,u]=(0,h.i)({prop:n,defaultProp:i??!0,onChange:a,caller:D});return(0,g.jsx)(d.C,{present:r||l,children:(0,g.jsx)(I,{open:l,...s,ref:t,onClose:()=>u(!1),onPause:(0,p.c)(e.onPause),onResume:(0,p.c)(e.onResume),onSwipeStart:(0,o.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,o.m)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${r}px`)}),onSwipeCancel:(0,o.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,o.m)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${r}px`),u(!1)})})})});N.displayName=D;var[L,F]=E(D,{onClose(){}}),I=n.forwardRef((e,t)=>{let{__scopeToast:r,type:s="foreground",duration:l,open:c,onClose:d,onEscapeKeyDown:h,onPause:m,onResume:y,onSwipeStart:v,onSwipeMove:w,onSwipeCancel:x,onSwipeEnd:E,..._}=e,P=R(D,r),[S,O]=n.useState(null),T=(0,a.s)(t,e=>O(e)),A=n.useRef(null),C=n.useRef(null),k=l||P.duration,N=n.useRef(0),F=n.useRef(k),I=n.useRef(0),{onToastAdd:B,onToastRemove:V}=P,$=(0,p.c)(()=>{S?.contains(document.activeElement)&&P.viewport?.focus(),d()}),H=n.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(I.current),N.current=new Date().getTime(),I.current=window.setTimeout($,e))},[$]);n.useEffect(()=>{let e=P.viewport;if(e){let t=()=>{H(F.current),y?.()},r=()=>{let e=new Date().getTime()-N.current;F.current=F.current-e,window.clearTimeout(I.current),m?.()};return e.addEventListener(j,r),e.addEventListener(M,t),()=>{e.removeEventListener(j,r),e.removeEventListener(M,t)}}},[P.viewport,k,m,y,H]),n.useEffect(()=>{c&&!P.isClosePausedRef.current&&H(k)},[c,k,P.isClosePausedRef,H]),n.useEffect(()=>(B(),()=>V()),[B,V]);let W=n.useMemo(()=>S?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{var n;if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),(n=t).nodeType===n.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,i=""===t.dataset.radixToastAnnounceExclude;if(!n)if(i){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}),r}(S):null,[S]);return P.viewport?(0,g.jsxs)(g.Fragment,{children:[W&&(0,g.jsx)(U,{__scopeToast:r,role:"status","aria-live":"foreground"===s?"assertive":"polite","aria-atomic":!0,children:W}),(0,g.jsx)(L,{scope:r,onClose:$,children:i.createPortal((0,g.jsx)(b.ItemSlot,{scope:r,children:(0,g.jsx)(u.bL,{asChild:!0,onEscapeKeyDown:(0,o.m)(h,()=>{P.isFocusedToastEscapeKeyDownRef.current||$(),P.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,g.jsx)(f.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":P.swipeDirection,..._,ref:T,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,o.m)(e.onKeyDown,e=>{"Escape"===e.key&&(h?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(P.isFocusedToastEscapeKeyDownRef.current=!0,$()))}),onPointerDown:(0,o.m)(e.onPointerDown,e=>{0===e.button&&(A.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,o.m)(e.onPointerMove,e=>{if(!A.current)return;let t=e.clientX-A.current.x,r=e.clientY-A.current.y,n=!!C.current,i=["left","right"].includes(P.swipeDirection),o=["left","up"].includes(P.swipeDirection)?Math.min:Math.max,a=i?o(0,t):0,s=i?0:o(0,r),l="touch"===e.pointerType?10:2,u={x:a,y:s},c={originalEvent:e,delta:u};n?(C.current=u,q("toast.swipeMove",w,c,{discrete:!1})):K(u,P.swipeDirection,l)?(C.current=u,q("toast.swipeStart",v,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(A.current=null)}),onPointerUp:(0,o.m)(e.onPointerUp,e=>{let t=C.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),C.current=null,A.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};K(t,P.swipeDirection,P.swipeThreshold)?q("toast.swipeEnd",E,n,{discrete:!0}):q("toast.swipeCancel",x,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),P.viewport)})]}):null}),U=e=>{let{__scopeToast:t,children:r,...i}=e,o=R(D,t),[a,s]=n.useState(!1),[l,u]=n.useState(!1);return function(e=()=>{}){let t=(0,p.c)(e);(0,m.N)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>s(!0)),n.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,g.jsx)(c.Z,{asChild:!0,children:(0,g.jsx)(y.s6,{...i,children:a&&(0,g.jsxs)(g.Fragment,{children:[o.label," ",r]})})})},B=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,g.jsx)(f.sG.div,{...n,ref:t})});B.displayName="ToastTitle";var V=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,g.jsx)(f.sG.div,{...n,ref:t})});V.displayName="ToastDescription";var $="ToastAction",H=n.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,g.jsx)(z,{altText:r,asChild:!0,children:(0,g.jsx)(G,{...n,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${$}\`. Expected non-empty \`string\`.`),null)});H.displayName=$;var W="ToastClose",G=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,i=F(W,r);return(0,g.jsx)(z,{asChild:!0,children:(0,g.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,i.onClose)})})});G.displayName=W;var z=n.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...i}=e;return(0,g.jsx)(f.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...i,ref:t})});function q(e,t,r,{discrete:n}){let i=r.originalEvent.currentTarget,o=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,f.hO)(i,o):i.dispatchEvent(o)}var K=(e,t,r=0)=>{let n=Math.abs(e.x),i=Math.abs(e.y),o=n>i;return"left"===t||"right"===t?o&&n>r:!o&&i>r};function X(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var Y=S,Q=A,J=N,Z=B,ee=V,et=H,er=G},49026:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return i},RedirectType:function(){return o},isRedirectError:function(){return a}});let n=r(52836),i="NEXT_REDIRECT";var o=function(e){return e.push="push",e.replace="replace",e}({});function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,o]=t,a=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===i&&("replace"===o||"push"===o)&&"string"==typeof a&&!isNaN(s)&&s in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49153:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41552).A)("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},49384:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n);return i}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n,A:()=>i});let i=n},49477:(e,t,r)=>{let{createProxy:n}=r(39844);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js")},50148:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},50593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return f},cancelPrefetchTask:function(){return l},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return a},navigate:function(){return i},prefetch:function(){return n},reschedulePrefetchTask:function(){return u},revalidateEntireCache:function(){return o},schedulePrefetchTask:function(){return s}});let r=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},n=r,i=r,o=r,a=r,s=r,l=r,u=r,c=r;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),f=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51215:(e,t,r)=>{"use strict";e.exports=r(94041).vendored["react-ssr"].ReactDOM},51550:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n})},51846:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return i}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},52513:(e,t,r)=>{"use strict";e.exports=r(20884)},52581:(e,t,r)=>{"use strict";r.d(t,{l$:()=>x,oR:()=>g});var n=r(43210),i=r(51215),o=e=>{switch(e){case"success":return l;case"info":return c;case"warning":return u;case"error":return d;default:return null}},a=Array(12).fill(0),s=({visible:e,className:t})=>n.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},n.createElement("div",{className:"sonner-spinner"},a.map((e,t)=>n.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${t}`})))),l=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),u=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),c=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),d=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),f=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},n.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),p=()=>{let[e,t]=n.useState(document.hidden);return n.useEffect(()=>{let e=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",e),()=>window.removeEventListener("visibilitychange",e)},[]),e},h=1,m=new class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:r,...n}=e,i="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:h++,o=this.toasts.find(e=>e.id===i),a=void 0===e.dismissible||e.dismissible;return this.dismissedToasts.has(i)&&this.dismissedToasts.delete(i),o?this.toasts=this.toasts.map(t=>t.id===i?(this.publish({...t,...e,id:i,title:r}),{...t,...e,id:i,dismissible:a,title:r}):t):this.addToast({title:r,...n,dismissible:a,id:i}),i},this.dismiss=e=>(this.dismissedToasts.add(e),e||this.toasts.forEach(e=>{this.subscribers.forEach(t=>t({id:e.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{let r;if(!t)return;void 0!==t.loading&&(r=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let i=e instanceof Promise?e:e(),o=void 0!==r,a,s=i.then(async e=>{if(a=["resolve",e],n.isValidElement(e))o=!1,this.create({id:r,type:"default",message:e});else if(y(e)&&!e.ok){o=!1;let n="function"==typeof t.error?await t.error(`HTTP error! status: ${e.status}`):t.error,i="function"==typeof t.description?await t.description(`HTTP error! status: ${e.status}`):t.description;this.create({id:r,type:"error",message:n,description:i})}else if(void 0!==t.success){o=!1;let n="function"==typeof t.success?await t.success(e):t.success,i="function"==typeof t.description?await t.description(e):t.description;this.create({id:r,type:"success",message:n,description:i})}}).catch(async e=>{if(a=["reject",e],void 0!==t.error){o=!1;let n="function"==typeof t.error?await t.error(e):t.error,i="function"==typeof t.description?await t.description(e):t.description;this.create({id:r,type:"error",message:n,description:i})}}).finally(()=>{var e;o&&(this.dismiss(r),r=void 0),null==(e=t.finally)||e.call(t)}),l=()=>new Promise((e,t)=>s.then(()=>"reject"===a[0]?t(a[1]):e(a[1])).catch(t));return"string"!=typeof r&&"number"!=typeof r?{unwrap:l}:Object.assign(r,{unwrap:l})},this.custom=(e,t)=>{let r=(null==t?void 0:t.id)||h++;return this.create({jsx:e(r),id:r,...t}),r},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},y=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status,g=Object.assign((e,t)=>{let r=(null==t?void 0:t.id)||h++;return m.addToast({title:e,...t,id:r}),r},{success:m.success,info:m.info,warning:m.warning,error:m.error,custom:m.custom,message:m.message,promise:m.promise,dismiss:m.dismiss,loading:m.loading},{getHistory:()=>m.toasts,getToasts:()=>m.getActiveToasts()});function v(e){return void 0!==e.label}function b(...e){return e.filter(Boolean).join(" ")}!function(e,{insertAt:t}={}){if(!e||"undefined"==typeof document)return;let r=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css","top"===t&&r.firstChild?r.insertBefore(n,r.firstChild):r.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);var w=e=>{var t,r,i,a,l,u,c,d,h,m,y;let{invert:g,toast:w,unstyled:x,interacting:E,setHeights:_,visibleToasts:P,heights:R,index:S,toasts:O,expanded:T,removeToast:j,defaultRichColors:M,closeButton:A,style:C,cancelButtonStyle:k,actionButtonStyle:D,className:N="",descriptionClassName:L="",duration:F,position:I,gap:U,loadingIcon:B,expandByDefault:V,classNames:$,icons:H,closeButtonAriaLabel:W="Close toast",pauseWhenPageIsHidden:G}=e,[z,q]=n.useState(null),[K,X]=n.useState(null),[Y,Q]=n.useState(!1),[J,Z]=n.useState(!1),[ee,et]=n.useState(!1),[er,en]=n.useState(!1),[ei,eo]=n.useState(!1),[ea,es]=n.useState(0),[el,eu]=n.useState(0),ec=n.useRef(w.duration||F||4e3),ed=n.useRef(null),ef=n.useRef(null),ep=0===S,eh=S+1<=P,em=w.type,ey=!1!==w.dismissible,eg=w.className||"",ev=w.descriptionClassName||"",eb=n.useMemo(()=>R.findIndex(e=>e.toastId===w.id)||0,[R,w.id]),ew=n.useMemo(()=>{var e;return null!=(e=w.closeButton)?e:A},[w.closeButton,A]),ex=n.useMemo(()=>w.duration||F||4e3,[w.duration,F]),eE=n.useRef(0),e_=n.useRef(0),eP=n.useRef(0),eR=n.useRef(null),[eS,eO]=I.split("-"),eT=n.useMemo(()=>R.reduce((e,t,r)=>r>=eb?e:e+t.height,0),[R,eb]),ej=p(),eM=w.invert||g,eA="loading"===em;e_.current=n.useMemo(()=>eb*U+eT,[eb,eT]),n.useEffect(()=>{ec.current=ex},[ex]),n.useEffect(()=>{Q(!0)},[]),n.useEffect(()=>{let e=ef.current;if(e){let t=e.getBoundingClientRect().height;return eu(t),_(e=>[{toastId:w.id,height:t,position:w.position},...e]),()=>_(e=>e.filter(e=>e.toastId!==w.id))}},[_,w.id]),n.useLayoutEffect(()=>{if(!Y)return;let e=ef.current,t=e.style.height;e.style.height="auto";let r=e.getBoundingClientRect().height;e.style.height=t,eu(r),_(e=>e.find(e=>e.toastId===w.id)?e.map(e=>e.toastId===w.id?{...e,height:r}:e):[{toastId:w.id,height:r,position:w.position},...e])},[Y,w.title,w.description,_,w.id]);let eC=n.useCallback(()=>{Z(!0),es(e_.current),_(e=>e.filter(e=>e.toastId!==w.id)),setTimeout(()=>{j(w)},200)},[w,j,_,e_]);return n.useEffect(()=>{let e;if((!w.promise||"loading"!==em)&&w.duration!==1/0&&"loading"!==w.type)return T||E||G&&ej?(()=>{if(eP.current<eE.current){let e=new Date().getTime()-eE.current;ec.current=ec.current-e}eP.current=new Date().getTime()})():ec.current!==1/0&&(eE.current=new Date().getTime(),e=setTimeout(()=>{var e;null==(e=w.onAutoClose)||e.call(w,w),eC()},ec.current)),()=>clearTimeout(e)},[T,E,w,em,G,ej,eC]),n.useEffect(()=>{w.delete&&eC()},[eC,w.delete]),n.createElement("li",{tabIndex:0,ref:ef,className:b(N,eg,null==$?void 0:$.toast,null==(t=null==w?void 0:w.classNames)?void 0:t.toast,null==$?void 0:$.default,null==$?void 0:$[em],null==(r=null==w?void 0:w.classNames)?void 0:r[em]),"data-sonner-toast":"","data-rich-colors":null!=(i=w.richColors)?i:M,"data-styled":!(w.jsx||w.unstyled||x),"data-mounted":Y,"data-promise":!!w.promise,"data-swiped":ei,"data-removed":J,"data-visible":eh,"data-y-position":eS,"data-x-position":eO,"data-index":S,"data-front":ep,"data-swiping":ee,"data-dismissible":ey,"data-type":em,"data-invert":eM,"data-swipe-out":er,"data-swipe-direction":K,"data-expanded":!!(T||V&&Y),style:{"--index":S,"--toasts-before":S,"--z-index":O.length-S,"--offset":`${J?ea:e_.current}px`,"--initial-height":V?"auto":`${el}px`,...C,...w.style},onDragEnd:()=>{et(!1),q(null),eR.current=null},onPointerDown:e=>{eA||!ey||(ed.current=new Date,es(e_.current),e.target.setPointerCapture(e.pointerId),"BUTTON"!==e.target.tagName&&(et(!0),eR.current={x:e.clientX,y:e.clientY}))},onPointerUp:()=>{var e,t,r,n;if(er||!ey)return;eR.current=null;let i=Number((null==(e=ef.current)?void 0:e.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),o=Number((null==(t=ef.current)?void 0:t.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),a=new Date().getTime()-(null==(r=ed.current)?void 0:r.getTime()),s="x"===z?i:o,l=Math.abs(s)/a;if(Math.abs(s)>=20||l>.11){es(e_.current),null==(n=w.onDismiss)||n.call(w,w),X("x"===z?i>0?"right":"left":o>0?"down":"up"),eC(),en(!0),eo(!1);return}et(!1),q(null)},onPointerMove:t=>{var r,n,i,o;if(!eR.current||!ey||(null==(r=window.getSelection())?void 0:r.toString().length)>0)return;let a=t.clientY-eR.current.y,s=t.clientX-eR.current.x,l=null!=(n=e.swipeDirections)?n:function(e){let[t,r]=e.split("-"),n=[];return t&&n.push(t),r&&n.push(r),n}(I);!z&&(Math.abs(s)>1||Math.abs(a)>1)&&q(Math.abs(s)>Math.abs(a)?"x":"y");let u={x:0,y:0};"y"===z?(l.includes("top")||l.includes("bottom"))&&(l.includes("top")&&a<0||l.includes("bottom")&&a>0)&&(u.y=a):"x"===z&&(l.includes("left")||l.includes("right"))&&(l.includes("left")&&s<0||l.includes("right")&&s>0)&&(u.x=s),(Math.abs(u.x)>0||Math.abs(u.y)>0)&&eo(!0),null==(i=ef.current)||i.style.setProperty("--swipe-amount-x",`${u.x}px`),null==(o=ef.current)||o.style.setProperty("--swipe-amount-y",`${u.y}px`)}},ew&&!w.jsx?n.createElement("button",{"aria-label":W,"data-disabled":eA,"data-close-button":!0,onClick:eA||!ey?()=>{}:()=>{var e;eC(),null==(e=w.onDismiss)||e.call(w,w)},className:b(null==$?void 0:$.closeButton,null==(a=null==w?void 0:w.classNames)?void 0:a.closeButton)},null!=(l=null==H?void 0:H.close)?l:f):null,w.jsx||(0,n.isValidElement)(w.title)?w.jsx?w.jsx:"function"==typeof w.title?w.title():w.title:n.createElement(n.Fragment,null,em||w.icon||w.promise?n.createElement("div",{"data-icon":"",className:b(null==$?void 0:$.icon,null==(u=null==w?void 0:w.classNames)?void 0:u.icon)},w.promise||"loading"===w.type&&!w.icon?w.icon||function(){var e,t,r;return null!=H&&H.loading?n.createElement("div",{className:b(null==$?void 0:$.loader,null==(e=null==w?void 0:w.classNames)?void 0:e.loader,"sonner-loader"),"data-visible":"loading"===em},H.loading):B?n.createElement("div",{className:b(null==$?void 0:$.loader,null==(t=null==w?void 0:w.classNames)?void 0:t.loader,"sonner-loader"),"data-visible":"loading"===em},B):n.createElement(s,{className:b(null==$?void 0:$.loader,null==(r=null==w?void 0:w.classNames)?void 0:r.loader),visible:"loading"===em})}():null,"loading"!==w.type?w.icon||(null==H?void 0:H[em])||o(em):null):null,n.createElement("div",{"data-content":"",className:b(null==$?void 0:$.content,null==(c=null==w?void 0:w.classNames)?void 0:c.content)},n.createElement("div",{"data-title":"",className:b(null==$?void 0:$.title,null==(d=null==w?void 0:w.classNames)?void 0:d.title)},"function"==typeof w.title?w.title():w.title),w.description?n.createElement("div",{"data-description":"",className:b(L,ev,null==$?void 0:$.description,null==(h=null==w?void 0:w.classNames)?void 0:h.description)},"function"==typeof w.description?w.description():w.description):null),(0,n.isValidElement)(w.cancel)?w.cancel:w.cancel&&v(w.cancel)?n.createElement("button",{"data-button":!0,"data-cancel":!0,style:w.cancelButtonStyle||k,onClick:e=>{var t,r;v(w.cancel)&&ey&&(null==(r=(t=w.cancel).onClick)||r.call(t,e),eC())},className:b(null==$?void 0:$.cancelButton,null==(m=null==w?void 0:w.classNames)?void 0:m.cancelButton)},w.cancel.label):null,(0,n.isValidElement)(w.action)?w.action:w.action&&v(w.action)?n.createElement("button",{"data-button":!0,"data-action":!0,style:w.actionButtonStyle||D,onClick:e=>{var t,r;v(w.action)&&(null==(r=(t=w.action).onClick)||r.call(t,e),e.defaultPrevented||eC())},className:b(null==$?void 0:$.actionButton,null==(y=null==w?void 0:w.classNames)?void 0:y.actionButton)},w.action.label):null))},x=(0,n.forwardRef)(function(e,t){let{invert:r,position:o="bottom-right",hotkey:a=["altKey","KeyT"],expand:s,closeButton:l,className:u,offset:c,mobileOffset:d,theme:f="light",richColors:p,duration:h,style:y,visibleToasts:g=3,toastOptions:v,dir:b="ltr",gap:x=14,loadingIcon:E,icons:_,containerAriaLabel:P="Notifications",pauseWhenPageIsHidden:R}=e,[S,O]=n.useState([]),T=n.useMemo(()=>Array.from(new Set([o].concat(S.filter(e=>e.position).map(e=>e.position)))),[S,o]),[j,M]=n.useState([]),[A,C]=n.useState(!1),[k,D]=n.useState(!1),[N,L]=n.useState("system"!==f?f:"light"),F=n.useRef(null),I=a.join("+").replace(/Key/g,"").replace(/Digit/g,""),U=n.useRef(null),B=n.useRef(!1),V=n.useCallback(e=>{O(t=>{var r;return null!=(r=t.find(t=>t.id===e.id))&&r.delete||m.dismiss(e.id),t.filter(({id:t})=>t!==e.id)})},[]);return n.useEffect(()=>m.subscribe(e=>{if(e.dismiss)return void O(t=>t.map(t=>t.id===e.id?{...t,delete:!0}:t));setTimeout(()=>{i.flushSync(()=>{O(t=>{let r=t.findIndex(t=>t.id===e.id);return -1!==r?[...t.slice(0,r),{...t[r],...e},...t.slice(r+1)]:[e,...t]})})})}),[]),n.useEffect(()=>{if("system"!==f)return void L(f);"system"===f&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?L("dark"):L("light"))},[f]),n.useEffect(()=>{S.length<=1&&C(!1)},[S]),n.useEffect(()=>{let e=e=>{var t,r;a.every(t=>e[t]||e.code===t)&&(C(!0),null==(t=F.current)||t.focus()),"Escape"===e.code&&(document.activeElement===F.current||null!=(r=F.current)&&r.contains(document.activeElement))&&C(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[a]),n.useEffect(()=>{if(F.current)return()=>{U.current&&(U.current.focus({preventScroll:!0}),U.current=null,B.current=!1)}},[F.current]),n.createElement("section",{ref:t,"aria-label":`${P} ${I}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},T.map((t,i)=>{var o;let a,[f,m]=t.split("-");return S.length?n.createElement("ol",{key:t,dir:"auto"===b?"ltr":b,tabIndex:-1,ref:F,className:u,"data-sonner-toaster":!0,"data-theme":N,"data-y-position":f,"data-lifted":A&&S.length>1&&!s,"data-x-position":m,style:{"--front-toast-height":`${(null==(o=j[0])?void 0:o.height)||0}px`,"--width":"356px","--gap":`${x}px`,...y,...(a={},[c,d].forEach((e,t)=>{let r=1===t,n=r?"--mobile-offset":"--offset",i=r?"16px":"32px";function o(e){["top","right","bottom","left"].forEach(t=>{a[`${n}-${t}`]="number"==typeof e?`${e}px`:e})}"number"==typeof e||"string"==typeof e?o(e):"object"==typeof e?["top","right","bottom","left"].forEach(t=>{void 0===e[t]?a[`${n}-${t}`]=i:a[`${n}-${t}`]="number"==typeof e[t]?`${e[t]}px`:e[t]}):o(i)}),a)},onBlur:e=>{B.current&&!e.currentTarget.contains(e.relatedTarget)&&(B.current=!1,U.current&&(U.current.focus({preventScroll:!0}),U.current=null))},onFocus:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||B.current||(B.current=!0,U.current=e.relatedTarget)},onMouseEnter:()=>C(!0),onMouseMove:()=>C(!0),onMouseLeave:()=>{k||C(!1)},onDragEnd:()=>C(!1),onPointerDown:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||D(!0)},onPointerUp:()=>D(!1)},S.filter(e=>!e.position&&0===i||e.position===t).map((i,o)=>{var a,u;return n.createElement(w,{key:i.id,icons:_,index:o,toast:i,defaultRichColors:p,duration:null!=(a=null==v?void 0:v.duration)?a:h,className:null==v?void 0:v.className,descriptionClassName:null==v?void 0:v.descriptionClassName,invert:r,visibleToasts:g,closeButton:null!=(u=null==v?void 0:v.closeButton)?u:l,interacting:k,position:t,style:null==v?void 0:v.style,unstyled:null==v?void 0:v.unstyled,classNames:null==v?void 0:v.classNames,cancelButtonStyle:null==v?void 0:v.cancelButtonStyle,actionButtonStyle:null==v?void 0:v.actionButtonStyle,removeToast:V,toasts:S.filter(e=>e.position==i.position),heights:j.filter(e=>e.position==i.position),setHeights:M,expandByDefault:s,gap:x,loadingIcon:E,expanded:A,pauseWhenPageIsHidden:R,swipeDirections:e.swipeDirections})})):null}))})},52637:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},52716:(e,t,r)=>{"use strict";r.d(t,{po:()=>o,tn:()=>s,yT:()=>a});var n=r(12441),i=r(28830);let o=e=>1-Math.sin(Math.acos(e)),a=(0,i.G)(o),s=(0,n.V)(o)},52825:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{atLeastOneTask:function(){return i},scheduleImmediate:function(){return n},scheduleOnNextTick:function(){return r},waitAtLeastOneReactRenderTask:function(){return o}});let r=e=>{Promise.resolve().then(()=>{process.nextTick(e)})},n=e=>{setImmediate(e)};function i(){return new Promise(e=>n(e))}function o(){return new Promise(e=>setImmediate(e))}},52836:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let n=r(43210);function i(e,t){let r=(0,n.useRef)(null),i=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(r.current=o(e,n)),t&&(i.current=o(t,n))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53342:(e,t,r)=>{"use strict";r.d(t,{G:()=>c});var n=r(19331),i=r(72789),o=r(23671),a=r(15124),s=r(17135);function l(e,t){let r=(0,s.d)(t()),n=()=>r.set(t());return n(),(0,a.E)(()=>{let t=()=>o.Gt.preRender(n,!1,!0),r=e.map(e=>e.on("change",t));return()=>{r.forEach(e=>e()),(0,o.WG)(n)}}),r}var u=r(24342);function c(e,t,r,i){if("function"==typeof e){u.bt.current=[],e();let t=l(u.bt.current,e);return u.bt.current=void 0,t}let o="function"==typeof t?t:function(...e){let t=!Array.isArray(e[0]),r=t?0:-1,i=e[0+r],o=e[1+r],a=e[2+r],s=e[3+r],l=(0,n.G)(o,a,s);return t?l(i):l}(t,r,i);return Array.isArray(e)?d(e,o):d([e],([e])=>o(e))}function d(e,t){let r=(0,i.M)(()=>[]);return l(e,()=>{r.length=0;let n=e.length;for(let t=0;t<n;t++)r[t]=e[t].get();return t(r)})}},53532:(e,t,r)=>{"use strict";r.d(t,{B:()=>o});var n=r(69825),i=r(38681);function o({keyframes:e,velocity:t=0,power:r=.8,timeConstant:o=325,bounceDamping:a=10,bounceStiffness:s=500,modifyTarget:l,min:u,max:c,restDelta:d=.5,restSpeed:f}){let p,h,m=e[0],y={done:!1,value:m},g=e=>void 0!==u&&e<u||void 0!==c&&e>c,v=e=>void 0===u?c:void 0===c||Math.abs(u-e)<Math.abs(c-e)?u:c,b=r*t,w=m+b,x=void 0===l?w:l(w);x!==w&&(b=x-m);let E=e=>-b*Math.exp(-e/o),_=e=>x+E(e),P=e=>{let t=E(e),r=_(e);y.done=Math.abs(t)<=d,y.value=y.done?x:r},R=e=>{g(y.value)&&(p=e,h=(0,n.o)({keyframes:[y.value,v(y.value)],velocity:(0,i.Y)(_,e,y.value),damping:a,stiffness:s,restDelta:d,restSpeed:f}))};return R(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==p||(t=!0,P(e),R(e)),void 0!==p&&e>=p)?h.next(e-p):(t||P(e),y)}}}},54674:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return o}});let n=r(84949),i=r(19169),o=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:o}=(0,i.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+o};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54717:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Postpone:function(){return P},abortAndThrowOnSynchronousRequestDataAccess:function(){return E},abortOnSynchronousPlatformIOAccess:function(){return w},accessedDynamicData:function(){return C},annotateDynamicAccess:function(){return I},consumeDynamicAccess:function(){return k},createDynamicTrackingState:function(){return f},createDynamicValidationState:function(){return p},createHangingInputAbortSignal:function(){return F},createPostponedAbortSignal:function(){return L},formatDynamicAPIAccesses:function(){return D},getFirstDynamicReason:function(){return h},isDynamicPostpone:function(){return O},isPrerenderInterruptedError:function(){return A},markCurrentScopeAsDynamic:function(){return m},postponeWithTracking:function(){return R},throwIfDisallowedDynamic:function(){return G},throwToInterruptStaticGeneration:function(){return g},trackAllowedDynamicAccess:function(){return W},trackDynamicDataInDynamicRender:function(){return v},trackFallbackParamAccessed:function(){return y},trackSynchronousPlatformIOAccessInDev:function(){return x},trackSynchronousRequestDataAccessInDev:function(){return _},useDynamicRouteParams:function(){return U}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(43210)),i=r(22113),o=r(7797),a=r(63033),s=r(29294),l=r(18238),u=r(24207),c=r(52825),d="function"==typeof n.default.unstable_postpone;function f(e){return{isDebugDynamicAccesses:e,dynamicAccesses:[],syncDynamicExpression:void 0,syncDynamicErrorWithStack:null}}function p(){return{hasSuspendedDynamic:!1,hasDynamicMetadata:!1,hasDynamicViewport:!1,hasSyncDynamicErrors:!1,dynamicErrors:[]}}function h(e){var t;return null==(t=e.dynamicAccesses[0])?void 0:t.expression}function m(e,t,r){if((!t||"cache"!==t.type&&"unstable-cache"!==t.type)&&!e.forceDynamic&&!e.forceStatic){if(e.dynamicShouldError)throw Object.defineProperty(new o.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${r}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(t){if("prerender-ppr"===t.type)R(e.route,r,t.dynamicTracking);else if("prerender-legacy"===t.type){t.revalidate=0;let n=Object.defineProperty(new i.DynamicServerError(`Route ${e.route} couldn't be rendered statically because it used ${r}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:!1,configurable:!0});throw e.dynamicUsageDescription=r,e.dynamicUsageStack=n.stack,n}}}}function y(e,t){let r=a.workUnitAsyncStorage.getStore();r&&"prerender-ppr"===r.type&&R(e.route,t,r.dynamicTracking)}function g(e,t,r){let n=Object.defineProperty(new i.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function v(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function b(e,t,r){let n=M(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let i=r.dynamicTracking;i&&i.dynamicAccesses.push({stack:i.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}function w(e,t,r,n){let i=n.dynamicTracking;i&&null===i.syncDynamicErrorWithStack&&(i.syncDynamicExpression=t,i.syncDynamicErrorWithStack=r),b(e,t,n)}function x(e){e.prerenderPhase=!1}function E(e,t,r,n){if(!1===n.controller.signal.aborted){let i=n.dynamicTracking;i&&null===i.syncDynamicErrorWithStack&&(i.syncDynamicExpression=t,i.syncDynamicErrorWithStack=r,!0===n.validating&&(i.syncDynamicLogged=!0)),b(e,t,n)}throw M(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}let _=x;function P({reason:e,route:t}){let r=a.workUnitAsyncStorage.getStore();R(t,e,r&&"prerender-ppr"===r.type?r.dynamicTracking:null)}function R(e,t,r){N(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.default.unstable_postpone(S(e,t))}function S(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function O(e){return"object"==typeof e&&null!==e&&"string"==typeof e.message&&T(e.message)}function T(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===T(S("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});let j="NEXT_PRERENDER_INTERRUPTED";function M(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest=j,t}function A(e){return"object"==typeof e&&null!==e&&e.digest===j&&"name"in e&&"message"in e&&e instanceof Error}function C(e){return e.length>0}function k(e,t){return e.dynamicAccesses.push(...t.dynamicAccesses),e.dynamicAccesses}function D(e){return e.filter(e=>"string"==typeof e.stack&&e.stack.length>0).map(({expression:e,stack:t})=>(t=t.split("\n").slice(4).filter(e=>!(e.includes("node_modules/next/")||e.includes(" (<anonymous>)")||e.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${e}:
${t}`))}function N(){if(!d)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})}function L(e){N();let t=new AbortController;try{n.default.unstable_postpone(e)}catch(e){t.abort(e)}return t.signal}function F(e){let t=new AbortController;return e.cacheSignal?e.cacheSignal.inputReady().then(()=>{t.abort()}):(0,c.scheduleOnNextTick)(()=>t.abort()),t.signal}function I(e,t){let r=t.dynamicTracking;r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:e})}function U(e){let t=s.workAsyncStorage.getStore();if(t&&t.isStaticGeneration&&t.fallbackRouteParams&&t.fallbackRouteParams.size>0){let r=a.workUnitAsyncStorage.getStore();r&&("prerender"===r.type?n.default.use((0,l.makeHangingPromise)(r.renderSignal,e)):"prerender-ppr"===r.type?R(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&g(e,t,r))}}let B=/\n\s+at Suspense \(<anonymous>\)/,V=RegExp(`\\n\\s+at ${u.METADATA_BOUNDARY_NAME}[\\n\\s]`),$=RegExp(`\\n\\s+at ${u.VIEWPORT_BOUNDARY_NAME}[\\n\\s]`),H=RegExp(`\\n\\s+at ${u.OUTLET_BOUNDARY_NAME}[\\n\\s]`);function W(e,t,r,n,i){if(!H.test(t)){if(V.test(t)){r.hasDynamicMetadata=!0;return}if($.test(t)){r.hasDynamicViewport=!0;return}if(B.test(t)){r.hasSuspendedDynamic=!0;return}else if(n.syncDynamicErrorWithStack||i.syncDynamicErrorWithStack){r.hasSyncDynamicErrors=!0;return}else{let n=function(e,t){let r=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r.stack="Error: "+e+t,r}(`Route "${e}": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a "use cache" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`,t);r.dynamicErrors.push(n);return}}}function G(e,t,r,n){let i,a,s;if(r.syncDynamicErrorWithStack?(i=r.syncDynamicErrorWithStack,a=r.syncDynamicExpression,s=!0===r.syncDynamicLogged):n.syncDynamicErrorWithStack?(i=n.syncDynamicErrorWithStack,a=n.syncDynamicExpression,s=!0===n.syncDynamicLogged):(i=null,a=void 0,s=!1),t.hasSyncDynamicErrors&&i)throw s||console.error(i),new o.StaticGenBailoutError;let l=t.dynamicErrors;if(l.length){for(let e=0;e<l.length;e++)console.error(l[e]);throw new o.StaticGenBailoutError}if(!t.hasSuspendedDynamic){if(t.hasDynamicMetadata){if(i)throw console.error(i),Object.defineProperty(new o.StaticGenBailoutError(`Route "${e}" has a \`generateMetadata\` that could not finish rendering before ${a} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E608",enumerable:!1,configurable:!0});throw Object.defineProperty(new o.StaticGenBailoutError(`Route "${e}" has a \`generateMetadata\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateMetadata\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E534",enumerable:!1,configurable:!0})}else if(t.hasDynamicViewport){if(i)throw console.error(i),Object.defineProperty(new o.StaticGenBailoutError(`Route "${e}" has a \`generateViewport\` that could not finish rendering before ${a} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E573",enumerable:!1,configurable:!0});throw Object.defineProperty(new o.StaticGenBailoutError(`Route "${e}" has a \`generateViewport\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateViewport\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E590",enumerable:!1,configurable:!0})}}}},54838:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AppleWebAppMeta:function(){return h},BasicMeta:function(){return l},FacebookMeta:function(){return c},FormatDetectionMeta:function(){return p},ItunesMeta:function(){return u},PinterestMeta:function(){return d},VerificationMeta:function(){return m},ViewportMeta:function(){return s}});let n=r(37413),i=r(80407),o=r(4871),a=r(77341);function s({viewport:e}){return(0,i.MetaFilter)([(0,n.jsx)("meta",{charSet:"utf-8"}),(0,i.Meta)({name:"viewport",content:function(e){let t=null;if(e&&"object"==typeof e){for(let r in t="",o.ViewportMetaKeys)if(r in e){let n=e[r];"boolean"==typeof n?n=n?"yes":"no":n||"initialScale"!==r||(n=void 0),n&&(t&&(t+=", "),t+=`${o.ViewportMetaKeys[r]}=${n}`)}}return t}(e)}),...e.themeColor?e.themeColor.map(e=>(0,i.Meta)({name:"theme-color",content:e.color,media:e.media})):[],(0,i.Meta)({name:"color-scheme",content:e.colorScheme})])}function l({metadata:e}){var t,r,o;let s=e.manifest?(0,a.getOrigin)(e.manifest):void 0;return(0,i.MetaFilter)([null!==e.title&&e.title.absolute?(0,n.jsx)("title",{children:e.title.absolute}):null,(0,i.Meta)({name:"description",content:e.description}),(0,i.Meta)({name:"application-name",content:e.applicationName}),...e.authors?e.authors.map(e=>[e.url?(0,n.jsx)("link",{rel:"author",href:e.url.toString()}):null,(0,i.Meta)({name:"author",content:e.name})]):[],e.manifest?(0,n.jsx)("link",{rel:"manifest",href:e.manifest.toString(),crossOrigin:s||"preview"!==process.env.VERCEL_ENV?void 0:"use-credentials"}):null,(0,i.Meta)({name:"generator",content:e.generator}),(0,i.Meta)({name:"keywords",content:null==(t=e.keywords)?void 0:t.join(",")}),(0,i.Meta)({name:"referrer",content:e.referrer}),(0,i.Meta)({name:"creator",content:e.creator}),(0,i.Meta)({name:"publisher",content:e.publisher}),(0,i.Meta)({name:"robots",content:null==(r=e.robots)?void 0:r.basic}),(0,i.Meta)({name:"googlebot",content:null==(o=e.robots)?void 0:o.googleBot}),(0,i.Meta)({name:"abstract",content:e.abstract}),...e.archives?e.archives.map(e=>(0,n.jsx)("link",{rel:"archives",href:e})):[],...e.assets?e.assets.map(e=>(0,n.jsx)("link",{rel:"assets",href:e})):[],...e.bookmarks?e.bookmarks.map(e=>(0,n.jsx)("link",{rel:"bookmarks",href:e})):[],...e.pagination?[e.pagination.previous?(0,n.jsx)("link",{rel:"prev",href:e.pagination.previous}):null,e.pagination.next?(0,n.jsx)("link",{rel:"next",href:e.pagination.next}):null]:[],(0,i.Meta)({name:"category",content:e.category}),(0,i.Meta)({name:"classification",content:e.classification}),...e.other?Object.entries(e.other).map(([e,t])=>Array.isArray(t)?t.map(t=>(0,i.Meta)({name:e,content:t})):(0,i.Meta)({name:e,content:t})):[]])}function u({itunes:e}){if(!e)return null;let{appId:t,appArgument:r}=e,i=`app-id=${t}`;return r&&(i+=`, app-argument=${r}`),(0,n.jsx)("meta",{name:"apple-itunes-app",content:i})}function c({facebook:e}){if(!e)return null;let{appId:t,admins:r}=e;return(0,i.MetaFilter)([t?(0,n.jsx)("meta",{property:"fb:app_id",content:t}):null,...r?r.map(e=>(0,n.jsx)("meta",{property:"fb:admins",content:e})):[]])}function d({pinterest:e}){if(!e||!e.richPin)return null;let{richPin:t}=e;return(0,n.jsx)("meta",{property:"pinterest-rich-pin",content:t.toString()})}let f=["telephone","date","address","email","url"];function p({formatDetection:e}){if(!e)return null;let t="";for(let r of f)r in e&&(t&&(t+=", "),t+=`${r}=no`);return(0,n.jsx)("meta",{name:"format-detection",content:t})}function h({appleWebApp:e}){if(!e)return null;let{capable:t,title:r,startupImage:o,statusBarStyle:a}=e;return(0,i.MetaFilter)([t?(0,i.Meta)({name:"mobile-web-app-capable",content:"yes"}):null,(0,i.Meta)({name:"apple-mobile-web-app-title",content:r}),o?o.map(e=>(0,n.jsx)("link",{href:e.url,media:e.media,rel:"apple-touch-startup-image"})):null,a?(0,i.Meta)({name:"apple-mobile-web-app-status-bar-style",content:a}):null])}function m({verification:e}){return e?(0,i.MetaFilter)([(0,i.MultiMeta)({namePrefix:"google-site-verification",contents:e.google}),(0,i.MultiMeta)({namePrefix:"y_key",contents:e.yahoo}),(0,i.MultiMeta)({namePrefix:"yandex-verification",contents:e.yandex}),(0,i.MultiMeta)({namePrefix:"me",contents:e.me}),...e.other?Object.entries(e.other).map(([e,t])=>(0,i.MultiMeta)({namePrefix:e,contents:t})):[]]):null}},55211:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return i}});let n=""+r(86358).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function i(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56526:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{copyNextErrorCode:function(){return n},createDigestWithErrorCode:function(){return r},extractNextErrorCode:function(){return i}});let r=(e,t)=>"object"==typeof e&&null!==e&&"__NEXT_ERROR_CODE"in e?`${t}@${e.__NEXT_ERROR_CODE}`:t,n=(e,t)=>{let r=i(e);r&&"object"==typeof t&&null!==t&&Object.defineProperty(t,"__NEXT_ERROR_CODE",{value:r,enumerable:!1,configurable:!0})},i=e=>"object"==typeof e&&null!==e&&"__NEXT_ERROR_CODE"in e&&"string"==typeof e.__NEXT_ERROR_CODE?e.__NEXT_ERROR_CODE:"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest?e.digest.split("@").find(e=>e.startsWith("E")):void 0},56928:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return o}});let n=r(41500),i=r(33898);function o(e,t,r,o,a){let{tree:s,seedData:l,head:u,isRootRender:c}=o;if(null===l)return!1;if(c){let i=l[1];r.loading=l[3],r.rsc=i,r.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(e,r,t,s,l,u,a)}else r.rsc=t.rsc,r.prefetchRsc=t.prefetchRsc,r.parallelRoutes=new Map(t.parallelRoutes),r.loading=t.loading,(0,i.fillCacheWithNewSubTreeData)(e,r,t,o,a);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57211:(e,t,r)=>{"use strict";r.d(t,{X:()=>i,f:()=>n});let n=e=>1e3*e,i=e=>e/1e3},57225:(e,t,r)=>{"use strict";r.d(t,{i:()=>w});var n=r(41455);let i=(0,n.A)(.42,0,1,1),o=(0,n.A)(0,0,.58,1),a=(0,n.A)(.42,0,.58,1),s=e=>Array.isArray(e)&&"number"!=typeof e[0];var l=r(66244),u=r(83361),c=r(73685),d=r(60912),f=r(52716),p=r(74177);let h={linear:u.l,easeIn:i,easeInOut:a,easeOut:o,circIn:f.po,circInOut:f.tn,circOut:f.yT,backIn:d.dg,backInOut:d.ZZ,backOut:d.Sz,anticipate:c.b},m=e=>"string"==typeof e,y=e=>{if((0,p.D)(e)){(0,l.V)(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,i,o]=e;return(0,n.A)(t,r,i,o)}return m(e)?((0,l.V)(void 0!==h[e],`Invalid easing type '${e}'`),h[e]):e};var g=r(19331),v=r(64068),b=r(68028);function w({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){var i;let o=s(n)?n.map(y):y(n),l={done:!1,value:t[0]},u=(i=r&&r.length===t.length?r:function(e){let t=[0];return!function(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let i=(0,v.q)(0,t,n);e.push((0,b.k)(r,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),c=(0,g.G)(u,t,{ease:Array.isArray(o)?o:t.map(()=>o||a).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(l.value=c(t),l.done=t>=e,l)}}},57373:(e,t)=>{"use strict";function r(e,t){return e?e.replace(/%s/g,t):t}function n(e,t){let n,i="string"!=typeof e&&e&&"template"in e?e.template:null;return("string"==typeof e?n=r(t,e):e&&("default"in e&&(n=r(t,e.default)),"absolute"in e&&e.absolute&&(n=e.absolute)),e&&"string"!=typeof e)?{template:i,absolute:n||""}:{absolute:n||e||"",template:i}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveTitle",{enumerable:!0,get:function(){return n}})},57391:(e,t)=>{"use strict";function r(e,t){return void 0===t&&(t=!0),e.pathname+e.search+(t?e.hash:"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createHrefFromUrl",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59008:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createFetch:function(){return m},createFromNextReadableStream:function(){return y},fetchServerResponse:function(){return h},urlToUrlWithoutFlightMarker:function(){return d}});let n=r(91563),i=r(11264),o=r(11448),a=r(59154),s=r(74007),l=r(59880),u=r(38637),{createFromReadableStream:c}=r(19357);function d(e){let t=new URL(e,location.origin);return t.searchParams.delete(n.NEXT_RSC_UNION_QUERY),t}function f(e){return{flightData:d(e).toString(),canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1}}let p=new AbortController;async function h(e,t){let{flightRouterState:r,nextUrl:i,prefetchKind:o}=t,u={[n.RSC_HEADER]:"1",[n.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(r))};o===a.PrefetchKind.AUTO&&(u[n.NEXT_ROUTER_PREFETCH_HEADER]="1"),i&&(u[n.NEXT_URL]=i);try{var c;let t=o?o===a.PrefetchKind.TEMPORARY?"high":"low":"auto",r=await m(e,u,t,p.signal),i=d(r.url),h=r.redirected?i:void 0,g=r.headers.get("content-type")||"",v=!!(null==(c=r.headers.get("vary"))?void 0:c.includes(n.NEXT_URL)),b=!!r.headers.get(n.NEXT_DID_POSTPONE_HEADER),w=r.headers.get(n.NEXT_ROUTER_STALE_TIME_HEADER),x=null!==w?1e3*parseInt(w,10):-1;if(!g.startsWith(n.RSC_CONTENT_TYPE_HEADER)||!r.ok||!r.body)return e.hash&&(i.hash=e.hash),f(i.toString());let E=b?function(e){let t=e.getReader();return new ReadableStream({async pull(e){for(;;){let{done:r,value:n}=await t.read();if(!r){e.enqueue(n);continue}return}}})}(r.body):r.body,_=await y(E);if((0,l.getAppBuildId)()!==_.b)return f(r.url);return{flightData:(0,s.normalizeFlightData)(_.f),canonicalUrl:h,couldBeIntercepted:v,prerendered:_.S,postponed:b,staleTime:x}}catch(t){return p.signal.aborted||console.error("Failed to fetch RSC payload for "+e+". Falling back to browser navigation.",t),{flightData:e.toString(),canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1}}}function m(e,t,r,n){let i=new URL(e);return(0,u.setCacheBustingSearchParam)(i,t),fetch(i,{credentials:"same-origin",headers:t,priority:r||void 0,signal:n})}function y(e){return c(e,{callServer:i.callServer,findSourceMapURL:o.findSourceMapURL})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59154:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_HMR_REFRESH:function(){return s},ACTION_NAVIGATE:function(){return n},ACTION_PREFETCH:function(){return a},ACTION_REFRESH:function(){return r},ACTION_RESTORE:function(){return i},ACTION_SERVER_ACTION:function(){return l},ACTION_SERVER_PATCH:function(){return o},PrefetchCacheEntryStatus:function(){return c},PrefetchKind:function(){return u}});let r="refresh",n="navigate",i="restore",o="server-patch",a="prefetch",s="hmr-refresh",l="server-action";var u=function(e){return e.AUTO="auto",e.FULL="full",e.TEMPORARY="temporary",e}({}),c=function(e){return e.fresh="fresh",e.reusable="reusable",e.expired="expired",e.stale="stale",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59435:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return o}});let n=r(70642);function i(e){return void 0!==e}function o(e,t){var r,o;let a=null==(r=t.shouldScroll)||r,s=e.nextUrl;if(i(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?s=r:s||(s=e.canonicalUrl)}return{canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!a&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:a?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:a?null!=(o=null==t?void 0:t.scrollableSegments)?o:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59521:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createMetadataComponents",{enumerable:!0,get:function(){return g}});let n=r(37413),i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=y(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(61120)),o=r(54838),a=r(36070),s=r(11804),l=r(14114),u=r(42706),c=r(80407),d=r(8704),f=r(67625),p=r(12089),h=r(52637),m=r(83091);function y(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(y=function(e){return e?r:t})(e)}function g({tree:e,parsedQuery:t,metadataContext:r,getDynamicParamFromSegment:o,appUsingSizeAdjustment:a,errorType:s,workStore:l,MetadataBoundary:u,ViewportBoundary:c,serveStreamingMetadata:y}){let g=(0,m.createServerSearchParamsForMetadata)(t,l);function b(){return E(e,g,o,l,s)}async function x(){try{return await b()}catch(t){if(!s&&(0,d.isHTTPAccessFallbackError)(t))try{return await P(e,g,o,l)}catch{}return null}}function _(){return v(e,g,o,r,l,s)}async function R(){let t,n=null;try{return{metadata:t=await _(),error:null,digest:void 0}}catch(i){if(n=i,!s&&(0,d.isHTTPAccessFallbackError)(i))try{return{metadata:t=await w(e,g,o,r,l),error:n,digest:null==n?void 0:n.digest}}catch(e){if(n=e,y&&(0,h.isPostpone)(e))throw e}if(y&&(0,h.isPostpone)(i))throw i;return{metadata:t,error:n,digest:null==n?void 0:n.digest}}}async function S(){let e=R();return y?(0,n.jsx)(i.Suspense,{fallback:null,children:(0,n.jsx)(p.AsyncMetadata,{promise:e})}):(await e).metadata}async function O(){y||await _()}async function T(){await b()}return x.displayName=f.VIEWPORT_BOUNDARY_NAME,S.displayName=f.METADATA_BOUNDARY_NAME,{ViewportTree:function(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c,{children:(0,n.jsx)(x,{})}),a?(0,n.jsx)("meta",{name:"next-size-adjust",content:""}):null]})},MetadataTree:function(){return(0,n.jsx)(u,{children:(0,n.jsx)(S,{})})},getViewportReady:T,getMetadataReady:O,StreamingMetadataOutlet:function(){return y?(0,n.jsx)(p.AsyncMetadataOutlet,{promise:R()}):null}}}let v=(0,i.cache)(b);async function b(e,t,r,n,i,o){return S(e,t,r,n,i,"redirect"===o?void 0:o)}let w=(0,i.cache)(x);async function x(e,t,r,n,i){return S(e,t,r,n,i,"not-found")}let E=(0,i.cache)(_);async function _(e,t,r,n,i){return O(e,t,r,n,"redirect"===i?void 0:i)}let P=(0,i.cache)(R);async function R(e,t,r,n){return O(e,t,r,n,"not-found")}async function S(e,t,r,d,f,p){var h;let m=(h=await (0,u.resolveMetadata)(e,t,p,r,f,d),(0,c.MetaFilter)([(0,o.BasicMeta)({metadata:h}),(0,a.AlternatesMetadata)({alternates:h.alternates}),(0,o.ItunesMeta)({itunes:h.itunes}),(0,o.FacebookMeta)({facebook:h.facebook}),(0,o.PinterestMeta)({pinterest:h.pinterest}),(0,o.FormatDetectionMeta)({formatDetection:h.formatDetection}),(0,o.VerificationMeta)({verification:h.verification}),(0,o.AppleWebAppMeta)({appleWebApp:h.appleWebApp}),(0,s.OpenGraphMetadata)({openGraph:h.openGraph}),(0,s.TwitterMetadata)({twitter:h.twitter}),(0,s.AppLinksMeta)({appLinks:h.appLinks}),(0,l.IconsMetadata)({icons:h.icons})]));return(0,n.jsx)(n.Fragment,{children:m.map((e,t)=>(0,i.cloneElement)(e,{key:t}))})}async function O(e,t,r,a,s){var l;let d=(l=await (0,u.resolveViewport)(e,t,s,r,a),(0,c.MetaFilter)([(0,o.ViewportMeta)({viewport:l})]));return(0,n.jsx)(n.Fragment,{children:d.map((e,t)=>(0,i.cloneElement)(e,{key:t}))})}},59656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>i});var n=0;function i(e){return"__private_"+n+++"_"+e}},59880:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getAppBuildId:function(){return i},setAppBuildId:function(){return n}});let r="";function n(e){r=e}function i(){return r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},60687:(e,t,r)=>{"use strict";e.exports=r(94041).vendored["react-ssr"].ReactJsxRuntime},60824:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createParamsFromClient:function(){return u},createPrerenderParamsForClientSegment:function(){return p},createServerParamsForMetadata:function(){return c},createServerParamsForRoute:function(){return d},createServerParamsForServerSegment:function(){return f}}),r(83717);let n=r(54717),i=r(63033),o=r(75539),a=r(84627),s=r(18238),l=r(14768);function u(e,t){var r;let n=i.workUnitAsyncStorage.getStore();if(n)switch(n.type){case"prerender":case"prerender-ppr":case"prerender-legacy":return h(e,t,n)}return r=0,y(e)}r(52825);let c=f;function d(e,t){var r;let n=i.workUnitAsyncStorage.getStore();if(n)switch(n.type){case"prerender":case"prerender-ppr":case"prerender-legacy":return h(e,t,n)}return r=0,y(e)}function f(e,t){var r;let n=i.workUnitAsyncStorage.getStore();if(n)switch(n.type){case"prerender":case"prerender-ppr":case"prerender-legacy":return h(e,t,n)}return r=0,y(e)}function p(e,t){let r=i.workUnitAsyncStorage.getStore();if(r&&"prerender"===r.type){let n=t.fallbackRouteParams;if(n){for(let t in e)if(n.has(t))return(0,s.makeHangingPromise)(r.renderSignal,"`params`")}}return Promise.resolve(e)}function h(e,t,r){let i=t.fallbackRouteParams;if(i){let o=!1;for(let t in e)if(i.has(t)){o=!0;break}if(o)return"prerender"===r.type?function(e,t,r){let i=m.get(e);if(i)return i;let o=(0,s.makeHangingPromise)(r.renderSignal,"`params`");return m.set(e,o),Object.keys(e).forEach(e=>{a.wellKnownProperties.has(e)||Object.defineProperty(o,e,{get(){let i=(0,a.describeStringPropertyAccess)("params",e),o=b(t,i);(0,n.abortAndThrowOnSynchronousRequestDataAccess)(t,i,o,r)},set(t){Object.defineProperty(o,e,{value:t,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0})}),o}(e,t.route,r):function(e,t,r,i){let o=m.get(e);if(o)return o;let s={...e},l=Promise.resolve(s);return m.set(e,l),Object.keys(e).forEach(o=>{a.wellKnownProperties.has(o)||(t.has(o)?(Object.defineProperty(s,o,{get(){let e=(0,a.describeStringPropertyAccess)("params",o);"prerender-ppr"===i.type?(0,n.postponeWithTracking)(r.route,e,i.dynamicTracking):(0,n.throwToInterruptStaticGeneration)(e,r,i)},enumerable:!0}),Object.defineProperty(l,o,{get(){let e=(0,a.describeStringPropertyAccess)("params",o);"prerender-ppr"===i.type?(0,n.postponeWithTracking)(r.route,e,i.dynamicTracking):(0,n.throwToInterruptStaticGeneration)(e,r,i)},set(e){Object.defineProperty(l,o,{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0})):l[o]=e[o])}),l}(e,i,t,r)}return y(e)}let m=new WeakMap;function y(e){let t=m.get(e);if(t)return t;let r=Promise.resolve(e);return m.set(e,r),Object.keys(e).forEach(t=>{a.wellKnownProperties.has(t)||(r[t]=e[t])}),r}let g=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(b),v=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t,r){let n=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${n}used ${t}. \`params\` should be awaited before using its properties. The following properties were not available through enumeration because they conflict with builtin property names: ${function(e){switch(e.length){case 0:throw Object.defineProperty(new o.InvariantError("Expected describeListOfPropertyNames to be called with a non-empty list of strings."),"__NEXT_ERROR_CODE",{value:"E531",enumerable:!1,configurable:!0});case 1:return`\`${e[0]}\``;case 2:return`\`${e[0]}\` and \`${e[1]}\``;default:{let t="";for(let r=0;r<e.length-1;r++)t+=`\`${e[r]}\`, `;return t+`, and \`${e[e.length-1]}\``}}}(r)}. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E482",enumerable:!1,configurable:!0})});function b(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`params\` should be awaited before using its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E307",enumerable:!1,configurable:!0})}},60912:(e,t,r)=>{"use strict";r.d(t,{Sz:()=>a,ZZ:()=>l,dg:()=>s});var n=r(41455),i=r(12441),o=r(28830);let a=(0,n.A)(.33,1.53,.69,.99),s=(0,o.G)(a),l=(0,i.V)(s)},61068:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Postpone",{enumerable:!0,get:function(){return n.Postpone}});let n=r(84971)},61794:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return o}});let n=r(79289),i=r(26736);function o(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,i.hasBasePath)(r.pathname)}catch(e){return!1}}},62713:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createFlightReactServerErrorHandler:function(){return p},createHTMLErrorHandler:function(){return m},createHTMLReactServerErrorHandler:function(){return h},getDigestForWellKnownError:function(){return f},isUserLandError:function(){return y}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(67839)),i=r(7308),o=r(81289),a=r(42471),s=r(51846),l=r(98479),u=r(31162),c=r(35715),d=r(56526);function f(e){if((0,s.isBailoutToCSRError)(e)||(0,u.isNextRouterError)(e)||(0,l.isDynamicServerError)(e))return e.digest}function p(e,t){return r=>{if("string"==typeof r)return(0,n.default)(r).toString();if((0,a.isAbortError)(r))return;let s=f(r);if(s)return s;let l=(0,c.getProperError)(r);l.digest||(l.digest=(0,n.default)(l.message+l.stack||"").toString()),e&&(0,i.formatServerError)(l);let u=(0,o.getTracer)().getActiveScopeSpan();return u&&(u.recordException(l),u.setStatus({code:o.SpanStatusCode.ERROR,message:l.message})),t(l),(0,d.createDigestWithErrorCode)(r,l.digest)}}function h(e,t,r,s,l){return u=>{var p;if("string"==typeof u)return(0,n.default)(u).toString();if((0,a.isAbortError)(u))return;let h=f(u);if(h)return h;let m=(0,c.getProperError)(u);if(m.digest||(m.digest=(0,n.default)(m.message+(m.stack||"")).toString()),r.has(m.digest)||r.set(m.digest,m),e&&(0,i.formatServerError)(m),!(t&&(null==m||null==(p=m.message)?void 0:p.includes("The specific message is omitted in production builds to avoid leaking sensitive details.")))){let e=(0,o.getTracer)().getActiveScopeSpan();e&&(e.recordException(m),e.setStatus({code:o.SpanStatusCode.ERROR,message:m.message})),s||null==l||l(m)}return(0,d.createDigestWithErrorCode)(u,m.digest)}}function m(e,t,r,s,l,u){return(p,h)=>{var m;let y=!0;if(s.push(p),(0,a.isAbortError)(p))return;let g=f(p);if(g)return g;let v=(0,c.getProperError)(p);if(v.digest?r.has(v.digest)&&(p=r.get(v.digest),y=!1):v.digest=(0,n.default)(v.message+((null==h?void 0:h.componentStack)||v.stack||"")).toString(),e&&(0,i.formatServerError)(v),!(t&&(null==v||null==(m=v.message)?void 0:m.includes("The specific message is omitted in production builds to avoid leaking sensitive details.")))){let e=(0,o.getTracer)().getActiveScopeSpan();e&&(e.recordException(v),e.setStatus({code:o.SpanStatusCode.ERROR,message:v.message})),!l&&y&&u(v,h)}return(0,d.createDigestWithErrorCode)(p,v.digest)}}function y(e){return!(0,a.isAbortError)(e)&&!(0,s.isBailoutToCSRError)(e)&&!(0,u.isNextRouterError)(e)}},62763:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MetadataBoundary:function(){return o},OutletBoundary:function(){return s},ViewportBoundary:function(){return a}});let n=r(24207),i={[n.METADATA_BOUNDARY_NAME]:function(e){let{children:t}=e;return t},[n.VIEWPORT_BOUNDARY_NAME]:function(e){let{children:t}=e;return t},[n.OUTLET_BOUNDARY_NAME]:function(e){let{children:t}=e;return t}},o=i[n.METADATA_BOUNDARY_NAME.slice(0)],a=i[n.VIEWPORT_BOUNDARY_NAME.slice(0)],s=i[n.OUTLET_BOUNDARY_NAME.slice(0)];("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63690:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return h},dispatchNavigateAction:function(){return g},dispatchTraverseAction:function(){return v},getCurrentAppRouterState:function(){return m},publicAppRouterInstance:function(){return b}});let n=r(59154),i=r(8830),o=r(43210),a=r(91992);r(50593);let s=r(19129),l=r(96127),u=r(89752),c=r(75076),d=r(73406);function f(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?p({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function p(e){let{actionQueue:t,action:r,setState:n}=e,i=t.state;t.pending=r;let o=r.payload,s=t.action(i,o);function l(e){r.discarded||(t.state=e,f(t,n),r.resolve(e))}(0,a.isThenable)(s)?s.then(l,e=>{f(t,n),r.reject(e)}):l(s)}function h(e,t){let r={state:e,dispatch:(e,t)=>(function(e,t,r){let i={resolve:r,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{i={resolve:e,reject:t}});(0,o.startTransition)(()=>{r(e)})}let a={payload:t,next:null,resolve:i.resolve,reject:i.reject};null===e.pending?(e.last=a,p({actionQueue:e,action:a,setState:r})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,a.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),p({actionQueue:e,action:a,setState:r})):(null!==e.last&&(e.last.next=a),e.last=a)})(r,e,t),action:async(e,t)=>(0,i.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return r}function m(){return null}function y(){return null}function g(e,t,r,i){let o=new URL((0,l.addBasePath)(e),location.href);(0,d.setLinkForCurrentNavigation)(i);(0,s.dispatchAppRouterAction)({type:n.ACTION_NAVIGATE,url:o,isExternalUrl:(0,u.isExternalURL)(o),locationSearch:location.search,shouldScroll:r,navigateType:t,allowAliasing:!0})}function v(e,t){(0,s.dispatchAppRouterAction)({type:n.ACTION_RESTORE,url:new URL(e),tree:t})}let b={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),i=(0,u.createPrefetchURL)(e);if(null!==i){var o;(0,c.prefetchReducer)(r.state,{type:n.ACTION_PREFETCH,url:i,kind:null!=(o=null==t?void 0:t.kind)?o:n.PrefetchKind.FULL})}},replace:(e,t)=>{(0,o.startTransition)(()=>{var r;g(e,"replace",null==(r=null==t?void 0:t.scroll)||r,null)})},push:(e,t)=>{(0,o.startTransition)(()=>{var r;g(e,"push",null==(r=null==t?void 0:t.scroll)||r,null)})},refresh:()=>{(0,o.startTransition)(()=>{(0,s.dispatchAppRouterAction)({type:n.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63830:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});class n{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}},64068:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});let n=(e,t,r)=>{let n=t-e;return 0===n?1:(r-e)/n}},65284:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let n=r(37413),i=r(1765);function o(){return(0,n.jsx)(i.HTTPAccessErrorFallback,{status:401,message:"You're not authorized to access this page."})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65551:(e,t,r)=>{"use strict";r.d(t,{i:()=>s});var n,i=r(43210),o=r(66156),a=(n||(n=r.t(i,2)))[" useInsertionEffect ".trim().toString()]||o.N;function s({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[o,s,l]=function({defaultProp:e,onChange:t}){let[r,n]=i.useState(e),o=i.useRef(r),s=i.useRef(t);return a(()=>{s.current=t},[t]),i.useEffect(()=>{o.current!==r&&(s.current?.(r),o.current=r)},[r,o]),[r,n,s]}({defaultProp:t,onChange:r}),u=void 0!==e,c=u?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,n])}return[c,i.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else s(t)},[u,e,s,l])]}Symbol("RADIX:SYNC_STATE")},65773:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return l.ReadonlyURLSearchParams},RedirectType:function(){return l.RedirectType},ServerInsertedHTMLContext:function(){return u.ServerInsertedHTMLContext},forbidden:function(){return l.forbidden},notFound:function(){return l.notFound},permanentRedirect:function(){return l.permanentRedirect},redirect:function(){return l.redirect},unauthorized:function(){return l.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow},useParams:function(){return h},usePathname:function(){return f},useRouter:function(){return p},useSearchParams:function(){return d},useSelectedLayoutSegment:function(){return y},useSelectedLayoutSegments:function(){return m},useServerInsertedHTML:function(){return u.useServerInsertedHTML}});let n=r(43210),i=r(22142),o=r(10449),a=r(17388),s=r(83913),l=r(80178),u=r(39695),c=r(54717).useDynamicRouteParams;function d(){let e=(0,n.useContext)(o.SearchParamsContext),t=(0,n.useMemo)(()=>e?new l.ReadonlyURLSearchParams(e):null,[e]);{let{bailoutToClientRendering:e}=r(9608);e("useSearchParams()")}return t}function f(){return null==c||c("usePathname()"),(0,n.useContext)(o.PathnameContext)}function p(){let e=(0,n.useContext)(i.AppRouterContext);if(null===e)throw Object.defineProperty(Error("invariant expected app router to be mounted"),"__NEXT_ERROR_CODE",{value:"E238",enumerable:!1,configurable:!0});return e}function h(){return null==c||c("useParams()"),(0,n.useContext)(o.PathParamsContext)}function m(e){void 0===e&&(e="children"),null==c||c("useSelectedLayoutSegments()");let t=(0,n.useContext)(i.LayoutRouterContext);return t?function e(t,r,n,i){let o;if(void 0===n&&(n=!0),void 0===i&&(i=[]),n)o=t[1][r];else{var l;let e=t[1];o=null!=(l=e.children)?l:Object.values(e)[0]}if(!o)return i;let u=o[0],c=(0,a.getSegmentValue)(u);return!c||c.startsWith(s.PAGE_SEGMENT_KEY)?i:(i.push(c),e(o,r,!1,i))}(t.parentTree,e):null}function y(e){void 0===e&&(e="children"),null==c||c("useSelectedLayoutSegment()");let t=m(e);if(!t||0===t.length)return null;let r="children"===e?t[0]:t[t.length-1];return r===s.DEFAULT_SEGMENT_KEY?null:r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65951:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[o,a]=r,[s,l]=t;return(0,i.matchSegment)(s,o)?!(t.length<=2)&&e((0,n.getNextFlightSegmentPath)(t),a[l]):!!Array.isArray(s)}}});let n=r(74007),i=r(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return h},listenForDynamicRequest:function(){return p},startPPRNavigation:function(){return u},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],i=t.parallelRoutes,a=new Map(i);for(let t in n){let r=n[t],s=r[0],l=(0,o.createRouterCacheKey)(s),u=i.get(t);if(void 0!==u){let n=u.get(l);if(void 0!==n){let i=e(n,r),o=new Map(u);o.set(l,i),a.set(t,o)}}}let s=t.rsc,l=g(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:l?t.prefetchHead:[null,null],prefetchRsc:l?t.prefetchRsc:null,loading:t.loading,parallelRoutes:a,navigatedAt:t.navigatedAt}}}});let n=r(83913),i=r(14077),o=r(33123),a=r(2030),s=r(5334),l={route:null,node:null,dynamicRequestTree:null,children:null};function u(e,t,r,a,s,u,f,p,h){return function e(t,r,a,s,u,f,p,h,m,y,g){let v=a[1],b=s[1],w=null!==f?f[2]:null;u||!0===s[4]&&(u=!0);let x=r.parallelRoutes,E=new Map(x),_={},P=null,R=!1,S={};for(let r in b){let a,s=b[r],d=v[r],f=x.get(r),O=null!==w?w[r]:null,T=s[0],j=y.concat([r,T]),M=(0,o.createRouterCacheKey)(T),A=void 0!==d?d[0]:void 0,C=void 0!==f?f.get(M):void 0;if(null!==(a=T===n.DEFAULT_SEGMENT_KEY?void 0!==d?{route:d,node:null,dynamicRequestTree:null,children:null}:c(t,d,s,C,u,void 0!==O?O:null,p,h,j,g):m&&0===Object.keys(s[1]).length?c(t,d,s,C,u,void 0!==O?O:null,p,h,j,g):void 0!==d&&void 0!==A&&(0,i.matchSegment)(T,A)&&void 0!==C&&void 0!==d?e(t,C,d,s,u,O,p,h,m,j,g):c(t,d,s,C,u,void 0!==O?O:null,p,h,j,g))){if(null===a.route)return l;null===P&&(P=new Map),P.set(r,a);let e=a.node;if(null!==e){let t=new Map(f);t.set(M,e),E.set(r,t)}let t=a.route;_[r]=t;let n=a.dynamicRequestTree;null!==n?(R=!0,S[r]=n):S[r]=t}else _[r]=s,S[r]=s}if(null===P)return null;let O={lazyData:null,rsc:r.rsc,prefetchRsc:r.prefetchRsc,head:r.head,prefetchHead:r.prefetchHead,loading:r.loading,parallelRoutes:E,navigatedAt:t};return{route:d(s,_),node:O,dynamicRequestTree:R?d(s,S):null,children:P}}(e,t,r,a,!1,s,u,f,p,[],h)}function c(e,t,r,n,i,u,c,p,h,m){return!i&&(void 0===t||(0,a.isNavigatingToNewRootLayout)(t,r))?l:function e(t,r,n,i,a,l,u,c){let p,h,m,y,g=r[1],v=0===Object.keys(g).length;if(void 0!==n&&n.navigatedAt+s.DYNAMIC_STALETIME_MS>t)p=n.rsc,h=n.loading,m=n.head,y=n.navigatedAt;else if(null===i)return f(t,r,null,a,l,u,c);else if(p=i[1],h=i[3],m=v?a:null,y=t,i[4]||l&&v)return f(t,r,i,a,l,u,c);let b=null!==i?i[2]:null,w=new Map,x=void 0!==n?n.parallelRoutes:null,E=new Map(x),_={},P=!1;if(v)c.push(u);else for(let r in g){let n=g[r],i=null!==b?b[r]:null,s=null!==x?x.get(r):void 0,d=n[0],f=u.concat([r,d]),p=(0,o.createRouterCacheKey)(d),h=e(t,n,void 0!==s?s.get(p):void 0,i,a,l,f,c);w.set(r,h);let m=h.dynamicRequestTree;null!==m?(P=!0,_[r]=m):_[r]=n;let y=h.node;if(null!==y){let e=new Map;e.set(p,y),E.set(r,e)}}return{route:r,node:{lazyData:null,rsc:p,prefetchRsc:null,head:m,prefetchHead:null,loading:h,parallelRoutes:E,navigatedAt:y},dynamicRequestTree:P?d(r,_):null,children:w}}(e,r,n,u,c,p,h,m)}function d(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}function f(e,t,r,n,i,a,s){let l=d(t,t[1]);return l[3]="refetch",{route:t,node:function e(t,r,n,i,a,s,l){let u=r[1],c=null!==n?n[2]:null,d=new Map;for(let r in u){let n=u[r],f=null!==c?c[r]:null,p=n[0],h=s.concat([r,p]),m=(0,o.createRouterCacheKey)(p),y=e(t,n,void 0===f?null:f,i,a,h,l),g=new Map;g.set(m,y),d.set(r,g)}let f=0===d.size;f&&l.push(s);let p=null!==n?n[1]:null,h=null!==n?n[3]:null;return{lazyData:null,parallelRoutes:d,prefetchRsc:void 0!==p?p:null,prefetchHead:f?i:[null,null],loading:void 0!==h?h:null,rsc:v(),head:f?v():null,navigatedAt:t}}(e,t,r,n,i,a,s),dynamicRequestTree:l,children:null}}function p(e,t){t.then(t=>{let{flightData:r}=t;if("string"!=typeof r){for(let t of r){let{segmentPath:r,tree:n,seedData:a,head:s}=t;a&&function(e,t,r,n,a){let s=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],o=s.children;if(null!==o){let e=o.get(r);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(n,t)){s=e;continue}}}return}!function e(t,r,n,a){if(null===t.dynamicRequestTree)return;let s=t.children,l=t.node;if(null===s){null!==l&&(function e(t,r,n,a,s){let l=r[1],u=n[1],c=a[2],d=t.parallelRoutes;for(let t in l){let r=l[t],n=u[t],a=c[t],f=d.get(t),p=r[0],h=(0,o.createRouterCacheKey)(p),y=void 0!==f?f.get(h):void 0;void 0!==y&&(void 0!==n&&(0,i.matchSegment)(p,n[0])&&null!=a?e(y,r,n,a,s):m(r,y,null))}let f=t.rsc,p=a[1];null===f?t.rsc=p:g(f)&&f.resolve(p);let h=t.head;g(h)&&h.resolve(s)}(l,t.route,r,n,a),t.dynamicRequestTree=null);return}let u=r[1],c=n[2];for(let t in r){let r=u[t],n=c[t],o=s.get(t);if(void 0!==o){let t=o.route[0];if((0,i.matchSegment)(r[0],t)&&null!=n)return e(o,r,n,a)}}}(s,r,n,a)}(e,r,n,a,s)}h(e,null)}},t=>{h(e,t)})}function h(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)m(e.route,r,t);else for(let e of n.values())h(e,t);e.dynamicRequestTree=null}function m(e,t,r){let n=e[1],i=t.parallelRoutes;for(let e in n){let t=n[e],a=i.get(e);if(void 0===a)continue;let s=t[0],l=(0,o.createRouterCacheKey)(s),u=a.get(l);void 0!==u&&m(t,u,r)}let a=t.rsc;g(a)&&(null===r?a.resolve(null):a.reject(r));let s=t.head;g(s)&&s.resolve(null)}let y=Symbol();function g(e){return e&&e.tag===y}function v(){let e,t,r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=y,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66156:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var n=r(43210),i=globalThis?.document?n.useLayoutEffect:()=>{}},66244:(e,t,r)=>{"use strict";r.d(t,{$:()=>n,V:()=>i});let n=()=>{},i=()=>{}},66483:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{resolveImages:function(){return u},resolveOpenGraph:function(){return d},resolveTwitter:function(){return p}});let n=r(77341),i=r(96258),o=r(57373),a=r(77359),s=r(21709),l={article:["authors","tags"],song:["albums","musicians"],playlist:["albums","musicians"],radio:["creators"],video:["actors","directors","writers","tags"],basic:["emails","phoneNumbers","faxNumbers","alternateLocale","audio","videos"]};function u(e,t,r){let o=(0,n.resolveAsArrayOrUndefined)(e);if(!o)return o;let l=[];for(let e of o){let n=function(e,t,r){if(!e)return;let n=(0,i.isStringOrURL)(e),o=n?e:e.url;if(!o)return;let l=!!process.env.VERCEL;if("string"==typeof o&&!(0,a.isFullStringUrl)(o)&&(!t||r)){let e=(0,i.getSocialImageMetadataBaseFallback)(t);l||t||(0,s.warnOnce)(`metadataBase property in metadata export is not set for resolving social open graph or twitter images, using "${e.origin}". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase`),t=e}return n?{url:(0,i.resolveUrl)(o,t)}:{...e,url:(0,i.resolveUrl)(o,t)}}(e,t,r);n&&l.push(n)}return l}let c={article:l.article,book:l.article,"music.song":l.song,"music.album":l.song,"music.playlist":l.playlist,"music.radio_station":l.radio,"video.movie":l.video,"video.episode":l.video},d=(e,t,r,a)=>{if(!e)return null;let s={...e,title:(0,o.resolveTitle)(e.title,a)};return!function(e,i){var o;for(let t of(o=i&&"type"in i?i.type:void 0)&&o in c?c[o].concat(l.basic):l.basic)if(t in i&&"url"!==t){let r=i[t];e[t]=r?(0,n.resolveArray)(r):null}e.images=u(i.images,t,r.isStaticMetadataRouteFile)}(s,e),s.url=e.url?(0,i.resolveAbsoluteUrlWithPathname)(e.url,t,r):null,s},f=["site","siteId","creator","creatorId","description"],p=(e,t,r,i)=>{var a;if(!e)return null;let s="card"in e?e.card:void 0,l={...e,title:(0,o.resolveTitle)(e.title,i)};for(let t of f)l[t]=e[t]||null;if(l.images=u(e.images,t,r.isStaticMetadataRouteFile),s=s||((null==(a=l.images)?void 0:a.length)?"summary_large_image":"summary"),l.card=s,"card"in l)switch(l.card){case"player":l.players=(0,n.resolveAsArrayOrUndefined)(l.players)||[];break;case"app":l.app=l.app||{}}return l}},67086:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectBoundary:function(){return d},RedirectErrorBoundary:function(){return c}});let n=r(40740),i=r(60687),o=n._(r(43210)),a=r(65773),s=r(36875),l=r(97860);function u(e){let{redirect:t,reset:r,redirectType:n}=e,i=(0,a.useRouter)();return(0,o.useEffect)(()=>{o.default.startTransition(()=>{n===l.RedirectType.push?i.push(t,{}):i.replace(t,{}),r()})},[t,n,r,i]),null}class c extends o.default.Component{static getDerivedStateFromError(e){if((0,l.isRedirectError)(e))return{redirect:(0,s.getURLFromRedirectError)(e),redirectType:(0,s.getRedirectTypeFromError)(e)};throw e}render(){let{redirect:e,redirectType:t}=this.state;return null!==e&&null!==t?(0,i.jsx)(u,{redirect:e,redirectType:t,reset:()=>this.setState({redirect:null})}):this.props.children}constructor(e){super(e),this.state={redirect:null,redirectType:null}}}function d(e){let{children:t}=e,r=(0,a.useRouter)();return(0,i.jsx)(c,{router:r,children:t})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67839:e=>{(()=>{"use strict";var t={328:e=>{e.exports=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var o=r[e]={exports:{}},a=!0;try{t[e](o,o.exports,n),a=!1}finally{a&&delete r[e]}return o.exports}n.ab=__dirname+"/",e.exports=n(328)})()},68028:(e,t,r)=>{"use strict";r.d(t,{k:()=>n});let n=(e,t,r)=>e+(t-e)*r},68214:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasInterceptionRouteInCurrentTree",{enumerable:!0,get:function(){return function e(t){let[r,i]=t;if(Array.isArray(r)&&("di"===r[2]||"ci"===r[2])||"string"==typeof r&&(0,n.isInterceptionRouteAppPath)(r))return!0;if(i){for(let t in i)if(e(i[t]))return!0}return!1}}});let n=r(72859);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68524:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.ServerInsertedMetadata},68613:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(42292).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68762:(e,t,r)=>{"use strict";r.d(t,{S:()=>n});let n=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},69024:(e,t,r)=>{"use strict";r.d(t,{bL:()=>l,s6:()=>s});var n=r(43210),i=r(14163),o=r(60687),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),s=n.forwardRef((e,t)=>(0,o.jsx)(i.sG.span,{...e,ref:t,style:{...a,...e.style}}));s.displayName="VisuallyHidden";var l=s},69385:(e,t)=>{"use strict";function r(e){return Object.prototype.toString.call(e)}function n(e){if("[object Object]"!==r(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getObjectClassLabel:function(){return r},isPlainObject:function(){return n}})},69825:(e,t,r)=>{"use strict";r.d(t,{o:()=>h});var n=r(97758),i=r(57211),o=r(88347),a=r(34948),s=r(38681);let l={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var u=r(66244);function c(e,t){return e*Math.sqrt(1-t*t)}let d=["duration","bounce"],f=["stiffness","damping","mass"];function p(e,t){return t.some(t=>void 0!==e[t])}function h(e=l.visualDuration,t=l.bounce){let r,m="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:y,restDelta:g}=m,v=m.keyframes[0],b=m.keyframes[m.keyframes.length-1],w={done:!1,value:v},{stiffness:x,damping:E,mass:_,duration:P,velocity:R,isResolvedFromDuration:S}=function(e){let t={velocity:l.velocity,stiffness:l.stiffness,damping:l.damping,mass:l.mass,isResolvedFromDuration:!1,...e};if(!p(e,f)&&p(e,d))if(e.visualDuration){let r=2*Math.PI/(1.2*e.visualDuration),i=r*r,o=2*(0,n.q)(.05,1,1-(e.bounce||0))*Math.sqrt(i);t={...t,mass:l.mass,stiffness:i,damping:o}}else{let r=function({duration:e=l.duration,bounce:t=l.bounce,velocity:r=l.velocity,mass:o=l.mass}){let a,s;(0,u.$)(e<=(0,i.f)(l.maxDuration),"Spring duration must be 10 seconds or less");let d=1-t;d=(0,n.q)(l.minDamping,l.maxDamping,d),e=(0,n.q)(l.minDuration,l.maxDuration,(0,i.X)(e)),d<1?(a=t=>{let n=t*d,i=n*e;return .001-(n-r)/c(t,d)*Math.exp(-i)},s=t=>{let n=t*d*e,i=Math.pow(d,2)*Math.pow(t,2)*e,o=Math.exp(-n),s=c(Math.pow(t,2),d);return(n*r+r-i)*o*(-a(t)+.001>0?-1:1)/s}):(a=t=>-.001+Math.exp(-t*e)*((t-r)*e+1),s=t=>e*e*(r-t)*Math.exp(-t*e));let f=function(e,t,r){let n=r;for(let r=1;r<12;r++)n-=e(n)/t(n);return n}(a,s,5/e);if(e=(0,i.f)(e),isNaN(f))return{stiffness:l.stiffness,damping:l.damping,duration:e};{let t=Math.pow(f,2)*o;return{stiffness:t,damping:2*d*Math.sqrt(o*t),duration:e}}}(e);(t={...t,...r,mass:l.mass}).isResolvedFromDuration=!0}return t}({...m,velocity:-(0,i.X)(m.velocity||0)}),O=R||0,T=E/(2*Math.sqrt(x*_)),j=b-v,M=(0,i.X)(Math.sqrt(x/_)),A=5>Math.abs(j);if(y||(y=A?l.restSpeed.granular:l.restSpeed.default),g||(g=A?l.restDelta.granular:l.restDelta.default),T<1){let e=c(M,T);r=t=>b-Math.exp(-T*M*t)*((O+T*M*j)/e*Math.sin(e*t)+j*Math.cos(e*t))}else if(1===T)r=e=>b-Math.exp(-M*e)*(j+(O+M*j)*e);else{let e=M*Math.sqrt(T*T-1);r=t=>{let r=Math.exp(-T*M*t),n=Math.min(e*t,300);return b-r*((O+T*M*j)*Math.sinh(n)+e*j*Math.cosh(n))/e}}let C={calculatedDuration:S&&P||null,next:e=>{let t=r(e);if(S)w.done=e>=P;else{let n=0===e?O:0;T<1&&(n=0===e?(0,i.f)(O):(0,s.Y)(r,e,t));let o=Math.abs(b-t)<=g;w.done=Math.abs(n)<=y&&o}return w.value=w.done?b:t,w},toString:()=>{let e=Math.min((0,a.t)(C),a.Y),t=(0,o.K)(t=>C.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return C}h.applyToOptions=e=>{let t=function(e,t=100,r){let n=r({...e,keyframes:[0,t]}),o=Math.min((0,a.t)(n),a.Y);return{type:"keyframes",ease:e=>n.next(o*e).value/t,duration:(0,i.X)(o)}}(e,100,h);return e.ease=t.ease,e.duration=(0,i.f)(t.duration),e.type="keyframes",e}},69848:(e,t,r)=>{"use strict";r.d(t,{I:()=>a});var n=r(97819);let i=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var o=r(82082);function a(e,t){let r=!1,a=!0,s={delta:0,timestamp:0,isProcessing:!1},l=()=>r=!0,u=i.reduce((e,r)=>(e[r]=function(e,t){let r=new Set,n=new Set,i=!1,a=!1,s=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},u=0;function c(t){s.has(t)&&(d.schedule(t),e()),u++,t(l)}let d={schedule:(e,t=!1,o=!1)=>{let a=o&&i?r:n;return t&&s.add(e),a.has(e)||a.add(e),e},cancel:e=>{n.delete(e),s.delete(e)},process:e=>{if(l=e,i){a=!0;return}i=!0,[r,n]=[n,r],r.forEach(c),t&&o.Q.value&&o.Q.value.frameloop[t].push(u),u=0,r.clear(),i=!1,a&&(a=!1,d.process(e))}};return d}(l,t?r:void 0),e),{}),{setup:c,read:d,resolveKeyframes:f,preUpdate:p,update:h,preRender:m,render:y,postRender:g}=u,v=()=>{let i=n.W.useManualTiming?s.timestamp:performance.now();r=!1,n.W.useManualTiming||(s.delta=a?1e3/60:Math.max(Math.min(i-s.timestamp,40),1)),s.timestamp=i,s.isProcessing=!0,c.process(s),d.process(s),f.process(s),p.process(s),h.process(s),m.process(s),y.process(s),g.process(s),s.isProcessing=!1,r&&t&&(a=!1,e(v))},b=()=>{r=!0,a=!0,s.isProcessing||e(v)};return{schedule:i.reduce((e,t)=>{let n=u[t];return e[t]=(e,t=!1,i=!1)=>(r||b(),n.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<i.length;t++)u[i[t]].cancel(e)},state:s,steps:u}}},70569:(e,t,r)=>{"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{m:()=>n})},70642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u},getSelectedParams:function(){return function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],o=Array.isArray(t),a=o?t[1]:t;!a||a.startsWith(i.PAGE_SEGMENT_KEY)||(o&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):o&&(r[t[0]]=t[1]),r=e(n,r))}return r}}});let n=r(72859),i=r(83913),o=r(14077),a=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=a(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===i.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(i.PAGE_SEGMENT_KEY))return"";let o=[s(r)],a=null!=(t=e[1])?t:{},c=a.children?u(a.children):void 0;if(void 0!==c)o.push(c);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let r=u(t);void 0!==r&&o.push(r)}return l(o)}function c(e,t){let r=function e(t,r){let[i,a]=t,[l,c]=r,d=s(i),f=s(l);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,o.matchSegment)(i,l)){var p;return null!=(p=u(r))?p:""}for(let t in a)if(c[t]){let r=e(a[t],c[t]);if(null!==r)return s(l)+"/"+r}return null}(e,t);return null==r||"/"===r?r:l(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},72244:(e,t,r)=>{"use strict";function n(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function i(e){let t=[{},{}];return e?.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}function o(e,t,r,n){if("function"==typeof t){let[o,a]=i(n);t=t(void 0!==r?r:e.custom,o,a)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[o,a]=i(n);t=t(void 0!==r?r:e.custom,o,a)}return t}function a(e,t,r){let n=e.getProps();return o(n,t,void 0!==r?r:n.custom,e)}function s(e,t){return e?.[t]??e?.default??e}r.d(t,{P:()=>io});var l,u,c=r(23671);let d=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],f=new Set(d),p=new Set(["width","height","top","left","right","bottom",...d]);var h=r(24342);let m=e=>Array.isArray(e);var y=r(97819),g=r(5927);function v(e,t){let r=e.getValue("willChange");if((0,g.S)(r)&&r.add)return r.add(t);if(!r&&y.W.WillChange){let r=new y.W.WillChange("auto");e.addValue("willChange",r),r.add(t)}}let b=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),w="data-"+b("framerAppearId");var x=r(73303),E=r(83361),_=r(24325),P=r(29070);let R=e=>180*e/Math.PI,S=e=>T(R(Math.atan2(e[1],e[0]))),O={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:S,rotateZ:S,skewX:e=>R(Math.atan(e[1])),skewY:e=>R(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},T=e=>((e%=360)<0&&(e+=360),e),j=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),M=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),A={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:j,scaleY:M,scale:e=>(j(e)+M(e))/2,rotateX:e=>T(R(Math.atan2(e[6],e[5]))),rotateY:e=>T(R(Math.atan2(-e[2],e[0]))),rotateZ:S,rotate:S,skewX:e=>R(Math.atan(e[4])),skewY:e=>R(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function C(e){return+!!e.includes("scale")}function k(e,t){let r,n;if(!e||"none"===e)return C(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)r=A,n=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=O,n=t}if(!n)return C(t);let o=r[t],a=n[1].split(",").map(N);return"function"==typeof o?o(a):a[o]}let D=(e,t)=>{let{transform:r="none"}=getComputedStyle(e);return k(r,t)};function N(e){return parseFloat(e.trim())}var L=r(95444),F=r(32874);let I=e=>e===L.ai||e===F.px,U=new Set(["x","y","z"]),B=d.filter(e=>!U.has(e)),V={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>k(t,"x"),y:(e,{transform:t})=>k(t,"y")};V.translateX=V.x,V.translateY=V.y;let $=new Set,H=!1,W=!1,G=!1;function z(){if(W){let e=Array.from($).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),r=new Map;t.forEach(e=>{let t=function(e){let t=[];return B.forEach(r=>{let n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(+!!r.startsWith("scale")))}),t}(e);t.length&&(r.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=r.get(e);t&&t.forEach(([t,r])=>{e.getValue(t)?.set(r)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}W=!1,H=!1,$.forEach(e=>e.complete(G)),$.clear()}function q(){$.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(W=!0)})}class K{constructor(e,t,r,n,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=r,this.motionValue=n,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?($.add(this),H||(H=!0,c.Gt.read(q),c.Gt.resolveKeyframes(z))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:r,motionValue:n}=this;if(null===e[0]){let i=n?.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(r&&t){let n=r.readValue(t,o);null!=n&&(e[0]=n)}void 0===e[0]&&(e[0]=o),n&&void 0===i&&n.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),$.delete(this)}cancel(){"scheduled"===this.state&&($.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}var X=r(57211),Y=r(66244);let Q=e=>e.startsWith("--");function J(e){let t;return()=>(void 0===t&&(t=e()),t)}let Z=J(()=>void 0!==window.ScrollTimeline);var ee=r(63830),et=r(96184),er=r(82082),en=r(74177);let ei={},eo=function(e,t){let r=J(e);return()=>ei[t]??r()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing");var ea=r(88347);let es=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,el={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:es([0,.65,.55,1]),circOut:es([.55,0,1,.45]),backIn:es([.31,.01,.66,-.59]),backOut:es([.33,1.53,.69,.99])};function eu(e){return"function"==typeof e&&"applyToOptions"in e}class ec extends ee.q{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:r,keyframes:n,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:a,onComplete:s}=e;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=e,(0,Y.V)("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return eu(e)&&eo()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,r,{delay:n=0,duration:i=300,repeat:o=0,repeatType:a="loop",ease:s="easeOut",times:l}={},u){let c={[t]:r};l&&(c.offset=l);let d=function e(t,r){if(t)return"function"==typeof t?eo()?(0,ea.K)(t,r):"ease-out":(0,en.D)(t)?es(t):Array.isArray(t)?t.map(t=>e(t,r)||el.easeOut):el[t]}(s,i);Array.isArray(d)&&(c.easing=d),er.Q.value&&et.q.waapi++;let f={delay:n,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:"reverse"===a?"alternate":"normal"};u&&(f.pseudoElement=u);let p=e.animate(c,f);return er.Q.value&&p.finished.finally(()=>{et.q.waapi--}),p}(t,r,n,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=(0,P.X)(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,r){Q(t)?e.style.setProperty(t,r):e.style[t]=r}(t,r,e),this.animation.cancel()}s?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let e=this.animation.effect?.getComputedTiming?.().duration||0;return(0,X.X)(Number(e))}get time(){return(0,X.X)(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=(0,X.f)(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&Z())?(this.animation.timeline=e,E.l):t(this)}}var ed=r(43500),ef=r(73685),ep=r(60912),eh=r(52716);let em={anticipate:ef.b,backInOut:ep.ZZ,circInOut:eh.tn};class ey extends ec{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in em&&(e.ease=em[e.ease])}(e),(0,ed.E)(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:r,onComplete:n,element:i,...o}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let a=new x.s({...o,autoplay:!1}),s=(0,X.f)(this.finishedTime??this.time);t.setWithVelocity(a.sample(s-10).value,a.sample(s).value,10),a.stop()}}var eg=r(39664);let ev=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eg.f.test(e)||"0"===e)&&!e.startsWith("url("));var eb=r(18171);let ew=new Set(["opacity","clipPath","filter","transform"]),ex=J(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eE extends ee.q{constructor({autoplay:e=!0,delay:t=0,type:r="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:o="loop",keyframes:a,name:s,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=_.k.now();let d={autoplay:e,delay:t,type:r,repeat:n,repeatDelay:i,repeatType:o,name:s,motionValue:l,element:u,...c},f=u?.KeyframeResolver||K;this.keyframeResolver=new f(a,(e,t,r)=>this.onKeyframesResolved(e,t,d,!r),s,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,r,n){this.keyframeResolver=void 0;let{name:i,type:o,velocity:a,delay:s,isHandoff:l,onUpdate:u}=r;this.resolvedAt=_.k.now(),!function(e,t,r,n){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],a=ev(i,t),s=ev(o,t);return(0,Y.$)(a===s,`You are trying to animate ${t} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!a&&!!s&&(function(e){let t=e[0];if(1===e.length)return!0;for(let r=0;r<e.length;r++)if(e[r]!==t)return!0}(e)||("spring"===r||eu(r))&&n)}(e,i,o,a)&&((y.W.instantAnimations||!s)&&u?.((0,P.X)(e,r,t)),e[0]=e[e.length-1],r.duration=0,r.repeat=0);let c={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...r,keyframes:e},d=!l&&function(e){let{motionValue:t,name:r,repeatDelay:n,repeatType:i,damping:o,type:a}=e;if(!(0,eb.s)(t?.owner?.current))return!1;let{onUpdate:s,transformTemplate:l}=t.owner.getProps();return ex()&&r&&ew.has(r)&&("transform"!==r||!l)&&!s&&!n&&"mirror"!==i&&0!==o&&"inertia"!==a}(c)?new ey({...c,element:c.motionValue.owner.current}):new x.s(c);d.finished.then(()=>this.notifyFinished()).catch(E.l),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),G=!0,q(),z(),G=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e_=e=>null!==e,eP={type:"spring",stiffness:500,damping:25,restSpeed:10},eR=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),eS={type:"keyframes",duration:.8},eO={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},eT=(e,{keyframes:t})=>t.length>2?eS:f.has(e)?e.startsWith("scale")?eR(t[1]):eP:eO,ej=(e,t,r,n={},i,o)=>a=>{let l=s(n,e)||{},u=l.delay||n.delay||0,{elapsed:d=0}=n;d-=(0,X.f)(u);let f={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-d,onUpdate:e=>{t.set(e),l.onUpdate&&l.onUpdate(e)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:o,repeatType:a,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(l)&&Object.assign(f,eT(e,f)),f.duration&&(f.duration=(0,X.f)(f.duration)),f.repeatDelay&&(f.repeatDelay=(0,X.f)(f.repeatDelay)),void 0!==f.from&&(f.keyframes[0]=f.from);let p=!1;if(!1!==f.type&&(0!==f.duration||f.repeatDelay)||(f.duration=0,0===f.delay&&(p=!0)),(y.W.instantAnimations||y.W.skipAnimations)&&(p=!0,f.duration=0,f.delay=0),f.allowFlatten=!l.type&&!l.ease,p&&!o&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:r="loop"},n){let i=e.filter(e_),o=t&&"loop"!==r&&t%2==1?0:i.length-1;return i[o]}(f.keyframes,l);if(void 0!==e)return void c.Gt.update(()=>{f.onUpdate(e),f.onComplete()})}return l.isSync?new x.s(f):new eE(f)};function eM(e,t,{delay:r=0,transitionOverride:n,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:l,...u}=t;n&&(o=n);let d=[],f=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let n=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||f&&function({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}(f,t))continue;let a={delay:r,...s(o||{},t)},l=n.get();if(void 0!==l&&!n.isAnimating&&!Array.isArray(i)&&i===l&&!a.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let r=e.props[w];if(r){let e=window.MotionHandoffAnimation(r,t,c.Gt);null!==e&&(a.startTime=e,h=!0)}}v(e,t),n.start(ej(t,n,i,e.shouldReduceMotion&&p.has(t)?{type:!1}:a,e,h));let m=n.animation;m&&d.push(m)}return l&&Promise.all(d).then(()=>{c.Gt.update(()=>{l&&function(e,t){let{transitionEnd:r={},transition:n={},...i}=a(e,t)||{};for(let t in i={...i,...r}){var o;let r=m(o=i[t])?o[o.length-1]||0:o;e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,(0,h.OQ)(r))}}(e,l)})}),d}function eA(e,t,r={}){let n=a(e,t,"exit"===r.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(i=r.transitionOverride);let o=n?()=>Promise.all(eM(e,n,r)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:o=0,staggerChildren:a,staggerDirection:s}=i;return function(e,t,r=0,n=0,i=1,o){let a=[],s=(e.variantChildren.size-1)*n,l=1===i?(e=0)=>e*n:(e=0)=>s-e*n;return Array.from(e.variantChildren).sort(eC).forEach((e,n)=>{e.notify("AnimationStart",t),a.push(eA(e,t,{...o,delay:r+l(n)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,o+n,a,s,r)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([o(),s(r.delay)]);{let[e,t]="beforeChildren"===l?[o,s]:[s,o];return e().then(()=>t())}}function eC(e,t){return e.sortNodePosition(t)}function ek(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function eD(e){return"string"==typeof e||Array.isArray(e)}let eN=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],eL=["initial",...eN],eF=eL.length,eI=[...eN].reverse(),eU=eN.length;function eB(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function eV(){return{animate:eB(!0),whileInView:eB(),whileHover:eB(),whileTap:eB(),whileDrag:eB(),whileFocus:eB(),exit:eB()}}class e${constructor(e){this.isMounted=!1,this.node=e}update(){}}class eH extends e${constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>eA(e,t,r)));else if("string"==typeof t)n=eA(e,t,r);else{let i="function"==typeof t?a(e,t,r.custom):t;n=Promise.all(eM(e,i,r))}return n.then(()=>{e.notify("AnimationComplete",t)})})(e,t,r))),r=eV(),i=!0,o=t=>(r,n)=>{let i=a(e,n,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...n}=i;r={...r,...n,...t}}return r};function s(s){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let r=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(r.initial=t.props.initial),r}let r={};for(let e=0;e<eF;e++){let n=eL[e],i=t.props[n];(eD(i)||!1===i)&&(r[n]=i)}return r}(e.parent)||{},c=[],d=new Set,f={},p=1/0;for(let t=0;t<eU;t++){var h,y;let a=eI[t],g=r[a],v=void 0!==l[a]?l[a]:u[a],b=eD(v),w=a===s?g.isActive:null;!1===w&&(p=t);let x=v===u[a]&&v!==l[a]&&b;if(x&&i&&e.manuallyAnimateOnMount&&(x=!1),g.protectedKeys={...f},!g.isActive&&null===w||!v&&!g.prevProp||n(v)||"boolean"==typeof v)continue;let E=(h=g.prevProp,"string"==typeof(y=v)?y!==h:!!Array.isArray(y)&&!ek(y,h)),_=E||a===s&&g.isActive&&!x&&b||t>p&&b,P=!1,R=Array.isArray(v)?v:[v],S=R.reduce(o(a),{});!1===w&&(S={});let{prevResolvedValues:O={}}=g,T={...O,...S},j=t=>{_=!0,d.has(t)&&(P=!0,d.delete(t)),g.needsAnimating[t]=!0;let r=e.getValue(t);r&&(r.liveStyle=!1)};for(let e in T){let t=S[e],r=O[e];if(f.hasOwnProperty(e))continue;let n=!1;(m(t)&&m(r)?ek(t,r):t===r)?void 0!==t&&d.has(e)?j(e):g.protectedKeys[e]=!0:null!=t?j(e):d.add(e)}g.prevProp=v,g.prevResolvedValues=S,g.isActive&&(f={...f,...S}),i&&e.blockInitialAnimation&&(_=!1);let M=!(x&&E)||P;_&&M&&c.push(...R.map(e=>({animation:e,options:{type:a}})))}if(d.size){let t={};if("boolean"!=typeof l.initial){let r=a(e,Array.isArray(l.initial)?l.initial[0]:l.initial);r&&r.transition&&(t.transition=r.transition)}d.forEach(r=>{let n=e.getBaseTarget(r),i=e.getValue(r);i&&(i.liveStyle=!0),t[r]=n??null}),c.push({animation:t})}let g=!!c.length;return i&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),i=!1,g?t(c):Promise.resolve()}return{animateChanges:s,setActive:function(t,n){if(r[t].isActive===n)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,n)),r[t].isActive=n;let i=s(t);for(let e in r)r[e].protectedKeys={};return i},setAnimateFunction:function(r){t=r(e)},getState:()=>r,reset:()=>{r=eV(),i=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();n(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let eW=0;class eG extends e${constructor(){super(...arguments),this.id=eW++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;let n=this.node.animationState.setActive("exit",!e);t&&!e&&n.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let ez={x:!1,y:!1};var eq=r(68028);function eK(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}let eX=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function eY(e){return{point:{x:e.pageX,y:e.pageY}}}let eQ=e=>t=>eX(t)&&e(t,eY(t));function eJ(e,t,r,n){return eK(e,t,eQ(r),n)}function eZ({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function e0(e){return e.max-e.min}function e1(e,t,r,n=.5){e.origin=n,e.originPoint=(0,eq.k)(t.min,t.max,e.origin),e.scale=e0(r)/e0(t),e.translate=(0,eq.k)(r.min,r.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function e2(e,t,r,n){e1(e.x,t.x,r.x,n?n.originX:void 0),e1(e.y,t.y,r.y,n?n.originY:void 0)}function e3(e,t,r){e.min=r.min+t.min,e.max=e.min+e0(t)}function e4(e,t,r){e.min=t.min-r.min,e.max=e.min+e0(t)}function e5(e,t,r){e4(e.x,t.x,r.x),e4(e.y,t.y,r.y)}let e9=()=>({translate:0,scale:1,origin:0,originPoint:0}),e7=()=>({x:e9(),y:e9()}),e6=()=>({min:0,max:0}),e8=()=>({x:e6(),y:e6()});function te(e){return[e("x"),e("y")]}function tt(e){return void 0===e||1===e}function tr({scale:e,scaleX:t,scaleY:r}){return!tt(e)||!tt(t)||!tt(r)}function tn(e){return tr(e)||ti(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function ti(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function to(e,t,r,n,i){return void 0!==i&&(e=n+i*(e-n)),n+r*(e-n)+t}function ta(e,t=0,r=1,n,i){e.min=to(e.min,t,r,n,i),e.max=to(e.max,t,r,n,i)}function ts(e,{x:t,y:r}){ta(e.x,t.translate,t.scale,t.originPoint),ta(e.y,r.translate,r.scale,r.originPoint)}function tl(e,t){e.min=e.min+t,e.max=e.max+t}function tu(e,t,r,n,i=.5){let o=(0,eq.k)(e.min,e.max,i);ta(e,t,r,o,n)}function tc(e,t){tu(e.x,t.x,t.scaleX,t.scale,t.originX),tu(e.y,t.y,t.scaleY,t.scale,t.originY)}function td(e,t){return eZ(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let tf=({current:e})=>e?e.ownerDocument.defaultView:null;function tp(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}var th=r(78205);let tm=(e,t)=>Math.abs(e-t);class ty{constructor(e,t,{transformPagePoint:r,contextWindow:n,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=tb(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){return Math.sqrt(tm(e.x,t.x)**2+tm(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:n}=e,{timestamp:i}=c.uv;this.history.push({...n,timestamp:i});let{onStart:o,onMove:a}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=tg(t,this.transformPagePoint),c.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=tb("pointercancel"===e.type?this.lastMoveEventInfo:tg(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,o),n&&n(e,o)},!eX(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=r,this.contextWindow=n||window;let o=tg(eY(e),this.transformPagePoint),{point:a}=o,{timestamp:s}=c.uv;this.history=[{...a,timestamp:s}];let{onSessionStart:l}=t;l&&l(e,tb(o,this.history)),this.removeListeners=(0,th.F)(eJ(this.contextWindow,"pointermove",this.handlePointerMove),eJ(this.contextWindow,"pointerup",this.handlePointerUp),eJ(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,c.WG)(this.updatePoint)}}function tg(e,t){return t?{point:t(e.point)}:e}function tv(e,t){return{x:e.x-t.x,y:e.y-t.y}}function tb({point:e},t){return{point:e,delta:tv(e,tw(t)),offset:tv(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,i=tw(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>(0,X.f)(.1)));)r--;if(!n)return{x:0,y:0};let o=(0,X.X)(i.timestamp-n.timestamp);if(0===o)return{x:0,y:0};let a={x:(i.x-n.x)/o,y:(i.y-n.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function tw(e){return e[e.length-1]}var tx=r(64068),tE=r(97758);function t_(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function tP(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function tR(e,t,r){return{min:tS(e,t),max:tS(e,r)}}function tS(e,t){return"number"==typeof e?e:e[t]||0}let tO=new WeakMap;class tT{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=e8(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new ty(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(eY(e).point)},onStart:(e,t)=>{let{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(ez[e])return null;else return ez[e]=!0,()=>{ez[e]=!1};return ez.x||ez.y?null:(ez.x=ez.y=!0,()=>{ez.x=ez.y=!1})}(r),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),te(e=>{let t=this.getAxisMotionValue(e).get()||0;if(F.KN.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[e];n&&(t=e0(n)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&c.Gt.postRender(()=>i(e,t)),v(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:o}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:a}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(a),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>te(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:tf(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:i}=this.getProps();i&&c.Gt.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!tj(e,n,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?(0,eq.k)(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?(0,eq.k)(r,e,n.max):Math.min(e,r)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;e&&tp(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=function(e,{top:t,left:r,bottom:n,right:i}){return{x:t_(e.x,r,i),y:t_(e.y,t,n)}}(r.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:tR(e,"left","right"),y:tR(e,"top","bottom")}}(t),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&te(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!tp(t))return!1;let n=t.current;(0,Y.V)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,r){let n=td(e,r),{scroll:i}=t;return i&&(tl(n.x,i.offset.x),tl(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),a=(e=i.layout.layoutBox,{x:tP(e.x,o.x),y:tP(e.y,o.y)});if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=eZ(e))}return a}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{};return Promise.all(te(a=>{if(!tj(a,t,this.currentDirection))return;let l=s&&s[a]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return v(this.visualElement,e),r.start(ej(e,r,0,t,this.visualElement,!1))}stopAnimation(){te(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){te(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){te(t=>{let{drag:r}=this.getProps();if(!tj(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){let{min:r,max:o}=n.layout.layoutBox[t];i.set(e[t]-(0,eq.k)(r,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!tp(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};te(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let r=t.get();n[e]=function(e,t){let r=.5,n=e0(e),i=e0(t);return i>n?r=(0,tx.q)(t.min,t.max-n,e.min):n>i&&(r=(0,tx.q)(e.min,e.max-i,t.min)),(0,tE.q)(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),te(t=>{if(!tj(t,e,null))return;let r=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];r.set((0,eq.k)(i,o,n[t]))})}addListeners(){if(!this.visualElement.current)return;tO.set(this.visualElement,this);let e=eJ(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();tp(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),c.Gt.read(t);let i=eK(window,"resize",()=>this.scalePositionWithinConstraints()),o=r.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(te(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),n(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function tj(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class tM extends e${constructor(e){super(e),this.removeGroupControls=E.l,this.removeListeners=E.l,this.controls=new tT(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||E.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let tA=e=>(t,r)=>{e&&c.Gt.postRender(()=>e(t,r))};class tC extends e${constructor(){super(...arguments),this.removePointerDownListener=E.l}onPointerDown(e){this.session=new ty(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:tf(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:tA(e),onStart:tA(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&c.Gt.postRender(()=>n(e,t))}}}mount(){this.removePointerDownListener=eJ(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var tk=r(60687);let{schedule:tD}=(0,r(69848).I)(queueMicrotask,!1);var tN=r(43210),tL=r(86044),tF=r(12157);let tI=(0,tN.createContext)({}),tU={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function tB(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let tV={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!F.px.test(e))return e;else e=parseFloat(e);let r=tB(e,t.target.x),n=tB(e,t.target.y);return`${r}% ${n}%`}};var t$=r(22238);let tH={};class tW extends tN.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;for(let e in tz)tH[e]=tz[e],(0,t$.j)(e)&&(tH[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),tU.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,{projection:o}=r;return o&&(o.isPresent=i,n||e.layoutDependency!==t||void 0===t||e.isPresent!==i?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||c.Gt.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),tD.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function tG(e){let[t,r]=(0,tL.xQ)(),n=(0,tN.useContext)(tF.L);return(0,tk.jsx)(tW,{...e,layoutGroup:n,switchLayoutGroup:(0,tN.useContext)(tI),isPresent:t,safeToRemove:r})}let tz={borderRadius:{...tV,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:tV,borderTopRightRadius:tV,borderBottomLeftRadius:tV,borderBottomRightRadius:tV,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let n=eg.f.parse(e);if(n.length>5)return e;let i=eg.f.createTransformer(e),o=+("number"!=typeof n[0]),a=r.x.scale*t.x,s=r.y.scale*t.y;n[0+o]/=a,n[1+o]/=s;let l=(0,eq.k)(a,s,.5);return"number"==typeof n[2+o]&&(n[2+o]/=l),"number"==typeof n[3+o]&&(n[3+o]/=l),i(n)}}};var tq=r(74479);function tK(e){return(0,tq.G)(e)&&"ownerSVGElement"in e}var tX=r(14296),tY=r(87556);let tQ=(e,t)=>e.depth-t.depth;class tJ{constructor(){this.children=[],this.isDirty=!1}add(e){(0,tY.Kq)(this.children,e),this.isDirty=!0}remove(e){(0,tY.Ai)(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(tQ),this.isDirty=!1,this.children.forEach(e)}}function tZ(e){return(0,g.S)(e)?e.get():e}let t0=["TopLeft","TopRight","BottomLeft","BottomRight"],t1=t0.length,t2=e=>"string"==typeof e?parseFloat(e):e,t3=e=>"number"==typeof e||F.px.test(e);function t4(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let t5=t7(0,.5,eh.yT),t9=t7(.5,.95,E.l);function t7(e,t,r){return n=>n<e?0:n>t?1:r((0,tx.q)(e,t,n))}function t6(e,t){e.min=t.min,e.max=t.max}function t8(e,t){t6(e.x,t.x),t6(e.y,t.y)}function re(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rt(e,t,r,n,i){return e-=t,e=n+1/r*(e-n),void 0!==i&&(e=n+1/i*(e-n)),e}function rr(e,t,[r,n,i],o,a){!function(e,t=0,r=1,n=.5,i,o=e,a=e){if(F.KN.test(t)&&(t=parseFloat(t),t=(0,eq.k)(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let s=(0,eq.k)(o.min,o.max,n);e===o&&(s-=t),e.min=rt(e.min,t,r,s,i),e.max=rt(e.max,t,r,s,i)}(e,t[r],t[n],t[i],t.scale,o,a)}let rn=["x","scaleX","originX"],ri=["y","scaleY","originY"];function ro(e,t,r,n){rr(e.x,t,rn,r?r.x:void 0,n?n.x:void 0),rr(e.y,t,ri,r?r.y:void 0,n?n.y:void 0)}function ra(e){return 0===e.translate&&1===e.scale}function rs(e){return ra(e.x)&&ra(e.y)}function rl(e,t){return e.min===t.min&&e.max===t.max}function ru(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rc(e,t){return ru(e.x,t.x)&&ru(e.y,t.y)}function rd(e){return e0(e.x)/e0(e.y)}function rf(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rp{constructor(){this.members=[]}add(e){(0,tY.Kq)(this.members,e),e.scheduleRender()}remove(e){if((0,tY.Ai)(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rh={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rm=["","X","Y","Z"],ry={visibility:"hidden"},rg=0;function rv(e,t,r,n){let{latestValues:i}=t;i[e]&&(r[e]=i[e],t.setStaticValue(e,0),n&&(n[e]=0))}function rb({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(e={},r=t?.()){this.id=rg++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,er.Q.value&&(rh.nodes=rh.calculatedTargetDeltas=rh.calculatedProjections=0),this.nodes.forEach(rE),this.nodes.forEach(rj),this.nodes.forEach(rM),this.nodes.forEach(r_),er.Q.addProjectionMetrics&&er.Q.addProjectionMetrics(rh)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new tJ)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new tX.v),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=tK(t)&&!(tK(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:r,layout:n,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||r)&&(this.isLayoutDirty=!0),e){let r,n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=_.k.now(),n=({timestamp:i})=>{let o=i-r;o>=250&&((0,c.WG)(n),e(o-t))};return c.Gt.setup(n,!0),()=>(0,c.WG)(n)}(n,250),tU.hasAnimatedSinceResize&&(tU.hasAnimatedSinceResize=!1,this.nodes.forEach(rT))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&i&&(r||n)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||rL,{onLayoutAnimationStart:a,onLayoutAnimationComplete:l}=i.getProps(),u=!this.targetLayout||!rc(this.targetLayout,n),c=!t&&r;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,c);let t={...s(o,"layout"),onPlay:a,onComplete:l};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||rT(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,c.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rA),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:r}=t.options;if(!r)return;let n=r.props[w];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:e,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",c.Gt,!(e||r))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rR);return}this.isUpdating||this.nodes.forEach(rS),this.isUpdating=!1,this.nodes.forEach(rO),this.nodes.forEach(rw),this.nodes.forEach(rx),this.clearAllSnapshots();let e=_.k.now();c.uv.delta=(0,tE.q)(0,1e3/60,e-c.uv.timestamp),c.uv.timestamp=e,c.uv.isProcessing=!0,c.PP.update.process(c.uv),c.PP.preRender.process(c.uv),c.PP.render.process(c.uv),c.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,tD.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rP),this.sharedNodes.forEach(rC)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,c.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){c.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||e0(this.snapshot.measuredBox.x)||e0(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=e8(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rs(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,o=n!==this.prevTransformTemplateValue;e&&this.instance&&(t||tn(this.latestValues)||o)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),rU((t=n).x),rU(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return e8();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rV))){let{scroll:e}=this.root;e&&(tl(t.x,e.offset.x),tl(t.y,e.offset.y))}return t}removeElementScroll(e){let t=e8();if(t8(t,e),this.scroll?.wasRoot)return t;for(let r=0;r<this.path.length;r++){let n=this.path[r],{scroll:i,options:o}=n;n!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&t8(t,e),tl(t.x,i.offset.x),tl(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let r=e8();t8(r,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&tc(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),tn(n.latestValues)&&tc(r,n.latestValues)}return tn(this.latestValues)&&tc(r,this.latestValues),r}removeTransform(e){let t=e8();t8(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!tn(r.latestValues))continue;tr(r.latestValues)&&r.updateSnapshot();let n=e8();t8(n,r.measurePageBox()),ro(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return tn(this.latestValues)&&ro(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==c.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let r=!!this.resumingFrom||this!==t;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:i}=this.options;if(this.layout&&(n||i)){if(this.resolvedRelativeTargetAt=c.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=e8(),this.relativeTargetOrigin=e8(),e5(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),t8(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=e8(),this.targetWithTransforms=e8()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,a,s;this.forceRelativeParentToResolveTarget(),o=this.target,a=this.relativeTarget,s=this.relativeParent.target,e3(o.x,a.x,s.x),e3(o.y,a.y,s.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):t8(this.target,this.layout.layoutBox),ts(this.target,this.targetDelta)):t8(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=e8(),this.relativeTargetOrigin=e8(),e5(this.relativeTargetOrigin,this.target,e.target),t8(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}er.Q.value&&rh.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||tr(this.parent.latestValues)||ti(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,r=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(r=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===c.uv.timestamp&&(r=!1),r)return;let{layout:n,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||i))return;t8(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(e,t,r,n=!1){let i,o,a=r.length;if(a){t.x=t.y=1;for(let s=0;s<a;s++){o=(i=r[s]).projectionDelta;let{visualElement:a}=i.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&tc(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,ts(e,o)),n&&tn(i.latestValues)&&tc(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=e8());let{target:s}=e;if(!s){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(re(this.prevProjectionDelta.x,this.projectionDelta.x),re(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),e2(this.projectionDelta,this.layoutCorrected,s,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&rf(this.projectionDelta.x,this.prevProjectionDelta.x)&&rf(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",s)),er.Q.value&&rh.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=e7(),this.projectionDelta=e7(),this.projectionDeltaWithTransform=e7()}setAnimationOrigin(e,t=!1){let r,n=this.snapshot,i=n?n.latestValues:{},o={...this.latestValues},a=e7();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let s=e8(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(rN));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(rk(a.x,e.x,n),rk(a.y,e.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,f,p,h,m,y;e5(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,h=this.relativeTargetOrigin,m=s,y=n,rD(p.x,h.x,m.x,y),rD(p.y,h.y,m.y,y),r&&(u=this.relativeTarget,f=r,rl(u.x,f.x)&&rl(u.y,f.y))&&(this.isProjectionDirty=!1),r||(r=e8()),t8(r,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,r,n,i,o){i?(e.opacity=(0,eq.k)(0,r.opacity??1,t5(n)),e.opacityExit=(0,eq.k)(t.opacity??1,0,t9(n))):o&&(e.opacity=(0,eq.k)(t.opacity??1,r.opacity??1,n));for(let i=0;i<t1;i++){let o=`border${t0[i]}Radius`,a=t4(t,o),s=t4(r,o);(void 0!==a||void 0!==s)&&(a||(a=0),s||(s=0),0===a||0===s||t3(a)===t3(s)?(e[o]=Math.max((0,eq.k)(t2(a),t2(s),n),0),(F.KN.test(s)||F.KN.test(a))&&(e[o]+="%")):e[o]=s)}(t.rotate||r.rotate)&&(e.rotate=(0,eq.k)(t.rotate||0,r.rotate||0,n))}(o,i,this.latestValues,n,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&((0,c.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=c.Gt.update(()=>{tU.hasAnimatedSinceResize=!0,et.q.layout++,this.motionValue||(this.motionValue=(0,h.OQ)(0)),this.currentAnimation=function(e,t,r){let n=(0,g.S)(e)?e:(0,h.OQ)(e);return n.start(ej("",n,t,r)),n.animation}(this.motionValue,[0,1e3],{...e,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{et.q.layout--},onComplete:()=>{et.q.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:n,latestValues:i}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&rB(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||e8();let t=e0(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let n=e0(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}t8(t,r),tc(t,i),e2(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rp),this.sharedNodes.get(e).add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(t=!0),!t)return;let n={};r.z&&rv("z",e,n,this.animationValues);for(let t=0;t<rm.length;t++)rv(`rotate${rm[t]}`,e,n,this.animationValues),rv(`skew${rm[t]}`,e,n,this.animationValues);for(let t in e.render(),n)e.setStaticValue(t,n[t]),this.animationValues&&(this.animationValues[t]=n[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return ry;let t={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=tZ(e?.pointerEvents)||"",t.transform=r?r(this.latestValues,""):"none",t;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=tZ(e?.pointerEvents)||""),this.hasProjected&&!tn(this.latestValues)&&(t.transform=r?r({},""):"none",this.hasProjected=!1),t}let i=n.animationValues||n.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,r){let n="",i=e.x.translate/t.x,o=e.y.translate/t.y,a=r?.z||0;if((i||o||a)&&(n=`translate3d(${i}px, ${o}px, ${a}px) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:a,skewY:s}=r;e&&(n=`perspective(${e}px) ${n}`),t&&(n+=`rotate(${t}deg) `),i&&(n+=`rotateX(${i}deg) `),o&&(n+=`rotateY(${o}deg) `),a&&(n+=`skewX(${a}deg) `),s&&(n+=`skewY(${s}deg) `)}let s=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==s||1!==l)&&(n+=`scale(${s}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),r&&(t.transform=r(i,t.transform));let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,n.animationValues?t.opacity=n===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=n===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,tH){if(void 0===i[e])continue;let{correct:r,applyTo:o,isCSSVariable:a}=tH[e],s="none"===t.transform?i[e]:r(i[e],n);if(o){let e=o.length;for(let r=0;r<e;r++)t[o[r]]=s}else a?this.options.visualElement.renderState.vars[e]=s:t[e]=s}return this.options.layoutId&&(t.pointerEvents=n===this?tZ(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop(!1)),this.root.nodes.forEach(rR),this.root.sharedNodes.clear()}}}function rw(e){e.updateLayout()}function rx(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:r,measuredBox:n}=e.layout,{animationType:i}=e.options,o=t.source!==e.layout.source;"size"===i?te(e=>{let n=o?t.measuredBox[e]:t.layoutBox[e],i=e0(n);n.min=r[e].min,n.max=n.min+i}):rB(i,t.layoutBox,r)&&te(n=>{let i=o?t.measuredBox[n]:t.layoutBox[n],a=e0(r[n]);i.max=i.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+a)});let a=e7();e2(a,r,t.layoutBox);let s=e7();o?e2(s,e.applyTransform(n,!0),t.measuredBox):e2(s,r,t.layoutBox);let l=!rs(a),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:o}=n;if(i&&o){let a=e8();e5(a,t.layoutBox,i.layoutBox);let s=e8();e5(s,r,o.layoutBox),rc(a,s)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=a,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:t,delta:s,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rE(e){er.Q.value&&rh.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function r_(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rP(e){e.clearSnapshot()}function rR(e){e.clearMeasurements()}function rS(e){e.isLayoutDirty=!1}function rO(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rT(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rj(e){e.resolveTargetDelta()}function rM(e){e.calcProjection()}function rA(e){e.resetSkewAndRotation()}function rC(e){e.removeLeadSnapshot()}function rk(e,t,r){e.translate=(0,eq.k)(t.translate,0,r),e.scale=(0,eq.k)(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function rD(e,t,r,n){e.min=(0,eq.k)(t.min,r.min,n),e.max=(0,eq.k)(t.max,r.max,n)}function rN(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let rL={duration:.45,ease:[.4,0,.1,1]},rF=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),rI=rF("applewebkit/")&&!rF("chrome/")?Math.round:E.l;function rU(e){e.min=rI(e.min),e.max=rI(e.max)}function rB(e,t,r){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rd(t)-rd(r)))}function rV(e){return e!==e.root&&e.scroll?.wasRoot}let r$=rb({attachResizeListener:(e,t)=>eK(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rH={current:void 0},rW=rb({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!rH.current){let e=new r$({});e.mount(window),e.setOptions({layoutScroll:!0}),rH.current=e}return rH.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function rG(e,t){let r=function(e,t,r){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,r=(void 0)??t.querySelectorAll(e);return r?Array.from(r):[]}return Array.from(e)}(e),n=new AbortController;return[r,{passive:!0,...t,signal:n.signal},()=>n.abort()]}function rz(e){return!("touch"===e.pointerType||ez.x||ez.y)}function rq(e,t,r){let{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover","Start"===r);let i=n["onHover"+r];i&&c.Gt.postRender(()=>i(t,eY(t)))}class rK extends e${mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,o]=rG(e,r),a=e=>{if(!rz(e))return;let{target:r}=e,n=t(r,e);if("function"!=typeof n||!r)return;let o=e=>{rz(e)&&(n(e),r.removeEventListener("pointerleave",o))};r.addEventListener("pointerleave",o,i)};return n.forEach(e=>{e.addEventListener("pointerenter",a,i)}),o}(e,(e,t)=>(rq(this.node,t,"Start"),e=>rq(this.node,e,"End"))))}unmount(){}}class rX extends e${constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,th.F)(eK(this.node.current,"focus",()=>this.onFocus()),eK(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rY=(e,t)=>!!t&&(e===t||rY(e,t.parentElement)),rQ=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rJ=new WeakSet;function rZ(e){return t=>{"Enter"===t.key&&e(t)}}function r0(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let r1=(e,t)=>{let r=e.currentTarget;if(!r)return;let n=rZ(()=>{if(rJ.has(r))return;r0(r,"down");let e=rZ(()=>{r0(r,"up")});r.addEventListener("keyup",e,t),r.addEventListener("blur",()=>r0(r,"cancel"),t)});r.addEventListener("keydown",n,t),r.addEventListener("blur",()=>r.removeEventListener("keydown",n),t)};function r2(e){return eX(e)&&!(ez.x||ez.y)}function r3(e,t,r){let{props:n}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap","Start"===r);let i=n["onTap"+("End"===r?"":r)];i&&c.Gt.postRender(()=>i(t,eY(t)))}class r4 extends e${mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,o]=rG(e,r),a=e=>{let n=e.currentTarget;if(!r2(e))return;rJ.add(n);let o=t(n,e),a=(e,t)=>{window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",l),rJ.has(n)&&rJ.delete(n),r2(e)&&"function"==typeof o&&o(e,{success:t})},s=e=>{a(e,n===window||n===document||r.useGlobalTarget||rY(n,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",s,i),window.addEventListener("pointercancel",l,i)};return n.forEach(e=>{((r.useGlobalTarget?window:e).addEventListener("pointerdown",a,i),(0,eb.s)(e))&&(e.addEventListener("focus",e=>r1(e,i)),rQ.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),o}(e,(e,t)=>(r3(this.node,t,"Start"),(e,{success:t})=>r3(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let r5=new WeakMap,r9=new WeakMap,r7=e=>{let t=r5.get(e.target);t&&t(e)},r6=e=>{e.forEach(r7)},r8={some:0,all:1};class ne extends e${constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:r8[n]};return function(e,t,r){let n=function({root:e,...t}){let r=e||document;r9.has(r)||r9.set(r,{});let n=r9.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(r6,{root:e,...t})),n[i]}(t);return r5.set(e,r),n.observe(e),()=>{r5.delete(e),n.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),o=t?r:n;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}let nt=(0,tN.createContext)({strict:!1});var nr=r(32582);let nn=(0,tN.createContext)({});function ni(e){return n(e.animate)||eL.some(t=>eD(e[t]))}function no(e){return!!(ni(e)||e.variants)}function na(e){return Array.isArray(e)?e.join(" "):e}var ns=r(7044);let nl={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nu={};for(let e in nl)nu[e]={isEnabled:t=>nl[e].some(e=>!!t[e])};let nc=Symbol.for("motionComponentSymbol");var nd=r(21279),nf=r(15124);function np(e,{layout:t,layoutId:r}){return f.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!tH[e]||"opacity"===e)}let nh=(e,t)=>t&&"number"==typeof e?t.transform(e):e,nm={...L.ai,transform:Math.round},ny={rotate:F.uj,rotateX:F.uj,rotateY:F.uj,rotateZ:F.uj,scale:L.hs,scaleX:L.hs,scaleY:L.hs,scaleZ:L.hs,skew:F.uj,skewX:F.uj,skewY:F.uj,distance:F.px,translateX:F.px,translateY:F.px,translateZ:F.px,x:F.px,y:F.px,z:F.px,perspective:F.px,transformPerspective:F.px,opacity:L.X4,originX:F.gQ,originY:F.gQ,originZ:F.px},ng={borderWidth:F.px,borderTopWidth:F.px,borderRightWidth:F.px,borderBottomWidth:F.px,borderLeftWidth:F.px,borderRadius:F.px,radius:F.px,borderTopLeftRadius:F.px,borderTopRightRadius:F.px,borderBottomRightRadius:F.px,borderBottomLeftRadius:F.px,width:F.px,maxWidth:F.px,height:F.px,maxHeight:F.px,top:F.px,right:F.px,bottom:F.px,left:F.px,padding:F.px,paddingTop:F.px,paddingRight:F.px,paddingBottom:F.px,paddingLeft:F.px,margin:F.px,marginTop:F.px,marginRight:F.px,marginBottom:F.px,marginLeft:F.px,backgroundPositionX:F.px,backgroundPositionY:F.px,...ny,zIndex:nm,fillOpacity:L.X4,strokeOpacity:L.X4,numOctaves:nm},nv={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nb=d.length;function nw(e,t,r){let{style:n,vars:i,transformOrigin:o}=e,a=!1,s=!1;for(let e in t){let r=t[e];if(f.has(e)){a=!0;continue}if((0,t$.j)(e)){i[e]=r;continue}{let t=nh(r,ng[e]);e.startsWith("origin")?(s=!0,o[e]=t):n[e]=t}}if(!t.transform&&(a||r?n.transform=function(e,t,r){let n="",i=!0;for(let o=0;o<nb;o++){let a=d[o],s=e[a];if(void 0===s)continue;let l=!0;if(!(l="number"==typeof s?s===+!!a.startsWith("scale"):0===parseFloat(s))||r){let e=nh(s,ng[a]);if(!l){i=!1;let t=nv[a]||a;n+=`${t}(${e}) `}r&&(t[a]=e)}}return n=n.trim(),r?n=r(t,i?"":n):i&&(n="none"),n}(t,e.transform,r):n.transform&&(n.transform="none")),s){let{originX:e="50%",originY:t="50%",originZ:r=0}=o;n.transformOrigin=`${e} ${t} ${r}`}}let nx=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nE(e,t,r){for(let n in t)(0,g.S)(t[n])||np(n,r)||(e[n]=t[n])}let n_={offset:"stroke-dashoffset",array:"stroke-dasharray"},nP={offset:"strokeDashoffset",array:"strokeDasharray"};function nR(e,{attrX:t,attrY:r,attrScale:n,pathLength:i,pathSpacing:o=1,pathOffset:a=0,...s},l,u,c){if(nw(e,s,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:f}=e;d.transform&&(f.transform=d.transform,delete d.transform),(f.transform||d.transformOrigin)&&(f.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),f.transform&&(f.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==r&&(d.y=r),void 0!==n&&(d.scale=n),void 0!==i&&function(e,t,r=1,n=0,i=!0){e.pathLength=1;let o=i?n_:nP;e[o.offset]=F.px.transform(-n);let a=F.px.transform(t),s=F.px.transform(r);e[o.array]=`${a} ${s}`}(d,i,o,a,!1)}let nS=()=>({...nx(),attrs:{}}),nO=e=>"string"==typeof e&&"svg"===e.toLowerCase(),nT=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nj(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||nT.has(e)}let nM=e=>!nj(e);try{!function(e){e&&(nM=t=>t.startsWith("on")?!nj(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let nA=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nC(e){if("string"!=typeof e||e.includes("-"));else if(nA.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var nk=r(72789);let nD=e=>(t,r)=>{let i=(0,tN.useContext)(nn),a=(0,tN.useContext)(nd.t),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},r,i,a){return{latestValues:function(e,t,r,i){let a={},s=i(e,{});for(let e in s)a[e]=tZ(s[e]);let{initial:l,animate:u}=e,c=ni(e),d=no(e);t&&d&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let f=!!r&&!1===r.initial,p=(f=f||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!n(p)){let t=Array.isArray(p)?p:[p];for(let r=0;r<t.length;r++){let n=o(e,t[r]);if(n){let{transitionEnd:e,transition:t,...r}=n;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=f?t.length-1:0;t=t[e]}null!==t&&(a[e]=t)}for(let t in e)a[t]=e[t]}}}return a}(r,i,a,e),renderState:t()}})(e,t,i,a);return r?s():(0,nk.M)(s)};function nN(e,t,r){let{style:n}=e,i={};for(let o in n)((0,g.S)(n[o])||t.style&&(0,g.S)(t.style[o])||np(o,e)||r?.getValue(o)?.liveStyle!==void 0)&&(i[o]=n[o]);return i}let nL={useVisualState:nD({scrapeMotionValuesFromProps:nN,createRenderState:nx})};function nF(e,t,r){let n=nN(e,t,r);for(let r in e)((0,g.S)(e[r])||(0,g.S)(t[r]))&&(n[-1!==d.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return n}let nI={useVisualState:nD({scrapeMotionValuesFromProps:nF,createRenderState:nS})},nU=e=>t=>t.test(e),nB=[L.ai,F.px,F.KN,F.uj,F.vw,F.vh,{test:e=>"auto"===e,parse:e=>e}],nV=e=>nB.find(nU(e)),n$=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),nH=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,nW=e=>/^0[^.\s]+$/u.test(e);var nG=r(68762);let nz=new Set(["brightness","contrast","saturate","opacity"]);function nq(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=r.match(nG.S)||[];if(!n)return e;let i=r.replace(n,""),o=+!!nz.has(t);return n!==r&&(o*=100),t+"("+o+i+")"}let nK=/\b([a-z-]*)\(.*?\)/gu,nX={...eg.f,getAnimatableNone:e=>{let t=e.match(nK);return t?t.map(nq).join(" "):e}};var nY=r(7504);let nQ={...ng,color:nY.y,backgroundColor:nY.y,outlineColor:nY.y,fill:nY.y,stroke:nY.y,borderColor:nY.y,borderTopColor:nY.y,borderRightColor:nY.y,borderBottomColor:nY.y,borderLeftColor:nY.y,filter:nX,WebkitFilter:nX},nJ=e=>nQ[e];function nZ(e,t){let r=nJ(e);return r!==nX&&(r=eg.f),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let n0=new Set(["auto","none","0"]);class n1 extends K{constructor(e,t,r,n,i){super(e,t,r,n,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:r}=this;if(!t||!t.current)return;super.readKeyframes();for(let r=0;r<e.length;r++){let n=e[r];if("string"==typeof n&&(n=n.trim(),(0,t$.p)(n))){let i=function e(t,r,n=1){(0,Y.V)(n<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,o]=function(e){let t=nH.exec(e);if(!t)return[,];let[,r,n,i]=t;return[`--${r??n}`,i]}(t);if(!i)return;let a=window.getComputedStyle(r).getPropertyValue(i);if(a){let e=a.trim();return n$(e)?parseFloat(e):e}return(0,t$.p)(o)?e(o,r,n+1):o}(n,t.current);void 0!==i&&(e[r]=i),r===e.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!p.has(r)||2!==e.length)return;let[n,i]=e,o=nV(n),a=nV(i);if(o!==a)if(I(o)&&I(a))for(let t=0;t<e.length;t++){let r=e[t];"string"==typeof r&&(e[t]=parseFloat(r))}else V[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,r=[];for(let t=0;t<e.length;t++){var n;(null===e[t]||("number"==typeof(n=e[t])?0===n:null===n||"none"===n||"0"===n||nW(n)))&&r.push(t)}r.length&&function(e,t,r){let n,i=0;for(;i<e.length&&!n;){let t=e[i];"string"==typeof t&&!n0.has(t)&&(0,eg.V)(t).values.length&&(n=e[i]),i++}if(n&&r)for(let i of t)e[i]=nZ(r,n)}(e,r,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:r}=this;if(!e||!e.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=V[r](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let n=t[t.length-1];void 0!==n&&e.getValue(r,n).jump(n,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:r}=this;if(!e||!e.current)return;let n=e.getValue(t);n&&n.jump(this.measuredOrigin,!1);let i=r.length-1,o=r[i];r[i]=V[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,r])=>{e.getValue(t).set(r)}),this.resolveNoneKeyframes()}}let n2=[...nB,nY.y,eg.f],n3=e=>n2.find(nU(e)),n4={current:null},n5={current:!1},n9=new WeakMap,n7=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class n6{scrapeMotionValuesFromProps(e,t,r){return{}}constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,blockInitialAnimation:i,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=K,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=_.k.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,c.Gt.render(this.render,!1,!0))};let{latestValues:s,renderState:l}=o;this.latestValues=s,this.baseTarget={...s},this.initialValues=t.initial?{...s}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=ni(t),this.isVariantNode=no(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...d}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in d){let t=d[e];void 0!==s[e]&&(0,g.S)(t)&&t.set(s[e],!1)}}mount(e){this.current=e,n9.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),n5.current||function(){if(n5.current=!0,ns.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>n4.current=e.matches;e.addListener(t),t()}else n4.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||n4.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),(0,c.WG)(this.notifyUpdate),(0,c.WG)(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let r;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let n=f.has(e);n&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&c.Gt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),r&&r(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in nu){let t=nu[e];if(!t)continue;let{isEnabled:r,Feature:n}=t;if(!this.features[e]&&n&&r(this.props)&&(this.features[e]=new n(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):e8()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<n7.length;t++){let r=n7[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=e["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(e,t,r){for(let n in t){let i=t[n],o=r[n];if((0,g.S)(i))e.addValue(n,i);else if((0,g.S)(o))e.addValue(n,(0,h.OQ)(i,{owner:e}));else if(o!==i)if(e.hasValue(n)){let t=e.getValue(n);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(n);e.addValue(n,(0,h.OQ)(void 0!==t?t:i,{owner:e}))}}for(let n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let r=this.values.get(e);t!==r&&(r&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=(0,h.OQ)(null===t?void 0:t,{owner:this}),this.addValue(e,r)),r}readValue(e,t){let r=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=r&&("string"==typeof r&&(n$(r)||nW(r))?r=parseFloat(r):!n3(r)&&eg.f.test(t)&&(r=nZ(e,t)),this.setBaseTarget(e,(0,g.S)(r)?r.get():r)),(0,g.S)(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let n=o(this.props,r,this.presenceContext?.custom);n&&(t=n[e])}if(r&&void 0!==t)return t;let n=this.getBaseTargetFromProps(this.props,e);return void 0===n||(0,g.S)(n)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new tX.v),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class n8 extends n6{constructor(){super(...arguments),this.KeyframeResolver=n1}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;(0,g.S)(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function ie(e,{style:t,vars:r},n,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(n)),r)e.style.setProperty(o,r[o])}class it extends n8{constructor(){super(...arguments),this.type="html",this.renderInstance=ie}readValueFromInstance(e,t){if(f.has(t))return this.projection?.isProjecting?C(t):D(e,t);{let r=window.getComputedStyle(e),n=((0,t$.j)(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return td(e,t)}build(e,t,r){nw(e,t,r.transformTemplate)}scrapeMotionValuesFromProps(e,t,r){return nN(e,t,r)}}let ir=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class ii extends n8{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=e8}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(f.has(t)){let e=nJ(t);return e&&e.default||0}return t=ir.has(t)?t:b(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,r){return nF(e,t,r)}build(e,t,r){nR(e,t,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(e,t,r,n){for(let r in ie(e,t,void 0,n),t.attrs)e.setAttribute(ir.has(r)?r:b(r),t.attrs[r])}mount(e){this.isSVGTag=nO(e.tagName),super.mount(e)}}let io=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}((l={animation:{Feature:eH},exit:{Feature:eG},inView:{Feature:ne},tap:{Feature:r4},focus:{Feature:rX},hover:{Feature:rK},pan:{Feature:tC},drag:{Feature:tM,ProjectionNode:rW,MeasureLayout:tG},layout:{ProjectionNode:rW,MeasureLayout:tG}},u=(e,t)=>nC(e)?new ii(t):new it(t,{allowProjection:e!==tN.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:n,Component:i}){function o(e,o){var a,s,l;let u,c={...(0,tN.useContext)(nr.Q),...e,layoutId:function({layoutId:e}){let t=(0,tN.useContext)(tF.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:d}=c,f=function(e){let{initial:t,animate:r}=function(e,t){if(ni(e)){let{initial:t,animate:r}=e;return{initial:!1===t||eD(t)?t:void 0,animate:eD(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,tN.useContext)(nn));return(0,tN.useMemo)(()=>({initial:t,animate:r}),[na(t),na(r)])}(e),p=n(e,d);if(!d&&ns.B){s=0,l=0,(0,tN.useContext)(nt).strict;let e=function(e){let{drag:t,layout:r}=nu;if(!t&&!r)return{};let n={...t,...r};return{MeasureLayout:t?.isEnabled(e)||r?.isEnabled(e)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(c);u=e.MeasureLayout,f.visualElement=function(e,t,r,n,i){let{visualElement:o}=(0,tN.useContext)(nn),a=(0,tN.useContext)(nt),s=(0,tN.useContext)(nd.t),l=(0,tN.useContext)(nr.Q).reducedMotion,u=(0,tN.useRef)(null);n=n||a.renderer,!u.current&&n&&(u.current=n(e,{visualState:t,parent:o,props:r,presenceContext:s,blockInitialAnimation:!!s&&!1===s.initial,reducedMotionConfig:l}));let c=u.current,d=(0,tN.useContext)(tI);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,r,n){let{layoutId:i,layout:o,drag:a,dragConstraints:s,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!a||s&&tp(s),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:n,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,r,i,d);let f=(0,tN.useRef)(!1);(0,tN.useInsertionEffect)(()=>{c&&f.current&&c.update(r,s)});let p=r[w],h=(0,tN.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,nf.E)(()=>{c&&(f.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),tD.render(c.render),h.current&&c.animationState&&c.animationState.animateChanges())}),(0,tN.useEffect)(()=>{c&&(!h.current&&c.animationState&&c.animationState.animateChanges(),h.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),h.current=!1))}),c}(i,p,c,t,e.ProjectionNode)}return(0,tk.jsxs)(nn.Provider,{value:f,children:[u&&f.visualElement?(0,tk.jsx)(u,{visualElement:f.visualElement,...c}):null,r(i,e,(a=f.visualElement,(0,tN.useCallback)(e=>{e&&p.onMount&&p.onMount(e),a&&(e?a.mount(e):a.unmount()),o&&("function"==typeof o?o(e):tp(o)&&(o.current=e))},[a])),p,d,f.visualElement)]})}e&&function(e){for(let t in e)nu[t]={...nu[t],...e[t]}}(e),o.displayName=`motion.${"string"==typeof i?i:`create(${i.displayName??i.name??""})`}`;let a=(0,tN.forwardRef)(o);return a[nc]=i,a}({...nC(e)?nI:nL,preloadedFeatures:l,useRender:function(e=!1){return(t,r,n,{latestValues:i},o)=>{let a=(nC(t)?function(e,t,r,n){let i=(0,tN.useMemo)(()=>{let r=nS();return nR(r,t,nO(n),e.transformTemplate,e.style),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};nE(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return nE(n,r,e),Object.assign(n,function({transformTemplate:e},t){return(0,tN.useMemo)(()=>{let r=nx();return nw(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,i,o,t),s=function(e,t,r){let n={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(nM(i)||!0===r&&nj(i)||!t&&!nj(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}(r,"string"==typeof t,e),l=t!==tN.Fragment?{...s,...a,ref:n}:{},{children:u}=r,c=(0,tN.useMemo)(()=>(0,g.S)(u)?u.get():u,[u]);return(0,tN.createElement)(t,{...l,children:c})}}(t),createVisualElement:u,Component:e})}))},72639:(e,t,r)=>{"use strict";function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n})},72789:(e,t,r)=>{"use strict";r.d(t,{M:()=>i});var n=r(43210);function i(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},72859:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return o}});let n=r(39444),i=["(..)(..)","(.)","(..)","(...)"];function o(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function a(e){let t,r,o;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,o]=e.split(r,2);break}if(!t||!r||!o)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":o="/"===t?"/"+o:t+"/"+o;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});o=t.split("/").slice(0,-1).concat(o).join("/");break;case"(...)":o="/"+o;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});o=a.slice(0,-2).concat(o).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:o}}},72900:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{preconnect:function(){return a},preloadFont:function(){return o},preloadStyle:function(){return i}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(46033));function i(e,t,r){let i={as:"style"};"string"==typeof t&&(i.crossOrigin=t),"string"==typeof r&&(i.nonce=r),n.default.preload(e,i)}function o(e,t,r,i){let o={as:"font",type:t};"string"==typeof r&&(o.crossOrigin=r),"string"==typeof i&&(o.nonce=i),n.default.preload(e,o)}function a(e,t,r){let i={};"string"==typeof t&&(i.crossOrigin=t),"string"==typeof r&&(i.nonce=r),n.default.preconnect(e,i)}},73063:(e,t,r)=>{"use strict";r.d(t,{u:()=>i});var n=r(21874);let i={test:(0,r(7236).$)("#"),parse:function(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:n.B.transform}},73102:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createParamsFromClient:function(){return u},createPrerenderParamsForClientSegment:function(){return p},createServerParamsForMetadata:function(){return c},createServerParamsForRoute:function(){return d},createServerParamsForServerSegment:function(){return f}}),r(43763);let n=r(84971),i=r(63033),o=r(71617),a=r(72609),s=r(68388),l=r(76926);function u(e,t){var r;let n=i.workUnitAsyncStorage.getStore();if(n)switch(n.type){case"prerender":case"prerender-ppr":case"prerender-legacy":return h(e,t,n)}return r=0,y(e)}r(44523);let c=f;function d(e,t){var r;let n=i.workUnitAsyncStorage.getStore();if(n)switch(n.type){case"prerender":case"prerender-ppr":case"prerender-legacy":return h(e,t,n)}return r=0,y(e)}function f(e,t){var r;let n=i.workUnitAsyncStorage.getStore();if(n)switch(n.type){case"prerender":case"prerender-ppr":case"prerender-legacy":return h(e,t,n)}return r=0,y(e)}function p(e,t){let r=i.workUnitAsyncStorage.getStore();if(r&&"prerender"===r.type){let n=t.fallbackRouteParams;if(n){for(let t in e)if(n.has(t))return(0,s.makeHangingPromise)(r.renderSignal,"`params`")}}return Promise.resolve(e)}function h(e,t,r){let i=t.fallbackRouteParams;if(i){let o=!1;for(let t in e)if(i.has(t)){o=!0;break}if(o)return"prerender"===r.type?function(e,t,r){let i=m.get(e);if(i)return i;let o=(0,s.makeHangingPromise)(r.renderSignal,"`params`");return m.set(e,o),Object.keys(e).forEach(e=>{a.wellKnownProperties.has(e)||Object.defineProperty(o,e,{get(){let i=(0,a.describeStringPropertyAccess)("params",e),o=b(t,i);(0,n.abortAndThrowOnSynchronousRequestDataAccess)(t,i,o,r)},set(t){Object.defineProperty(o,e,{value:t,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0})}),o}(e,t.route,r):function(e,t,r,i){let o=m.get(e);if(o)return o;let s={...e},l=Promise.resolve(s);return m.set(e,l),Object.keys(e).forEach(o=>{a.wellKnownProperties.has(o)||(t.has(o)?(Object.defineProperty(s,o,{get(){let e=(0,a.describeStringPropertyAccess)("params",o);"prerender-ppr"===i.type?(0,n.postponeWithTracking)(r.route,e,i.dynamicTracking):(0,n.throwToInterruptStaticGeneration)(e,r,i)},enumerable:!0}),Object.defineProperty(l,o,{get(){let e=(0,a.describeStringPropertyAccess)("params",o);"prerender-ppr"===i.type?(0,n.postponeWithTracking)(r.route,e,i.dynamicTracking):(0,n.throwToInterruptStaticGeneration)(e,r,i)},set(e){Object.defineProperty(l,o,{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0})):l[o]=e[o])}),l}(e,i,t,r)}return y(e)}let m=new WeakMap;function y(e){let t=m.get(e);if(t)return t;let r=Promise.resolve(e);return m.set(e,r),Object.keys(e).forEach(t=>{a.wellKnownProperties.has(t)||(r[t]=e[t])}),r}let g=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(b),v=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t,r){let n=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${n}used ${t}. \`params\` should be awaited before using its properties. The following properties were not available through enumeration because they conflict with builtin property names: ${function(e){switch(e.length){case 0:throw Object.defineProperty(new o.InvariantError("Expected describeListOfPropertyNames to be called with a non-empty list of strings."),"__NEXT_ERROR_CODE",{value:"E531",enumerable:!1,configurable:!0});case 1:return`\`${e[0]}\``;case 2:return`\`${e[0]}\` and \`${e[1]}\``;default:{let t="";for(let r=0;r<e.length-1;r++)t+=`\`${e[r]}\`, `;return t+`, and \`${e[e.length-1]}\``}}}(r)}. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E482",enumerable:!1,configurable:!0})});function b(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`params\` should be awaited before using its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E307",enumerable:!1,configurable:!0})}},73303:(e,t,r)=>{"use strict";r.d(t,{s:()=>v});var n=r(78205),i=r(97758),o=r(57211),a=r(24325),s=r(96184),l=r(91955),u=r(23671);let c=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>u.Gt.update(t,e),stop:()=>(0,u.WG)(t),now:()=>u.uv.isProcessing?u.uv.timestamp:a.k.now()}};var d=r(53532),f=r(57225),p=r(34948),h=r(29070),m=r(43500),y=r(63830);let g=e=>e/100;class v extends y.q{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(e=!0)=>{if(e){let{motionValue:e}=this.options;e&&e.updatedAt!==a.k.now()&&this.tick(a.k.now())}this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},s.q.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;(0,m.E)(e);let{type:t=f.i,repeat:r=0,repeatDelay:i=0,repeatType:o,velocity:a=0}=e,{keyframes:s}=e,u=t||f.i;u!==f.i&&"number"!=typeof s[0]&&(this.mixKeyframes=(0,n.F)(g,(0,l.j)(s[0],s[1])),s=[0,100]);let c=u({...e,keyframes:s});"mirror"===o&&(this.mirroredGenerator=u({...e,keyframes:[...s].reverse(),velocity:-a})),null===c.calculatedDuration&&(c.calculatedDuration=(0,p.t)(c));let{calculatedDuration:d}=c;this.calculatedDuration=d,this.resolvedDuration=d+i,this.totalDuration=this.resolvedDuration*(r+1)-i,this.generator=c}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:r,totalDuration:n,mixKeyframes:o,mirroredGenerator:a,resolvedDuration:s,calculatedDuration:l}=this;if(null===this.startTime)return r.next(0);let{delay:u=0,keyframes:c,repeat:f,repeatType:p,repeatDelay:m,type:y,onUpdate:g,finalKeyframe:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-n/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let b=this.currentTime-u*(this.playbackSpeed>=0?1:-1),w=this.playbackSpeed>=0?b<0:b>n;this.currentTime=Math.max(b,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let x=this.currentTime,E=r;if(f){let e=Math.min(this.currentTime,n)/s,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,(t=Math.min(t,f+1))%2&&("reverse"===p?(r=1-r,m&&(r-=m/s)):"mirror"===p&&(E=a)),x=(0,i.q)(0,1,r)*s}let _=w?{done:!1,value:c[0]}:E.next(x);o&&(_.value=o(_.value));let{done:P}=_;w||null===l||(P=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let R=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return R&&y!==d.B&&(_.value=(0,h.X)(c,this.options,v,this.speed)),g&&g(_.value),R&&this.finish(),_}then(e,t){return this.finished.then(e,t)}get duration(){return(0,o.X)(this.calculatedDuration)}get time(){return(0,o.X)(this.currentTime)}set time(e){e=(0,o.f)(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(a.k.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=(0,o.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=c,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=t??r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(a.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,s.q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}},73406:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{IDLE_LINK_STATUS:function(){return u},PENDING_LINK_STATUS:function(){return l},mountFormInstance:function(){return v},mountLinkInstance:function(){return g},onLinkVisibilityChanged:function(){return w},onNavigationIntent:function(){return x},pingVisibleLinks:function(){return _},setLinkForCurrentNavigation:function(){return c},unmountLinkForCurrentNavigation:function(){return d},unmountPrefetchableInstance:function(){return b}}),r(63690);let n=r(89752),i=r(59154),o=r(50593),a=r(43210),s=null,l={pending:!0},u={pending:!1};function c(e){(0,a.startTransition)(()=>{null==s||s.setOptimisticLinkStatus(u),null==e||e.setOptimisticLinkStatus(l),s=e})}function d(e){s===e&&(s=null)}let f="function"==typeof WeakMap?new WeakMap:new Map,p=new Set,h="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;w(t.target,e)}},{rootMargin:"200px"}):null;function m(e,t){void 0!==f.get(e)&&b(e),f.set(e,t),null!==h&&h.observe(e)}function y(e){try{return(0,n.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function g(e,t,r,n,i,o){if(i){let i=y(t);if(null!==i){let t={router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:o};return m(e,t),t}}return{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:o}}function v(e,t,r,n){let i=y(t);null!==i&&m(e,{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:null})}function b(e){let t=f.get(e);if(void 0!==t){f.delete(e),p.delete(t);let r=t.prefetchTask;null!==r&&(0,o.cancelPrefetchTask)(r)}null!==h&&h.unobserve(e)}function w(e,t){let r=f.get(e);void 0!==r&&(r.isVisible=t,t?p.add(r):p.delete(r),E(r))}function x(e,t){let r=f.get(e);void 0!==r&&void 0!==r&&(r.wasHoveredOrTouched=!0,E(r))}function E(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,o.cancelPrefetchTask)(t);return}}function _(e,t){let r=(0,o.getCurrentCacheVersion)();for(let n of p){let a=n.prefetchTask;if(null!==a&&n.cacheVersion===r&&a.key.nextUrl===e&&a.treeAtTimeOfPrefetch===t)continue;null!==a&&(0,o.cancelPrefetchTask)(a);let s=(0,o.createCacheKey)(n.prefetchHref,e),l=n.wasHoveredOrTouched?o.PrefetchPriority.Intent:o.PrefetchPriority.Default;n.prefetchTask=(0,o.schedulePrefetchTask)(s,t,n.kind===i.PrefetchKind.FULL,l),n.cacheVersion=(0,o.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73685:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var n=r(60912);let i=e=>(e*=2)<1?.5*(0,n.dg)(e):.5*(2-Math.pow(2,-10*(e-1)))},74007:(e,t)=>{"use strict";function r(e){var t;let[r,n,i,o]=e.slice(-4),a=e.slice(0,-4);return{pathToSegment:a.slice(0,-1),segmentPath:a,segment:null!=(t=a[a.length-1])?t:"",tree:r,seedData:n,head:i,isHeadPartial:o,isRootRender:4===e.length}}function n(e){return e.slice(2)}function i(e){return"string"==typeof e?e:e.map(r)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getFlightDataPartsFromPath:function(){return r},getNextFlightSegmentPath:function(){return n},normalizeFlightData:function(){return i}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74177:(e,t,r)=>{"use strict";r.d(t,{D:()=>n});let n=e=>Array.isArray(e)&&"number"==typeof e[0]},74479:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&null!==e}r.d(t,{G:()=>n})},75076:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return o},prefetchReducer:function(){return a}});let n=r(5144),i=r(5334),o=new n.PromiseQueue(5),a=function(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return(0,i.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75317:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{bgBlack:function(){return S},bgBlue:function(){return M},bgCyan:function(){return C},bgGreen:function(){return T},bgMagenta:function(){return A},bgRed:function(){return O},bgWhite:function(){return k},bgYellow:function(){return j},black:function(){return y},blue:function(){return w},bold:function(){return u},cyan:function(){return _},dim:function(){return c},gray:function(){return R},green:function(){return v},hidden:function(){return h},inverse:function(){return p},italic:function(){return d},magenta:function(){return x},purple:function(){return E},red:function(){return g},reset:function(){return l},strikethrough:function(){return m},underline:function(){return f},white:function(){return P},yellow:function(){return b}});let{env:n,stdout:i}=(null==(r=globalThis)?void 0:r.process)??{},o=n&&!n.NO_COLOR&&(n.FORCE_COLOR||(null==i?void 0:i.isTTY)&&!n.CI&&"dumb"!==n.TERM),a=(e,t,r,n)=>{let i=e.substring(0,n)+r,o=e.substring(n+t.length),s=o.indexOf(t);return~s?i+a(o,t,r,s):i+o},s=(e,t,r=e)=>o?n=>{let i=""+n,o=i.indexOf(t,e.length);return~o?e+a(i,t,r,o)+t:e+i+t}:String,l=o?e=>`\x1b[0m${e}\x1b[0m`:String,u=s("\x1b[1m","\x1b[22m","\x1b[22m\x1b[1m"),c=s("\x1b[2m","\x1b[22m","\x1b[22m\x1b[2m"),d=s("\x1b[3m","\x1b[23m"),f=s("\x1b[4m","\x1b[24m"),p=s("\x1b[7m","\x1b[27m"),h=s("\x1b[8m","\x1b[28m"),m=s("\x1b[9m","\x1b[29m"),y=s("\x1b[30m","\x1b[39m"),g=s("\x1b[31m","\x1b[39m"),v=s("\x1b[32m","\x1b[39m"),b=s("\x1b[33m","\x1b[39m"),w=s("\x1b[34m","\x1b[39m"),x=s("\x1b[35m","\x1b[39m"),E=s("\x1b[38;2;173;127;168m","\x1b[39m"),_=s("\x1b[36m","\x1b[39m"),P=s("\x1b[37m","\x1b[39m"),R=s("\x1b[90m","\x1b[39m"),S=s("\x1b[40m","\x1b[49m"),O=s("\x1b[41m","\x1b[49m"),T=s("\x1b[42m","\x1b[49m"),j=s("\x1b[43m","\x1b[49m"),M=s("\x1b[44m","\x1b[49m"),A=s("\x1b[45m","\x1b[49m"),C=s("\x1b[46m","\x1b[49m"),k=s("\x1b[47m","\x1b[49m")},75539:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"InvariantError",{enumerable:!0,get:function(){return r}});class r extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}},76299:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},76715:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function o(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},76926:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(61120));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}let o={current:null},a="function"==typeof n.cache?n.cache:e=>e,s=console.warn;function l(e){return function(...t){s(e(...t))}}a(e=>{try{s(o.current)}finally{o.current=null}})},77022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let n=r(43210),i=r(51215),o="next-route-announcer";function a(e){let{tree:t}=e,[r,a]=(0,n.useState)(null);(0,n.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(o)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(o);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(o)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,l]=(0,n.useState)(""),u=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),r?(0,i.createPortal)(s,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77341:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e:[e]}function n(e){if(null!=e)return r(e)}function i(e){let t;if("string"==typeof e)try{t=(e=new URL(e)).origin}catch{}return t}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getOrigin:function(){return i},resolveArray:function(){return r},resolveAsArrayOrUndefined:function(){return n}})},77359:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isFullStringUrl:function(){return o},parseUrl:function(){return a},stripNextRscUnionQuery:function(){return s}});let n=r(9977),i="http://n";function o(e){return/https?:\/\//.test(e)}function a(e){let t;try{t=new URL(e,i)}catch{}return t}function s(e){let t=new URL(e,i);return t.searchParams.delete(n.NEXT_RSC_UNION_QUERY),t.pathname+t.search}},78205:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});let n=(e,t)=>r=>t(e(r)),i=(...e)=>e.reduce(n)},78671:(e,t,r)=>{"use strict";e.exports=r(33873)},78866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return h}});let n=r(59008),i=r(57391),o=r(86770),a=r(2030),s=r(25232),l=r(59435),u=r(41500),c=r(89752),d=r(96493),f=r(68214),p=r(22308);function h(e,t){let{origin:r}=t,h={},m=e.canonicalUrl,y=e.tree;h.preserveCustomHistoryState=!1;let g=(0,c.createEmptyCacheNode)(),v=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);g.lazyData=(0,n.fetchServerResponse)(new URL(m,r),{flightRouterState:[y[0],y[1],y[2],"refetch"],nextUrl:v?e.nextUrl:null});let b=Date.now();return g.lazyData.then(async r=>{let{flightData:n,canonicalUrl:c}=r;if("string"==typeof n)return(0,s.handleExternalUrl)(e,h,n,e.pushRef.pendingPush);for(let r of(g.lazyData=null,n)){let{tree:n,seedData:l,head:f,isRootRender:w}=r;if(!w)return console.log("REFRESH FAILED"),e;let x=(0,o.applyRouterStatePatchToTree)([""],y,n,e.canonicalUrl);if(null===x)return(0,d.handleSegmentMismatch)(e,t,n);if((0,a.isNavigatingToNewRootLayout)(y,x))return(0,s.handleExternalUrl)(e,h,m,e.pushRef.pendingPush);let E=c?(0,i.createHrefFromUrl)(c):void 0;if(c&&(h.canonicalUrl=E),null!==l){let e=l[1],t=l[3];g.rsc=e,g.prefetchRsc=null,g.loading=t,(0,u.fillLazyItemsTillLeafWithHead)(b,g,void 0,n,l,f,void 0),h.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:x,updatedCache:g,includeNextUrl:v,canonicalUrl:h.canonicalUrl||e.canonicalUrl}),h.cache=g,h.patchedTree=x,y=x}return(0,l.handleMutable)(e,h)},()=>e)}r(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return y},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},79351:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41552).A)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},80178:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return i.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return o.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=r(36875),i=r(97860),o=r(55211),a=r(80414),s=r(80929),l=r(68613);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},80407:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Meta:function(){return o},MetaFilter:function(){return a},MultiMeta:function(){return u}});let n=r(37413);r(61120);let i=r(89735);function o({name:e,property:t,content:r,media:i}){return null!=r&&""!==r?(0,n.jsx)("meta",{...e?{name:e}:{property:t},...i?{media:i}:void 0,content:"string"==typeof r?r:r.toString()}):null}function a(e){let t=[];for(let r of e)Array.isArray(r)?t.push(...r.filter(i.nonNullable)):(0,i.nonNullable)(r)&&t.push(r);return t}let s=new Set(["og:image","twitter:image","og:video","og:audio"]);function l(e,t){return s.has(e)&&"url"===t?e:((e.startsWith("og:")||e.startsWith("twitter:"))&&(t=t.replace(/([A-Z])/g,function(e){return"_"+e.toLowerCase()})),e+":"+t)}function u({propertyPrefix:e,namePrefix:t,contents:r}){return null==r?null:a(r.map(r=>"string"==typeof r||"number"==typeof r||r instanceof URL?o({...e?{property:e}:{name:t},content:r}):function({content:e,namePrefix:t,propertyPrefix:r}){return e?a(Object.entries(e).map(([e,n])=>void 0===n?null:o({...r&&{property:l(r,e)},...t&&{name:l(t,e)},content:"string"==typeof n?n:null==n?void 0:n.toString()}))):null}({namePrefix:t,propertyPrefix:e,content:r})))}},80414:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(86358).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},80929:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(86358).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81208:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return i}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},82082:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});let n={value:null,addProjectionMetrics:null}},82348:(e,t,r)=>{"use strict";r.d(t,{QP:()=>X});let n=e=>{let t=s(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),i(r,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let i=r[e]||[];return t&&n[e]?[...i,...n[e]]:i}}},i=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),o=n?i(e.slice(1),n):void 0;if(o)return o;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},o=/^\[(.+)\]$/,a=e=>{if(o.test(e)){let t=o.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},s=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),r).forEach(([e,r])=>{l(r,n,e,t)}),n},l=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e)return c(e)?void l(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,i])=>{l(i,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,i=(i,o)=>{r.set(i,o),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(i(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):i(e,t)}}},p=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,i=t[0],o=t.length,a=e=>{let r,a=[],s=0,l=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===s){if(c===i&&(n||e.slice(u,u+o)===t)){a.push(e.slice(l,u)),l=u+o;continue}if("/"===c){r=u;continue}}"["===c?s++:"]"===c&&s--}let u=0===a.length?e:e.substring(l),c=u.startsWith("!"),d=c?u.substring(1):u;return{modifiers:a,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?e=>r({className:e,parseClassName:a}):a},h=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},m=e=>({cache:f(e.cacheSize),parseClassName:p(e),...n(e)}),y=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:i}=t,o=[],a=e.trim().split(y),s="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{modifiers:l,hasImportantModifier:u,baseClassName:c,maybePostfixModifierPosition:d}=r(t),f=!!d,p=n(f?c.substring(0,d):c);if(!p){if(!f||!(p=n(c))){s=t+(s.length>0?" "+s:s);continue}f=!1}let m=h(l).join(":"),y=u?m+"!":m,g=y+p;if(o.includes(g))continue;o.push(g);let v=i(p,f);for(let e=0;e<v.length;++e){let t=v[e];o.push(y+t)}s=t+(s.length>0?" "+s:s)}return s};function v(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=b(e))&&(n&&(n+=" "),n+=t);return n}let b=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=b(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:([a-z-]+):)?(.+)\]$/i,E=/^\d+\/\d+$/,_=new Set(["px","full","screen"]),P=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,R=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,O=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,T=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,j=e=>A(e)||_.has(e)||E.test(e),M=e=>W(e,"length",G),A=e=>!!e&&!Number.isNaN(Number(e)),C=e=>W(e,"number",A),k=e=>!!e&&Number.isInteger(Number(e)),D=e=>e.endsWith("%")&&A(e.slice(0,-1)),N=e=>x.test(e),L=e=>P.test(e),F=new Set(["length","size","percentage"]),I=e=>W(e,F,z),U=e=>W(e,"position",z),B=new Set(["image","url"]),V=e=>W(e,B,K),$=e=>W(e,"",q),H=()=>!0,W=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},G=e=>R.test(e)&&!S.test(e),z=()=>!1,q=e=>O.test(e),K=e=>T.test(e);Symbol.toStringTag;let X=function(e,...t){let r,n,i,o=function(s){return n=(r=m(t.reduce((e,t)=>t(e),e()))).cache.get,i=r.cache.set,o=a,a(s)};function a(e){let t=n(e);if(t)return t;let o=g(e,r);return i(e,o),o}return function(){return o(v.apply(null,arguments))}}(()=>{let e=w("colors"),t=w("spacing"),r=w("blur"),n=w("brightness"),i=w("borderColor"),o=w("borderRadius"),a=w("borderSpacing"),s=w("borderWidth"),l=w("contrast"),u=w("grayscale"),c=w("hueRotate"),d=w("invert"),f=w("gap"),p=w("gradientColorStops"),h=w("gradientColorStopPositions"),m=w("inset"),y=w("margin"),g=w("opacity"),v=w("padding"),b=w("saturate"),x=w("scale"),E=w("sepia"),_=w("skew"),P=w("space"),R=w("translate"),S=()=>["auto","contain","none"],O=()=>["auto","hidden","clip","visible","scroll"],T=()=>["auto",N,t],F=()=>[N,t],B=()=>["",j,M],W=()=>["auto",A,N],G=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],z=()=>["solid","dashed","dotted","double","none"],q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],K=()=>["start","end","center","between","around","evenly","stretch"],X=()=>["","0",N],Y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Q=()=>[A,N];return{cacheSize:500,separator:":",theme:{colors:[H],spacing:[j,M],blur:["none","",L,N],brightness:Q(),borderColor:[e],borderRadius:["none","","full",L,N],borderSpacing:F(),borderWidth:B(),contrast:Q(),grayscale:X(),hueRotate:Q(),invert:X(),gap:F(),gradientColorStops:[e],gradientColorStopPositions:[D,M],inset:T(),margin:T(),opacity:Q(),padding:F(),saturate:Q(),scale:Q(),sepia:X(),skew:Q(),space:F(),translate:F()},classGroups:{aspect:[{aspect:["auto","square","video",N]}],container:["container"],columns:[{columns:[L]}],"break-after":[{"break-after":Y()}],"break-before":[{"break-before":Y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...G(),N]}],overflow:[{overflow:O()}],"overflow-x":[{"overflow-x":O()}],"overflow-y":[{"overflow-y":O()}],overscroll:[{overscroll:S()}],"overscroll-x":[{"overscroll-x":S()}],"overscroll-y":[{"overscroll-y":S()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",k,N]}],basis:[{basis:T()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",N]}],grow:[{grow:X()}],shrink:[{shrink:X()}],order:[{order:["first","last","none",k,N]}],"grid-cols":[{"grid-cols":[H]}],"col-start-end":[{col:["auto",{span:["full",k,N]},N]}],"col-start":[{"col-start":W()}],"col-end":[{"col-end":W()}],"grid-rows":[{"grid-rows":[H]}],"row-start-end":[{row:["auto",{span:[k,N]},N]}],"row-start":[{"row-start":W()}],"row-end":[{"row-end":W()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",N]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",N]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...K()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...K(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...K(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[y]}],mx:[{mx:[y]}],my:[{my:[y]}],ms:[{ms:[y]}],me:[{me:[y]}],mt:[{mt:[y]}],mr:[{mr:[y]}],mb:[{mb:[y]}],ml:[{ml:[y]}],"space-x":[{"space-x":[P]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[P]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",N,t]}],"min-w":[{"min-w":[N,t,"min","max","fit"]}],"max-w":[{"max-w":[N,t,"none","full","min","max","fit","prose",{screen:[L]},L]}],h:[{h:[N,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[N,t,"auto","min","max","fit"]}],"font-size":[{text:["base",L,M]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",C]}],"font-family":[{font:[H]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",N]}],"line-clamp":[{"line-clamp":["none",A,C]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",j,N]}],"list-image":[{"list-image":["none",N]}],"list-style-type":[{list:["none","disc","decimal",N]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...z(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",j,M]}],"underline-offset":[{"underline-offset":["auto",j,N]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:F()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...G(),U]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",I]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},V]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[h]}],"gradient-via-pos":[{via:[h]}],"gradient-to-pos":[{to:[h]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...z(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:z()}],"border-color":[{border:[i]}],"border-color-x":[{"border-x":[i]}],"border-color-y":[{"border-y":[i]}],"border-color-s":[{"border-s":[i]}],"border-color-e":[{"border-e":[i]}],"border-color-t":[{"border-t":[i]}],"border-color-r":[{"border-r":[i]}],"border-color-b":[{"border-b":[i]}],"border-color-l":[{"border-l":[i]}],"divide-color":[{divide:[i]}],"outline-style":[{outline:["",...z()]}],"outline-offset":[{"outline-offset":[j,N]}],"outline-w":[{outline:[j,M]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[j,M]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",L,$]}],"shadow-color":[{shadow:[H]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...q(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":q()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",L,N]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[b]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",N]}],duration:[{duration:Q()}],ease:[{ease:["linear","in","out","in-out",N]}],delay:[{delay:Q()}],animate:[{animate:["none","spin","ping","pulse","bounce",N]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[k,N]}],"translate-x":[{"translate-x":[R]}],"translate-y":[{"translate-y":[R]}],"skew-x":[{"skew-x":[_]}],"skew-y":[{"skew-y":[_]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",N]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",N]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":F()}],"scroll-mx":[{"scroll-mx":F()}],"scroll-my":[{"scroll-my":F()}],"scroll-ms":[{"scroll-ms":F()}],"scroll-me":[{"scroll-me":F()}],"scroll-mt":[{"scroll-mt":F()}],"scroll-mr":[{"scroll-mr":F()}],"scroll-mb":[{"scroll-mb":F()}],"scroll-ml":[{"scroll-ml":F()}],"scroll-p":[{"scroll-p":F()}],"scroll-px":[{"scroll-px":F()}],"scroll-py":[{"scroll-py":F()}],"scroll-ps":[{"scroll-ps":F()}],"scroll-pe":[{"scroll-pe":F()}],"scroll-pt":[{"scroll-pt":F()}],"scroll-pr":[{"scroll-pr":F()}],"scroll-pb":[{"scroll-pb":F()}],"scroll-pl":[{"scroll-pl":F()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",N]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[j,M,C]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},83091:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createPrerenderSearchParamsForClientPage:function(){return h},createSearchParamsFromClient:function(){return d},createServerSearchParamsForMetadata:function(){return f},createServerSearchParamsForServerPage:function(){return p},makeErroringExoticSearchParamsForUseCache:function(){return b}});let n=r(43763),i=r(84971),o=r(63033),a=r(71617),s=r(68388),l=r(76926),u=r(72609),c=r(8719);function d(e,t){let r=o.workUnitAsyncStorage.getStore();if(r)switch(r.type){case"prerender":case"prerender-ppr":case"prerender-legacy":return m(t,r)}return y(e,t)}r(44523);let f=p;function p(e,t){let r=o.workUnitAsyncStorage.getStore();if(r)switch(r.type){case"prerender":case"prerender-ppr":case"prerender-legacy":return m(t,r)}return y(e,t)}function h(e){if(e.forceStatic)return Promise.resolve({});let t=o.workUnitAsyncStorage.getStore();return t&&"prerender"===t.type?(0,s.makeHangingPromise)(t.renderSignal,"`searchParams`"):Promise.resolve({})}function m(e,t){return e.forceStatic?Promise.resolve({}):"prerender"===t.type?function(e,t){let r=g.get(t);if(r)return r;let o=(0,s.makeHangingPromise)(t.renderSignal,"`searchParams`"),a=new Proxy(o,{get(r,a,s){if(Object.hasOwn(o,a))return n.ReflectAdapter.get(r,a,s);switch(a){case"then":return(0,i.annotateDynamicAccess)("`await searchParams`, `searchParams.then`, or similar",t),n.ReflectAdapter.get(r,a,s);case"status":return(0,i.annotateDynamicAccess)("`use(searchParams)`, `searchParams.status`, or similar",t),n.ReflectAdapter.get(r,a,s);default:if("string"==typeof a&&!u.wellKnownProperties.has(a)){let r=(0,u.describeStringPropertyAccess)("searchParams",a),n=E(e,r);(0,i.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}return n.ReflectAdapter.get(r,a,s)}},has(r,o){if("string"==typeof o){let r=(0,u.describeHasCheckingStringProperty)("searchParams",o),n=E(e,r);(0,i.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}return n.ReflectAdapter.has(r,o)},ownKeys(){let r="`{...searchParams}`, `Object.keys(searchParams)`, or similar",n=E(e,r);(0,i.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}});return g.set(t,a),a}(e.route,t):function(e,t){let r=g.get(e);if(r)return r;let o=Promise.resolve({}),a=new Proxy(o,{get(r,a,s){if(Object.hasOwn(o,a))return n.ReflectAdapter.get(r,a,s);switch(a){case"then":{let r="`await searchParams`, `searchParams.then`, or similar";e.dynamicShouldError?(0,c.throwWithStaticGenerationBailoutErrorWithDynamicError)(e.route,r):"prerender-ppr"===t.type?(0,i.postponeWithTracking)(e.route,r,t.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(r,e,t);return}case"status":{let r="`use(searchParams)`, `searchParams.status`, or similar";e.dynamicShouldError?(0,c.throwWithStaticGenerationBailoutErrorWithDynamicError)(e.route,r):"prerender-ppr"===t.type?(0,i.postponeWithTracking)(e.route,r,t.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(r,e,t);return}default:if("string"==typeof a&&!u.wellKnownProperties.has(a)){let r=(0,u.describeStringPropertyAccess)("searchParams",a);e.dynamicShouldError?(0,c.throwWithStaticGenerationBailoutErrorWithDynamicError)(e.route,r):"prerender-ppr"===t.type?(0,i.postponeWithTracking)(e.route,r,t.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(r,e,t)}return n.ReflectAdapter.get(r,a,s)}},has(r,o){if("string"==typeof o){let r=(0,u.describeHasCheckingStringProperty)("searchParams",o);return e.dynamicShouldError?(0,c.throwWithStaticGenerationBailoutErrorWithDynamicError)(e.route,r):"prerender-ppr"===t.type?(0,i.postponeWithTracking)(e.route,r,t.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(r,e,t),!1}return n.ReflectAdapter.has(r,o)},ownKeys(){let r="`{...searchParams}`, `Object.keys(searchParams)`, or similar";e.dynamicShouldError?(0,c.throwWithStaticGenerationBailoutErrorWithDynamicError)(e.route,r):"prerender-ppr"===t.type?(0,i.postponeWithTracking)(e.route,r,t.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(r,e,t)}});return g.set(e,a),a}(e,t)}function y(e,t){return t.forceStatic?Promise.resolve({}):function(e,t){let r=g.get(e);if(r)return r;let n=Promise.resolve(e);return g.set(e,n),Object.keys(e).forEach(r=>{u.wellKnownProperties.has(r)||Object.defineProperty(n,r,{get(){let n=o.workUnitAsyncStorage.getStore();return(0,i.trackDynamicDataInDynamicRender)(t,n),e[r]},set(e){Object.defineProperty(n,r,{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0})}),n}(e,t)}let g=new WeakMap,v=new WeakMap;function b(e){let t=v.get(e);if(t)return t;let r=Promise.resolve({}),i=new Proxy(r,{get:(t,i,o)=>(Object.hasOwn(r,i)||"string"!=typeof i||"then"!==i&&u.wellKnownProperties.has(i)||(0,c.throwForSearchParamsAccessInUseCache)(e),n.ReflectAdapter.get(t,i,o)),has:(t,r)=>("string"!=typeof r||"then"!==r&&u.wellKnownProperties.has(r)||(0,c.throwForSearchParamsAccessInUseCache)(e),n.ReflectAdapter.has(t,r)),ownKeys(){(0,c.throwForSearchParamsAccessInUseCache)(e)}});return v.set(e,i),i}let w=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(E),x=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t,r){let n=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${n}used ${t}. \`searchParams\` should be awaited before using its properties. The following properties were not available through enumeration because they conflict with builtin or well-known property names: ${function(e){switch(e.length){case 0:throw Object.defineProperty(new a.InvariantError("Expected describeListOfPropertyNames to be called with a non-empty list of strings."),"__NEXT_ERROR_CODE",{value:"E531",enumerable:!1,configurable:!0});case 1:return`\`${e[0]}\``;case 2:return`\`${e[0]}\` and \`${e[1]}\``;default:{let t="";for(let r=0;r<e.length-1;r++)t+=`\`${e[r]}\`, `;return t+`, and \`${e[e.length-1]}\``}}}(r)}. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E2",enumerable:!1,configurable:!0})});function E(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`searchParams\` should be awaited before using its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E249",enumerable:!1,configurable:!0})}},83361:(e,t,r)=>{"use strict";r.d(t,{l:()=>n});let n=e=>e},83717:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},83913:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}function n(e){return e.startsWith("@")&&"@children"!==e}function i(e,t){if(e.includes(o)){let e=JSON.stringify(t);return"{}"!==e?o+"?"+e:o}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_SEGMENT_KEY:function(){return a},PAGE_SEGMENT_KEY:function(){return o},addSearchParamsIfPageSegment:function(){return i},isGroupSegment:function(){return r},isParallelRouteSegment:function(){return n}});let o="__PAGE__",a="__DEFAULT__"},84627:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return i},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return o}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function i(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let o=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},84949:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},85429:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ServerInsertMetadata",{enumerable:!0,get:function(){return a}});let n=r(43210),i=r(68524),o=e=>{let t=(0,n.useContext)(i.ServerInsertedMetadataContext);t&&t(e)};function a(e){let{promise:t}=e,{metadata:r}=(0,n.use)(t);return o(()=>r),null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},85814:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return y},useLinkStatus:function(){return v}});let n=r(40740),i=r(60687),o=n._(r(43210)),a=r(30195),s=r(22142),l=r(59154),u=r(53038),c=r(79289),d=r(96127);r(50148);let f=r(73406),p=r(61794),h=r(63690);function m(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function y(e){let t,r,n,[a,y]=(0,o.useOptimistic)(f.IDLE_LINK_STATUS),v=(0,o.useRef)(null),{href:b,as:w,children:x,prefetch:E=null,passHref:_,replace:P,shallow:R,scroll:S,onClick:O,onMouseEnter:T,onTouchStart:j,legacyBehavior:M=!1,onNavigate:A,ref:C,unstable_dynamicOnHover:k,...D}=e;t=x,M&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let N=o.default.useContext(s.AppRouterContext),L=!1!==E,F=null===E?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:I,as:U}=o.default.useMemo(()=>{let e=m(b);return{href:e,as:w?m(w):e}},[b,w]);M&&(r=o.default.Children.only(t));let B=M?r&&"object"==typeof r&&r.ref:C,V=o.default.useCallback(e=>(null!==N&&(v.current=(0,f.mountLinkInstance)(e,I,N,F,L,y)),()=>{v.current&&((0,f.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,f.unmountPrefetchableInstance)(e)}),[L,I,N,F,y]),$={ref:(0,u.useMergedRef)(V,B),onClick(e){M||"function"!=typeof O||O(e),M&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),N&&(e.defaultPrevented||function(e,t,r,n,i,a,s){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),o.default.startTransition(()=>{if(s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(r||t,i?"replace":"push",null==a||a,n.current)})}}(e,I,U,v,P,S,A))},onMouseEnter(e){M||"function"!=typeof T||T(e),M&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),N&&L&&(0,f.onNavigationIntent)(e.currentTarget,!0===k)},onTouchStart:function(e){M||"function"!=typeof j||j(e),M&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),N&&L&&(0,f.onNavigationIntent)(e.currentTarget,!0===k)}};return(0,c.isAbsoluteUrl)(U)?$.href=U:M&&!_&&("a"!==r.type||"href"in r.props)||($.href=(0,d.addBasePath)(U)),n=M?o.default.cloneElement(r,$):(0,i.jsx)("a",{...D,...$,children:t}),(0,i.jsx)(g.Provider,{value:a,children:n})}r(32708);let g=(0,o.createContext)(f.IDLE_LINK_STATUS),v=()=>(0,o.useContext)(g);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86044:(e,t,r)=>{"use strict";r.d(t,{xQ:()=>o});var n=r(43210),i=r(21279);function o(e=!0){let t=(0,n.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:a,register:s}=t,l=(0,n.useId)();(0,n.useEffect)(()=>{if(e)return s(l)},[e]);let u=(0,n.useCallback)(()=>e&&a&&a(l),[l,a,e]);return!r&&a?[!1,u]:[!0]}},86346:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ClientPageRoot",{enumerable:!0,get:function(){return o}});let n=r(60687),i=r(75539);function o(e){let{Component:t,searchParams:o,params:a,promises:s}=e;{let e,s,{workAsyncStorage:l}=r(29294),u=l.getStore();if(!u)throw Object.defineProperty(new i.InvariantError("Expected workStore to exist when handling searchParams in a client Page."),"__NEXT_ERROR_CODE",{value:"E564",enumerable:!1,configurable:!0});let{createSearchParamsFromClient:c}=r(9221);e=c(o,u);let{createParamsFromClient:d}=r(60824);return s=d(a,u),(0,n.jsx)(t,{params:s,searchParams:e})}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86358:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return i},getAccessFallbackErrorTypeByStatus:function(){return s},getAccessFallbackHTTPStatus:function(){return a},isHTTPAccessFallbackError:function(){return o}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),i="NEXT_HTTP_ERROR_FALLBACK";function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}function a(e){return Number(e.digest.split(";")[1])}function s(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86719:(e,t)=>{"use strict";function r(e,t){if(void 0===t&&(t={}),t.onlyHashChange)return void e();let r=document.documentElement,n=r.style.scrollBehavior;r.style.scrollBehavior="auto",t.dontForceLayout||r.getClientRects(),e(),r.style.scrollBehavior=n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSmoothScroll",{enumerable:!0,get:function(){return r}})},86770:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,l){let u,[c,d,f,p,h]=r;if(1===t.length){let e=s(r,n);return(0,a.addRefreshMarkerToActiveParallelSegments)(e,l),e}let[m,y]=t;if(!(0,o.matchSegment)(m,c))return null;if(2===t.length)u=s(d[y],n);else if(null===(u=e((0,i.getNextFlightSegmentPath)(t),d[y],n,l)))return null;let g=[t[0],{...d,[y]:u},f,p];return h&&(g[4]=!0),(0,a.addRefreshMarkerToActiveParallelSegments)(g,l),g}}});let n=r(83913),i=r(74007),o=r(14077),a=r(22308);function s(e,t){let[r,i]=e,[a,l]=t;if(a===n.DEFAULT_SEGMENT_KEY&&r!==n.DEFAULT_SEGMENT_KEY)return e;if((0,o.matchSegment)(r,a)){let t={};for(let e in i)void 0!==l[e]?t[e]=s(i[e],l[e]):t[e]=i[e];for(let e in l)t[e]||(t[e]=l[e]);let n=[r,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87556:(e,t,r)=>{"use strict";function n(e,t){-1===e.indexOf(t)&&e.push(t)}function i(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}r.d(t,{Ai:()=>i,Kq:()=>n})},88092:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return o}});let n=r(86358),i=r(97860);function o(e){return(0,i.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88170:(e,t,r)=>{let{createProxy:n}=r(39844);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/error-boundary.js")},88347:(e,t,r)=>{"use strict";r.d(t,{K:()=>n});let n=(e,t,r=10)=>{let n="",i=Math.max(Math.round(t/r),2);for(let t=0;t<i;t++)n+=e(t/(i-1))+", ";return`linear(${n.substring(0,n.length-2)})`}},88920:(e,t,r)=>{"use strict";r.d(t,{N:()=>v});var n=r(60687),i=r(43210),o=r(12157),a=r(72789),s=r(15124),l=r(21279),u=r(18171),c=r(32582);class d extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,u.s)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f({children:e,isPresent:t,anchorX:r}){let o=(0,i.useId)(),a=(0,i.useRef)(null),s=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,i.useContext)(c.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:n,top:i,left:u,right:c}=s.current;if(t||!a.current||!e||!n)return;let d="left"===r?`left: ${u}`:`right: ${c}`;a.current.dataset.motionPopId=o;let f=document.createElement("style");return l&&(f.nonce=l),document.head.appendChild(f),f.sheet&&f.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${n}px !important;
            ${d}px !important;
            top: ${i}px !important;
          }
        `),()=>{document.head.contains(f)&&document.head.removeChild(f)}},[t]),(0,n.jsx)(d,{isPresent:t,childRef:a,sizeRef:s,children:i.cloneElement(e,{ref:a})})}let p=({children:e,initial:t,isPresent:r,onExitComplete:o,custom:s,presenceAffectsLayout:u,mode:c,anchorX:d})=>{let p=(0,a.M)(h),m=(0,i.useId)(),y=!0,g=(0,i.useMemo)(()=>(y=!1,{id:m,initial:t,isPresent:r,custom:s,onExitComplete:e=>{for(let t of(p.set(e,!0),p.values()))if(!t)return;o&&o()},register:e=>(p.set(e,!1),()=>p.delete(e))}),[r,p,o]);return u&&y&&(g={...g}),(0,i.useMemo)(()=>{p.forEach((e,t)=>p.set(t,!1))},[r]),i.useEffect(()=>{r||p.size||!o||o()},[r]),"popLayout"===c&&(e=(0,n.jsx)(f,{isPresent:r,anchorX:d,children:e})),(0,n.jsx)(l.t.Provider,{value:g,children:e})};function h(){return new Map}var m=r(86044);let y=e=>e.key||"";function g(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let v=({children:e,custom:t,initial:r=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:c="sync",propagate:d=!1,anchorX:f="left"})=>{let[h,v]=(0,m.xQ)(d),b=(0,i.useMemo)(()=>g(e),[e]),w=d&&!h?[]:b.map(y),x=(0,i.useRef)(!0),E=(0,i.useRef)(b),_=(0,a.M)(()=>new Map),[P,R]=(0,i.useState)(b),[S,O]=(0,i.useState)(b);(0,s.E)(()=>{x.current=!1,E.current=b;for(let e=0;e<S.length;e++){let t=y(S[e]);w.includes(t)?_.delete(t):!0!==_.get(t)&&_.set(t,!1)}},[S,w.length,w.join("-")]);let T=[];if(b!==P){let e=[...b];for(let t=0;t<S.length;t++){let r=S[t],n=y(r);w.includes(n)||(e.splice(t,0,r),T.push(r))}return"wait"===c&&T.length&&(e=T),O(g(e)),R(b),null}let{forceRender:j}=(0,i.useContext)(o.L);return(0,n.jsx)(n.Fragment,{children:S.map(e=>{let i=y(e),o=(!d||!!h)&&(b===S||w.includes(i));return(0,n.jsx)(p,{isPresent:o,initial:(!x.current||!!r)&&void 0,custom:t,presenceAffectsLayout:u,mode:c,onExitComplete:o?void 0:()=>{if(!_.has(i))return;_.set(i,!0);let e=!0;_.forEach(t=>{t||(e=!1)}),e&&(j?.(),O(E.current),d&&v?.(),l&&l())},anchorX:f,children:e},i)})})}},89330:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unresolvedThenable",{enumerable:!0,get:function(){return r}});let r={then:()=>{}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89735:(e,t)=>{"use strict";function r(e){return null!=e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"nonNullable",{enumerable:!0,get:function(){return r}})},89752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return j},createPrefetchURL:function(){return O},default:function(){return k},isExternalURL:function(){return S}});let n=r(40740),i=r(60687),o=n._(r(43210)),a=r(22142),s=r(59154),l=r(57391),u=r(10449),c=r(19129),d=n._(r(35656)),f=r(35416),p=r(96127),h=r(77022),m=r(67086),y=r(44397),g=r(89330),v=r(25942),b=r(26736),w=r(70642),x=r(12776),E=r(63690),_=r(36875),P=r(97860);r(73406);let R={};function S(e){return e.origin!==window.location.origin}function O(e){let t;if((0,f.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return S(t)?null:t}function T(e){let{appRouterState:t}=e;return(0,o.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,i={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(i,"",n)):window.history.replaceState(i,"",n)},[t]),(0,o.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function j(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function M(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function A(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,i=null!==n?n:r;return(0,o.useDeferredValue)(r,i)}function C(e){let t,{actionQueue:r,assetPrefix:n,globalError:l}=e,f=(0,c.useActionQueue)(r),{canonicalUrl:p}=f,{searchParams:x,pathname:S}=(0,o.useMemo)(()=>{let e=new URL(p,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[p]);(0,o.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(R.pendingMpaPath=void 0,(0,c.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,o.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,P.isRedirectError)(t)){e.preventDefault();let r=(0,_.getURLFromRedirectError)(t);(0,_.getRedirectTypeFromError)(t)===P.RedirectType.push?E.publicAppRouterInstance.push(r,{}):E.publicAppRouterInstance.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:O}=f;if(O.mpaNavigation){if(R.pendingMpaPath!==p){let e=window.location;O.pendingPush?e.assign(p):e.replace(p),R.pendingMpaPath=p}(0,o.use)(g.unresolvedThenable)}(0,o.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,o.startTransition)(()=>{(0,c.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=M(t),i&&r(i)),e(t,n,i)},window.history.replaceState=function(e,n,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=M(e),i&&r(i)),t(e,n,i)};let n=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,o.startTransition)(()=>{(0,E.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[]);let{cache:j,tree:C,nextUrl:k,focusAndScrollRef:D}=f,N=(0,o.useMemo)(()=>(0,y.findHeadInCache)(j,C[1]),[j,C]),F=(0,o.useMemo)(()=>(0,w.getSelectedParams)(C),[C]),I=(0,o.useMemo)(()=>({parentTree:C,parentCacheNode:j,parentSegmentPath:null,url:p}),[C,j,p]),U=(0,o.useMemo)(()=>({tree:C,focusAndScrollRef:D,nextUrl:k}),[C,D,k]);if(null!==N){let[e,r]=N;t=(0,i.jsx)(A,{headCacheNode:e},r)}else t=null;let B=(0,i.jsxs)(m.RedirectBoundary,{children:[t,j.rsc,(0,i.jsx)(h.AppRouterAnnouncer,{tree:C})]});return B=(0,i.jsx)(d.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:B}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(T,{appRouterState:f}),(0,i.jsx)(L,{}),(0,i.jsx)(u.PathParamsContext.Provider,{value:F,children:(0,i.jsx)(u.PathnameContext.Provider,{value:S,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:x,children:(0,i.jsx)(a.GlobalLayoutRouterContext.Provider,{value:U,children:(0,i.jsx)(a.AppRouterContext.Provider,{value:E.publicAppRouterInstance,children:(0,i.jsx)(a.LayoutRouterContext.Provider,{value:I,children:B})})})})})})]})}function k(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:o}=e;return(0,x.useNavFailureHandler)(),(0,i.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,i.jsx)(C,{actionQueue:t,assetPrefix:o,globalError:[r,n]})})}let D=new Set,N=new Set;function L(){let[,e]=o.default.useState(0),t=D.size;return(0,o.useEffect)(()=>{let r=()=>e(e=>e+1);return N.add(r),t!==D.size&&r(),()=>{N.delete(r)}},[t,e]),[...D].map((e,t)=>(0,i.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=D.size;return D.add(e),D.size!==t&&N.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89999:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let n=r(37413),i=r(1765);function o(){return(0,n.jsx)(i.HTTPAccessErrorFallback,{status:403,message:"This page could not be accessed."})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},91563:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_HEADER:function(){return n},FLIGHT_HEADERS:function(){return d},NEXT_DID_POSTPONE_HEADER:function(){return h},NEXT_HMR_REFRESH_HASH_COOKIE:function(){return l},NEXT_HMR_REFRESH_HEADER:function(){return s},NEXT_IS_PRERENDER_HEADER:function(){return g},NEXT_REWRITTEN_PATH_HEADER:function(){return m},NEXT_REWRITTEN_QUERY_HEADER:function(){return y},NEXT_ROUTER_PREFETCH_HEADER:function(){return o},NEXT_ROUTER_SEGMENT_PREFETCH_HEADER:function(){return a},NEXT_ROUTER_STALE_TIME_HEADER:function(){return p},NEXT_ROUTER_STATE_TREE_HEADER:function(){return i},NEXT_RSC_UNION_QUERY:function(){return f},NEXT_URL:function(){return u},RSC_CONTENT_TYPE_HEADER:function(){return c},RSC_HEADER:function(){return r}});let r="RSC",n="Next-Action",i="Next-Router-State-Tree",o="Next-Router-Prefetch",a="Next-Router-Segment-Prefetch",s="Next-HMR-Refresh",l="__next_hmr_refresh_hash__",u="Next-Url",c="text/x-component",d=[r,i,o,s,a],f="_rsc",p="x-nextjs-stale-time",h="x-nextjs-postponed",m="x-nextjs-rewritten-path",y="x-nextjs-rewritten-query",g="x-nextjs-prerender";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},91955:(e,t,r)=>{"use strict";r.d(t,{j:()=>R});var n=r(78205),i=r(66244),o=r(22238),a=r(7504),s=r(39664),l=r(73063),u=r(12742);function c(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}var d=r(21874);function f(e,t){return r=>r>0?t:e}var p=r(68028);let h=(e,t,r)=>{let n=e*e,i=r*(t*t-n)+n;return i<0?0:Math.sqrt(i)},m=[l.u,d.B,u.V],y=e=>m.find(t=>t.test(e));function g(e){let t=y(e);if((0,i.$)(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let r=t.parse(e);return t===u.V&&(r=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let i=0,o=0,a=0;if(t/=100){let n=r<.5?r*(1+t):r+t-r*t,s=2*r-n;i=c(s,n,e+1/3),o=c(s,n,e),a=c(s,n,e-1/3)}else i=o=a=r;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*a),alpha:n}}(r)),r}let v=(e,t)=>{let r=g(e),n=g(t);if(!r||!n)return f(e,t);let i={...r};return e=>(i.red=h(r.red,n.red,e),i.green=h(r.green,n.green,e),i.blue=h(r.blue,n.blue,e),i.alpha=(0,p.k)(r.alpha,n.alpha,e),d.B.transform(i))},b=new Set(["none","hidden"]);function w(e,t){return r=>(0,p.k)(e,t,r)}function x(e){return"number"==typeof e?w:"string"==typeof e?(0,o.p)(e)?f:a.y.test(e)?v:P:Array.isArray(e)?E:"object"==typeof e?a.y.test(e)?v:_:f}function E(e,t){let r=[...e],n=r.length,i=e.map((e,r)=>x(e)(e,t[r]));return e=>{for(let t=0;t<n;t++)r[t]=i[t](e);return r}}function _(e,t){let r={...e,...t},n={};for(let i in r)void 0!==e[i]&&void 0!==t[i]&&(n[i]=x(e[i])(e[i],t[i]));return e=>{for(let t in n)r[t]=n[t](e);return r}}let P=(e,t)=>{let r=s.f.createTransformer(t),o=(0,s.V)(e),a=(0,s.V)(t);return o.indexes.var.length===a.indexes.var.length&&o.indexes.color.length===a.indexes.color.length&&o.indexes.number.length>=a.indexes.number.length?b.has(e)&&!a.values.length||b.has(t)&&!o.values.length?function(e,t){return b.has(e)?r=>r<=0?e:t:r=>r>=1?t:e}(e,t):(0,n.F)(E(function(e,t){let r=[],n={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let o=t.types[i],a=e.indexes[o][n[o]],s=e.values[a]??0;r[i]=s,n[o]++}return r}(o,a),a.values),r):((0,i.$)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),f(e,t))};function R(e,t,r){return"number"==typeof e&&"number"==typeof t&&"number"==typeof r?(0,p.k)(e,t,r):x(e)(e,t)}},91992:(e,t)=>{"use strict";function r(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isThenable",{enumerable:!0,get:function(){return r}})},92314:(e,t,r)=>{"use strict";r.d(t,{E:()=>L});var n="undefined"==typeof window||"Deno"in globalThis;function i(){}function o(e,t){return"function"==typeof e?e(t):e}function a(e,t){let{type:r="all",exact:n,fetchStatus:i,predicate:o,queryKey:a,stale:s}=e;if(a){if(n){if(t.queryHash!==l(a,t.options))return!1}else if(!c(t.queryKey,a))return!1}if("all"!==r){let e=t.isActive();if("active"===r&&!e||"inactive"===r&&e)return!1}return("boolean"!=typeof s||t.isStale()===s)&&(!i||i===t.state.fetchStatus)&&(!o||!!o(t))}function s(e,t){let{exact:r,status:n,predicate:i,mutationKey:o}=e;if(o){if(!t.options.mutationKey)return!1;if(r){if(u(t.options.mutationKey)!==u(o))return!1}else if(!c(t.options.mutationKey,o))return!1}return(!n||t.state.status===n)&&(!i||!!i(t))}function l(e,t){return(t?.queryKeyHashFn||u)(e)}function u(e){return JSON.stringify(e,(e,t)=>f(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t)}function c(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(r=>c(e[r],t[r]))}function d(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function f(e){if(!p(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!!p(r)&&!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function p(e){return"[object Object]"===Object.prototype.toString.call(e)}function h(e,t,r=0){let n=[...e,t];return r&&n.length>r?n.slice(1):n}function m(e,t,r=0){let n=[t,...e];return r&&n.length>r?n.slice(0,-1):n}var y=Symbol();function g(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==y?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}var v=e=>setTimeout(e,0),b=function(){let e=[],t=0,r=e=>{e()},n=e=>{e()},i=v,o=n=>{t?e.push(n):i(()=>{r(n)})},a=()=>{let t=e;e=[],t.length&&i(()=>{n(()=>{t.forEach(e=>{r(e)})})})};return{batch:e=>{let r;t++;try{r=e()}finally{--t||a()}return r},batchCalls:e=>(...t)=>{o(()=>{e(...t)})},schedule:o,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{n=e},setScheduler:e=>{i=e}}}(),w=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},x=new class extends w{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!n&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},E=new class extends w{#n=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!n&&window.addEventListener){let t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#n!==e&&(this.#n=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#n}};function _(e){return Math.min(1e3*2**e,3e4)}function P(e){return(e??"online")!=="online"||E.isOnline()}var R=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function S(e){return e instanceof R}function O(e){let t,r=!1,i=0,o=!1,a=function(){let e,t,r=new Promise((r,n)=>{e=r,t=n});function n(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=t=>{n({status:"fulfilled",value:t}),e(t)},r.reject=e=>{n({status:"rejected",reason:e}),t(e)},r}(),s=()=>x.isFocused()&&("always"===e.networkMode||E.isOnline())&&e.canRun(),l=()=>P(e.networkMode)&&e.canRun(),u=r=>{o||(o=!0,e.onSuccess?.(r),t?.(),a.resolve(r))},c=r=>{o||(o=!0,e.onError?.(r),t?.(),a.reject(r))},d=()=>new Promise(r=>{t=e=>{(o||s())&&r(e)},e.onPause?.()}).then(()=>{t=void 0,o||e.onContinue?.()}),f=()=>{let t;if(o)return;let a=0===i?e.initialPromise:void 0;try{t=a??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(u).catch(t=>{if(o)return;let a=e.retry??3*!n,l=e.retryDelay??_,u="function"==typeof l?l(i,t):l,p=!0===a||"number"==typeof a&&i<a||"function"==typeof a&&a(i,t);if(r||!p)return void c(t);i++,e.onFail?.(i,t),new Promise(e=>{setTimeout(e,u)}).then(()=>s()?void 0:d()).then(()=>{r?c(t):f()})})};return{promise:a,cancel:t=>{o||(c(new R(t)),e.abort?.())},continue:()=>(t?.(),a),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:l,start:()=>(l()?f():d().then(f),a)}}var T=class{#i;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#i=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(n?1/0:3e5))}clearGcTimeout(){this.#i&&(clearTimeout(this.#i),this.#i=void 0)}},j=class extends T{#o;#a;#s;#l;#u;#c;#d;constructor(e){super(),this.#d=!1,this.#c=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#l=e.client,this.#s=this.#l.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#o=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#o,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#u?.promise}setOptions(e){this.options={...this.#c,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#s.remove(this)}setData(e,t){var r,n;let i=(r=this.state.data,"function"==typeof(n=this.options).structuralSharing?n.structuralSharing(r,e):!1!==n.structuralSharing?function e(t,r){if(t===r)return t;let n=d(t)&&d(r);if(n||f(t)&&f(r)){let i=n?t:Object.keys(t),o=i.length,a=n?r:Object.keys(r),s=a.length,l=n?[]:{},u=0;for(let o=0;o<s;o++){let s=n?o:a[o];(!n&&i.includes(s)||n)&&void 0===t[s]&&void 0===r[s]?(l[s]=void 0,u++):(l[s]=e(t[s],r[s]),l[s]===t[s]&&void 0!==t[s]&&u++)}return o===s&&u===o?t:l}return r}(r,e):e);return this.#f({data:i,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),i}setState(e,t){this.#f({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#u?.promise;return this.#u?.cancel(e),t?t.then(i).catch(i):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#o)}isActive(){return this.observers.some(e=>{var t;return!1!==(t=e.options.enabled,"function"==typeof t?t(this):t)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===y||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!Math.max(this.state.dataUpdatedAt+(e||0)-Date.now(),0)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#u?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#u?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#u&&(this.#d?this.#u.cancel({revert:!0}):this.#u.cancelRetry()),this.scheduleGc()),this.#s.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#f({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#u)return this.#u.continueRetry(),this.#u.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,n=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#d=!0,r.signal)})},i={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#l,state:this.state,fetchFn:()=>{let e=g(this.options,t),r={client:this.#l,queryKey:this.queryKey,meta:this.meta};return(n(r),this.#d=!1,this.options.persister)?this.options.persister(e,r,this):e(r)}};n(i),this.options.behavior?.onFetch(i,this),this.#a=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==i.fetchOptions?.meta)&&this.#f({type:"fetch",meta:i.fetchOptions?.meta});let o=e=>{S(e)&&e.silent||this.#f({type:"error",error:e}),S(e)||(this.#s.config.onError?.(e,this),this.#s.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#u=O({initialPromise:t?.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e)return void o(Error(`${this.queryHash} data is undefined`));try{this.setData(e)}catch(e){o(e);return}this.#s.config.onSuccess?.(e,this),this.#s.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:o,onFail:(e,t)=>{this.#f({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#f({type:"pause"})},onContinue:()=>{this.#f({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#u.start()}#f(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":var r;return{...t,...(r=t.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:P(this.options.networkMode)?"fetching":"paused",...void 0===r&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let n=e.error;if(S(n)&&n.revert&&this.#a)return{...this.#a,fetchStatus:"idle"};return{...t,error:n,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),b.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#s.notify({query:this,type:"updated",action:e})})}},M=class extends w{constructor(e={}){super(),this.config=e,this.#p=new Map}#p;build(e,t,r){let n=t.queryKey,i=t.queryHash??l(n,t),o=this.get(i);return o||(o=new j({client:e,queryKey:n,queryHash:i,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(n)}),this.add(o)),o}add(e){this.#p.has(e.queryHash)||(this.#p.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#p.get(e.queryHash);t&&(e.destroy(),t===e&&this.#p.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){b.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#p.get(e)}getAll(){return[...this.#p.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>a(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>a(e,t)):t}notify(e){b.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){b.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){b.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},A=class extends T{#h;#m;#u;constructor(e){super(),this.mutationId=e.mutationId,this.#m=e.mutationCache,this.#h=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#h.includes(e)||(this.#h.push(e),this.clearGcTimeout(),this.#m.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#h=this.#h.filter(t=>t!==e),this.scheduleGc(),this.#m.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#h.length||("pending"===this.state.status?this.scheduleGc():this.#m.remove(this))}continue(){return this.#u?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#f({type:"continue"})};this.#u=O({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#f({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#f({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#m.canRun(this)});let r="pending"===this.state.status,n=!this.#u.canStart();try{if(r)t();else{this.#f({type:"pending",variables:e,isPaused:n}),await this.#m.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#f({type:"pending",context:t,variables:e,isPaused:n})}let i=await this.#u.start();return await this.#m.config.onSuccess?.(i,e,this.state.context,this),await this.options.onSuccess?.(i,e,this.state.context),await this.#m.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,e,this.state.context),this.#f({type:"success",data:i}),i}catch(t){try{throw await this.#m.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#m.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#f({type:"error",error:t})}}finally{this.#m.runNext(this)}}#f(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),b.batch(()=>{this.#h.forEach(t=>{t.onMutationUpdate(e)}),this.#m.notify({mutation:this,type:"updated",action:e})})}},C=class extends w{constructor(e={}){super(),this.config=e,this.#y=new Set,this.#g=new Map,this.#v=0}#y;#g;#v;build(e,t,r){let n=new A({mutationCache:this,mutationId:++this.#v,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){this.#y.add(e);let t=k(e);if("string"==typeof t){let r=this.#g.get(t);r?r.push(e):this.#g.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#y.delete(e)){let t=k(e);if("string"==typeof t){let r=this.#g.get(t);if(r)if(r.length>1){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#g.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=k(e);if("string"!=typeof t)return!0;{let r=this.#g.get(t),n=r?.find(e=>"pending"===e.state.status);return!n||n===e}}runNext(e){let t=k(e);if("string"!=typeof t)return Promise.resolve();{let r=this.#g.get(t)?.find(t=>t!==e&&t.state.isPaused);return r?.continue()??Promise.resolve()}}clear(){b.batch(()=>{this.#y.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#y.clear(),this.#g.clear()})}getAll(){return Array.from(this.#y)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>s(t,e))}findAll(e={}){return this.getAll().filter(t=>s(e,t))}notify(e){b.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return b.batch(()=>Promise.all(e.map(e=>e.continue().catch(i))))}};function k(e){return e.options.scope?.id}function D(e){return{onFetch:(t,r)=>{let n=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,o=t.state.data?.pages||[],a=t.state.data?.pageParams||[],s={pages:[],pageParams:[]},l=0,u=async()=>{let r=!1,u=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",()=>{r=!0}),t.signal)})},c=g(t.options,t.fetchOptions),d=async(e,n,i)=>{if(r)return Promise.reject();if(null==n&&e.pages.length)return Promise.resolve(e);let o={client:t.client,queryKey:t.queryKey,pageParam:n,direction:i?"backward":"forward",meta:t.options.meta};u(o);let a=await c(o),{maxPages:s}=t.options,l=i?m:h;return{pages:l(e.pages,a,s),pageParams:l(e.pageParams,n,s)}};if(i&&o.length){let e="backward"===i,t={pages:o,pageParams:a},r=(e?function(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}:N)(n,t);s=await d(t,r,e)}else{let t=e??o.length;do{let e=0===l?a[0]??n.initialPageParam:N(n,s);if(l>0&&null==e)break;s=await d(s,e),l++}while(l<t)}return s};t.options.persister?t.fetchFn=()=>t.options.persister?.(u,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=u}}}function N(e,{pages:t,pageParams:r}){let n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,r[n],r):void 0}var L=class{#b;#m;#c;#w;#x;#E;#_;#P;constructor(e={}){this.#b=e.queryCache||new M,this.#m=e.mutationCache||new C,this.#c=e.defaultOptions||{},this.#w=new Map,this.#x=new Map,this.#E=0}mount(){this.#E++,1===this.#E&&(this.#_=x.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#b.onFocus())}),this.#P=E.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#b.onOnline())}))}unmount(){this.#E--,0===this.#E&&(this.#_?.(),this.#_=void 0,this.#P?.(),this.#P=void 0)}isFetching(e){return this.#b.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#m.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#b.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),r=this.#b.build(this,t),n=r.state.data;return void 0===n?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime(o(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(n))}getQueriesData(e){return this.#b.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let n=this.defaultQueryOptions({queryKey:e}),i=this.#b.get(n.queryHash),o=i?.state.data,a="function"==typeof t?t(o):t;if(void 0!==a)return this.#b.build(this,n).setData(a,{...r,manual:!0})}setQueriesData(e,t,r){return b.batch(()=>this.#b.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#b.get(t.queryHash)?.state}removeQueries(e){let t=this.#b;b.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#b;return b.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let r={revert:!0,...t};return Promise.all(b.batch(()=>this.#b.findAll(e).map(e=>e.cancel(r)))).then(i).catch(i)}invalidateQueries(e,t={}){return b.batch(()=>(this.#b.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let r={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(b.batch(()=>this.#b.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(i)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(i)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#b.build(this,t);return r.isStaleByTime(o(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(i).catch(i)}fetchInfiniteQuery(e){return e.behavior=D(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(i).catch(i)}ensureInfiniteQueryData(e){return e.behavior=D(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return E.isOnline()?this.#m.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#b}getMutationCache(){return this.#m}getDefaultOptions(){return this.#c}setDefaultOptions(e){this.#c=e}setQueryDefaults(e,t){this.#w.set(u(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#w.values()],r={};return t.forEach(t=>{c(e,t.queryKey)&&Object.assign(r,t.defaultOptions)}),r}setMutationDefaults(e,t){this.#x.set(u(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#x.values()],r={};return t.forEach(t=>{c(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#c.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=l(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===y&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#c.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#b.clear(),this.#m.clear()}}},93883:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useUntrackedPathname",{enumerable:!0,get:function(){return o}});let n=r(43210),i=r(10449);function o(){return!function(){{let{workAsyncStorage:e}=r(29294),t=e.getStore();if(!t)return!1;let{fallbackRouteParams:n}=t;return!!n&&0!==n.size}}()?(0,n.useContext)(i.PathnameContext):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},93972:(e,t,r)=>{"use strict";e.exports=r(65239).vendored["react-rsc"].ReactServerDOMWebpackStaticEdge},94041:(e,t,r)=>{"use strict";e.exports=r(10846)},95444:(e,t,r)=>{"use strict";r.d(t,{X4:()=>o,ai:()=>i,hs:()=>a});var n=r(97758);let i={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},o={...i,transform:e=>(0,n.q)(0,1,e)},a={...i,default:1}},95796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},96127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return o}});let n=r(98834),i=r(54674);function o(e,t){return(0,i.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96184:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});let n={layout:0,mainThread:0,waapi:0}},96258:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSocialImageMetadataBaseFallback:function(){return a},isStringOrURL:function(){return i},resolveAbsoluteUrlWithPathname:function(){return c},resolveRelativeUrl:function(){return l},resolveUrl:function(){return s}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(78671));function i(e){return"string"==typeof e||e instanceof URL}function o(){return new URL(`http://localhost:${process.env.PORT||3e3}`)}function a(e){let t=o(),r=function(){let e=process.env.VERCEL_BRANCH_URL||process.env.VERCEL_URL;return e?new URL(`https://${e}`):void 0}(),n=function(){let e=process.env.VERCEL_PROJECT_PRODUCTION_URL;return e?new URL(`https://${e}`):void 0}();return r&&"preview"===process.env.VERCEL_ENV?r:e||n||t}function s(e,t){if(e instanceof URL)return e;if(!e)return null;try{return new URL(e)}catch{}t||(t=o());let r=t.pathname||"";return new URL(n.default.posix.join(r,e),t)}function l(e,t){return"string"==typeof e&&e.startsWith("./")?n.default.posix.resolve(t,e):e}let u=/^(?:\/((?!\.well-known(?:\/.*)?)(?:[^/]+\/)*[^/]+\.\w+))(\/?|$)/i;function c(e,t,{trailingSlash:r,pathname:n}){e=l(e,n);let i="",o=t?s(e,t):e;if(i="string"==typeof o?o:"/"===o.pathname?o.origin:o.href,r&&!i.endsWith("/")){let e=i.startsWith("/"),r=i.includes("?"),n=!1,o=!1;if(!e){try{var a;let e=new URL(i);n=null!=t&&e.origin!==t.origin,a=e.pathname,o=u.test(a)}catch{n=!0}if(!o&&!n&&!r)return`${i}/`}}return i}},96493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let n=r(25232);function i(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96844:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});function n(){throw Object.defineProperty(Error("Taint can only be used with the taint flag."),"__NEXT_ERROR_CODE",{value:"E354",enumerable:!1,configurable:!0})}!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{taintObjectReference:function(){return i},taintUniqueValue:function(){return o}}),r(61120);let i=n,o=n},96963:(e,t,r)=>{"use strict";r.d(t,{B:()=>l});var n,i=r(43210),o=r(66156),a=(n||(n=r.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),s=0;function l(e){let[t,r]=i.useState(a());return(0,o.N)(()=>{e||r(e=>e??String(s++))},[e]),e||(t?`radix-${t}`:"")}},97095:(e,t,r)=>{"use strict";r.d(t,{a:()=>n});let n=e=>Math.round(1e5*e)/1e5},97173:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(40740),i=r(60687),o=n._(r(43210)),a=r(22142);function s(){let e=(0,o.useContext)(a.TemplateContext);return(0,i.jsx)(i.Fragment,{children:e})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97181:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{resolveIcon:function(){return a},resolveIcons:function(){return s}});let n=r(77341),i=r(96258),o=r(4871);function a(e){return(0,i.isStringOrURL)(e)?{url:e}:(Array.isArray(e),e)}let s=e=>{if(!e)return null;let t={icon:[],apple:[]};if(Array.isArray(e))t.icon=e.map(a).filter(Boolean);else if((0,i.isStringOrURL)(e))t.icon=[a(e)];else for(let r of o.IconKeys){let i=(0,n.resolveAsArrayOrUndefined)(e[r]);i&&(t[r]=i.map(a))}return t}},97464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,o){let a=o.length<=2,[s,l]=o,u=(0,i.createRouterCacheKey)(l),c=r.parallelRoutes.get(s),d=t.parallelRoutes.get(s);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(s,d));let f=null==c?void 0:c.get(u),p=d.get(u);if(a){p&&p.lazyData&&p!==f||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!p||!f){p||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},d.set(u,p)),e(p,f,(0,n.getNextFlightSegmentPath)(o))}}});let n=r(74007),i=r(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97758:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});let n=(e,t,r)=>r>t?t:r<e?e:r},97819:(e,t,r)=>{"use strict";r.d(t,{W:()=>n});let n={}},97860:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return i},RedirectType:function(){return o},isRedirectError:function(){return a}});let n=r(17974),i="NEXT_REDIRECT";var o=function(e){return e.push="push",e.replace="replace",e}({});function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,o]=t,a=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===i&&("replace"===o||"push"===o)&&"string"==typeof a&&!isNaN(s)&&s in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97936:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return n}}),r(59008),r(57391),r(86770),r(2030),r(25232),r(59435),r(56928),r(89752),r(96493),r(68214);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98599:(e,t,r)=>{"use strict";r.d(t,{s:()=>a,t:()=>o});var n=r(43210);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}function a(...e){return n.useCallback(o(...e),e)}},98834:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let n=r(19169);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:o}=(0,n.parsePath)(e);return""+t+r+i+o}},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41552).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};