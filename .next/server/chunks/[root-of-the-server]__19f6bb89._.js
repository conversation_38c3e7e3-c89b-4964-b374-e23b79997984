module.exports = {

"[project]/.next-internal/server/app/api/github/repos/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/github/repos/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map();
// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10; // 10 requests per minute
function isRateLimited(ip) {
    const now = Date.now();
    const userLimit = rateLimitStore.get(ip);
    if (!userLimit || now > userLimit.resetTime) {
        rateLimitStore.set(ip, {
            count: 1,
            resetTime: now + RATE_LIMIT_WINDOW
        });
        return false;
    }
    if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
        return true;
    }
    userLimit.count++;
    return false;
}
async function GET(request) {
    try {
        // Basic rate limiting
        const ip = request.headers.get('x-forwarded-for') || 'unknown';
        if (isRateLimited(ip)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Rate limit exceeded. Please try again later.'
            }, {
                status: 429
            });
        }
        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const per_page = Math.min(parseInt(searchParams.get('per_page') || '10'), 100);
        const githubToken = process.env.GITHUB_TOKEN;
        const githubUsername = process.env.GITHUB_USERNAME || 'GreenHacker420';
        if (!githubToken) {
            console.warn('GitHub token not configured, using mock data');
            return getMockRepos();
        }
        const response = await fetch(`https://api.github.com/users/${githubUsername}/repos?page=${page}&per_page=${per_page}&sort=updated&type=owner`, {
            headers: {
                'Authorization': `Bearer ${githubToken}`,
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'Portfolio-App'
            },
            next: {
                revalidate: 1800
            } // Cache for 30 minutes
        });
        if (!response.ok) {
            console.error('GitHub repos API error:', response.status, response.statusText);
            // Check if it's a rate limit error
            if (response.status === 403) {
                const rateLimitRemaining = response.headers.get('x-ratelimit-remaining');
                const rateLimitReset = response.headers.get('x-ratelimit-reset');
                console.error('GitHub API rate limit exceeded:', {
                    remaining: rateLimitRemaining,
                    reset: rateLimitReset
                });
            }
            return getMockRepos();
        }
        const repos = await response.json();
        // Filter and format repository data
        const formattedRepos = repos.filter((repo)=>!repo.fork && !repo.private) // Only show original public repos
        .map((repo)=>({
                id: repo.id,
                name: repo.name,
                full_name: repo.full_name,
                description: repo.description,
                html_url: repo.html_url,
                homepage: repo.homepage,
                language: repo.language,
                stargazers_count: repo.stargazers_count,
                forks_count: repo.forks_count,
                watchers_count: repo.watchers_count,
                size: repo.size,
                created_at: repo.created_at,
                updated_at: repo.updated_at,
                pushed_at: repo.pushed_at,
                topics: repo.topics || [],
                license: repo.license?.name || null,
                default_branch: repo.default_branch,
                open_issues_count: repo.open_issues_count
            }));
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            repos: formattedRepos,
            total_count: formattedRepos.length,
            page,
            per_page
        });
    } catch (error) {
        console.error('GitHub repos API error:', error);
        return getMockRepos();
    }
}
function getMockRepos() {
    const mockRepos = [
        {
            id: 1,
            name: 'portfolio-nextjs',
            full_name: 'GreenHacker420/portfolio-nextjs',
            description: 'Modern portfolio website built with Next.js, Three.js, and AI integration',
            html_url: 'https://github.com/GreenHacker420/portfolio-nextjs',
            homepage: 'https://greenhacker420.vercel.app',
            language: 'TypeScript',
            stargazers_count: 15,
            forks_count: 3,
            watchers_count: 15,
            size: 2048,
            created_at: '2024-01-15T00:00:00Z',
            updated_at: new Date().toISOString(),
            pushed_at: new Date().toISOString(),
            topics: [
                'nextjs',
                'portfolio',
                'threejs',
                'ai',
                'typescript'
            ],
            license: 'MIT',
            default_branch: 'main',
            open_issues_count: 2
        },
        {
            id: 2,
            name: 'ai-chat-assistant',
            full_name: 'GreenHacker420/ai-chat-assistant',
            description: 'Intelligent chat assistant powered by Gemini AI with advanced conversation capabilities',
            html_url: 'https://github.com/GreenHacker420/ai-chat-assistant',
            homepage: null,
            language: 'Python',
            stargazers_count: 8,
            forks_count: 2,
            watchers_count: 8,
            size: 1024,
            created_at: '2024-02-01T00:00:00Z',
            updated_at: new Date(Date.now() - 86400000).toISOString(),
            pushed_at: new Date(Date.now() - 86400000).toISOString(),
            topics: [
                'ai',
                'chatbot',
                'gemini',
                'python',
                'machine-learning'
            ],
            license: 'Apache-2.0',
            default_branch: 'main',
            open_issues_count: 1
        },
        {
            id: 3,
            name: 'react-3d-components',
            full_name: 'GreenHacker420/react-3d-components',
            description: 'Collection of reusable 3D React components using Three.js and React Three Fiber',
            html_url: 'https://github.com/GreenHacker420/react-3d-components',
            homepage: 'https://react-3d-components.vercel.app',
            language: 'JavaScript',
            stargazers_count: 12,
            forks_count: 4,
            watchers_count: 12,
            size: 1536,
            created_at: '2023-11-20T00:00:00Z',
            updated_at: new Date(Date.now() - 172800000).toISOString(),
            pushed_at: new Date(Date.now() - 172800000).toISOString(),
            topics: [
                'react',
                'threejs',
                'components',
                '3d',
                'webgl'
            ],
            license: 'MIT',
            default_branch: 'main',
            open_issues_count: 0
        }
    ];
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        repos: mockRepos,
        total_count: mockRepos.length,
        page: 1,
        per_page: 10
    });
}
async function POST() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        error: 'Method not allowed'
    }, {
        status: 405
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__19f6bb89._.js.map