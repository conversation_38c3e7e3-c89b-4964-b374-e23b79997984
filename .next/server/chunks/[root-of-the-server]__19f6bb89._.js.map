{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/api/github/repos/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\n\n// Rate limiting store (in production, use Redis or similar)\nconst rateLimitStore = new Map<string, { count: number; resetTime: number }>();\n\n// Rate limiting configuration\nconst RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute\nconst RATE_LIMIT_MAX_REQUESTS = 10; // 10 requests per minute\n\nfunction isRateLimited(ip: string): boolean {\n  const now = Date.now();\n  const userLimit = rateLimitStore.get(ip);\n\n  if (!userLimit || now > userLimit.resetTime) {\n    rateLimitStore.set(ip, {\n      count: 1,\n      resetTime: now + RATE_LIMIT_WINDOW\n    });\n    return false;\n  }\n\n  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {\n    return true;\n  }\n\n  userLimit.count++;\n  return false;\n}\n\nexport async function GET(request: Request) {\n  try {\n    // Basic rate limiting\n    const ip = request.headers.get('x-forwarded-for') || 'unknown';\n    if (isRateLimited(ip)) {\n      return NextResponse.json(\n        { error: 'Rate limit exceeded. Please try again later.' },\n        { status: 429 }\n      );\n    }\n\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const per_page = Math.min(parseInt(searchParams.get('per_page') || '10'), 100);\n\n    const githubToken = process.env.GITHUB_TOKEN;\n    const githubUsername = process.env.GITHUB_USERNAME || 'GreenHacker420';\n\n    if (!githubToken) {\n      console.warn('GitHub token not configured, using mock data');\n      return getMockRepos();\n    }\n\n    const response = await fetch(\n      `https://api.github.com/users/${githubUsername}/repos?page=${page}&per_page=${per_page}&sort=updated&type=owner`,\n      {\n        headers: {\n          'Authorization': `Bearer ${githubToken}`,\n          'Accept': 'application/vnd.github.v3+json',\n          'User-Agent': 'Portfolio-App'\n        },\n        next: { revalidate: 1800 } // Cache for 30 minutes\n      }\n    );\n\n    if (!response.ok) {\n      console.error('GitHub repos API error:', response.status, response.statusText);\n\n      // Check if it's a rate limit error\n      if (response.status === 403) {\n        const rateLimitRemaining = response.headers.get('x-ratelimit-remaining');\n        const rateLimitReset = response.headers.get('x-ratelimit-reset');\n\n        console.error('GitHub API rate limit exceeded:', {\n          remaining: rateLimitRemaining,\n          reset: rateLimitReset\n        });\n      }\n\n      return getMockRepos();\n    }\n\n    const repos = await response.json();\n\n    // Filter and format repository data\n    const formattedRepos = repos\n      .filter((repo: any) => !repo.fork && !repo.private) // Only show original public repos\n      .map((repo: any) => ({\n        id: repo.id,\n        name: repo.name,\n        full_name: repo.full_name,\n        description: repo.description,\n        html_url: repo.html_url,\n        homepage: repo.homepage,\n        language: repo.language,\n        stargazers_count: repo.stargazers_count,\n        forks_count: repo.forks_count,\n        watchers_count: repo.watchers_count,\n        size: repo.size,\n        created_at: repo.created_at,\n        updated_at: repo.updated_at,\n        pushed_at: repo.pushed_at,\n        topics: repo.topics || [],\n        license: repo.license?.name || null,\n        default_branch: repo.default_branch,\n        open_issues_count: repo.open_issues_count,\n      }));\n\n    return NextResponse.json({\n      repos: formattedRepos,\n      total_count: formattedRepos.length,\n      page,\n      per_page\n    });\n\n  } catch (error) {\n    console.error('GitHub repos API error:', error);\n    return getMockRepos();\n  }\n}\n\nfunction getMockRepos() {\n  const mockRepos = [\n    {\n      id: 1,\n      name: 'portfolio-nextjs',\n      full_name: 'GreenHacker420/portfolio-nextjs',\n      description: 'Modern portfolio website built with Next.js, Three.js, and AI integration',\n      html_url: 'https://github.com/GreenHacker420/portfolio-nextjs',\n      homepage: 'https://greenhacker420.vercel.app',\n      language: 'TypeScript',\n      stargazers_count: 15,\n      forks_count: 3,\n      watchers_count: 15,\n      size: 2048,\n      created_at: '2024-01-15T00:00:00Z',\n      updated_at: new Date().toISOString(),\n      pushed_at: new Date().toISOString(),\n      topics: ['nextjs', 'portfolio', 'threejs', 'ai', 'typescript'],\n      license: 'MIT',\n      default_branch: 'main',\n      open_issues_count: 2,\n    },\n    {\n      id: 2,\n      name: 'ai-chat-assistant',\n      full_name: 'GreenHacker420/ai-chat-assistant',\n      description: 'Intelligent chat assistant powered by Gemini AI with advanced conversation capabilities',\n      html_url: 'https://github.com/GreenHacker420/ai-chat-assistant',\n      homepage: null,\n      language: 'Python',\n      stargazers_count: 8,\n      forks_count: 2,\n      watchers_count: 8,\n      size: 1024,\n      created_at: '2024-02-01T00:00:00Z',\n      updated_at: new Date(Date.now() - 86400000).toISOString(),\n      pushed_at: new Date(Date.now() - 86400000).toISOString(),\n      topics: ['ai', 'chatbot', 'gemini', 'python', 'machine-learning'],\n      license: 'Apache-2.0',\n      default_branch: 'main',\n      open_issues_count: 1,\n    },\n    {\n      id: 3,\n      name: 'react-3d-components',\n      full_name: 'GreenHacker420/react-3d-components',\n      description: 'Collection of reusable 3D React components using Three.js and React Three Fiber',\n      html_url: 'https://github.com/GreenHacker420/react-3d-components',\n      homepage: 'https://react-3d-components.vercel.app',\n      language: 'JavaScript',\n      stargazers_count: 12,\n      forks_count: 4,\n      watchers_count: 12,\n      size: 1536,\n      created_at: '2023-11-20T00:00:00Z',\n      updated_at: new Date(Date.now() - 172800000).toISOString(),\n      pushed_at: new Date(Date.now() - 172800000).toISOString(),\n      topics: ['react', 'threejs', 'components', '3d', 'webgl'],\n      license: 'MIT',\n      default_branch: 'main',\n      open_issues_count: 0,\n    }\n  ];\n\n  return NextResponse.json({\n    repos: mockRepos,\n    total_count: mockRepos.length,\n    page: 1,\n    per_page: 10\n  });\n}\n\nexport async function POST() {\n  return NextResponse.json(\n    { error: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,4DAA4D;AAC5D,MAAM,iBAAiB,IAAI;AAE3B,8BAA8B;AAC9B,MAAM,oBAAoB,KAAK,MAAM,WAAW;AAChD,MAAM,0BAA0B,IAAI,yBAAyB;AAE7D,SAAS,cAAc,EAAU;IAC/B,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,eAAe,GAAG,CAAC;IAErC,IAAI,CAAC,aAAa,MAAM,UAAU,SAAS,EAAE;QAC3C,eAAe,GAAG,CAAC,IAAI;YACrB,OAAO;YACP,WAAW,MAAM;QACnB;QACA,OAAO;IACT;IAEA,IAAI,UAAU,KAAK,IAAI,yBAAyB;QAC9C,OAAO;IACT;IAEA,UAAU,KAAK;IACf,OAAO;AACT;AAEO,eAAe,IAAI,OAAgB;IACxC,IAAI;QACF,sBAAsB;QACtB,MAAM,KAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;QACrD,IAAI,cAAc,KAAK;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+C,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,WAAW,KAAK,GAAG,CAAC,SAAS,aAAa,GAAG,CAAC,eAAe,OAAO;QAE1E,MAAM,cAAc,QAAQ,GAAG,CAAC,YAAY;QAC5C,MAAM,iBAAiB,QAAQ,GAAG,CAAC,eAAe,IAAI;QAEtD,IAAI,CAAC,aAAa;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,6BAA6B,EAAE,eAAe,YAAY,EAAE,KAAK,UAAU,EAAE,SAAS,wBAAwB,CAAC,EAChH;YACE,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa;gBACxC,UAAU;gBACV,cAAc;YAChB;YACA,MAAM;gBAAE,YAAY;YAAK,EAAE,uBAAuB;QACpD;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,2BAA2B,SAAS,MAAM,EAAE,SAAS,UAAU;YAE7E,mCAAmC;YACnC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,MAAM,qBAAqB,SAAS,OAAO,CAAC,GAAG,CAAC;gBAChD,MAAM,iBAAiB,SAAS,OAAO,CAAC,GAAG,CAAC;gBAE5C,QAAQ,KAAK,CAAC,mCAAmC;oBAC/C,WAAW;oBACX,OAAO;gBACT;YACF;YAEA,OAAO;QACT;QAEA,MAAM,QAAQ,MAAM,SAAS,IAAI;QAEjC,oCAAoC;QACpC,MAAM,iBAAiB,MACpB,MAAM,CAAC,CAAC,OAAc,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,OAAO,EAAE,kCAAkC;SACrF,GAAG,CAAC,CAAC,OAAc,CAAC;gBACnB,IAAI,KAAK,EAAE;gBACX,MAAM,KAAK,IAAI;gBACf,WAAW,KAAK,SAAS;gBACzB,aAAa,KAAK,WAAW;gBAC7B,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ;gBACvB,kBAAkB,KAAK,gBAAgB;gBACvC,aAAa,KAAK,WAAW;gBAC7B,gBAAgB,KAAK,cAAc;gBACnC,MAAM,KAAK,IAAI;gBACf,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;gBAC3B,WAAW,KAAK,SAAS;gBACzB,QAAQ,KAAK,MAAM,IAAI,EAAE;gBACzB,SAAS,KAAK,OAAO,EAAE,QAAQ;gBAC/B,gBAAgB,KAAK,cAAc;gBACnC,mBAAmB,KAAK,iBAAiB;YAC3C,CAAC;QAEH,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,aAAa,eAAe,MAAM;YAClC;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAEA,SAAS;IACP,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,aAAa;YACb,UAAU;YACV,UAAU;YACV,UAAU;YACV,kBAAkB;YAClB,aAAa;YACb,gBAAgB;YAChB,MAAM;YACN,YAAY;YACZ,YAAY,IAAI,OAAO,WAAW;YAClC,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ;gBAAC;gBAAU;gBAAa;gBAAW;gBAAM;aAAa;YAC9D,SAAS;YACT,gBAAgB;YAChB,mBAAmB;QACrB;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,aAAa;YACb,UAAU;YACV,UAAU;YACV,UAAU;YACV,kBAAkB;YAClB,aAAa;YACb,gBAAgB;YAChB,MAAM;YACN,YAAY;YACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,UAAU,WAAW;YACvD,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,UAAU,WAAW;YACtD,QAAQ;gBAAC;gBAAM;gBAAW;gBAAU;gBAAU;aAAmB;YACjE,SAAS;YACT,gBAAgB;YAChB,mBAAmB;QACrB;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,aAAa;YACb,UAAU;YACV,UAAU;YACV,UAAU;YACV,kBAAkB;YAClB,aAAa;YACb,gBAAgB;YAChB,MAAM;YACN,YAAY;YACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,WAAW;YACxD,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,WAAW;YACvD,QAAQ;gBAAC;gBAAS;gBAAW;gBAAc;gBAAM;aAAQ;YACzD,SAAS;YACT,gBAAgB;YAChB,mBAAmB;QACrB;KACD;IAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,OAAO;QACP,aAAa,UAAU,MAAM;QAC7B,MAAM;QACN,UAAU;IACZ;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,OAAO;IAAqB,GAC9B;QAAE,QAAQ;IAAI;AAElB", "debugId": null}}]}