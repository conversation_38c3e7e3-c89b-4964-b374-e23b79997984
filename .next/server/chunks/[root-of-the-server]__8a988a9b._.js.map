{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/api/ai/chat/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { GoogleGenerativeAI } from '@google/generative-ai';\n\n// Rate limiting store (in production, use Redis or similar)\nconst rateLimitStore = new Map<string, { count: number; resetTime: number }>();\n\n// Rate limiting configuration\nconst RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute\nconst RATE_LIMIT_MAX_REQUESTS = 5; // 5 requests per minute for AI\n\nfunction isRateLimited(ip: string): boolean {\n  const now = Date.now();\n  const userLimit = rateLimitStore.get(ip);\n\n  if (!userLimit || now > userLimit.resetTime) {\n    rateLimitStore.set(ip, {\n      count: 1,\n      resetTime: now + RATE_LIMIT_WINDOW\n    });\n    return false;\n  }\n\n  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {\n    return true;\n  }\n\n  userLimit.count++;\n  return false;\n}\n\nexport async function POST(request: Request) {\n  try {\n    // Basic rate limiting\n    const ip = request.headers.get('x-forwarded-for') || 'unknown';\n    if (isRateLimited(ip)) {\n      return NextResponse.json(\n        { error: 'Rate limit exceeded. Please try again later.' },\n        { status: 429 }\n      );\n    }\n\n    const { message, context } = await request.json();\n\n    if (!message || typeof message !== 'string') {\n      return NextResponse.json(\n        { error: 'Message is required and must be a string' },\n        { status: 400 }\n      );\n    }\n\n    if (message.length > 1000) {\n      return NextResponse.json(\n        { error: 'Message too long. Please keep it under 1000 characters.' },\n        { status: 400 }\n      );\n    }\n\n    const geminiApiKey = process.env.GEMINI_API_KEY;\n\n    if (!geminiApiKey) {\n      console.warn('Gemini API key not configured, using mock response');\n      return getMockResponse(message);\n    }\n\n    // Initialize Gemini AI\n    const genAI = new GoogleGenerativeAI(geminiApiKey);\n    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });\n\n    // Create a context-aware prompt\n    const systemPrompt = `You are an AI assistant for Green Hacker's portfolio website. You are knowledgeable about:\n- Full-stack web development (React, Next.js, TypeScript, Node.js)\n- AI and machine learning technologies\n- 3D web development with Three.js\n- Modern web technologies and best practices\n- Green Hacker's projects and skills\n\nKeep responses helpful, professional, and concise. If asked about Green Hacker's background, mention their expertise in AI, web development, and creating innovative digital experiences.\n\nContext: ${context || 'General portfolio inquiry'}\n\nUser message: ${message}`;\n\n    const result = await model.generateContent(systemPrompt);\n    const response = await result.response;\n    const text = response.text();\n\n    return NextResponse.json({\n      success: true,\n      response: text,\n      timestamp: new Date().toISOString()\n    });\n\n  } catch (error) {\n    console.error('Gemini AI API error:', error);\n\n    // Check if it's an API quota error\n    if (error instanceof Error && error.message.includes('quota')) {\n      return NextResponse.json(\n        { error: 'AI service temporarily unavailable due to quota limits. Please try again later.' },\n        { status: 503 }\n      );\n    }\n\n    // Check if it's an authentication error\n    if (error instanceof Error && error.message.includes('API key')) {\n      console.error('Gemini API authentication error');\n      return getMockResponse(await request.json().then(data => data.message).catch(() => 'Hello'));\n    }\n\n    return getMockResponse(await request.json().then(data => data.message).catch(() => 'Hello'));\n  }\n}\n\nfunction getMockResponse(message: string) {\n  const responses = {\n    greeting: \"Hello! I'm Green Hacker's AI assistant. I'm here to help you learn more about their work in web development, AI, and innovative digital experiences. What would you like to know?\",\n    skills: \"Green Hacker specializes in full-stack development with React, Next.js, TypeScript, and Node.js. They're also experienced in AI integration, 3D web development with Three.js, and creating modern, interactive user experiences.\",\n    projects: \"Green Hacker has worked on various projects including AI-powered portfolios, machine learning applications, React component libraries, and 3D web experiences. Each project showcases their commitment to innovation and technical excellence.\",\n    contact: \"You can connect with Green Hacker through their GitHub profile, LinkedIn, or the contact form on this portfolio. They're always interested in discussing new opportunities and collaborations.\",\n    default: \"Thanks for your interest in Green Hacker's work! They're a passionate full-stack developer with expertise in AI, modern web technologies, and creating engaging digital experiences. Feel free to explore the portfolio to learn more about their projects and skills.\"\n  };\n\n  const lowerMessage = message.toLowerCase();\n\n  let responseText = responses.default;\n\n  if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {\n    responseText = responses.greeting;\n  } else if (lowerMessage.includes('skill') || lowerMessage.includes('technology') || lowerMessage.includes('tech')) {\n    responseText = responses.skills;\n  } else if (lowerMessage.includes('project') || lowerMessage.includes('work') || lowerMessage.includes('portfolio')) {\n    responseText = responses.projects;\n  } else if (lowerMessage.includes('contact') || lowerMessage.includes('reach') || lowerMessage.includes('connect')) {\n    responseText = responses.contact;\n  }\n\n  return NextResponse.json({\n    success: true,\n    response: responseText,\n    mock: true,\n    timestamp: new Date().toISOString()\n  });\n}\n\nexport async function GET() {\n  return NextResponse.json(\n    { error: 'Method not allowed. Use POST to send messages.' },\n    { status: 405 }\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,4DAA4D;AAC5D,MAAM,iBAAiB,IAAI;AAE3B,8BAA8B;AAC9B,MAAM,oBAAoB,KAAK,MAAM,WAAW;AAChD,MAAM,0BAA0B,GAAG,+BAA+B;AAElE,SAAS,cAAc,EAAU;IAC/B,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,eAAe,GAAG,CAAC;IAErC,IAAI,CAAC,aAAa,MAAM,UAAU,SAAS,EAAE;QAC3C,eAAe,GAAG,CAAC,IAAI;YACrB,OAAO;YACP,WAAW,MAAM;QACnB;QACA,OAAO;IACT;IAEA,IAAI,UAAU,KAAK,IAAI,yBAAyB;QAC9C,OAAO;IACT;IAEA,UAAU,KAAK;IACf,OAAO;AACT;AAEO,eAAe,KAAK,OAAgB;IACzC,IAAI;QACF,sBAAsB;QACtB,MAAM,KAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;QACrD,IAAI,cAAc,KAAK;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+C,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE/C,IAAI,CAAC,WAAW,OAAO,YAAY,UAAU;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2C,GACpD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,QAAQ,MAAM,GAAG,MAAM;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0D,GACnE;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,eAAe,QAAQ,GAAG,CAAC,cAAc;QAE/C,IAAI,CAAC,cAAc;YACjB,QAAQ,IAAI,CAAC;YACb,OAAO,gBAAgB;QACzB;QAEA,uBAAuB;QACvB,MAAM,QAAQ,IAAI,gKAAA,CAAA,qBAAkB,CAAC;QACrC,MAAM,QAAQ,MAAM,kBAAkB,CAAC;YAAE,OAAO;QAAmB;QAEnE,gCAAgC;QAChC,MAAM,eAAe,CAAC;;;;;;;;;SASjB,EAAE,WAAW,4BAA4B;;cAEpC,EAAE,SAAS;QAErB,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;QAC3C,MAAM,WAAW,MAAM,OAAO,QAAQ;QACtC,MAAM,OAAO,SAAS,IAAI;QAE1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,UAAU;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QAEtC,mCAAmC;QACnC,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;YAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkF,GAC3F;gBAAE,QAAQ;YAAI;QAElB;QAEA,wCAAwC;QACxC,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;YAC/D,QAAQ,KAAK,CAAC;YACd,OAAO,gBAAgB,MAAM,QAAQ,IAAI,GAAG,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,EAAE,KAAK,CAAC,IAAM;QACrF;QAEA,OAAO,gBAAgB,MAAM,QAAQ,IAAI,GAAG,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,EAAE,KAAK,CAAC,IAAM;IACrF;AACF;AAEA,SAAS,gBAAgB,OAAe;IACtC,MAAM,YAAY;QAChB,UAAU;QACV,QAAQ;QACR,UAAU;QACV,SAAS;QACT,SAAS;IACX;IAEA,MAAM,eAAe,QAAQ,WAAW;IAExC,IAAI,eAAe,UAAU,OAAO;IAEpC,IAAI,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,SAAS,aAAa,QAAQ,CAAC,QAAQ;QACjG,eAAe,UAAU,QAAQ;IACnC,OAAO,IAAI,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,iBAAiB,aAAa,QAAQ,CAAC,SAAS;QACjH,eAAe,UAAU,MAAM;IACjC,OAAO,IAAI,aAAa,QAAQ,CAAC,cAAc,aAAa,QAAQ,CAAC,WAAW,aAAa,QAAQ,CAAC,cAAc;QAClH,eAAe,UAAU,QAAQ;IACnC,OAAO,IAAI,aAAa,QAAQ,CAAC,cAAc,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,YAAY;QACjH,eAAe,UAAU,OAAO;IAClC;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,UAAU;QACV,MAAM;QACN,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,OAAO;IAAiD,GAC1D;QAAE,QAAQ;IAAI;AAElB", "debugId": null}}]}