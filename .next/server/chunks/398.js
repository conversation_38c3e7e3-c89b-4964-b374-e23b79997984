"use strict";exports.id=398,exports.ids=[398],exports.modules={2398:(t,r,a)=>{a.r(r),a.d(r,{CompressedGaussianSplats:()=>G,GSplineBuffer:()=>r8,GaussianPLYData:()=>R});var e=Object.create,n=Object.defineProperty,s=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,o=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty,u=(t,r,a)=>r in t?n(t,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[r]=a,l=(t,r)=>()=>(r||t((r={exports:{}}).exports,r),r.exports),c=(t,r)=>{for(var a in r)n(t,a,{get:r[a],enumerable:!0})},f=(t,r,a,e)=>{if(r&&"object"==typeof r||"function"==typeof r)for(let o of i(r))h.call(t,o)||o===a||n(t,o,{get:()=>r[o],enumerable:!(e=s(r,o))||e.enumerable});return t},p=(t,r,a)=>(a=null!=t?e(o(t)):{},f(!r&&t&&t.__esModule?a:n(a,"default",{value:t,enumerable:!0}),t)),d=(t,r,a)=>(u(t,"symbol"!=typeof r?r+"":r,a),a),y=l((t,r)=>{r.exports=function(t){for(var r=Array(t),a=0;a<t;++a)r[a]=a;return r}}),g=l((t,r)=>{function a(t){return!!t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}r.exports=function(t){var r;return null!=t&&(a(t)||"function"==typeof(r=t).readFloatLE&&"function"==typeof r.slice&&a(r.slice(0,0))||!!t._isBuffer)}}),m=l((t,r)=>{var a=y(),e=g(),n="u">typeof Float64Array;function s(t,r){return t[0]-r[0]}function i(){var t,r=this.stride,a=Array(r.length);for(t=0;t<a.length;++t)a[t]=[Math.abs(r[t]),t];a.sort(s);var e=Array(a.length);for(t=0;t<e.length;++t)e[t]=a[t][1];return e}var o={float32:[],float64:[],int8:[],int16:[],int32:[],uint8:[],uint16:[],uint32:[],array:[],uint8_clamped:[],bigint64:[],biguint64:[],buffer:[],generic:[]};r.exports=function(t,r,s,h){if(void 0===t){var u=o.array[0];return u([])}"number"==typeof t&&(t=[t]),void 0===r&&(r=[t.length]);var l=r.length;if(void 0===s){s=Array(l);for(var c=l-1,f=1;c>=0;--c)s[c]=f,f*=r[c]}if(void 0===h){h=0;for(var c=0;c<l;++c)s[c]<0&&(h-=(r[c]-1)*s[c])}for(var p=function(t){if(e(t))return"buffer";if(n)switch(Object.prototype.toString.call(t)){case"[object Float64Array]":return"float64";case"[object Float32Array]":return"float32";case"[object Int8Array]":return"int8";case"[object Int16Array]":return"int16";case"[object Int32Array]":return"int32";case"[object Uint8Array]":return"uint8";case"[object Uint16Array]":return"uint16";case"[object Uint32Array]":return"uint32";case"[object Uint8ClampedArray]":return"uint8_clamped";case"[object BigInt64Array]":return"bigint64";case"[object BigUint64Array]":return"biguint64"}return Array.isArray(t)?"array":"generic"}(t),d=o[p];d.length<=l+1;)d.push(function(t,r){var e=["View",r,"d",t].join("");r<0&&(e="View_Nil"+t);var n="generic"===t;if(-1===r){var s="function "+e+"(a){this.data=a;};var proto="+e+".prototype;proto.dtype='"+t+"';proto.index=function(){return -1};proto.size=0;proto.dimension=-1;proto.shape=proto.stride=proto.order=[];proto.lo=proto.hi=proto.transpose=proto.step=function(){return new "+e+"(this.data);};proto.get=proto.set=function(){};proto.pick=function(){return null};return function construct_"+e+"(a){return new "+e+"(a);}",h=Function(s);return h()}if(0===r){var s="function "+e+"(a,d) {this.data = a;this.offset = d};var proto="+e+".prototype;proto.dtype='"+t+"';proto.index=function(){return this.offset};proto.dimension=0;proto.size=1;proto.shape=proto.stride=proto.order=[];proto.lo=proto.hi=proto.transpose=proto.step=function "+e+"_copy() {return new "+e+"(this.data,this.offset)};proto.pick=function "+e+"_pick(){return TrivialArray(this.data);};proto.valueOf=proto.get=function "+e+"_get(){return "+(n?"this.data.get(this.offset)":"this.data[this.offset]")+"};proto.set=function "+e+"_set(v){return "+(n?"this.data.set(this.offset,v)":"this.data[this.offset]=v")+"};return function construct_"+e+"(a,b,c,d){return new "+e+"(a,d)}",h=Function("TrivialArray",s);return h(o[t][0])}var s=["'use strict'"],u=a(r),l=u.map(function(t){return"i"+t}),c="this.offset+"+u.map(function(t){return"this.stride["+t+"]*i"+t}).join("+"),f=u.map(function(t){return"b"+t}).join(","),p=u.map(function(t){return"c"+t}).join(",");s.push("function "+e+"(a,"+f+","+p+",d){this.data=a","this.shape=["+f+"]","this.stride=["+p+"]","this.offset=d|0}","var proto="+e+".prototype","proto.dtype='"+t+"'","proto.dimension="+r),s.push("Object.defineProperty(proto,'size',{get:function "+e+"_size(){return "+u.map(function(t){return"this.shape["+t+"]"}).join("*"),"}})"),1===r?s.push("proto.order=[0]"):(s.push("Object.defineProperty(proto,'order',{get:"),r<4?(s.push("function "+e+"_order(){"),2===r?s.push("return (Math.abs(this.stride[0])>Math.abs(this.stride[1]))?[1,0]:[0,1]}})"):3===r&&s.push("var s0=Math.abs(this.stride[0]),s1=Math.abs(this.stride[1]),s2=Math.abs(this.stride[2]);if(s0>s1){if(s1>s2){return [2,1,0];}else if(s0>s2){return [1,2,0];}else{return [1,0,2];}}else if(s0>s2){return [2,0,1];}else if(s2>s1){return [0,1,2];}else{return [0,2,1];}}})")):s.push("ORDER})")),s.push("proto.set=function "+e+"_set("+l.join(",")+",v){"),n?s.push("return this.data.set("+c+",v)}"):s.push("return this.data["+c+"]=v}"),s.push("proto.get=function "+e+"_get("+l.join(",")+"){"),n?s.push("return this.data.get("+c+")}"):s.push("return this.data["+c+"]}"),s.push("proto.index=function "+e+"_index(",l.join(),"){return "+c+"}"),s.push("proto.hi=function "+e+"_hi("+l.join(",")+"){return new "+e+"(this.data,"+u.map(function(t){return["(typeof i",t,"!=='number'||i",t,"<0)?this.shape[",t,"]:i",t,"|0"].join("")}).join(",")+","+u.map(function(t){return"this.stride["+t+"]"}).join(",")+",this.offset)}");var d=u.map(function(t){return"a"+t+"=this.shape["+t+"]"}),y=u.map(function(t){return"c"+t+"=this.stride["+t+"]"});s.push("proto.lo=function "+e+"_lo("+l.join(",")+"){var b=this.offset,d=0,"+d.join(",")+","+y.join(","));for(var g=0;g<r;++g)s.push("if(typeof i"+g+"==='number'&&i"+g+">=0){d=i"+g+"|0;b+=c"+g+"*d;a"+g+"-=d}");s.push("return new "+e+"(this.data,"+u.map(function(t){return"a"+t}).join(",")+","+u.map(function(t){return"c"+t}).join(",")+",b)}"),s.push("proto.step=function "+e+"_step("+l.join(",")+"){var "+u.map(function(t){return"a"+t+"=this.shape["+t+"]"}).join(",")+","+u.map(function(t){return"b"+t+"=this.stride["+t+"]"}).join(",")+",c=this.offset,d=0,ceil=Math.ceil");for(var g=0;g<r;++g)s.push("if(typeof i"+g+"==='number'){d=i"+g+"|0;if(d<0){c+=b"+g+"*(a"+g+"-1);a"+g+"=ceil(-a"+g+"/d)}else{a"+g+"=ceil(a"+g+"/d)}b"+g+"*=d}");s.push("return new "+e+"(this.data,"+u.map(function(t){return"a"+t}).join(",")+","+u.map(function(t){return"b"+t}).join(",")+",c)}");for(var m=Array(r),b=Array(r),g=0;g<r;++g)m[g]="a[i"+g+"]",b[g]="b[i"+g+"]";s.push("proto.transpose=function "+e+"_transpose("+l+"){"+l.map(function(t,r){return t+"=("+t+"===undefined?"+r+":"+t+"|0)"}).join(";"),"var a=this.shape,b=this.stride;return new "+e+"(this.data,"+m.join(",")+","+b.join(",")+",this.offset)}"),s.push("proto.pick=function "+e+"_pick("+l+"){var a=[],b=[],c=this.offset");for(var g=0;g<r;++g)s.push("if(typeof i"+g+"==='number'&&i"+g+">=0){c=(c+this.stride["+g+"]*i"+g+")|0}else{a.push(this.shape["+g+"]);b.push(this.stride["+g+"])}");s.push("var ctor=CTOR_LIST[a.length+1];return ctor(this.data,a,b,c)}"),s.push("return function construct_"+e+"(data,shape,stride,offset){return new "+e+"(data,"+u.map(function(t){return"shape["+t+"]"}).join(",")+","+u.map(function(t){return"stride["+t+"]"}).join(",")+",offset)}");var h=Function("CTOR_LIST","ORDER",s.join(`
`));return h(o[t],i)}(p,d.length-1));var u=d[l+1];return u(t,r,s,h)}}),b=l((t,r)=>{r.exports=function(t,r,a){return 0===t.length?t:r?(a||t.sort(r),function(t,r){for(var a=1,e=t.length,n=t[0],s=t[0],i=1;i<e;++i)if(s=n,r(n=t[i],s)){if(i===a){a++;continue}t[a++]=n}return t.length=a,t}(t,r)):(a||t.sort(),function(t){for(var r=1,a=t.length,e=t[0],n=t[0],s=1;s<a;++s,n=e)if(n=e,(e=t[s])!==n){if(s===r){r++;continue}t[r++]=e}return t.length=r,t}(t))}}),_=l((t,r)=>{var a=b();function e(t,r,a){var e,n,s=t.length,i=r.arrayArgs.length,o=r.indexArgs.length>0,h=[],u=[],l=0,c=0;for(e=0;e<s;++e)u.push(["i",e,"=0"].join(""));for(n=0;n<i;++n)for(e=0;e<s;++e)c=l,l=t[e],0===e?u.push(["d",n,"s",e,"=t",n,"p",l].join("")):u.push(["d",n,"s",e,"=(t",n,"p",l,"-s",c,"*t",n,"p",c,")"].join(""));for(u.length>0&&h.push("var "+u.join(",")),e=s-1;e>=0;--e)l=t[e],h.push(["for(i",e,"=0;i",e,"<s",l,";++i",e,"){"].join(""));for(h.push(a),e=0;e<s;++e){for(c=l,l=t[e],n=0;n<i;++n)h.push(["p",n,"+=d",n,"s",e].join(""));o&&(e>0&&h.push(["index[",c,"]-=s",c].join("")),h.push(["++index[",l,"]"].join(""))),h.push("}")}return h.join(`
`)}function n(t,r,a){for(var e=t.body,n=[],s=[],i=0;i<t.args.length;++i){var o=t.args[i];if(!(o.count<=0)){var h=RegExp(o.name,"g"),u="",l=r.arrayArgs.indexOf(i);switch(r.argTypes[i]){case"offset":var c=r.offsetArgIndex.indexOf(i);l=r.offsetArgs[c].array,u="+q"+c;case"array":u="p"+l+u;var f="l"+i,p="a"+l;if(0===r.arrayBlockIndices[l])1===o.count?"generic"===a[l]?o.lvalue?(n.push(["var ",f,"=",p,".get(",u,")"].join("")),e=e.replace(h,f),s.push([p,".set(",u,",",f,")"].join(""))):e=e.replace(h,[p,".get(",u,")"].join("")):e=e.replace(h,[p,"[",u,"]"].join("")):"generic"===a[l]?(n.push(["var ",f,"=",p,".get(",u,")"].join("")),e=e.replace(h,f),o.lvalue&&s.push([p,".set(",u,",",f,")"].join(""))):(n.push(["var ",f,"=",p,"[",u,"]"].join("")),e=e.replace(h,f),o.lvalue&&s.push([p,"[",u,"]=",f].join("")));else{for(var d=[o.name],y=[u],g=0;g<Math.abs(r.arrayBlockIndices[l]);g++)d.push("\\s*\\[([^\\]]+)\\]"),y.push("$"+(g+1)+"*t"+l+"b"+g);if(h=RegExp(d.join(""),"g"),u=y.join("+"),"generic"===a[l])throw Error("cwise: Generic arrays not supported in combination with blocks!");e=e.replace(h,[p,"[",u,"]"].join(""))}break;case"scalar":e=e.replace(h,"Y"+r.scalarArgs.indexOf(i));break;case"index":e=e.replace(h,"index");break;case"shape":e=e.replace(h,"shape")}}}return[n.join(`
`),e,s.join(`
`)].join(`
`).trim()}r.exports=function(t,r){for(var s=r[1].length-Math.abs(t.arrayBlockIndices[0])|0,i=Array(t.arrayArgs.length),o=Array(t.arrayArgs.length),h=0;h<t.arrayArgs.length;++h)o[h]=r[2*h],i[h]=r[2*h+1];for(var u=[],l=[],c=[],f=[],p=[],h=0;h<t.arrayArgs.length;++h){t.arrayBlockIndices[h]<0?(c.push(0),f.push(s),u.push(s),l.push(s+t.arrayBlockIndices[h])):(c.push(t.arrayBlockIndices[h]),f.push(t.arrayBlockIndices[h]+s),u.push(0),l.push(t.arrayBlockIndices[h]));for(var d=[],y=0;y<i[h].length;y++)c[h]<=i[h][y]&&i[h][y]<f[h]&&d.push(i[h][y]-c[h]);p.push(d)}for(var g=["SS"],m=["'use strict'"],b=[],y=0;y<s;++y)b.push(["s",y,"=SS[",y,"]"].join(""));for(var h=0;h<t.arrayArgs.length;++h){g.push("a"+h),g.push("t"+h),g.push("p"+h);for(var y=0;y<s;++y)b.push(["t",h,"p",y,"=t",h,"[",c[h]+y,"]"].join(""));for(var y=0;y<Math.abs(t.arrayBlockIndices[h]);++y)b.push(["t",h,"b",y,"=t",h,"[",u[h]+y,"]"].join(""))}for(var h=0;h<t.scalarArgs.length;++h)g.push("Y"+h);if(t.shapeArgs.length>0&&b.push("shape=SS.slice(0)"),t.indexArgs.length>0){for(var _=Array(s),h=0;h<s;++h)_[h]="0";b.push(["index=[",_.join(","),"]"].join(""))}for(var h=0;h<t.offsetArgs.length;++h){for(var v=t.offsetArgs[h],M=[],y=0;y<v.offset.length;++y)0!==v.offset[y]&&(1===v.offset[y]?M.push(["t",v.array,"p",y].join("")):M.push([v.offset[y],"*t",v.array,"p",y].join("")));0===M.length?b.push("q"+h+"=0"):b.push(["q",h,"=",M.join("+")].join(""))}var x=a([].concat(t.pre.thisVars).concat(t.body.thisVars).concat(t.post.thisVars));(b=b.concat(x)).length>0&&m.push("var "+b.join(","));for(var h=0;h<t.arrayArgs.length;++h)m.push("p"+h+"|=0");t.pre.body.length>3&&m.push(n(t.pre,t,o));var k=n(t.body,t,o),w=function(t){for(var r=0,a=t[0].length;r<a;){for(var e=1;e<t.length;++e)if(t[e][r]!==t[0][r])return r;++r}return r}(p);w<s?m.push(function(t,r,a,n){for(var s=r.length,i=a.arrayArgs.length,o=a.blockSize,h=a.indexArgs.length>0,u=[],l=0;l<i;++l)u.push(["var offset",l,"=p",l].join(""));for(var l=t;l<s;++l)u.push(["for(var j"+l+"=SS[",r[l],"]|0;j",l,">0;){"].join("")),u.push(["if(j",l,"<",o,"){"].join("")),u.push(["s",r[l],"=j",l].join("")),u.push(["j",l,"=0"].join("")),u.push(["}else{s",r[l],"=",o].join("")),u.push(["j",l,"-=",o,"}"].join("")),h&&u.push(["index[",r[l],"]=j",l].join(""));for(var l=0;l<i;++l){for(var c=["offset"+l],f=t;f<s;++f)c.push(["j",f,"*t",l,"p",r[f]].join(""));u.push(["p",l,"=(",c.join("+"),")"].join(""))}u.push(e(r,a,n));for(var l=t;l<s;++l)u.push("}");return u.join(`
`)}(w,p[0],t,k)):m.push(e(p[0],t,k)),t.post.body.length>3&&m.push(n(t.post,t,o)),t.debug&&console.log("-----Generated cwise routine for ",r,`:
`+m.join(`
`)+`
----------`);var A=[t.funcName||"unnamed","_cwise_loop_",i[0].join("s"),"m",w,function(t){for(var r=Array(t.length),a=!0,e=0;e<t.length;++e){var n=t[e],s=n.match(/\d+/);s=s?s[0]:"",0===n.charAt(0)?r[e]="u"+n.charAt(1)+s:r[e]=n.charAt(0)+s,e>0&&(a=a&&r[e]===r[e-1])}return a?r[0]:r.join("")}(o)].join("");return Function(["function ",A,"(",g.join(","),"){",m.join(`
`),"} return ",A].join(""))()}}),v=l((t,r)=>{var a=_();r.exports=function(t){var r=["'use strict'","var CACHED={}"],e=[];r.push(["return function ",t.funcName+"_cwise_thunk","(",t.shimArgs.join(","),"){"].join(""));for(var n=[],s=[],i=[["array",t.arrayArgs[0],".shape.slice(",Math.max(0,t.arrayBlockIndices[0]),t.arrayBlockIndices[0]<0?","+t.arrayBlockIndices[0]+")":")"].join("")],o=[],h=[],u=0;u<t.arrayArgs.length;++u){var l=t.arrayArgs[u];e.push(["t",l,"=array",l,".dtype,","r",l,"=array",l,".order"].join("")),n.push("t"+l),n.push("r"+l),s.push("t"+l),s.push("r"+l+".join()"),i.push("array"+l+".data"),i.push("array"+l+".stride"),i.push("array"+l+".offset|0"),u>0&&(o.push("array"+t.arrayArgs[0]+".shape.length===array"+l+".shape.length+"+(Math.abs(t.arrayBlockIndices[0])-Math.abs(t.arrayBlockIndices[u]))),h.push("array"+t.arrayArgs[0]+".shape[shapeIndex+"+Math.max(0,t.arrayBlockIndices[0])+"]===array"+l+".shape[shapeIndex+"+Math.max(0,t.arrayBlockIndices[u])+"]"))}t.arrayArgs.length>1&&(r.push("if (!("+o.join(" && ")+")) throw new Error('cwise: Arrays do not all have the same dimensionality!')"),r.push("for(var shapeIndex=array"+t.arrayArgs[0]+".shape.length-"+Math.abs(t.arrayBlockIndices[0])+"; shapeIndex--\x3e0;) {"),r.push("if (!("+h.join(" && ")+")) throw new Error('cwise: Arrays do not all have the same shape!')"),r.push("}"));for(var u=0;u<t.scalarArgs.length;++u)i.push("scalar"+t.scalarArgs[u]);return e.push(["type=[",s.join(","),"].join()"].join("")),e.push("proc=CACHED[type]"),r.push("var "+e.join(",")),r.push(["if(!proc){","CACHED[type]=proc=compile([",n.join(","),"])}","return proc(",i.join(","),")}"].join("")),t.debug&&console.log(`-----Generated thunk:
`+r.join(`
`)+`
----------`),Function("compile",r.join(`
`))(a.bind(void 0,t))}}),M=l((t,r)=>{var a=v();function e(){this.argTypes=[],this.shimArgs=[],this.arrayArgs=[],this.arrayBlockIndices=[],this.scalarArgs=[],this.offsetArgs=[],this.offsetArgIndex=[],this.indexArgs=[],this.shapeArgs=[],this.funcName="",this.pre=null,this.body=null,this.post=null,this.debug=!1}r.exports=function(t){var r=new e;r.pre=t.pre,r.body=t.body,r.post=t.post;var n=t.args.slice(0);r.argTypes=n;for(var s=0;s<n.length;++s){var i=n[s];if("array"===i||"object"==typeof i&&i.blockIndices){if(r.argTypes[s]="array",r.arrayArgs.push(s),r.arrayBlockIndices.push(i.blockIndices?i.blockIndices:0),r.shimArgs.push("array"+s),s<r.pre.args.length&&r.pre.args[s].count>0)throw Error("cwise: pre() block may not reference array args");if(s<r.post.args.length&&r.post.args[s].count>0)throw Error("cwise: post() block may not reference array args")}else if("scalar"===i)r.scalarArgs.push(s),r.shimArgs.push("scalar"+s);else if("index"===i){if(r.indexArgs.push(s),s<r.pre.args.length&&r.pre.args[s].count>0)throw Error("cwise: pre() block may not reference array index");if(s<r.body.args.length&&r.body.args[s].lvalue)throw Error("cwise: body() block may not write to array index");if(s<r.post.args.length&&r.post.args[s].count>0)throw Error("cwise: post() block may not reference array index")}else if("shape"===i){if(r.shapeArgs.push(s),s<r.pre.args.length&&r.pre.args[s].lvalue)throw Error("cwise: pre() block may not write to array shape");if(s<r.body.args.length&&r.body.args[s].lvalue)throw Error("cwise: body() block may not write to array shape");if(s<r.post.args.length&&r.post.args[s].lvalue)throw Error("cwise: post() block may not write to array shape")}else if("object"==typeof i&&i.offset)r.argTypes[s]="offset",r.offsetArgs.push({array:i.array,offset:i.offset}),r.offsetArgIndex.push(s);else throw Error("cwise: Unknown argument type "+n[s])}if(r.arrayArgs.length<=0)throw Error("cwise: No array arguments specified");if(r.pre.args.length>n.length)throw Error("cwise: Too many arguments in pre() block");if(r.body.args.length>n.length)throw Error("cwise: Too many arguments in body() block");if(r.post.args.length>n.length)throw Error("cwise: Too many arguments in post() block");return r.debug=!!t.printCode||!!t.debug,r.funcName=t.funcName||"cwise",r.blockSize=t.blockSize||64,a(r)}}),x=l(t=>{var r=M(),a={body:"",args:[],thisVars:[],localVars:[]};function e(t){if(!t)return a;for(var r=0;r<t.args.length;++r){var e=t.args[r];0===r?t.args[r]={name:e,lvalue:!0,rvalue:!!t.rvalue,count:t.count||1}:t.args[r]={name:e,lvalue:!1,rvalue:!0,count:1}}return t.thisVars||(t.thisVars=[]),t.localVars||(t.localVars=[]),t}function n(t){for(var a=[],n=0;n<t.args.length;++n)a.push("a"+n);return Function("P",["return function ",t.funcName,"_ndarrayops(",a.join(","),") {P(",a.join(","),");return a0}"].join(""))(r({args:t.args,pre:e(t.pre),body:e(t.body),post:e(t.proc),funcName:t.funcName}))}var s={add:"+",sub:"-",mul:"*",div:"/",mod:"%",band:"&",bor:"|",bxor:"^",lshift:"<<",rshift:">>",rrshift:">>>"};!function(){for(var r in s){var a=s[r];t[r]=n({args:["array","array","array"],body:{args:["a","b","c"],body:"a=b"+a+"c"},funcName:r}),t[r+"eq"]=n({args:["array","array"],body:{args:["a","b"],body:"a"+a+"=b"},rvalue:!0,funcName:r+"eq"}),t[r+"s"]=n({args:["array","array","scalar"],body:{args:["a","b","s"],body:"a=b"+a+"s"},funcName:r+"s"}),t[r+"seq"]=n({args:["array","scalar"],body:{args:["a","s"],body:"a"+a+"=s"},rvalue:!0,funcName:r+"seq"})}}();var i={not:"!",bnot:"~",neg:"-",recip:"1.0/"};!function(){for(var r in i){var a=i[r];t[r]=n({args:["array","array"],body:{args:["a","b"],body:"a="+a+"b"},funcName:r}),t[r+"eq"]=n({args:["array"],body:{args:["a"],body:"a="+a+"a"},rvalue:!0,count:2,funcName:r+"eq"})}}();var o={and:"&&",or:"||",eq:"===",neq:"!==",lt:"<",gt:">",leq:"<=",geq:">="};!function(){for(var r in o){var a=o[r];t[r]=n({args:["array","array","array"],body:{args:["a","b","c"],body:"a=b"+a+"c"},funcName:r}),t[r+"s"]=n({args:["array","array","scalar"],body:{args:["a","b","s"],body:"a=b"+a+"s"},funcName:r+"s"}),t[r+"eq"]=n({args:["array","array"],body:{args:["a","b"],body:"a=a"+a+"b"},rvalue:!0,count:2,funcName:r+"eq"}),t[r+"seq"]=n({args:["array","scalar"],body:{args:["a","s"],body:"a=a"+a+"s"},rvalue:!0,count:2,funcName:r+"seq"})}}();var h=["abs","acos","asin","atan","ceil","cos","exp","floor","log","round","sin","sqrt","tan"];!function(){for(var r=0;r<h.length;++r){var a=h[r];t[a]=n({args:["array","array"],pre:{args:[],body:"this_f=Math."+a,thisVars:["this_f"]},body:{args:["a","b"],body:"a=this_f(b)",thisVars:["this_f"]},funcName:a}),t[a+"eq"]=n({args:["array"],pre:{args:[],body:"this_f=Math."+a,thisVars:["this_f"]},body:{args:["a"],body:"a=this_f(a)",thisVars:["this_f"]},rvalue:!0,count:2,funcName:a+"eq"})}}();var u=["max","min","atan2","pow"];!function(){for(var r=0;r<u.length;++r){var a=u[r];t[a]=n({args:["array","array","array"],pre:{args:[],body:"this_f=Math."+a,thisVars:["this_f"]},body:{args:["a","b","c"],body:"a=this_f(b,c)",thisVars:["this_f"]},funcName:a}),t[a+"s"]=n({args:["array","array","scalar"],pre:{args:[],body:"this_f=Math."+a,thisVars:["this_f"]},body:{args:["a","b","c"],body:"a=this_f(b,c)",thisVars:["this_f"]},funcName:a+"s"}),t[a+"eq"]=n({args:["array","array"],pre:{args:[],body:"this_f=Math."+a,thisVars:["this_f"]},body:{args:["a","b"],body:"a=this_f(a,b)",thisVars:["this_f"]},rvalue:!0,count:2,funcName:a+"eq"}),t[a+"seq"]=n({args:["array","scalar"],pre:{args:[],body:"this_f=Math."+a,thisVars:["this_f"]},body:{args:["a","b"],body:"a=this_f(a,b)",thisVars:["this_f"]},rvalue:!0,count:2,funcName:a+"seq"})}}();var l=["atan2","pow"];!function(){for(var r=0;r<l.length;++r){var a=l[r];t[a+"op"]=n({args:["array","array","array"],pre:{args:[],body:"this_f=Math."+a,thisVars:["this_f"]},body:{args:["a","b","c"],body:"a=this_f(c,b)",thisVars:["this_f"]},funcName:a+"op"}),t[a+"ops"]=n({args:["array","array","scalar"],pre:{args:[],body:"this_f=Math."+a,thisVars:["this_f"]},body:{args:["a","b","c"],body:"a=this_f(c,b)",thisVars:["this_f"]},funcName:a+"ops"}),t[a+"opeq"]=n({args:["array","array"],pre:{args:[],body:"this_f=Math."+a,thisVars:["this_f"]},body:{args:["a","b"],body:"a=this_f(b,a)",thisVars:["this_f"]},rvalue:!0,count:2,funcName:a+"opeq"}),t[a+"opseq"]=n({args:["array","scalar"],pre:{args:[],body:"this_f=Math."+a,thisVars:["this_f"]},body:{args:["a","b"],body:"a=this_f(b,a)",thisVars:["this_f"]},rvalue:!0,count:2,funcName:a+"opseq"})}}(),t.any=r({args:["array"],pre:a,body:{args:[{name:"a",lvalue:!1,rvalue:!0,count:1}],body:"if(a){return true}",localVars:[],thisVars:[]},post:{args:[],localVars:[],thisVars:[],body:"return false"},funcName:"any"}),t.all=r({args:["array"],pre:a,body:{args:[{name:"x",lvalue:!1,rvalue:!0,count:1}],body:"if(!x){return false}",localVars:[],thisVars:[]},post:{args:[],localVars:[],thisVars:[],body:"return true"},funcName:"all"}),t.sum=r({args:["array"],pre:{args:[],localVars:[],thisVars:["this_s"],body:"this_s=0"},body:{args:[{name:"a",lvalue:!1,rvalue:!0,count:1}],body:"this_s+=a",localVars:[],thisVars:["this_s"]},post:{args:[],localVars:[],thisVars:["this_s"],body:"return this_s"},funcName:"sum"}),t.prod=r({args:["array"],pre:{args:[],localVars:[],thisVars:["this_s"],body:"this_s=1"},body:{args:[{name:"a",lvalue:!1,rvalue:!0,count:1}],body:"this_s*=a",localVars:[],thisVars:["this_s"]},post:{args:[],localVars:[],thisVars:["this_s"],body:"return this_s"},funcName:"prod"}),t.norm2squared=r({args:["array"],pre:{args:[],localVars:[],thisVars:["this_s"],body:"this_s=0"},body:{args:[{name:"a",lvalue:!1,rvalue:!0,count:2}],body:"this_s+=a*a",localVars:[],thisVars:["this_s"]},post:{args:[],localVars:[],thisVars:["this_s"],body:"return this_s"},funcName:"norm2squared"}),t.norm2=r({args:["array"],pre:{args:[],localVars:[],thisVars:["this_s"],body:"this_s=0"},body:{args:[{name:"a",lvalue:!1,rvalue:!0,count:2}],body:"this_s+=a*a",localVars:[],thisVars:["this_s"]},post:{args:[],localVars:[],thisVars:["this_s"],body:"return Math.sqrt(this_s)"},funcName:"norm2"}),t.norminf=r({args:["array"],pre:{args:[],localVars:[],thisVars:["this_s"],body:"this_s=0"},body:{args:[{name:"a",lvalue:!1,rvalue:!0,count:4}],body:"if(-a>this_s){this_s=-a}else if(a>this_s){this_s=a}",localVars:[],thisVars:["this_s"]},post:{args:[],localVars:[],thisVars:["this_s"],body:"return this_s"},funcName:"norminf"}),t.norm1=r({args:["array"],pre:{args:[],localVars:[],thisVars:["this_s"],body:"this_s=0"},body:{args:[{name:"a",lvalue:!1,rvalue:!0,count:3}],body:"this_s+=a<0?-a:a",localVars:[],thisVars:["this_s"]},post:{args:[],localVars:[],thisVars:["this_s"],body:"return this_s"},funcName:"norm1"}),t.sup=r({args:["array"],pre:{body:"this_h=-Infinity",args:[],thisVars:["this_h"],localVars:[]},body:{body:"if(_inline_1_arg0_>this_h)this_h=_inline_1_arg0_",args:[{name:"_inline_1_arg0_",lvalue:!1,rvalue:!0,count:2}],thisVars:["this_h"],localVars:[]},post:{body:"return this_h",args:[],thisVars:["this_h"],localVars:[]}}),t.inf=r({args:["array"],pre:{body:"this_h=Infinity",args:[],thisVars:["this_h"],localVars:[]},body:{body:"if(_inline_1_arg0_<this_h)this_h=_inline_1_arg0_",args:[{name:"_inline_1_arg0_",lvalue:!1,rvalue:!0,count:2}],thisVars:["this_h"],localVars:[]},post:{body:"return this_h",args:[],thisVars:["this_h"],localVars:[]}}),t.argmin=r({args:["index","array","shape"],pre:{body:"{this_v=Infinity;this_i=_inline_0_arg2_.slice(0)}",args:[{name:"_inline_0_arg0_",lvalue:!1,rvalue:!1,count:0},{name:"_inline_0_arg1_",lvalue:!1,rvalue:!1,count:0},{name:"_inline_0_arg2_",lvalue:!1,rvalue:!0,count:1}],thisVars:["this_i","this_v"],localVars:[]},body:{body:"{if(_inline_1_arg1_<this_v){this_v=_inline_1_arg1_;for(var _inline_1_k=0;_inline_1_k<_inline_1_arg0_.length;++_inline_1_k){this_i[_inline_1_k]=_inline_1_arg0_[_inline_1_k]}}}",args:[{name:"_inline_1_arg0_",lvalue:!1,rvalue:!0,count:2},{name:"_inline_1_arg1_",lvalue:!1,rvalue:!0,count:2}],thisVars:["this_i","this_v"],localVars:["_inline_1_k"]},post:{body:"{return this_i}",args:[],thisVars:["this_i"],localVars:[]}}),t.argmax=r({args:["index","array","shape"],pre:{body:"{this_v=-Infinity;this_i=_inline_0_arg2_.slice(0)}",args:[{name:"_inline_0_arg0_",lvalue:!1,rvalue:!1,count:0},{name:"_inline_0_arg1_",lvalue:!1,rvalue:!1,count:0},{name:"_inline_0_arg2_",lvalue:!1,rvalue:!0,count:1}],thisVars:["this_i","this_v"],localVars:[]},body:{body:"{if(_inline_1_arg1_>this_v){this_v=_inline_1_arg1_;for(var _inline_1_k=0;_inline_1_k<_inline_1_arg0_.length;++_inline_1_k){this_i[_inline_1_k]=_inline_1_arg0_[_inline_1_k]}}}",args:[{name:"_inline_1_arg0_",lvalue:!1,rvalue:!0,count:2},{name:"_inline_1_arg1_",lvalue:!1,rvalue:!0,count:2}],thisVars:["this_i","this_v"],localVars:["_inline_1_k"]},post:{body:"{return this_i}",args:[],thisVars:["this_i"],localVars:[]}}),t.random=n({args:["array"],pre:{args:[],body:"this_f=Math.random",thisVars:["this_f"]},body:{args:["a"],body:"a=this_f()",thisVars:["this_f"]},funcName:"random"}),t.assign=n({args:["array","array"],body:{args:["a","b"],body:"a=b"},funcName:"assign"}),t.assigns=n({args:["array","scalar"],body:{args:["a","b"],body:"a=b"},funcName:"assigns"}),t.equals=r({args:["array","array"],pre:a,body:{args:[{name:"x",lvalue:!1,rvalue:!0,count:1},{name:"y",lvalue:!1,rvalue:!0,count:1}],body:"if(x!==y){return false}",localVars:[],thisVars:[]},post:{args:[],localVars:[],thisVars:[],body:"return true"},funcName:"equals"})}),k=p(m(),1),w=p(x(),1),A=p(m(),1),q=p(x(),1),z=class{constructor(t,r,a){this._dataNormalized=t,this._minD=r,this._maxD=a}static createFromUnnormalized(t){let r=q.sup(t),a=q.inf(t),e=(0,A.default)(new Float32Array(t.size),t.shape),n=r-a;return n<1e-4?q.assigns(e,0):(q.subs(e,t,a),q.divs(e,e,n)),new z(e,a,r)}get data(){return this._dataNormalized}get minD(){return this._minD}get maxD(){return this._maxD}denormalize(){let t=(0,A.default)(new Float32Array(this._dataNormalized.size),this._dataNormalized.shape);return q.muls(t,this._dataNormalized,this._maxD-this._minD),q.adds(t,t,this._minD),t}},j=class{constructor(t,r){this._quantized=t,this._method=r}get quantized(){return this._quantized}static maxIntBits(t){return 2**t-1}static fromNormalized(t,r){let a=t.data,e;if("norm8x"===r){let t=j.maxIntBits(8),r=(0,A.default)(new Float32Array(a.size),a.shape);q.muls(r,a,t),q.roundeq(r),e=(0,A.default)(new Uint8Array(r.data),a.shape)}else if("norm565"===r){let t=(0,A.default)(new Float32Array(a.size),a.shape);q.assign(t,a),q.mulseq(t.pick(null,0),j.maxIntBits(5)),q.mulseq(t.pick(null,1),j.maxIntBits(6)),q.mulseq(t.pick(null,2),j.maxIntBits(5)),q.roundeq(t);let r=(0,A.default)(new Uint16Array(t.data),a.shape),n=(0,A.default)(new Uint16Array(a.shape[0]),[a.shape[0]]),s=(0,A.default)(new Uint16Array(a.shape[0]),[a.shape[0]]);q.lshifts(n,r.pick(null,0),11),q.lshifts(s,r.pick(null,1),5),q.boreq(n,s),q.boreq(n,r.pick(null,2)),e=n}else{let t=(0,A.default)(new Float32Array(a.size),a.shape);q.assign(t,a),q.mulseq(t.pick(null,0),j.maxIntBits(11)),q.mulseq(t.pick(null,1),j.maxIntBits(10)),q.mulseq(t.pick(null,2),j.maxIntBits(11)),q.roundeq(t);let r=(0,A.default)(new Uint32Array(t.data),a.shape),n=(0,A.default)(new Uint32Array(a.shape[0]),[a.shape[0]]),s=(0,A.default)(new Uint32Array(a.shape[0]),[a.shape[0]]);q.lshifts(n,r.pick(null,0),21),q.lshifts(s,r.pick(null,1),11),q.boreq(n,s),q.boreq(n,r.pick(null,2)),e=n}return new j(e,r)}dequantize(t,r){let a=this._method,e,n=this._quantized;if("norm8x"===a){let t=j.maxIntBits(8);e=(0,A.default)(new Float32Array(n.size),n.shape),q.muls(e,n,1/t)}else if("norm565"===a){let t=(0,A.default)(new Uint8Array(n.shape[0]),[n.shape[0]]),r=(0,A.default)(new Uint8Array(n.shape[0]),[n.shape[0]]),a=(0,A.default)(new Uint8Array(n.shape[0]),[n.shape[0]]);q.rrshifts(t,n,11),q.rrshifts(r,n,5),q.bandseq(r,j.maxIntBits(6)),q.bands(a,n,j.maxIntBits(5)),e=(0,A.default)(new Float32Array(3*n.shape[0]),[n.shape[0],3]),q.muls(e.pick(null,0),t,1/j.maxIntBits(5)),q.muls(e.pick(null,1),r,1/j.maxIntBits(6)),q.muls(e.pick(null,2),a,1/j.maxIntBits(5))}else{let t=(0,A.default)(new Uint16Array(n.shape[0]),[n.shape[0]]),r=(0,A.default)(new Uint16Array(n.shape[0]),[n.shape[0]]),a=(0,A.default)(new Uint16Array(n.shape[0]),[n.shape[0]]);q.rrshifts(t,n,21),q.rrshifts(r,n,11),q.bandseq(r,j.maxIntBits(10)),q.bands(a,n,j.maxIntBits(11)),e=(0,A.default)(new Float32Array(3*n.shape[0]),[n.shape[0],3]),q.muls(e.pick(null,0),t,1/j.maxIntBits(11)),q.muls(e.pick(null,1),r,1/j.maxIntBits(10)),q.muls(e.pick(null,2),a,1/j.maxIntBits(11))}return new z(e,t,r)}},V=class{constructor(t,r,a,e,n,s=!1){this._quantized=t,this._minMaxMatrix=r,this._chunkSize=a,this._quantizationMethod=e,this._variableChunkSize=n,this._isDynamicChunks=s}get length(){return this._quantized.shape[0]}get nchunks(){return this._minMaxMatrix.shape[0]}get quantized(){return this._quantized}get method(){return this._quantizationMethod}get minmaxMatrix(){return this._minMaxMatrix}_createPrunedMinMax(t){let r=t.length,a=this.minmaxMatrix.shape[0]-r,e=(0,k.default)(new Float32Array(2*a),[a,2]),n=0,s=a,i=0,o=this.minmaxMatrix.shape[0];for(let r=0;r<t.length;r++)(s=(o=t[r])-i+n)>n&&w.assign(e.hi(s,2).lo(n,0),this.minmaxMatrix.hi(o,2).lo(i,0)),n=s,i=o+1;return n<a&&w.assign(e.lo(n,0),this.minmaxMatrix.lo(i,0)),e}_createPrunedQuantized(t){let r=t.length,a=this.quantized.shape[0]-r,e=this._quantizationMethod,n,s;if("norm8x"===e){let t=(s=this._quantized.shape[1])?a*s:a;n=(0,k.default)(new Uint8Array(t),s?[a,s]:[a,1])}else n="norm565"===e?(0,k.default)(new Uint16Array(a),[a]):(0,k.default)(new Uint32Array(a),[a]);let i=0,o=a,h=0,u=n.shape[0];for(let r=0;r<t.length;r++)(o=(u=t[r])-h+i)>i&&(s?w.assign(n.hi(o,s).lo(i,0),this._quantized.hi(u,s).lo(h,0)):w.assign(n.hi(o).lo(i),this._quantized.hi(u).lo(h))),i=o,h=u+1;return i<a&&(s?w.assign(n.lo(i,0),this._quantized.lo(h,0)):w.assign(n.lo(i),this._quantized.lo(h))),n}pruneFeature(t,r,a){return new V(this._createPrunedQuantized(t),this._createPrunedMinMax(r),this._chunkSize,this._quantizationMethod,a,!0)}static getRequiredNChunks(t,r){return Math.floor(t/r)}static fromArray(t,r,a){let e=t.shape[0],n=Math.floor(e/a),s=(0,k.default)(new Float32Array(2*n),[n,2],[2,1]),i;i="norm8x"===r?(0,k.default)(new Uint8Array(t.size),t.shape):"norm565"===r?(0,k.default)(new Uint16Array(t.shape[0]),[t.shape[0]]):(0,k.default)(new Uint32Array(t.shape[0]),[t.shape[0]]);for(let o=0;o<n;o++){let h=o*a,u=o+1<n?(o+1)*a:e,l;l=t.shape.length>1?z.createFromUnnormalized(t.hi(u,t.shape[1]).lo(h,0)):z.createFromUnnormalized(t.hi(u).lo(h)),s.set(o,0,l.minD),s.set(o,1,l.maxD),i.shape.length>1?w.assign(i.hi(u,i.shape[1]).lo(h,0),j.fromNormalized(l,r).quantized):w.assign(i.hi(u).lo(h),j.fromNormalized(l,r).quantized)}return new V(i,s,a,r)}denormDequant(){let t,r=this._minMaxMatrix.shape[0],a=this._quantized,e=a.shape[0],n=this._quantizationMethod,s=this._chunkSize,i;if(this._isDynamicChunks){if(!this._variableChunkSize)throw Error("variable chunk must exists if chunkSize isDynamic");i=this._variableChunkSize}t="norm8x"===n?(0,k.default)(new Float32Array(a.size),a.shape):(0,k.default)(new Float32Array(3*e),[e,3]);let o=0,h=s;for(let s=0;s<r;s++){let[u,l]=[this._minMaxMatrix.get(s,0),this._minMaxMatrix.get(s,1)];this._isDynamicChunks&&(h=i[s]);let c=s+1<r?o+h:e,f;f=a.shape.length>1?new j(a.hi(c,a.shape[1]).lo(o,0),n):new j(a.hi(c).lo(o),n),w.assign(t.hi(c,t.shape[1]).lo(o,0),f.dequantize(u,l).denormalize()),o=c}return t}static async fetchArrayBuffer(t){return await (await fetch(t,{mode:"cors"})).arrayBuffer()}},S=p(m(),1),I=p(x(),1),D=p(m(),1),F=p(x(),1),B=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9];function N(t){return t<1e5?t<100?t<10?0:1:t<1e4?t<1e3?2:3:4:t<1e7?t<1e6?5:6:t<1e9?t<1e8?7:8:9}function U(t,r){if(t===r)return 0;if(~~t===t&&~~r===r){if(0===t||0===r)return t<r?-1:1;if(t<0||r<0){if(r>=0)return -1;if(t>=0)return 1;t=-t,r=-r}let a=N(t),e=N(r),n=0;return a<e?(t*=B[e-a-1],r/=10,n=-1):a>e&&(r*=B[a-e-1],t/=10,n=1),t===r?n:t<r?-1:1}let a=String(t),e=String(r);return a===e?0:a<e?-1:1}function C(t,r,a,e){let n=r+1;if(n===a)return 1;if(0>e(t[n++],t[r])){for(;n<a&&0>e(t[n],t[n-1]);)n++;var s=t,i=r,o=n;for(o--;i<o;){let t=s[i];s[i++]=s[o],s[o--]=t}}else for(;n<a&&e(t[n],t[n-1])>=0;)n++;return n-r}function L(t,r,a,e,n){for(e===r&&e++;e<a;e++){let a=t[e],s=r,i=e;for(;s<i;){let r=s+i>>>1;0>n(a,t[r])?i=r:s=r+1}let o=e-s;switch(o){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;o>0;)t[s+o]=t[s+o-1],o--}t[s]=a}}function E(t,r,a,e,n,s){let i=0,o=0,h=1;if(s(t,r[a+n])>0){for(o=e-n;h<o&&s(t,r[a+n+h])>0;)i=h,(h=(h<<1)+1)<=0&&(h=o);h>o&&(h=o),i+=n,h+=n}else{for(o=n+1;h<o&&0>=s(t,r[a+n-h]);)i=h,(h=(h<<1)+1)<=0&&(h=o);h>o&&(h=o);let e=i;i=n-h,h=n-e}for(i++;i<h;){let e=i+(h-i>>>1);s(t,r[a+e])>0?i=e+1:h=e}return h}function O(t,r,a,e,n,s){let i=0,o=0,h=1;if(0>s(t,r[a+n])){for(o=n+1;h<o&&0>s(t,r[a+n-h]);)i=h,(h=(h<<1)+1)<=0&&(h=o);h>o&&(h=o);let e=i;i=n-h,h=n-e}else{for(o=e-n;h<o&&s(t,r[a+n+h])>=0;)i=h,(h=(h<<1)+1)<=0&&(h=o);h>o&&(h=o),i+=n,h+=n}for(i++;i<h;){let e=i+(h-i>>>1);0>s(t,r[a+e])?h=e:i=e+1}return h}var $=class{constructor(t,r){d(this,"array",null),d(this,"compare",null),d(this,"minGallop",7),d(this,"length",0),d(this,"tmpStorageLength",256),d(this,"stackLength",0),d(this,"runStart",null),d(this,"runLength",null),d(this,"stackSize",0),this.array=t,this.compare=r,this.length=t.length,this.length<512&&(this.tmpStorageLength=this.length>>>1),this.tmp=Array(this.tmpStorageLength),this.stackLength=this.length<120?5:this.length<1542?10:this.length<119151?19:40,this.runStart=Array(this.stackLength),this.runLength=Array(this.stackLength)}pushRun(t,r){this.runStart[this.stackSize]=t,this.runLength[this.stackSize]=r,this.stackSize+=1}mergeRuns(){for(;this.stackSize>1;){let t=this.stackSize-2;if(t>=1&&this.runLength[t-1]<=this.runLength[t]+this.runLength[t+1]||t>=2&&this.runLength[t-2]<=this.runLength[t]+this.runLength[t-1])this.runLength[t-1]<this.runLength[t+1]&&t--;else if(this.runLength[t]>this.runLength[t+1])break;this.mergeAt(t)}}forceMergeRuns(){for(;this.stackSize>1;){let t=this.stackSize-2;t>0&&this.runLength[t-1]<this.runLength[t+1]&&t--,this.mergeAt(t)}}mergeAt(t){let r=this.compare,a=this.array,e=this.runStart[t],n=this.runLength[t],s=this.runStart[t+1],i=this.runLength[t+1];this.runLength[t]=n+i,t===this.stackSize-3&&(this.runStart[t+1]=this.runStart[t+2],this.runLength[t+1]=this.runLength[t+2]),this.stackSize--;let o=O(a[s],a,e,n,0,r);e+=o,0!=(n-=o)&&0!==(i=E(a[e+n-1],a,s,i,i-1,r))&&(n<=i?this.mergeLow(e,n,s,i):this.mergeHigh(e,n,s,i))}mergeLow(t,r,a,e){let n=this.compare,s=this.array,i=this.tmp,o=0;for(o=0;o<r;o++)i[o]=s[t+o];let h=0,u=a,l=t;if(s[l++]=s[u++],0==--e){for(o=0;o<r;o++)s[l+o]=i[h+o];return}if(1===r){for(o=0;o<e;o++)s[l+o]=s[u+o];s[l+e]=i[h];return}let c=this.minGallop;for(;;){let t=0,a=0,f=!1;do if(0>n(s[u],i[h])){if(s[l++]=s[u++],a++,t=0,0==--e){f=!0;break}}else if(s[l++]=i[h++],t++,a=0,1==--r){f=!0;break}while((t|a)<c);if(f)break;do{if(0!==(t=O(s[u],i,h,r,0,n))){for(o=0;o<t;o++)s[l+o]=i[h+o];if(l+=t,h+=t,(r-=t)<=1){f=!0;break}}if(s[l++]=s[u++],0==--e){f=!0;break}if(0!==(a=E(i[h],s,u,e,0,n))){for(o=0;o<a;o++)s[l+o]=s[u+o];if(l+=a,u+=a,0==(e-=a)){f=!0;break}}if(s[l++]=i[h++],1==--r){f=!0;break}c--}while(t>=7||a>=7);if(f)break;c<0&&(c=0),c+=2}if(this.minGallop=c,c<1&&(this.minGallop=1),1===r){for(o=0;o<e;o++)s[l+o]=s[u+o];s[l+e]=i[h]}else{if(0===r)throw Error("mergeLow preconditions were not respected");for(o=0;o<r;o++)s[l+o]=i[h+o]}}mergeHigh(t,r,a,e){let n=this.compare,s=this.array,i=this.tmp,o=0;for(o=0;o<e;o++)i[o]=s[a+o];let h=t+r-1,u=e-1,l=a+e-1,c=0,f=0;if(s[l--]=s[h--],0==--r){for(c=l-(e-1),o=0;o<e;o++)s[c+o]=i[o];return}if(1===e){for(l-=r,h-=r,f=l+1,c=h+1,o=r-1;o>=0;o--)s[f+o]=s[c+o];s[l]=i[u];return}let p=this.minGallop;for(;;){let a=0,d=0,y=!1;do if(0>n(i[u],s[h])){if(s[l--]=s[h--],a++,d=0,0==--r){y=!0;break}}else if(s[l--]=i[u--],d++,a=0,1==--e){y=!0;break}while((a|d)<p);if(y)break;do{if(0!=(a=r-O(i[u],s,t,r,r-1,n))){for(l-=a,h-=a,r-=a,f=l+1,c=h+1,o=a-1;o>=0;o--)s[f+o]=s[c+o];if(0===r){y=!0;break}}if(s[l--]=i[u--],1==--e){y=!0;break}if(0!=(d=e-E(s[h],i,0,e,e-1,n))){for(l-=d,u-=d,e-=d,f=l+1,c=u+1,o=0;o<d;o++)s[f+o]=i[c+o];if(e<=1){y=!0;break}}if(s[l--]=s[h--],0==--r){y=!0;break}p--}while(a>=7||d>=7);if(y)break;p<0&&(p=0),p+=2}if(this.minGallop=p,p<1&&(this.minGallop=1),1===e){for(l-=r,h-=r,f=l+1,c=h+1,o=r-1;o>=0;o--)s[f+o]=s[c+o];s[l]=i[u]}else{if(0===e)throw Error("mergeHigh preconditions were not respected");for(c=l-(e-1),o=0;o<e;o++)s[c+o]=i[o]}}};function P(t){let r=(0,D.default)(new Int32Array(t.shape[0]),[t.shape[0]]),a=(0,D.default)(new Int32Array(t.shape[0]),[t.shape[0]]);return F.bands(r,t,1023),F.lshifts(a,r,16),F.bxoreq(r,a),F.bandseq(r,0xff0000ff),F.lshifts(a,r,8),F.bxoreq(r,a),F.bandseq(r,0x300f00f),F.lshifts(a,r,4),F.bxoreq(r,a),F.bandseq(r,0x30c30c3),F.lshifts(a,r,2),F.bxoreq(r,a),F.bandseq(r,0x9249249),r}function T(t,r){if(t.shape[0]!==r.shape[0])throw Error("wrong length");let a=(0,D.default)(new Float32Array(t.size),t.shape,t.stride,t.offset);for(let e=0;e<r.shape[0];e++){let n=r.get(e);if(t.shape.length>1)for(let r=0;r<t.shape[1];r++)a.set(e,r,t.get(n,r));else a.set(e,t.get(n))}return a}var R=class{constructor(t,r,a,e,n,s,i,o,h,u){this.propertyDescs=t,this.format=r,this.nsplats=a,this.xyz=e,this.colors=n,this.harmonics=s,this.opacity=i,this.scaling=o,this.rotation=h,this.maxSHDegree=u}getPlyBinary(){let t=R._generateHeaderString(this.propertyDescs,this.format,this.nsplats),r=new TextEncoder().encode(t),a=Object.keys(this.propertyDescs).length,e=(0,S.default)(new Float32Array(this.nsplats*a),[this.nsplats,a]);if(I.assign(e.pick(null,this.propertyDescs.x.index),this.xyz.pick(null,0)),I.assign(e.pick(null,this.propertyDescs.y.index),this.xyz.pick(null,1)),I.assign(e.pick(null,this.propertyDescs.z.index),this.xyz.pick(null,2)),I.assign(e.pick(null,this.propertyDescs.f_dc_0.index),this.colors.pick(null,0)),I.assign(e.pick(null,this.propertyDescs.f_dc_1.index),this.colors.pick(null,1)),I.assign(e.pick(null,this.propertyDescs.f_dc_2.index),this.colors.pick(null,2)),I.assign(e.pick(null,this.propertyDescs.opacity.index),this.opacity.pick(null,0)),I.assign(e.pick(null,this.propertyDescs.scale_0.index),this.scaling.pick(null,0)),I.assign(e.pick(null,this.propertyDescs.scale_1.index),this.scaling.pick(null,1)),I.assign(e.pick(null,this.propertyDescs.scale_2.index),this.scaling.pick(null,2)),I.assign(e.pick(null,this.propertyDescs.rot_0.index),this.rotation.pick(null,0)),I.assign(e.pick(null,this.propertyDescs.rot_1.index),this.rotation.pick(null,1)),I.assign(e.pick(null,this.propertyDescs.rot_2.index),this.rotation.pick(null,2)),I.assign(e.pick(null,this.propertyDescs.rot_3.index),this.rotation.pick(null,3)),this.harmonics&&this.harmonics.length>0)for(let t=0;t<this.harmonics.length;t++){let r=3*t;I.assign(e.pick(null,this.propertyDescs[`f_rest_${r}`].index),this.harmonics[t].pick(null,0)),I.assign(e.pick(null,this.propertyDescs[`f_rest_${r+1}`].index),this.harmonics[t].pick(null,1)),I.assign(e.pick(null,this.propertyDescs[`f_rest_${r+2}`].index),this.harmonics[t].pick(null,2))}let n=new Uint8Array(e.data.buffer),s=new Uint8Array(n.length+r.length);return s.set(r),s.set(n,r.length),s.buffer}save(t,r){let a=new File([new Blob([this.getPlyBinary()],{type:"application/octet-stream"})],t),e=new FormData;e.append("file",a),e.append("filename",t),e.append("basedir",r),fetch("http://127.0.0.1:8000/push_file",{method:"POST",body:e})}static async loadFile(t){return await (await fetch(t)).arrayBuffer()}mortonPositionSplatsSort(){var t,r;let a,e,n,s,i,o,h,u=(t=this.xyz,a=1e3/Math.min(1e3,F.sup(t)-F.inf(t)),e=(0,D.default)(new Float32Array(t.data),t.shape),F.mulseq(e,a),!function(t,r,a,e){if(!Array.isArray(t))throw TypeError("Can only sort arrays");r?"function"!=typeof r&&(e=a,a=r,r=U):r=U,a||(a=0),e||(e=t.length);let n=e-a;if(n<2)return;let s=0;if(n<32){s=C(t,a,e,r),L(t,a,e,a+s,r);return}let i=new $(t,r),o=function(t){let r=0;for(;t>=32;)r|=1&t,t>>=1;return t+r}(n);do{if((s=C(t,a,e,r))<o){let e=n;e>o&&(e=o),L(t,a,a+e,a+s,r),s=e}i.pushRun(a,s),i.mergeRuns(),n-=s,a+=s}while(0!==n);i.forceMergeRuns()}(o=Array.from((n=P((r=(0,D.default)(new Int32Array(e.data),t.shape)).pick(null,0)),s=P(r.pick(null,1)),F.lshiftseq(s,1),i=P(r.pick(null,2)),F.lshiftseq(i,2),F.boreq(n,s),F.boreq(n,i),n).data).map((t,r)=>[t,r]),(t,r)=>t[0]-r[0]),h=o.map(([t,r])=>r),(0,D.default)(Uint32Array.from(h))),l=T(this.xyz,u),c=T(this.colors,u),f=T(this.opacity,u),p=T(this.scaling,u),d=T(this.rotation,u),y=[];for(let t=0;t<this.harmonics.length;t++)y.push(T(this.harmonics[t],u));return new R(this.propertyDescs,this.format,this.nsplats,l,c,y,f,p,d,this.maxSHDegree)}static _generateHeaderString(t,r,a){let e=`ply
format ${r.format} ${r.version}
element vertex ${a}`,n=Array(Object.keys(t).length);for(let r in t){let a=t[r];n[a.index]={name:r,dtype:a.dtype}}for(let t=0;t<n.length;t++)e=`${e}
property ${n[t].dtype} ${n[t].name}`;return`${e}
end_header
`}static fromArrayBuffer(t,r=3){let{splatCount:a,vertexData:e,propertiesDesc:n,format:s}=R.decodeHeader(t),i=e.buffer.slice(e.byteOffset),o=Object.keys(n).length,h=(0,S.default)(new Float32Array(i),[a,o]),u=0,l={},c={double:8,int:4,uint:4,float:4,short:2,ushort:2,uchar:1,char:1};for(let t in n)if(n.hasOwnProperty(t)){let r=n[t].dtype;l[t]=u,u+=c[r]}let f=(0,S.default)(new Float32Array(3*a),[a,3]);I.assign(f.pick(null,0),h.pick(null,l.x/4)),I.assign(f.pick(null,1),h.pick(null,l.y/4)),I.assign(f.pick(null,2),h.pick(null,l.z/4));let p=(0,S.default)(new Float32Array(3*a),[a,3]);I.assign(p.pick(null,0),h.pick(null,l.scale_0/4)),I.assign(p.pick(null,1),h.pick(null,l.scale_1/4)),I.assign(p.pick(null,2),h.pick(null,l.scale_2/4));let d=(0,S.default)(new Float32Array(3*a),[a,3]);I.assign(d.pick(null,0),h.pick(null,l.f_dc_0/4)),I.assign(d.pick(null,1),h.pick(null,l.f_dc_1/4)),I.assign(d.pick(null,2),h.pick(null,l.f_dc_2/4));let y=(0,S.default)(new Float32Array(4*a),[a,4]);I.assign(y.pick(null,0),h.pick(null,l.rot_1/4)),I.assign(y.pick(null,1),h.pick(null,l.rot_2/4)),I.assign(y.pick(null,2),h.pick(null,l.rot_3/4)),I.assign(y.pick(null,3),h.pick(null,l.rot_0/4));for(let t=0;t<a;t++){let r=y.pick(t,null),a=Math.sqrt(r.get(0)**2+r.get(1)**2+r.get(2)**2+r.get(3)**2);I.divseq(r,a)}let g=(0,S.default)(new Float32Array(+a),[a,1]);I.assign(g.pick(null,0),h.pick(null,l.opacity/4)),I.negeq(g),I.expeq(g),I.addseq(g,1),I.recipeq(g),I.mulseq(g,255);let m=(Math.min(Math.max(r,0),3)+1)**2-1,b=[];for(let t=0;t<m;t++){let r=(0,S.default)(new Float32Array(3*a),[a,3]),e=3*t;I.assign(r.pick(null,0),h.pick(null,l[`f_rest_${e}`]/4)),I.assign(r.pick(null,1),h.pick(null,l[`f_rest_${e+1}`]/4)),I.assign(r.pick(null,2),h.pick(null,l[`f_rest_${e+2}`]/4)),b.push(r)}return new R(n,s,a,f,d,b,g,p,y,r)}static async fromPLYFile(t,r=3){let a=await R.loadFile(t);return R.fromArrayBuffer(a,r)}static decodeHeader(t){let r=new TextDecoder,a=0,e="";for(;;){if(a+100>=t.byteLength)throw Error("End of file reached while searching for end of header");let n=new Uint8Array(t,a,100);e+=r.decode(n);let s=(a+=100)-200,i=new Uint8Array(t,Math.max(0,s),s>0?200:100);if(r.decode(i).includes("end_header"))break}let n=e.split(`
`),s=0,i={},o={},h=0,u;for(let t=0;t<n.length;t++){let r=n[t].trim();if(r.startsWith("element vertex")){let t=r.match(/\d+/);t&&(s=parseInt(t[0]))}else if(r.startsWith("property")){let t=r.match(/(\w+)\s+(\w+)\s+(\w+)/);if(t){let r=t[2],a=t[3];i[a]=h,o[a]={dtype:r,index:h},h++}}else if(r.startsWith("format")){let t=r.match(/(\w+)\s+(\w+)\s+(\d+\.?\d*)/);t&&(u={format:t[2],version:t[3]})}else if("end_header"===r)break}return{splatCount:s,vertexData:new DataView(t,e.indexOf("end_header")+10+1),headerOffset:a,propertiesDesc:o,format:u}}},G=class{constructor(t,r,a,e,n,s,i,o){this.config=t,this.xyz=r,this.scaling=a,this.color=e,this.opacity=n,this.harmonics=i,this.quaternion=s,this.variableChunkSize=o}get isDynamicChunks(){return this.variableChunkSize&&this.variableChunkSize.length>0}get nchunks(){return this.xyz.nchunks}get nsplats(){return this.xyz.length}get chunkSize(){return this.config.chunkSize}static compressFromGaussianData(t,r){let a=V.fromArray(t.xyz,r.xyz,r.chunkSize),e=V.fromArray(t.scaling,r.scaling,r.chunkSize),n=V.fromArray(t.colors,r.color,r.chunkSize),s=V.fromArray(t.opacity,r.opacity,r.chunkSize),i=V.fromArray(t.rotation,r.quaternion,r.chunkSize),o=t.harmonics,h=[];if(r.harmonics)for(let t=0;t<o.length;t++){let a=V.fromArray(o[t],r.harmonics,r.chunkSize);h.push(a)}return new G(r,a,e,n,s,i,h)}_countIndexesInChunks(t){let r=[],a=this.nchunks,e=this.chunkSize,n=this.nsplats;if(a===V.getRequiredNChunks(n,e))for(let a=0;a<t.length;a++){let e=t[a],n=Math.floor(e/this.chunkSize);n in r?r[n].push(e):r[n]=[e]}else{let n=this.variableChunkSize,s={},i=0;for(let t=0;t<a;t++)s[t]=i,i+=n[t];for(let i=0;i<t.length;i++){let o=t[i],h=Math.min(Math.floor(o/e),a-1);for(;o>=s[h]+n[h];)h++;h in r?r[h].push(o):r[h]=[o]}}return r}pruneSplats(t){let r=this._countIndexesInChunks(t),a,e=[];return r.length>0&&(a=this.variableChunkSize?[...this.variableChunkSize]:Array(this.nchunks).fill(this.chunkSize),r.forEach((t,r)=>{a[r]-=t.length,a[r]<=0&&e.push(r)}),a=a.filter(t=>t>0)),new G(this.config,this.xyz.pruneFeature(t,e,a),this.scaling.pruneFeature(t,e,a),this.color.pruneFeature(t,e,a),this.opacity.pruneFeature(t,e,a),this.quaternion.pruneFeature(t,e,a),this.harmonics?this.harmonics.map(r=>r.pruneFeature(t,e,this.variableChunkSize)):void 0,a)}static async loadConfig(t){return await (await fetch(t,{method:"GET",mode:"cors",headers:{Accept:"application/json"}})).json()}toGaussians(){let t={},r=0;if(t.x={dtype:"float",index:0},t.y={dtype:"float",index:++r},t.z={dtype:"float",index:++r},t.f_dc_0={dtype:"float",index:++r},t.f_dc_1={dtype:"float",index:++r},t.f_dc_2={dtype:"float",index:++r},r++,this.harmonics&&this.harmonics.length>0)for(let a=0;a<this.harmonics.length;a++)t[`f_rest_${a}`]={dtype:"float",index:r},r++,t[`f_rest_${a+1}`]={dtype:"float",index:r},r++,t[`f_rest_${a+2}`]={dtype:"float",index:r},r++;t.opacity={dtype:"float",index:r},t.scale_0={dtype:"float",index:++r},t.scale_1={dtype:"float",index:++r},t.scale_2={dtype:"float",index:++r},t.rot_0={dtype:"float",index:++r},t.rot_1={dtype:"float",index:++r},t.rot_2={dtype:"float",index:++r},t.rot_3={dtype:"float",index:++r},r++;let a=this.harmonics?.map(t=>t.denormDequant());return new R(t,{format:"binary_little_endian",version:"1.0"},this.xyz.length,this.xyz.denormDequant(),this.color.denormDequant(),a||[],this.opacity.denormDequant(),this.scaling.denormDequant(),this.quaternion.denormDequant(),3)}},H=p(m(),1),W=p(x(),1),Y="u">typeof Float32Array?Float32Array:Array,Q=Math.random;Math.hypot||(Math.hypot=function(){for(var t=0,r=arguments.length;r--;)t+=arguments[r]*arguments[r];return Math.sqrt(t)});var Z={};function X(){var t=new Y(9);return Y!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[5]=0,t[6]=0,t[7]=0),t[0]=1,t[4]=1,t[8]=1,t}function J(t,r){return t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[4],t[4]=r[5],t[5]=r[6],t[6]=r[8],t[7]=r[9],t[8]=r[10],t}function K(t){var r=new Y(9);return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r[6]=t[6],r[7]=t[7],r[8]=t[8],r}function tt(t,r){return t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[3],t[4]=r[4],t[5]=r[5],t[6]=r[6],t[7]=r[7],t[8]=r[8],t}function tr(t,r,a,e,n,s,i,o,h){var u=new Y(9);return u[0]=t,u[1]=r,u[2]=a,u[3]=e,u[4]=n,u[5]=s,u[6]=i,u[7]=o,u[8]=h,u}function ta(t,r,a,e,n,s,i,o,h,u){return t[0]=r,t[1]=a,t[2]=e,t[3]=n,t[4]=s,t[5]=i,t[6]=o,t[7]=h,t[8]=u,t}function te(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function tn(t,r){if(t===r){var a=r[1],e=r[2],n=r[5];t[1]=r[3],t[2]=r[6],t[3]=a,t[5]=r[7],t[6]=e,t[7]=n}else t[0]=r[0],t[1]=r[3],t[2]=r[6],t[3]=r[1],t[4]=r[4],t[5]=r[7],t[6]=r[2],t[7]=r[5],t[8]=r[8];return t}function ts(t,r){var a=r[0],e=r[1],n=r[2],s=r[3],i=r[4],o=r[5],h=r[6],u=r[7],l=r[8],c=l*i-o*u,f=-l*s+o*h,p=u*s-i*h,d=a*c+e*f+n*p;return d?(d=1/d,t[0]=c*d,t[1]=(-l*e+n*u)*d,t[2]=(o*e-n*i)*d,t[3]=f*d,t[4]=(l*a-n*h)*d,t[5]=(-o*a+n*s)*d,t[6]=p*d,t[7]=(-u*a+e*h)*d,t[8]=(i*a-e*s)*d,t):null}function ti(t,r){var a=r[0],e=r[1],n=r[2],s=r[3],i=r[4],o=r[5],h=r[6],u=r[7],l=r[8];return t[0]=i*l-o*u,t[1]=n*u-e*l,t[2]=e*o-n*i,t[3]=o*h-s*l,t[4]=a*l-n*h,t[5]=n*s-a*o,t[6]=s*u-i*h,t[7]=e*h-a*u,t[8]=a*i-e*s,t}function to(t){var r=t[0],a=t[1],e=t[2],n=t[3],s=t[4],i=t[5],o=t[6],h=t[7],u=t[8];return r*(u*s-i*h)+a*(-u*n+i*o)+e*(h*n-s*o)}function th(t,r,a){var e=r[0],n=r[1],s=r[2],i=r[3],o=r[4],h=r[5],u=r[6],l=r[7],c=r[8],f=a[0],p=a[1],d=a[2],y=a[3],g=a[4],m=a[5],b=a[6],_=a[7],v=a[8];return t[0]=f*e+p*i+d*u,t[1]=f*n+p*o+d*l,t[2]=f*s+p*h+d*c,t[3]=y*e+g*i+m*u,t[4]=y*n+g*o+m*l,t[5]=y*s+g*h+m*c,t[6]=b*e+_*i+v*u,t[7]=b*n+_*o+v*l,t[8]=b*s+_*h+v*c,t}function tu(t,r,a){var e=r[0],n=r[1],s=r[2],i=r[3],o=r[4],h=r[5],u=r[6],l=r[7],c=r[8],f=a[0],p=a[1];return t[0]=e,t[1]=n,t[2]=s,t[3]=i,t[4]=o,t[5]=h,t[6]=f*e+p*i+u,t[7]=f*n+p*o+l,t[8]=f*s+p*h+c,t}function tl(t,r,a){var e=r[0],n=r[1],s=r[2],i=r[3],o=r[4],h=r[5],u=r[6],l=r[7],c=r[8],f=Math.sin(a),p=Math.cos(a);return t[0]=p*e+f*i,t[1]=p*n+f*o,t[2]=p*s+f*h,t[3]=p*i-f*e,t[4]=p*o-f*n,t[5]=p*h-f*s,t[6]=u,t[7]=l,t[8]=c,t}function tc(t,r,a){var e=a[0],n=a[1];return t[0]=e*r[0],t[1]=e*r[1],t[2]=e*r[2],t[3]=n*r[3],t[4]=n*r[4],t[5]=n*r[5],t[6]=r[6],t[7]=r[7],t[8]=r[8],t}function tf(t,r){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=r[0],t[7]=r[1],t[8]=1,t}function tp(t,r){var a=Math.sin(r),e=Math.cos(r);return t[0]=e,t[1]=a,t[2]=0,t[3]=-a,t[4]=e,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function td(t,r){return t[0]=r[0],t[1]=0,t[2]=0,t[3]=0,t[4]=r[1],t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function ty(t,r){return t[0]=r[0],t[1]=r[1],t[2]=0,t[3]=r[2],t[4]=r[3],t[5]=0,t[6]=r[4],t[7]=r[5],t[8]=1,t}function tg(t,r){var a=r[0],e=r[1],n=r[2],s=r[3],i=a+a,o=e+e,h=n+n,u=a*i,l=e*i,c=e*o,f=n*i,p=n*o,d=n*h,y=s*i,g=s*o,m=s*h;return t[0]=1-c-d,t[3]=l-m,t[6]=f+g,t[1]=l+m,t[4]=1-u-d,t[7]=p-y,t[2]=f-g,t[5]=p+y,t[8]=1-u-c,t}function tm(t,r){var a=r[0],e=r[1],n=r[2],s=r[3],i=r[4],o=r[5],h=r[6],u=r[7],l=r[8],c=r[9],f=r[10],p=r[11],d=r[12],y=r[13],g=r[14],m=r[15],b=a*o-e*i,_=a*h-n*i,v=a*u-s*i,M=e*h-n*o,x=e*u-s*o,k=n*u-s*h,w=l*y-c*d,A=l*g-f*d,q=l*m-p*d,z=c*g-f*y,j=c*m-p*y,V=f*m-p*g,S=b*V-_*j+v*z+M*q-x*A+k*w;return S?(S=1/S,t[0]=(o*V-h*j+u*z)*S,t[1]=(h*q-i*V-u*A)*S,t[2]=(i*j-o*q+u*w)*S,t[3]=(n*j-e*V-s*z)*S,t[4]=(a*V-n*q+s*A)*S,t[5]=(e*q-a*j-s*w)*S,t[6]=(y*k-g*x+m*M)*S,t[7]=(g*v-d*k-m*_)*S,t[8]=(d*x-y*v+m*b)*S,t):null}function tb(t,r,a){return t[0]=2/r,t[1]=0,t[2]=0,t[3]=0,t[4]=-2/a,t[5]=0,t[6]=-1,t[7]=1,t[8]=1,t}function t_(t){return"mat3("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+")"}function tv(t){return Math.hypot(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8])}function tM(t,r,a){return t[0]=r[0]+a[0],t[1]=r[1]+a[1],t[2]=r[2]+a[2],t[3]=r[3]+a[3],t[4]=r[4]+a[4],t[5]=r[5]+a[5],t[6]=r[6]+a[6],t[7]=r[7]+a[7],t[8]=r[8]+a[8],t}function tx(t,r,a){return t[0]=r[0]-a[0],t[1]=r[1]-a[1],t[2]=r[2]-a[2],t[3]=r[3]-a[3],t[4]=r[4]-a[4],t[5]=r[5]-a[5],t[6]=r[6]-a[6],t[7]=r[7]-a[7],t[8]=r[8]-a[8],t}function tk(t,r,a){return t[0]=r[0]*a,t[1]=r[1]*a,t[2]=r[2]*a,t[3]=r[3]*a,t[4]=r[4]*a,t[5]=r[5]*a,t[6]=r[6]*a,t[7]=r[7]*a,t[8]=r[8]*a,t}function tw(t,r,a,e){return t[0]=r[0]+a[0]*e,t[1]=r[1]+a[1]*e,t[2]=r[2]+a[2]*e,t[3]=r[3]+a[3]*e,t[4]=r[4]+a[4]*e,t[5]=r[5]+a[5]*e,t[6]=r[6]+a[6]*e,t[7]=r[7]+a[7]*e,t[8]=r[8]+a[8]*e,t}function tA(t,r){return t[0]===r[0]&&t[1]===r[1]&&t[2]===r[2]&&t[3]===r[3]&&t[4]===r[4]&&t[5]===r[5]&&t[6]===r[6]&&t[7]===r[7]&&t[8]===r[8]}function tq(t,r){var a=t[0],e=t[1],n=t[2],s=t[3],i=t[4],o=t[5],h=t[6],u=t[7],l=t[8],c=r[0],f=r[1],p=r[2],d=r[3],y=r[4],g=r[5],m=r[6],b=r[7],_=r[8];return Math.abs(a-c)<=1e-6*Math.max(1,Math.abs(a),Math.abs(c))&&Math.abs(e-f)<=1e-6*Math.max(1,Math.abs(e),Math.abs(f))&&Math.abs(n-p)<=1e-6*Math.max(1,Math.abs(n),Math.abs(p))&&Math.abs(s-d)<=1e-6*Math.max(1,Math.abs(s),Math.abs(d))&&Math.abs(i-y)<=1e-6*Math.max(1,Math.abs(i),Math.abs(y))&&Math.abs(o-g)<=1e-6*Math.max(1,Math.abs(o),Math.abs(g))&&Math.abs(h-m)<=1e-6*Math.max(1,Math.abs(h),Math.abs(m))&&Math.abs(u-b)<=1e-6*Math.max(1,Math.abs(u),Math.abs(b))&&Math.abs(l-_)<=1e-6*Math.max(1,Math.abs(l),Math.abs(_))}c(Z,{add:()=>tM,adjoint:()=>ti,clone:()=>K,copy:()=>tt,create:()=>X,determinant:()=>to,equals:()=>tq,exactEquals:()=>tA,frob:()=>tv,fromMat2d:()=>ty,fromMat4:()=>J,fromQuat:()=>tg,fromRotation:()=>tp,fromScaling:()=>td,fromTranslation:()=>tf,fromValues:()=>tr,identity:()=>te,invert:()=>ts,mul:()=>tz,multiply:()=>th,multiplyScalar:()=>tk,multiplyScalarAndAdd:()=>tw,normalFromMat4:()=>tm,projection:()=>tb,rotate:()=>tl,scale:()=>tc,set:()=>ta,str:()=>t_,sub:()=>tj,subtract:()=>tx,translate:()=>tu,transpose:()=>tn});var tz=th,tj=tx,tV={};function tS(){var t=new Y(16);return Y!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=0,t[12]=0,t[13]=0,t[14]=0),t[0]=1,t[5]=1,t[10]=1,t[15]=1,t}function tI(t){var r=new Y(16);return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r[6]=t[6],r[7]=t[7],r[8]=t[8],r[9]=t[9],r[10]=t[10],r[11]=t[11],r[12]=t[12],r[13]=t[13],r[14]=t[14],r[15]=t[15],r}function tD(t,r){return t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[3],t[4]=r[4],t[5]=r[5],t[6]=r[6],t[7]=r[7],t[8]=r[8],t[9]=r[9],t[10]=r[10],t[11]=r[11],t[12]=r[12],t[13]=r[13],t[14]=r[14],t[15]=r[15],t}function tF(t,r,a,e,n,s,i,o,h,u,l,c,f,p,d,y){var g=new Y(16);return g[0]=t,g[1]=r,g[2]=a,g[3]=e,g[4]=n,g[5]=s,g[6]=i,g[7]=o,g[8]=h,g[9]=u,g[10]=l,g[11]=c,g[12]=f,g[13]=p,g[14]=d,g[15]=y,g}function tB(t,r,a,e,n,s,i,o,h,u,l,c,f,p,d,y,g){return t[0]=r,t[1]=a,t[2]=e,t[3]=n,t[4]=s,t[5]=i,t[6]=o,t[7]=h,t[8]=u,t[9]=l,t[10]=c,t[11]=f,t[12]=p,t[13]=d,t[14]=y,t[15]=g,t}function tN(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function tU(t,r){if(t===r){var a=r[1],e=r[2],n=r[3],s=r[6],i=r[7],o=r[11];t[1]=r[4],t[2]=r[8],t[3]=r[12],t[4]=a,t[6]=r[9],t[7]=r[13],t[8]=e,t[9]=s,t[11]=r[14],t[12]=n,t[13]=i,t[14]=o}else t[0]=r[0],t[1]=r[4],t[2]=r[8],t[3]=r[12],t[4]=r[1],t[5]=r[5],t[6]=r[9],t[7]=r[13],t[8]=r[2],t[9]=r[6],t[10]=r[10],t[11]=r[14],t[12]=r[3],t[13]=r[7],t[14]=r[11],t[15]=r[15];return t}function tC(t,r){var a=r[0],e=r[1],n=r[2],s=r[3],i=r[4],o=r[5],h=r[6],u=r[7],l=r[8],c=r[9],f=r[10],p=r[11],d=r[12],y=r[13],g=r[14],m=r[15],b=a*o-e*i,_=a*h-n*i,v=a*u-s*i,M=e*h-n*o,x=e*u-s*o,k=n*u-s*h,w=l*y-c*d,A=l*g-f*d,q=l*m-p*d,z=c*g-f*y,j=c*m-p*y,V=f*m-p*g,S=b*V-_*j+v*z+M*q-x*A+k*w;return S?(S=1/S,t[0]=(o*V-h*j+u*z)*S,t[1]=(n*j-e*V-s*z)*S,t[2]=(y*k-g*x+m*M)*S,t[3]=(f*x-c*k-p*M)*S,t[4]=(h*q-i*V-u*A)*S,t[5]=(a*V-n*q+s*A)*S,t[6]=(g*v-d*k-m*_)*S,t[7]=(l*k-f*v+p*_)*S,t[8]=(i*j-o*q+u*w)*S,t[9]=(e*q-a*j-s*w)*S,t[10]=(d*x-y*v+m*b)*S,t[11]=(c*v-l*x-p*b)*S,t[12]=(o*A-i*z-h*w)*S,t[13]=(a*z-e*A+n*w)*S,t[14]=(y*_-d*M-g*b)*S,t[15]=(l*M-c*_+f*b)*S,t):null}function tL(t,r){var a=r[0],e=r[1],n=r[2],s=r[3],i=r[4],o=r[5],h=r[6],u=r[7],l=r[8],c=r[9],f=r[10],p=r[11],d=r[12],y=r[13],g=r[14],m=r[15];return t[0]=o*(f*m-p*g)-c*(h*m-u*g)+y*(h*p-u*f),t[1]=-(e*(f*m-p*g)-c*(n*m-s*g)+y*(n*p-s*f)),t[2]=e*(h*m-u*g)-o*(n*m-s*g)+y*(n*u-s*h),t[3]=-(e*(h*p-u*f)-o*(n*p-s*f)+c*(n*u-s*h)),t[4]=-(i*(f*m-p*g)-l*(h*m-u*g)+d*(h*p-u*f)),t[5]=a*(f*m-p*g)-l*(n*m-s*g)+d*(n*p-s*f),t[6]=-(a*(h*m-u*g)-i*(n*m-s*g)+d*(n*u-s*h)),t[7]=a*(h*p-u*f)-i*(n*p-s*f)+l*(n*u-s*h),t[8]=i*(c*m-p*y)-l*(o*m-u*y)+d*(o*p-u*c),t[9]=-(a*(c*m-p*y)-l*(e*m-s*y)+d*(e*p-s*c)),t[10]=a*(o*m-u*y)-i*(e*m-s*y)+d*(e*u-s*o),t[11]=-(a*(o*p-u*c)-i*(e*p-s*c)+l*(e*u-s*o)),t[12]=-(i*(c*g-f*y)-l*(o*g-h*y)+d*(o*f-h*c)),t[13]=a*(c*g-f*y)-l*(e*g-n*y)+d*(e*f-n*c),t[14]=-(a*(o*g-h*y)-i*(e*g-n*y)+d*(e*h-n*o)),t[15]=a*(o*f-h*c)-i*(e*f-n*c)+l*(e*h-n*o),t}function tE(t){var r=t[0],a=t[1],e=t[2],n=t[3],s=t[4],i=t[5],o=t[6],h=t[7],u=t[8],l=t[9],c=t[10],f=t[11],p=t[12],d=t[13],y=t[14],g=t[15];return(r*i-a*s)*(c*g-f*y)-(r*o-e*s)*(l*g-f*d)+(r*h-n*s)*(l*y-c*d)+(a*o-e*i)*(u*g-f*p)-(a*h-n*i)*(u*y-c*p)+(e*h-n*o)*(u*d-l*p)}function tO(t,r,a){var e=r[0],n=r[1],s=r[2],i=r[3],o=r[4],h=r[5],u=r[6],l=r[7],c=r[8],f=r[9],p=r[10],d=r[11],y=r[12],g=r[13],m=r[14],b=r[15],_=a[0],v=a[1],M=a[2],x=a[3];return t[0]=_*e+v*o+M*c+x*y,t[1]=_*n+v*h+M*f+x*g,t[2]=_*s+v*u+M*p+x*m,t[3]=_*i+v*l+M*d+x*b,_=a[4],v=a[5],M=a[6],x=a[7],t[4]=_*e+v*o+M*c+x*y,t[5]=_*n+v*h+M*f+x*g,t[6]=_*s+v*u+M*p+x*m,t[7]=_*i+v*l+M*d+x*b,_=a[8],v=a[9],M=a[10],x=a[11],t[8]=_*e+v*o+M*c+x*y,t[9]=_*n+v*h+M*f+x*g,t[10]=_*s+v*u+M*p+x*m,t[11]=_*i+v*l+M*d+x*b,_=a[12],v=a[13],M=a[14],x=a[15],t[12]=_*e+v*o+M*c+x*y,t[13]=_*n+v*h+M*f+x*g,t[14]=_*s+v*u+M*p+x*m,t[15]=_*i+v*l+M*d+x*b,t}function t$(t,r,a){var e,n,s,i,o,h,u,l,c,f,p,d,y=a[0],g=a[1],m=a[2];return r===t?(t[12]=r[0]*y+r[4]*g+r[8]*m+r[12],t[13]=r[1]*y+r[5]*g+r[9]*m+r[13],t[14]=r[2]*y+r[6]*g+r[10]*m+r[14],t[15]=r[3]*y+r[7]*g+r[11]*m+r[15]):(e=r[0],n=r[1],s=r[2],i=r[3],o=r[4],h=r[5],u=r[6],l=r[7],c=r[8],f=r[9],p=r[10],d=r[11],t[0]=e,t[1]=n,t[2]=s,t[3]=i,t[4]=o,t[5]=h,t[6]=u,t[7]=l,t[8]=c,t[9]=f,t[10]=p,t[11]=d,t[12]=e*y+o*g+c*m+r[12],t[13]=n*y+h*g+f*m+r[13],t[14]=s*y+u*g+p*m+r[14],t[15]=i*y+l*g+d*m+r[15]),t}function tP(t,r,a){var e=a[0],n=a[1],s=a[2];return t[0]=r[0]*e,t[1]=r[1]*e,t[2]=r[2]*e,t[3]=r[3]*e,t[4]=r[4]*n,t[5]=r[5]*n,t[6]=r[6]*n,t[7]=r[7]*n,t[8]=r[8]*s,t[9]=r[9]*s,t[10]=r[10]*s,t[11]=r[11]*s,t[12]=r[12],t[13]=r[13],t[14]=r[14],t[15]=r[15],t}function tT(t,r,a,e){var n,s,i,o,h,u,l,c,f,p,d,y,g,m,b,_,v,M,x,k,w,A,q,z,j=e[0],V=e[1],S=e[2],I=Math.hypot(j,V,S);return I<1e-6?null:(j*=I=1/I,V*=I,S*=I,n=Math.sin(a),i=1-(s=Math.cos(a)),o=r[0],h=r[1],u=r[2],l=r[3],c=r[4],f=r[5],p=r[6],d=r[7],y=r[8],g=r[9],m=r[10],b=r[11],_=j*j*i+s,v=V*j*i+S*n,M=S*j*i-V*n,x=j*V*i-S*n,k=V*V*i+s,w=S*V*i+j*n,A=j*S*i+V*n,q=V*S*i-j*n,z=S*S*i+s,t[0]=o*_+c*v+y*M,t[1]=h*_+f*v+g*M,t[2]=u*_+p*v+m*M,t[3]=l*_+d*v+b*M,t[4]=o*x+c*k+y*w,t[5]=h*x+f*k+g*w,t[6]=u*x+p*k+m*w,t[7]=l*x+d*k+b*w,t[8]=o*A+c*q+y*z,t[9]=h*A+f*q+g*z,t[10]=u*A+p*q+m*z,t[11]=l*A+d*q+b*z,r!==t&&(t[12]=r[12],t[13]=r[13],t[14]=r[14],t[15]=r[15]),t)}function tR(t,r,a){var e=Math.sin(a),n=Math.cos(a),s=r[4],i=r[5],o=r[6],h=r[7],u=r[8],l=r[9],c=r[10],f=r[11];return r!==t&&(t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[3],t[12]=r[12],t[13]=r[13],t[14]=r[14],t[15]=r[15]),t[4]=s*n+u*e,t[5]=i*n+l*e,t[6]=o*n+c*e,t[7]=h*n+f*e,t[8]=u*n-s*e,t[9]=l*n-i*e,t[10]=c*n-o*e,t[11]=f*n-h*e,t}function tG(t,r,a){var e=Math.sin(a),n=Math.cos(a),s=r[0],i=r[1],o=r[2],h=r[3],u=r[8],l=r[9],c=r[10],f=r[11];return r!==t&&(t[4]=r[4],t[5]=r[5],t[6]=r[6],t[7]=r[7],t[12]=r[12],t[13]=r[13],t[14]=r[14],t[15]=r[15]),t[0]=s*n-u*e,t[1]=i*n-l*e,t[2]=o*n-c*e,t[3]=h*n-f*e,t[8]=s*e+u*n,t[9]=i*e+l*n,t[10]=o*e+c*n,t[11]=h*e+f*n,t}function tH(t,r,a){var e=Math.sin(a),n=Math.cos(a),s=r[0],i=r[1],o=r[2],h=r[3],u=r[4],l=r[5],c=r[6],f=r[7];return r!==t&&(t[8]=r[8],t[9]=r[9],t[10]=r[10],t[11]=r[11],t[12]=r[12],t[13]=r[13],t[14]=r[14],t[15]=r[15]),t[0]=s*n+u*e,t[1]=i*n+l*e,t[2]=o*n+c*e,t[3]=h*n+f*e,t[4]=u*n-s*e,t[5]=l*n-i*e,t[6]=c*n-o*e,t[7]=f*n-h*e,t}function tW(t,r){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=r[0],t[13]=r[1],t[14]=r[2],t[15]=1,t}function tY(t,r){return t[0]=r[0],t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=r[1],t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=r[2],t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function tQ(t,r,a){var e,n,s,i=a[0],o=a[1],h=a[2],u=Math.hypot(i,o,h);return u<1e-6?null:(i*=u=1/u,o*=u,h*=u,e=Math.sin(r),s=1-(n=Math.cos(r)),t[0]=i*i*s+n,t[1]=o*i*s+h*e,t[2]=h*i*s-o*e,t[3]=0,t[4]=i*o*s-h*e,t[5]=o*o*s+n,t[6]=h*o*s+i*e,t[7]=0,t[8]=i*h*s+o*e,t[9]=o*h*s-i*e,t[10]=h*h*s+n,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t)}function tZ(t,r){var a=Math.sin(r),e=Math.cos(r);return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=e,t[6]=a,t[7]=0,t[8]=0,t[9]=-a,t[10]=e,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function tX(t,r){var a=Math.sin(r),e=Math.cos(r);return t[0]=e,t[1]=0,t[2]=-a,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=a,t[9]=0,t[10]=e,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function tJ(t,r){var a=Math.sin(r),e=Math.cos(r);return t[0]=e,t[1]=a,t[2]=0,t[3]=0,t[4]=-a,t[5]=e,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function tK(t,r,a){var e=r[0],n=r[1],s=r[2],i=r[3],o=e+e,h=n+n,u=s+s,l=e*o,c=e*h,f=e*u,p=n*h,d=n*u,y=s*u,g=i*o,m=i*h,b=i*u;return t[0]=1-(p+y),t[1]=c+b,t[2]=f-m,t[3]=0,t[4]=c-b,t[5]=1-(l+y),t[6]=d+g,t[7]=0,t[8]=f+m,t[9]=d-g,t[10]=1-(l+p),t[11]=0,t[12]=a[0],t[13]=a[1],t[14]=a[2],t[15]=1,t}function t1(t,r){var a=new Y(3),e=-r[0],n=-r[1],s=-r[2],i=r[3],o=r[4],h=r[5],u=r[6],l=r[7],c=e*e+n*n+s*s+i*i;return c>0?(a[0]=(o*i+l*e+h*s-u*n)*2/c,a[1]=(h*i+l*n+u*e-o*s)*2/c,a[2]=(u*i+l*s+o*n-h*e)*2/c):(a[0]=(o*i+l*e+h*s-u*n)*2,a[1]=(h*i+l*n+u*e-o*s)*2,a[2]=(u*i+l*s+o*n-h*e)*2),tK(t,r,a),t}function t0(t,r){return t[0]=r[12],t[1]=r[13],t[2]=r[14],t}function t2(t,r){var a=r[0],e=r[1],n=r[2],s=r[4],i=r[5],o=r[6],h=r[8],u=r[9],l=r[10];return t[0]=Math.hypot(a,e,n),t[1]=Math.hypot(s,i,o),t[2]=Math.hypot(h,u,l),t}function t3(t,r){var a=new Y(3);t2(a,r);var e=1/a[0],n=1/a[1],s=1/a[2],i=r[0]*e,o=r[1]*n,h=r[2]*s,u=r[4]*e,l=r[5]*n,c=r[6]*s,f=r[8]*e,p=r[9]*n,d=r[10]*s,y=i+l+d,g=0;return y>0?(g=2*Math.sqrt(y+1),t[3]=.25*g,t[0]=(c-p)/g,t[1]=(f-h)/g,t[2]=(o-u)/g):i>l&&i>d?(g=2*Math.sqrt(1+i-l-d),t[3]=(c-p)/g,t[0]=.25*g,t[1]=(o+u)/g,t[2]=(f+h)/g):l>d?(g=2*Math.sqrt(1+l-i-d),t[3]=(f-h)/g,t[0]=(o+u)/g,t[1]=.25*g,t[2]=(c+p)/g):(g=2*Math.sqrt(1+d-i-l),t[3]=(o-u)/g,t[0]=(f+h)/g,t[1]=(c+p)/g,t[2]=.25*g),t}function t4(t,r,a,e){var n=r[0],s=r[1],i=r[2],o=r[3],h=n+n,u=s+s,l=i+i,c=n*h,f=n*u,p=n*l,d=s*u,y=s*l,g=i*l,m=o*h,b=o*u,_=o*l,v=e[0],M=e[1],x=e[2];return t[0]=(1-(d+g))*v,t[1]=(f+_)*v,t[2]=(p-b)*v,t[3]=0,t[4]=(f-_)*M,t[5]=(1-(c+g))*M,t[6]=(y+m)*M,t[7]=0,t[8]=(p+b)*x,t[9]=(y-m)*x,t[10]=(1-(c+d))*x,t[11]=0,t[12]=a[0],t[13]=a[1],t[14]=a[2],t[15]=1,t}function t5(t,r,a,e,n){var s=r[0],i=r[1],o=r[2],h=r[3],u=s+s,l=i+i,c=o+o,f=s*u,p=s*l,d=s*c,y=i*l,g=i*c,m=o*c,b=h*u,_=h*l,v=h*c,M=e[0],x=e[1],k=e[2],w=n[0],A=n[1],q=n[2],z=(1-(y+m))*M,j=(p+v)*M,V=(d-_)*M,S=(p-v)*x,I=(1-(f+m))*x,D=(g+b)*x,F=(d+_)*k,B=(g-b)*k,N=(1-(f+y))*k;return t[0]=z,t[1]=j,t[2]=V,t[3]=0,t[4]=S,t[5]=I,t[6]=D,t[7]=0,t[8]=F,t[9]=B,t[10]=N,t[11]=0,t[12]=a[0]+w-(z*w+S*A+F*q),t[13]=a[1]+A-(j*w+I*A+B*q),t[14]=a[2]+q-(V*w+D*A+N*q),t[15]=1,t}function t6(t,r){var a=r[0],e=r[1],n=r[2],s=r[3],i=a+a,o=e+e,h=n+n,u=a*i,l=e*i,c=e*o,f=n*i,p=n*o,d=n*h,y=s*i,g=s*o,m=s*h;return t[0]=1-c-d,t[1]=l+m,t[2]=f-g,t[3]=0,t[4]=l-m,t[5]=1-u-d,t[6]=p+y,t[7]=0,t[8]=f+g,t[9]=p-y,t[10]=1-u-c,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function t8(t,r,a,e,n,s,i){var o=1/(a-r),h=1/(n-e),u=1/(s-i);return t[0]=2*s*o,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=2*s*h,t[6]=0,t[7]=0,t[8]=(a+r)*o,t[9]=(n+e)*h,t[10]=(i+s)*u,t[11]=-1,t[12]=0,t[13]=0,t[14]=i*s*2*u,t[15]=0,t}function t7(t,r,a,e,n){var s,i=1/Math.tan(r/2);return t[0]=i/a,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=i,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=-1,t[12]=0,t[13]=0,t[15]=0,null!=n&&n!==1/0?(s=1/(e-n),t[10]=(n+e)*s,t[14]=2*n*e*s):(t[10]=-1,t[14]=-2*e),t}c(tV,{add:()=>ru,adjoint:()=>tL,clone:()=>tI,copy:()=>tD,create:()=>tS,determinant:()=>tE,equals:()=>rd,exactEquals:()=>rp,frob:()=>rh,fromQuat:()=>t6,fromQuat2:()=>t1,fromRotation:()=>tQ,fromRotationTranslation:()=>tK,fromRotationTranslationScale:()=>t4,fromRotationTranslationScaleOrigin:()=>t5,fromScaling:()=>tY,fromTranslation:()=>tW,fromValues:()=>tF,fromXRotation:()=>tZ,fromYRotation:()=>tX,fromZRotation:()=>tJ,frustum:()=>t8,getRotation:()=>t3,getScaling:()=>t2,getTranslation:()=>t0,identity:()=>tN,invert:()=>tC,lookAt:()=>rs,mul:()=>ry,multiply:()=>tO,multiplyScalar:()=>rc,multiplyScalarAndAdd:()=>rf,ortho:()=>re,orthoNO:()=>ra,orthoZO:()=>rn,perspective:()=>t9,perspectiveFromFieldOfView:()=>rr,perspectiveNO:()=>t7,perspectiveZO:()=>rt,rotate:()=>tT,rotateX:()=>tR,rotateY:()=>tG,rotateZ:()=>tH,scale:()=>tP,set:()=>tB,str:()=>ro,sub:()=>rg,subtract:()=>rl,targetTo:()=>ri,translate:()=>t$,transpose:()=>tU});var t9=t7;function rt(t,r,a,e,n){var s,i=1/Math.tan(r/2);return t[0]=i/a,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=i,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=-1,t[12]=0,t[13]=0,t[15]=0,null!=n&&n!==1/0?(s=1/(e-n),t[10]=n*s,t[14]=n*e*s):(t[10]=-1,t[14]=-e),t}function rr(t,r,a,e){var n=Math.tan(r.upDegrees*Math.PI/180),s=Math.tan(r.downDegrees*Math.PI/180),i=Math.tan(r.leftDegrees*Math.PI/180),o=Math.tan(r.rightDegrees*Math.PI/180),h=2/(i+o),u=2/(n+s);return t[0]=h,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=u,t[6]=0,t[7]=0,t[8]=-((i-o)*h*.5),t[9]=(n-s)*u*.5,t[10]=e/(a-e),t[11]=-1,t[12]=0,t[13]=0,t[14]=e*a/(a-e),t[15]=0,t}function ra(t,r,a,e,n,s,i){var o=1/(r-a),h=1/(e-n),u=1/(s-i);return t[0]=-2*o,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*h,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=2*u,t[11]=0,t[12]=(r+a)*o,t[13]=(n+e)*h,t[14]=(i+s)*u,t[15]=1,t}var re=ra;function rn(t,r,a,e,n,s,i){var o=1/(r-a),h=1/(e-n),u=1/(s-i);return t[0]=-2*o,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*h,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=u,t[11]=0,t[12]=(r+a)*o,t[13]=(n+e)*h,t[14]=s*u,t[15]=1,t}function rs(t,r,a,e){var n,s,i,o,h,u,l,c,f,p,d=r[0],y=r[1],g=r[2],m=e[0],b=e[1],_=e[2],v=a[0],M=a[1],x=a[2];return 1e-6>Math.abs(d-v)&&1e-6>Math.abs(y-M)&&1e-6>Math.abs(g-x)?tN(t):(p=1/Math.hypot(l=d-v,c=y-M,f=g-x),l*=p,c*=p,f*=p,(p=Math.hypot(n=b*f-_*c,s=_*l-m*f,i=m*c-b*l))?(n*=p=1/p,s*=p,i*=p):(n=0,s=0,i=0),(p=Math.hypot(o=c*i-f*s,h=f*n-l*i,u=l*s-c*n))?(o*=p=1/p,h*=p,u*=p):(o=0,h=0,u=0),t[0]=n,t[1]=o,t[2]=l,t[3]=0,t[4]=s,t[5]=h,t[6]=c,t[7]=0,t[8]=i,t[9]=u,t[10]=f,t[11]=0,t[12]=-(n*d+s*y+i*g),t[13]=-(o*d+h*y+u*g),t[14]=-(l*d+c*y+f*g),t[15]=1,t)}function ri(t,r,a,e){var n=r[0],s=r[1],i=r[2],o=e[0],h=e[1],u=e[2],l=n-a[0],c=s-a[1],f=i-a[2],p=l*l+c*c+f*f;p>0&&(l*=p=1/Math.sqrt(p),c*=p,f*=p);var d=h*f-u*c,y=u*l-o*f,g=o*c-h*l;return(p=d*d+y*y+g*g)>0&&(d*=p=1/Math.sqrt(p),y*=p,g*=p),t[0]=d,t[1]=y,t[2]=g,t[3]=0,t[4]=c*g-f*y,t[5]=f*d-l*g,t[6]=l*y-c*d,t[7]=0,t[8]=l,t[9]=c,t[10]=f,t[11]=0,t[12]=n,t[13]=s,t[14]=i,t[15]=1,t}function ro(t){return"mat4("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+", "+t[9]+", "+t[10]+", "+t[11]+", "+t[12]+", "+t[13]+", "+t[14]+", "+t[15]+")"}function rh(t){return Math.hypot(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15])}function ru(t,r,a){return t[0]=r[0]+a[0],t[1]=r[1]+a[1],t[2]=r[2]+a[2],t[3]=r[3]+a[3],t[4]=r[4]+a[4],t[5]=r[5]+a[5],t[6]=r[6]+a[6],t[7]=r[7]+a[7],t[8]=r[8]+a[8],t[9]=r[9]+a[9],t[10]=r[10]+a[10],t[11]=r[11]+a[11],t[12]=r[12]+a[12],t[13]=r[13]+a[13],t[14]=r[14]+a[14],t[15]=r[15]+a[15],t}function rl(t,r,a){return t[0]=r[0]-a[0],t[1]=r[1]-a[1],t[2]=r[2]-a[2],t[3]=r[3]-a[3],t[4]=r[4]-a[4],t[5]=r[5]-a[5],t[6]=r[6]-a[6],t[7]=r[7]-a[7],t[8]=r[8]-a[8],t[9]=r[9]-a[9],t[10]=r[10]-a[10],t[11]=r[11]-a[11],t[12]=r[12]-a[12],t[13]=r[13]-a[13],t[14]=r[14]-a[14],t[15]=r[15]-a[15],t}function rc(t,r,a){return t[0]=r[0]*a,t[1]=r[1]*a,t[2]=r[2]*a,t[3]=r[3]*a,t[4]=r[4]*a,t[5]=r[5]*a,t[6]=r[6]*a,t[7]=r[7]*a,t[8]=r[8]*a,t[9]=r[9]*a,t[10]=r[10]*a,t[11]=r[11]*a,t[12]=r[12]*a,t[13]=r[13]*a,t[14]=r[14]*a,t[15]=r[15]*a,t}function rf(t,r,a,e){return t[0]=r[0]+a[0]*e,t[1]=r[1]+a[1]*e,t[2]=r[2]+a[2]*e,t[3]=r[3]+a[3]*e,t[4]=r[4]+a[4]*e,t[5]=r[5]+a[5]*e,t[6]=r[6]+a[6]*e,t[7]=r[7]+a[7]*e,t[8]=r[8]+a[8]*e,t[9]=r[9]+a[9]*e,t[10]=r[10]+a[10]*e,t[11]=r[11]+a[11]*e,t[12]=r[12]+a[12]*e,t[13]=r[13]+a[13]*e,t[14]=r[14]+a[14]*e,t[15]=r[15]+a[15]*e,t}function rp(t,r){return t[0]===r[0]&&t[1]===r[1]&&t[2]===r[2]&&t[3]===r[3]&&t[4]===r[4]&&t[5]===r[5]&&t[6]===r[6]&&t[7]===r[7]&&t[8]===r[8]&&t[9]===r[9]&&t[10]===r[10]&&t[11]===r[11]&&t[12]===r[12]&&t[13]===r[13]&&t[14]===r[14]&&t[15]===r[15]}function rd(t,r){var a=t[0],e=t[1],n=t[2],s=t[3],i=t[4],o=t[5],h=t[6],u=t[7],l=t[8],c=t[9],f=t[10],p=t[11],d=t[12],y=t[13],g=t[14],m=t[15],b=r[0],_=r[1],v=r[2],M=r[3],x=r[4],k=r[5],w=r[6],A=r[7],q=r[8],z=r[9],j=r[10],V=r[11],S=r[12],I=r[13],D=r[14],F=r[15];return Math.abs(a-b)<=1e-6*Math.max(1,Math.abs(a),Math.abs(b))&&Math.abs(e-_)<=1e-6*Math.max(1,Math.abs(e),Math.abs(_))&&Math.abs(n-v)<=1e-6*Math.max(1,Math.abs(n),Math.abs(v))&&Math.abs(s-M)<=1e-6*Math.max(1,Math.abs(s),Math.abs(M))&&Math.abs(i-x)<=1e-6*Math.max(1,Math.abs(i),Math.abs(x))&&Math.abs(o-k)<=1e-6*Math.max(1,Math.abs(o),Math.abs(k))&&Math.abs(h-w)<=1e-6*Math.max(1,Math.abs(h),Math.abs(w))&&Math.abs(u-A)<=1e-6*Math.max(1,Math.abs(u),Math.abs(A))&&Math.abs(l-q)<=1e-6*Math.max(1,Math.abs(l),Math.abs(q))&&Math.abs(c-z)<=1e-6*Math.max(1,Math.abs(c),Math.abs(z))&&Math.abs(f-j)<=1e-6*Math.max(1,Math.abs(f),Math.abs(j))&&Math.abs(p-V)<=1e-6*Math.max(1,Math.abs(p),Math.abs(V))&&Math.abs(d-S)<=1e-6*Math.max(1,Math.abs(d),Math.abs(S))&&Math.abs(y-I)<=1e-6*Math.max(1,Math.abs(y),Math.abs(I))&&Math.abs(g-D)<=1e-6*Math.max(1,Math.abs(g),Math.abs(D))&&Math.abs(m-F)<=1e-6*Math.max(1,Math.abs(m),Math.abs(F))}var ry=tO,rg=rl,rm={};function rb(){var t=new Y(3);return Y!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0),t}function r_(t,r,a){var e=new Y(3);return e[0]=t,e[1]=r,e[2]=a,e}function rv(t,r,a){var e=r[0],n=r[1],s=r[2],i=a[0],o=a[1],h=a[2];return t[0]=n*h-s*o,t[1]=s*i-e*h,t[2]=e*o-n*i,t}function rM(){var t=new Y(4);return Y!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0),t[3]=1,t}function rx(t){return t[0]=0,t[1]=0,t[2]=0,t[3]=1,t}function rk(t,r,a){var e=Math.sin(a*=.5);return t[0]=e*r[0],t[1]=e*r[1],t[2]=e*r[2],t[3]=Math.cos(a),t}function rw(t,r){var a=2*Math.acos(r[3]),e=Math.sin(a/2);return e>1e-6?(t[0]=r[0]/e,t[1]=r[1]/e,t[2]=r[2]/e):(t[0]=1,t[1]=0,t[2]=0),a}function rA(t,r){var a=rY(t,r);return Math.acos(2*a*a-1)}function rq(t,r,a){var e=r[0],n=r[1],s=r[2],i=r[3],o=a[0],h=a[1],u=a[2],l=a[3];return t[0]=e*l+i*o+n*u-s*h,t[1]=n*l+i*h+s*o-e*u,t[2]=s*l+i*u+e*h-n*o,t[3]=i*l-e*o-n*h-s*u,t}function rz(t,r,a){a*=.5;var e=r[0],n=r[1],s=r[2],i=r[3],o=Math.sin(a),h=Math.cos(a);return t[0]=e*h+i*o,t[1]=n*h+s*o,t[2]=s*h-n*o,t[3]=i*h-e*o,t}function rj(t,r,a){a*=.5;var e=r[0],n=r[1],s=r[2],i=r[3],o=Math.sin(a),h=Math.cos(a);return t[0]=e*h-s*o,t[1]=n*h+i*o,t[2]=s*h+e*o,t[3]=i*h-n*o,t}function rV(t,r,a){a*=.5;var e=r[0],n=r[1],s=r[2],i=r[3],o=Math.sin(a),h=Math.cos(a);return t[0]=e*h+n*o,t[1]=n*h-e*o,t[2]=s*h+i*o,t[3]=i*h-s*o,t}function rS(t,r){var a=r[0],e=r[1],n=r[2];return t[0]=a,t[1]=e,t[2]=n,t[3]=Math.sqrt(Math.abs(1-a*a-e*e-n*n)),t}function rI(t,r){var a=r[0],e=r[1],n=r[2],s=r[3],i=Math.sqrt(a*a+e*e+n*n),o=Math.exp(s),h=i>0?o*Math.sin(i)/i:0;return t[0]=a*h,t[1]=e*h,t[2]=n*h,t[3]=o*Math.cos(i),t}function rD(t,r){var a=r[0],e=r[1],n=r[2],s=r[3],i=Math.sqrt(a*a+e*e+n*n),o=i>0?Math.atan2(i,s)/i:0;return t[0]=a*o,t[1]=e*o,t[2]=n*o,t[3]=.5*Math.log(a*a+e*e+n*n+s*s),t}function rF(t,r,a){return rD(t,r),rW(t,t,a),rI(t,t),t}function rB(t,r,a,e){var n,s,i,o,h,u=r[0],l=r[1],c=r[2],f=r[3],p=a[0],d=a[1],y=a[2],g=a[3];return(s=u*p+l*d+c*y+f*g)<0&&(s=-s,p=-p,d=-d,y=-y,g=-g),1-s>1e-6?(i=Math.sin(n=Math.acos(s)),o=Math.sin((1-e)*n)/i,h=Math.sin(e*n)/i):(o=1-e,h=e),t[0]=o*u+h*p,t[1]=o*l+h*d,t[2]=o*c+h*y,t[3]=o*f+h*g,t}function rN(t){var r=Q(),a=Q(),e=Q(),n=Math.sqrt(1-r),s=Math.sqrt(r);return t[0]=n*Math.sin(2*Math.PI*a),t[1]=n*Math.cos(2*Math.PI*a),t[2]=s*Math.sin(2*Math.PI*e),t[3]=s*Math.cos(2*Math.PI*e),t}function rU(t,r){var a=r[0],e=r[1],n=r[2],s=r[3],i=a*a+e*e+n*n+s*s,o=i?1/i:0;return t[0]=-a*o,t[1]=-e*o,t[2]=-n*o,t[3]=s*o,t}function rC(t,r){return t[0]=-r[0],t[1]=-r[1],t[2]=-r[2],t[3]=r[3],t}function rL(t,r){var a,e=r[0]+r[4]+r[8];if(e>0)a=Math.sqrt(e+1),t[3]=.5*a,a=.5/a,t[0]=(r[5]-r[7])*a,t[1]=(r[6]-r[2])*a,t[2]=(r[1]-r[3])*a;else{var n=0;r[4]>r[0]&&(n=1),r[8]>r[3*n+n]&&(n=2);var s=(n+1)%3,i=(n+2)%3;a=Math.sqrt(r[3*n+n]-r[3*s+s]-r[3*i+i]+1),t[n]=.5*a,a=.5/a,t[3]=(r[3*s+i]-r[3*i+s])*a,t[s]=(r[3*s+n]+r[3*n+s])*a,t[i]=(r[3*i+n]+r[3*n+i])*a}return t}function rE(t,r,a,e){var n=.5*Math.PI/180;r*=n,a*=n,e*=n;var s=Math.sin(r),i=Math.cos(r),o=Math.sin(a),h=Math.cos(a),u=Math.sin(e),l=Math.cos(e);return t[0]=s*h*l-i*o*u,t[1]=i*o*l+s*h*u,t[2]=i*h*u-s*o*l,t[3]=i*h*l+s*o*u,t}function rO(t){return"quat("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+")"}c(rm,{add:()=>rG,calculateW:()=>rS,clone:()=>r$,conjugate:()=>rC,copy:()=>rT,create:()=>rM,dot:()=>rY,equals:()=>r2,exactEquals:()=>r0,exp:()=>rI,fromEuler:()=>rE,fromMat3:()=>rL,fromValues:()=>rP,getAngle:()=>rA,getAxisAngle:()=>rw,identity:()=>rx,invert:()=>rU,len:()=>rX,length:()=>rZ,lerp:()=>rQ,ln:()=>rD,mul:()=>rH,multiply:()=>rq,normalize:()=>r1,pow:()=>rF,random:()=>rN,rotateX:()=>rz,rotateY:()=>rj,rotateZ:()=>rV,rotationTo:()=>r3,scale:()=>rW,set:()=>rR,setAxes:()=>r5,setAxisAngle:()=>rk,slerp:()=>rB,sqlerp:()=>r4,sqrLen:()=>rK,squaredLength:()=>rJ,str:()=>rO}),rb(),!function(){var t;t=new Y(4),Y!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0,t[3]=0)}();var r$=function(t){var r=new Y(4);return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r},rP=function(t,r,a,e){var n=new Y(4);return n[0]=t,n[1]=r,n[2]=a,n[3]=e,n},rT=function(t,r){return t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[3],t},rR=function(t,r,a,e,n){return t[0]=r,t[1]=a,t[2]=e,t[3]=n,t},rG=function(t,r,a){return t[0]=r[0]+a[0],t[1]=r[1]+a[1],t[2]=r[2]+a[2],t[3]=r[3]+a[3],t},rH=rq,rW=function(t,r,a){return t[0]=r[0]*a,t[1]=r[1]*a,t[2]=r[2]*a,t[3]=r[3]*a,t},rY=function(t,r){return t[0]*r[0]+t[1]*r[1]+t[2]*r[2]+t[3]*r[3]},rQ=function(t,r,a,e){var n=r[0],s=r[1],i=r[2],o=r[3];return t[0]=n+e*(a[0]-n),t[1]=s+e*(a[1]-s),t[2]=i+e*(a[2]-i),t[3]=o+e*(a[3]-o),t},rZ=function(t){return Math.hypot(t[0],t[1],t[2],t[3])},rX=rZ,rJ=function(t){var r=t[0],a=t[1],e=t[2],n=t[3];return r*r+a*a+e*e+n*n},rK=rJ,r1=function(t,r){var a=r[0],e=r[1],n=r[2],s=r[3],i=a*a+e*e+n*n+s*s;return i>0&&(i=1/Math.sqrt(i)),t[0]=a*i,t[1]=e*i,t[2]=n*i,t[3]=s*i,t},r0=function(t,r){return t[0]===r[0]&&t[1]===r[1]&&t[2]===r[2]&&t[3]===r[3]},r2=function(t,r){var a=t[0],e=t[1],n=t[2],s=t[3],i=r[0],o=r[1],h=r[2],u=r[3];return Math.abs(a-i)<=1e-6*Math.max(1,Math.abs(a),Math.abs(i))&&Math.abs(e-o)<=1e-6*Math.max(1,Math.abs(e),Math.abs(o))&&Math.abs(n-h)<=1e-6*Math.max(1,Math.abs(n),Math.abs(h))&&Math.abs(s-u)<=1e-6*Math.max(1,Math.abs(s),Math.abs(u))},r3=function(){var t=rb(),r=r_(1,0,0),a=r_(0,1,0);return function(e,n,s){var i,o,h,u,l=n[0]*s[0]+n[1]*s[1]+n[2]*s[2];return l<-.999999?(rv(t,r,n),1e-6>Math.hypot(t[0],t[1],t[2])&&rv(t,a,n),(u=(i=t[0])*i+(o=t[1])*o+(h=t[2])*h)>0&&(u=1/Math.sqrt(u)),t[0]=t[0]*u,t[1]=t[1]*u,t[2]=t[2]*u,rk(e,t,Math.PI),e):l>.999999?(e[0]=0,e[1]=0,e[2]=0,e[3]=1,e):(rv(t,n,s),e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=1+l,r1(e,e))}}(),r4=function(){var t=rM(),r=rM();return function(a,e,n,s,i,o){return rB(t,e,i,o),rB(r,n,s,o),rB(a,t,r,2*o*(1-o)),a}}(),r5=function(){var t=X();return function(r,a,e,n){return t[0]=e[0],t[3]=e[1],t[6]=e[2],t[1]=n[0],t[4]=n[1],t[7]=n[2],t[2]=-a[0],t[5]=-a[1],t[8]=-a[2],r1(r,rL(r,t))}}(),r6={xyz:3,color:3,opacity:1,scaling:3,quaternion:4,harmonics:3},r8=class{constructor(t){this.version="",this._buffer=t}get buffer(){return this._buffer}get decoded(){return this._decoded||(this._decoded=this.decodeBuffer()),this._decoded}get colorsA(){let t=this.decoded.color.denormDequant(),r=this.decoded.opacity.denormDequant(),a=(0,H.default)(new Float32Array(4*t.shape[0]),[t.shape[0],4]);return W.mulseq(t,.28209479177387814),W.addseq(t,.5),W.mulseq(t,255),W.maxseq(t,0),W.minseq(t,255),""===this.version&&(W.negeq(r),W.expeq(r),W.addseq(r,1),W.recipeq(r),W.mulseq(r,255)),W.assign(a.hi(t.shape[0],3).lo(0,0),t),W.assign(a.hi(t.shape[0],4).lo(0,3),r),(0,H.default)(new Uint8Array(a.data),[t.shape[0],4]).data}get nsplats(){return this.decoded.nsplats}getSplatCount(){return this.decoded.nsplats}get precomputedCovarianceBufferData(){return this._precomputedCovarianceBufferData}decodeBuffer(){let{splatCount:t,chunkCount:r,chunkSize:a,typeChunks:e,vertexData:n,propertiesDesc:s,version:i}=this.decodeHeader();this.version=i;let o={xyz:s.xyz.compressionMethod,color:s.color.compressionMethod,opacity:s.opacity.compressionMethod,scaling:s.scaling.compressionMethod,quaternion:s.quaternion.compressionMethod,chunkSize:a};s.harmonics_0&&(o.harmonics=s.harmonics_0.compressionMethod);let h=n.byteOffset,u=Array(Object.keys(s).length);for(let t in s)u[s[t].index]={name:t,method:s[t].compressionMethod};let l=2*r*4,c=h,f="dynamic"===e?2*r:0,p,d=!1;if(f>0){let t=new Uint16Array(n.buffer.slice(c,c+f));c+=f,p=Array.from(t),d=!0}let y={};for(let e of u){let s,i,o=0,h=!0;if("norm8x"===e.method)o=t*r6[e.name];else if("norm11"===e.method)o=4*t;else if("norm565"===e.method)o=2*t;else throw h=!1,Error(`Not Implemented format: ${e.method}`);if(h){let t=n.buffer.slice(c,c+l);s=(0,H.default)(new Float32Array(t),[r,2]),c+=l}else throw Error("loading chunk byt hasnot minmax!");let u=n.buffer.slice(c,c+o);if(c+=o,"norm8x"===e.method)i=(0,H.default)(new Uint8Array(u),[t,r6[e.name]]);else if("norm11"===e.method)i=(0,H.default)(new Uint32Array(u));else if("norm565"===e.method)i=(0,H.default)(new Uint16Array(u));else throw Error(`Not Implemented format: ${e.method}`);y[e.name]=new V(i,s,a,e.method,p,d)}let g=[];for(let t=0;t<15;t++){let r=y[`harmonics_${t}`];r&&(g.push(r),delete y[`harmonics_${t}`])}return g.length>0&&(y.harmonics=g),new G(o,y.xyz,y.scaling,y.color,y.opacity,y.quaternion,y.harmonics,p)}buildPreComputedBuffers(){let t=this.decoded,r=t.nsplats,a=new ArrayBuffer(24*r),e=new Float32Array(a),n=t.scaling.denormDequant(),s=t.quaternion.denormDequant(),i=rm.create(),o=Z.create(),h=Z.create(),u=Z.create(),l=tV.create();for(let t=0;t<r;t++)tV.fromScaling(l,[Math.exp(n.get(t,0)),Math.exp(n.get(t,1)),Math.exp(n.get(t,2))]),Z.fromMat4(h,l),rm.set(i,s.get(t,0),s.get(t,1),s.get(t,2),s.get(t,3)),Z.fromQuat(o,i),Z.multiply(u,o,h),e[6*t]=u[0]*u[0]+u[3]*u[3]+u[6]*u[6],e[6*t+1]=u[0]*u[1]+u[3]*u[4]+u[6]*u[7],e[6*t+2]=u[0]*u[2]+u[3]*u[5]+u[6]*u[8],e[6*t+3]=u[1]*u[1]+u[4]*u[4]+u[7]*u[7],e[6*t+4]=u[1]*u[2]+u[4]*u[5]+u[7]*u[8],e[6*t+5]=u[2]*u[2]+u[5]*u[5]+u[8]*u[8];this._precomputedCovarianceBufferData=a}decodeHeader(){let t=this._buffer,r=new TextDecoder,a=0,e="";for(;;){if(a+100>=t.byteLength)throw Error("End of file reached while searching for end of header");let n=new Uint8Array(t,a,100);e+=r.decode(n);let s=(a+=100)-200,i=new Uint8Array(t,Math.max(0,s),s>=0?200:100);if(r.decode(i).includes("end_header"))break}let n=e.split(`
`),s=0,i=0,o=0,h=0,u="",l="",c={};for(let t=0;t<n.length;t++){let r=n[t].trim();if(r.startsWith("version"))l=r.split(" ")[1]??"";else if(r.startsWith("element vertex")){let t=r.match(/\d+/);t&&(s=parseInt(t[0]))}else if(r.startsWith("property")){let t=r.match(/(\w+)\s+(\w+)\s+(\w+)/);if(t){let r=t[2],a=t[3];c[r]={compressionMethod:a,index:h},h++}}else if(r.startsWith("element chunks")){let t=r.match(/\d+/);t&&(i=parseInt(t[0]))}else if(r.startsWith("element chunkSize")){let t=r.match(/\d+/);t&&(o=parseInt(t[0]))}else if(r.startsWith("element typeChunks")){let t=r.match(/(\w+)\s+(\w+)\s+(\w+)/);t&&(u=t[3])}else if("end_header"===r)break}return{splatCount:s,chunkCount:i,chunkSize:o,typeChunks:u,vertexData:new DataView(t,e.indexOf("end_header")+10+1),propertiesDesc:c,version:l}}pruneSplats(t){let r=this.decodeBuffer().pruneSplats(t);return r8.fromCompressedGaussianSplats(r,this.version)}static fromCompressedGaussianSplats(t,r){let a=t.xyz.length,e=t.xyz.nchunks,n=`gspline
version ${r}
element vertex ${a}
element chunks ${e}
element chunkSize ${t.chunkSize}
element typeChunks ${t.isDynamicChunks?"dynamic":"static"}
property xyz ${t.xyz.method}
property color ${t.color.method}
property opacity ${t.opacity.method}
property scaling ${t.scaling.method}
property quaternion ${t.quaternion.method}`;if(t.harmonics&&t.harmonics.length>0)for(let r=0;r<t.harmonics.length;r++)n=`${n}
property harmonics_${r} ${t.harmonics[r].method}`;n=`${n}
end_header
`;let s=new TextEncoder().encode(n),i=2*e*4,o=t.xyz.quantized.data.buffer.byteLength,h=t.xyz instanceof V?i:0,u=t.color.quantized.data.buffer.byteLength,l=t.color instanceof V?i:0,c=t.opacity.quantized.data.buffer.byteLength,f=t.opacity instanceof V?i:0,p=t.scaling.quantized.data.buffer.byteLength,d=t.scaling instanceof V?i:0,y=t.quaternion.quantized.data.buffer.byteLength,g=t.quaternion instanceof V?i:0,m=t.variableChunkSize?Uint16Array.from(t.variableChunkSize):void 0,b=m?m.byteLength:0,_=s.byteLength+b+o+h+u+l+c+f+p+d+y+g,v=0,M=0;if(t.harmonics&&t.harmonics.length>0)for(let r=0;r<t.harmonics.length;r++)v+=t.harmonics[r].quantized.data.buffer.byteLength,t.harmonics[r]instanceof V;let x=new Uint8Array(_+=(v=0)+(M=0)),k=0;if(x.set(s,k),k+=s.byteLength,b>0&&(x.set(new Uint8Array(m.buffer),k),k+=b),t.xyz instanceof V&&(x.set(new Uint8Array(t.xyz.minmaxMatrix.data.buffer),k),k+=i),x.set(new Uint8Array(t.xyz.quantized.data.buffer),k),k+=o,t.color instanceof V&&(x.set(new Uint8Array(t.color.minmaxMatrix.data.buffer),k),k+=i),x.set(new Uint8Array(t.color.quantized.data.buffer),k),k+=u,t.opacity instanceof V&&(x.set(new Uint8Array(t.opacity.minmaxMatrix.data.buffer),k),k+=i),x.set(new Uint8Array(t.opacity.quantized.data.buffer),k),k+=c,t.scaling instanceof V&&(x.set(new Uint8Array(t.scaling.minmaxMatrix.data.buffer),k),k+=i),x.set(new Uint8Array(t.scaling.quantized.data.buffer),k),k+=p,t.quaternion instanceof V&&(x.set(new Uint8Array(t.quaternion.minmaxMatrix.data.buffer),k),k+=i),x.set(new Uint8Array(t.quaternion.quantized.data.buffer),k),k+=y,v>0&&t.harmonics&&t.harmonics.length>0)for(let r=0;r<t.harmonics.length;r++){let a=t.harmonics[r];a instanceof V&&(x.set(new Uint8Array(a.minmaxMatrix.data.buffer),k),k+=i),x.set(new Uint8Array(a.quantized.data.buffer),k),k+=a.quantized.data.byteLength}return new r8(x.buffer)}}}};