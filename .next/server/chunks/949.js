exports.id=949,exports.ids=[949],exports.modules={4780:(e,t,n)=>{"use strict";n.d(t,{cn:()=>i});var o=n(49384),s=n(82348);function i(...e){return(0,s.QP)((0,o.$)(e))}},5672:(e,t,n)=>{Promise.resolve().then(n.bind(n,29519))},20144:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,86346,23)),Promise.resolve().then(n.t.bind(n,27924,23)),Promise.resolve().then(n.t.bind(n,35656,23)),Promise.resolve().then(n.t.bind(n,40099,23)),Promise.resolve().then(n.t.bind(n,38243,23)),Promise.resolve().then(n.t.bind(n,28827,23)),Promise.resolve().then(n.t.bind(n,62763,23)),Promise.resolve().then(n.t.bind(n,97173,23))},25709:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>g});var o=n(60687),s=n(85814),i=n.n(s),r=n(72244),a=n(32192),l=n(99270),d=n(28559),c=n(43210),u=n(8730),m=n(24224),p=n(4780);let h=(0,m.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),x=c.forwardRef(({className:e,variant:t,size:n,asChild:s=!1,...i},r)=>{let a=s?u.DX:"button";return(0,o.jsx)(a,{className:(0,p.cn)(h({variant:t,size:n,className:e})),ref:r,...i})});function g(){return(0,o.jsx)("div",{className:"min-h-screen bg-github-dark text-github-text flex items-center justify-center",children:(0,o.jsx)("div",{className:"max-w-md mx-auto text-center px-6",children:(0,o.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,o.jsx)(r.P.div,{className:"text-8xl font-bold text-neon-green mb-8",initial:{scale:.5},animate:{scale:1},transition:{duration:.5,type:"spring",stiffness:200},children:"404"}),(0,o.jsx)(r.P.h1,{className:"text-3xl font-bold text-white mb-4",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:"Page Not Found"}),(0,o.jsx)(r.P.p,{className:"text-github-text mb-8 leading-relaxed",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:"Oops! The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL."}),(0,o.jsxs)(r.P.div,{className:"flex flex-col sm:flex-row gap-4 justify-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,o.jsx)(x,{asChild:!0,className:"bg-neon-green text-black hover:bg-neon-green/90",children:(0,o.jsxs)(i(),{href:"/",className:"flex items-center gap-2",children:[(0,o.jsx)(a.A,{size:18}),"Go Home"]})}),(0,o.jsx)(x,{variant:"outline",asChild:!0,className:"border-github-border text-github-text hover:bg-github-light",children:(0,o.jsxs)(i(),{href:"/#contact",className:"flex items-center gap-2",children:[(0,o.jsx)(l.A,{size:18}),"Contact Support"]})})]}),(0,o.jsx)(r.P.div,{className:"mt-8",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:(0,o.jsxs)(x,{variant:"ghost",onClick:()=>window.history.back(),className:"text-github-text hover:text-white flex items-center gap-2",children:[(0,o.jsx)(d.A,{size:18}),"Go Back"]})}),(0,o.jsx)(r.P.div,{className:"absolute top-1/4 left-1/4 w-2 h-2 bg-neon-green rounded-full opacity-50",animate:{scale:[1,1.5,1],opacity:[.5,1,.5]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}),(0,o.jsx)(r.P.div,{className:"absolute top-1/3 right-1/4 w-1 h-1 bg-neon-blue rounded-full opacity-50",animate:{scale:[1,2,1],opacity:[.3,.8,.3]},transition:{duration:3,repeat:1/0,ease:"easeInOut",delay:1}})]})})})}x.displayName="Button"},26768:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,16444,23)),Promise.resolve().then(n.t.bind(n,16042,23)),Promise.resolve().then(n.t.bind(n,88170,23)),Promise.resolve().then(n.t.bind(n,49477,23)),Promise.resolve().then(n.t.bind(n,29345,23)),Promise.resolve().then(n.t.bind(n,12089,23)),Promise.resolve().then(n.t.bind(n,46577,23)),Promise.resolve().then(n.t.bind(n,31307,23))},29519:(e,t,n)=>{"use strict";n.d(t,{Providers:()=>o});let o=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/providers.tsx","Providers")},29867:(e,t,n)=>{"use strict";n.d(t,{dj:()=>m,oR:()=>u});var o=n(43210);let s=0,i=new Map,r=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},a=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:n}=t;return n?r(n):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===n||void 0===n?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=a(d,e),l.forEach(e=>{e(d)})}function u({...e}){let t=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||n()}}}),{id:t,dismiss:n,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function m(){let[e,t]=o.useState(d);return o.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},39612:(e,t,n)=>{Promise.resolve().then(n.bind(n,25709))},54413:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>o});let o=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/not-found.tsx","default")},61135:()=>{},69224:(e,t,n)=>{Promise.resolve().then(n.bind(n,87026))},81468:(e,t,n)=>{Promise.resolve().then(n.bind(n,54413))},87026:(e,t,n)=>{"use strict";n.d(t,{Providers:()=>K});var o=n(60687),s=n(92314),i=n(8693),r=n(43210),a=n.n(r),l=n(20592),d=n(4780);let c=l.Kq;l.bL,l.l9,r.forwardRef(({className:e,sideOffset:t=4,...n},s)=>(0,o.jsx)(l.UC,{ref:s,sideOffset:t,className:(0,d.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})).displayName=l.UC.displayName;let u=(0,r.createContext)(void 0),m=({children:e})=>{let[t,n]=(0,r.useState)("dark");return(0,r.useEffect)(()=>{},[]),(0,o.jsx)(u.Provider,{value:{theme:t,toggleTheme:()=>{n("dark"===t?"light":"dark")}},children:e})};var p=n(29867),h=n(47313),x=n(24224),g=n(11860);let f=h.Kq,v=r.forwardRef(({className:e,...t},n)=>(0,o.jsx)(h.LM,{ref:n,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));v.displayName=h.LM.displayName;let b=(0,x.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),y=r.forwardRef(({className:e,variant:t,...n},s)=>(0,o.jsx)(h.bL,{ref:s,className:(0,d.cn)(b({variant:t}),e),...n}));y.displayName=h.bL.displayName,r.forwardRef(({className:e,...t},n)=>(0,o.jsx)(h.rc,{ref:n,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=h.rc.displayName;let w=r.forwardRef(({className:e,...t},n)=>(0,o.jsx)(h.bm,{ref:n,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,o.jsx)(g.A,{className:"h-4 w-4"})}));w.displayName=h.bm.displayName;let j=r.forwardRef(({className:e,...t},n)=>(0,o.jsx)(h.hE,{ref:n,className:(0,d.cn)("text-sm font-semibold",e),...t}));j.displayName=h.hE.displayName;let N=r.forwardRef(({className:e,...t},n)=>(0,o.jsx)(h.VY,{ref:n,className:(0,d.cn)("text-sm opacity-90",e),...t}));function k(){let{toasts:e}=(0,p.dj)();return(0,o.jsxs)(f,{children:[e.map(function({id:e,title:t,description:n,action:s,...i}){return(0,o.jsxs)(y,{...i,children:[(0,o.jsxs)("div",{className:"grid gap-1",children:[t&&(0,o.jsx)(j,{children:t}),n&&(0,o.jsx)(N,{children:n})]}),s,(0,o.jsx)(w,{})]},e)}),(0,o.jsx)(v,{})]})}N.displayName=h.VY.displayName;var P=n(10218),E=n(52581);let T=({...e})=>{let{theme:t="system"}=(0,P.D)();return(0,o.jsx)(E.l$,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var A=n(72244);let S=()=>{let[e,t]=(0,r.useState)(0),[n,s]=(0,r.useState)("Initializing system..."),[i,a]=(0,r.useState)(!0),[l,d]=(0,r.useState)(!1),c=[{text:"Initializing system...",duration:1200},{text:"Establishing secure connection...",duration:1e3},{text:"Authenticating credentials...",duration:800},{text:"Bypassing security protocols...",duration:1500},{text:"Loading developer assets...",duration:1e3},{text:"Compiling portfolio data...",duration:1200},{text:"Optimizing display modules...",duration:900},{text:"Rendering interface...",duration:1300},{text:"System ready. Welcome to GreenHacker portfolio v2.0",duration:1e3}];return(0,r.useEffect)(()=>{let e=setInterval(()=>{a(e=>!e)},500),n=0,o=setTimeout(function e(){if(n<c.length){let{text:o,duration:i}=c[n];s(o),t(Math.min(100,Math.round((n+1)/c.length*100))),n++,setTimeout(e,i)}else d(!0),setTimeout(()=>{},1e3)},500);return()=>{clearInterval(e),clearTimeout(o)}},[]),(0,o.jsxs)(A.P.div,{className:"fixed inset-0 bg-black flex items-center justify-center z-50",initial:{opacity:1},exit:{opacity:0},transition:{duration:.6,ease:"easeInOut"},children:[(0,o.jsxs)(A.P.div,{className:"w-full max-w-3xl bg-black border border-neon-green p-6 rounded-md shadow-neon-green terminal-window",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.8}}},initial:"hidden",animate:"visible",children:[(0,o.jsxs)("div",{className:"terminal-header flex items-center justify-between mb-4",children:[(0,o.jsx)("div",{className:"text-neon-green font-mono text-sm",children:"~/green-hacker/portfolio"}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500"}),(0,o.jsx)("div",{className:"w-3 h-3 rounded-full bg-yellow-500"}),(0,o.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"})]})]}),(0,o.jsxs)("div",{className:"terminal-content space-y-2 font-mono text-sm overflow-hidden",children:[(0,o.jsxs)("div",{className:"line",children:[(0,o.jsx)("span",{className:"text-neon-blue",children:"$ "}),(0,o.jsx)("span",{className:"text-white",children:"load portfolio --env=production --secure"})]}),(0,o.jsxs)(A.P.div,{className:"line text-neon-green",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[n,i?"▋":" "]}),(0,o.jsxs)(A.P.div,{className:"line",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:[(0,o.jsxs)("div",{className:"text-github-text",children:["Progress: ",e,"%"]}),(0,o.jsx)("div",{className:"w-full bg-github-dark rounded-full h-2 mt-1",children:(0,o.jsx)(A.P.div,{className:"h-2 rounded-full bg-neon-green",initial:{width:0},animate:{width:`${e}%`},transition:{duration:.5}})})]}),l&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(A.P.div,{className:"line",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:[(0,o.jsx)("span",{className:"text-neon-blue",children:"$ "}),(0,o.jsx)("span",{className:"text-white",children:"launch --mode=interactive"})]}),(0,o.jsx)(A.P.div,{className:"line text-neon-purple",initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},children:"Launching portfolio interface..."})]})]}),(0,o.jsx)("div",{className:"ascii-art mt-8 text-neon-green font-mono text-xs whitespace-pre",children:` ██████╗ ██████╗ ███████╗███████╗███╗   ██╗██╗  ██╗ █████╗  ██████╗██╗  ██╗███████╗██████╗
██╔════╝ ██╔══██╗██╔════╝██╔════╝████╗  ██║██║  ██║██╔══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗
██║  ███╗██████╔╝█████╗  █████╗  ██╔██╗ ██║███████║███████║██║     █████╔╝ █████╗  ██████╔╝
██║   ██║██╔══██╗██╔══╝  ██╔══╝  ██║╚██╗██║██╔══██║██╔══██║██║     ██╔═██╗ ██╔══╝  ██╔══██╗
╚██████╔╝██║  ██║███████╗███████╗██║ ╚████║██║  ██║██║  ██║╚██████╗██║  ██╗███████╗██║  ██║
 ╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝`}),l&&(0,o.jsxs)(A.P.div,{className:"mt-6 text-center",initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},children:[(0,o.jsx)("span",{className:"text-github-text text-sm",children:"Press "}),(0,o.jsx)("span",{className:"px-2 py-1 bg-github-light rounded text-white text-sm mx-1",children:"ENTER"}),(0,o.jsx)("span",{className:"text-github-text text-sm",children:" to continue"})]})]}),(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:`
        .terminal-window {
          box-shadow: 0 0 10px rgba(63, 185, 80, 0.3), 0 0 20px rgba(63, 185, 80, 0.2);
        }

        @keyframes scan {
          from { top: 0; }
          to { top: 100%; }
        }

        .terminal-window::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background-color: rgba(63, 185, 80, 0.5);
          animation: scan 3s linear infinite;
        }
      `}})]})},R=()=>{let[e,t]=(0,r.useState)({x:0,y:0}),[n,s]=(0,r.useState)(!1),[i,a]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=e=>{t({x:e.clientX,y:e.clientY})},n=()=>{s(!0),setTimeout(()=>s(!1),300)},o=()=>{document.body.style.cursor="none"},i=()=>{document.body.style.cursor="auto"},r=e=>{let t=e.target;a(!!("button"===t.tagName.toLowerCase()||"a"===t.tagName.toLowerCase()||t.closest("button")||t.closest("a")))};return document.addEventListener("mousemove",e),document.addEventListener("mousedown",n),document.addEventListener("mouseenter",o),document.addEventListener("mouseleave",i),document.addEventListener("mouseover",r),()=>{document.removeEventListener("mousemove",e),document.removeEventListener("mousedown",n),document.removeEventListener("mouseenter",o),document.removeEventListener("mouseleave",i),document.removeEventListener("mouseover",r),document.body.style.cursor="auto"}},[]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(A.P.div,{className:"fixed top-0 left-0 w-8 h-8 rounded-full border border-neon-green z-[9999] pointer-events-none",animate:{x:e.x-16,y:e.y-16,scale:n?.8:i?1.5:1},transition:{type:"spring",stiffness:300,damping:20,mass:.5}}),(0,o.jsx)(A.P.div,{className:"fixed top-0 left-0 w-2 h-2 bg-neon-green rounded-full z-[10000] pointer-events-none",animate:{x:e.x-4,y:e.y-4,opacity:n?.5:1},transition:{type:"spring",stiffness:400,damping:15}})]})};var I=n(17135),C=n(43203);let L=()=>{let e=(0,I.d)(0),t=(0,I.d)(0),n={stiffness:50,damping:50},s=(0,C.z)(e,n),i=(0,C.z)(t,n),a=(0,r.useRef)(null);return(0,r.useEffect)(()=>{let n=n=>{if(!a.current)return;let o=a.current.getBoundingClientRect(),s=o.left+o.width/2,i=o.top+o.height/2,r=(n.clientX-s)/(o.width/2),l=(n.clientY-i)/(o.height/2);e.set(10*r),t.set(10*l)};return window.addEventListener("mousemove",n),()=>{window.removeEventListener("mousemove",n)}},[e,t]),(0,o.jsx)("div",{ref:a,className:"fixed inset-0 pointer-events-none z-0 overflow-hidden",children:(0,o.jsxs)(A.P.div,{className:"absolute inset-0 opacity-20",style:{translateX:s,translateY:i},children:[(0,o.jsx)("div",{className:"absolute top-0 -left-4 w-[50vw] h-[50vw] bg-neon-purple rounded-full mix-blend-screen filter blur-[100px] opacity-70"}),(0,o.jsx)("div",{className:"absolute top-[30%] -right-[10%] w-[40vw] h-[40vw] bg-neon-green rounded-full mix-blend-screen filter blur-[100px] opacity-70"}),(0,o.jsx)("div",{className:"absolute -bottom-[20%] left-[20%] w-[60vw] h-[60vw] bg-neon-blue rounded-full mix-blend-screen filter blur-[100px] opacity-40"})]})})};var z=n(88920),D=n(24366),H=n(49153),G=n(79351),F=n(27900);let O={help:["\uD83D\uDCBB Available Commands:","- help: Display this help message","- about: Learn about Green Hacker","- skills: View technical skills","- projects: Show recent projects","- contact: Get contact information","- clear: Clear the terminal","- exit: Close the chatbot","","You can also just chat naturally!"],about:["Hey there! \uD83D\uDC4B I'm Green Hacker, a full-stack developer and ML enthusiast.","When I'm not coding, I'm probably hiking, gaming, or learning something new.","I specialize in creating interactive web experiences and AI-powered applications."],skills:["\uD83D\uDE80 Technical Skills:","- Frontend: React, TypeScript, Tailwind CSS, Framer Motion","- Backend: Node.js, Express, FastAPI, GraphQL","- ML/AI: PyTorch, TensorFlow, Computer Vision","- DevOps: Docker, AWS, CI/CD, Kubernetes","- Other: Three.js, React Three Fiber, WebGL"],projects:["\uD83D\uDCC1 Recent Projects:","1. AI Photo Platform - Face recognition for intelligent photo organization","2. Portfolio Website - You're looking at it right now!","3. ML Research Tool - Natural language processing for scientific papers","4. Real-time Collaboration App - WebRTC and WebSockets for seamless teamwork","",'Type "project [number]" for more details!'],"project 1":["\uD83D\uDCF7 AI Photo Platform","A machine learning application that uses facial recognition to organize and tag photos.","Tech stack: React, TypeScript, PyTorch, AWS S3, Tailwind CSS","Features: Face recognition, automatic tagging, search by person, cloud storage"],"project 2":["\uD83C\uDF10 Portfolio Website","An interactive portfolio showcasing my projects and skills with 3D elements.","Tech stack: React, Three.js, Framer Motion, Tailwind CSS","Features: 3D visualization, interactive components, responsive design"],"project 3":["\uD83D\uDCDA ML Research Tool","An AI-powered tool that helps researchers find relevant papers and extract insights.","Tech stack: Python, TensorFlow, FastAPI, React","Features: Paper recommendation, text summarization, citation network analysis"],"project 4":["\uD83D\uDC65 Real-time Collaboration App","A platform for teams to collaborate with document sharing and real-time editing.","Tech stack: React, Node.js, Socket.io, WebRTC, MongoDB","Features: Live document editing, video chat, project management tools"],contact:["\uD83D\uDCEB Contact Information:","Email: <EMAIL>","GitHub: github.com/greenhacker","LinkedIn: linkedin.com/in/greenhacker","Twitter: @greenhacker"],clear:[""],exit:["\uD83D\uDC4B Goodbye! You can open me again by clicking the terminal icon."]},M=()=>{let[e,t]=(0,r.useState)(!1),[n,s]=(0,r.useState)(!1),[i,l]=(0,r.useState)([{type:"bot",content:["\uD83D\uDC4B Hi there! I'm GREENHACKER's AI assistant.","I can tell you about GREENHACKER, their skills, projects, or how to get in touch.",'Type "help" to see what I can do!']}]),[d,c]=(0,r.useState)(""),[u,m]=(0,r.useState)(!1),p=(0,r.useRef)(null),h=(0,r.useRef)(null);(0,r.useEffect)(()=>{p.current?.scrollIntoView({behavior:"smooth"})},[i]),(0,r.useEffect)(()=>{e&&h.current?.focus()},[e]);let x=()=>{t(!e)},f=e=>{let n=e.toLowerCase().trim();if("exit"===n){l([...i,{type:"user",content:[e]},{type:"bot",content:O.exit}]),setTimeout(()=>t(!1),1e3);return}return"clear"===n?void l([]):O[n]?void l([...i,{type:"user",content:[e]},{type:"bot",content:O[n]}]):void(l([...i,{type:"user",content:[e]}]),m(!0),setTimeout(()=>{let t=v(e);l(e=>[...e,{type:"bot",content:t}]),m(!1)},1e3+1e3*Math.random()))},v=e=>{let t=e.toLowerCase();if(t.includes("hi")||t.includes("hello")||t.includes("hey"))return["Hello! How can I help you today? \uD83D\uDE0A",'Type "help" to see what I can do.'];if(t.includes("thanks")||t.includes("thank you"))return["You're welcome! Anything else you'd like to know?"];if(t.includes("experience")||t.includes("work"))return["GREENHACKER has over 5 years of experience in full-stack development and machine learning projects.","They've worked with startups and enterprise companies on various AI-powered applications."];if(t.includes("education"))return["GREENHACKER has a Master's degree in Computer Science with a specialization in Artificial Intelligence.","They're also continually learning through courses and self-study."];if(t.includes("name"))return["My name is GreenBot! I'm GREENHACKER's AI assistant."];else return["I'm not sure I understand that query.",'Type "help" to see what commands are available.']};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(A.P.button,{className:"fixed bottom-8 right-8 bg-neon-green text-black h-12 w-12 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform z-50",whileHover:{scale:1.1},whileTap:{scale:.9},onClick:x,children:(0,o.jsx)(D.A,{size:20})}),(0,o.jsx)(z.N,{children:e&&(0,o.jsxs)(A.P.div,{className:`fixed ${n?"inset-4 md:inset-10":"bottom-24 right-8 w-[350px] md:w-[400px] h-[500px]"} bg-black border border-neon-green/50 rounded-lg shadow-lg overflow-hidden z-50 flex flex-col`,initial:{opacity:0,y:50},animate:{opacity:1,y:0},exit:{opacity:0,y:50},transition:{duration:.3},children:[(0,o.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-neon-green/30 bg-black",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)(D.A,{className:"text-neon-green mr-2",size:18}),(0,o.jsx)("h3",{className:"text-neon-green font-mono text-sm",children:"GREENHACKER Terminal"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("button",{className:"text-neon-green hover:text-white transition-colors focus:outline-none",onClick:()=>{s(!n)},children:n?(0,o.jsx)(H.A,{size:16}):(0,o.jsx)(G.A,{size:16})}),(0,o.jsx)("button",{className:"text-neon-green hover:text-white transition-colors focus:outline-none",onClick:x,children:(0,o.jsx)(g.A,{size:16})})]})]}),(0,o.jsx)("div",{className:"flex-grow overflow-y-auto p-4",style:{backgroundColor:"#0d1117"},children:(0,o.jsxs)("div",{className:"space-y-4",children:[i.map((e,t)=>(0,o.jsxs)("div",{className:`${"user"===e.type?"ml-auto max-w-[80%]":"mr-auto max-w-[80%]"}`,children:[(0,o.jsx)("div",{className:`rounded-lg p-3 ${"user"===e.type?"bg-neon-green/20 text-white":"bg-github-light text-neon-green"}`,children:e.content.map((e,t)=>(0,o.jsx)(a().Fragment,{children:""===e?(0,o.jsx)("br",{}):(0,o.jsx)("p",{className:"font-mono text-sm",children:e})},t))}),(0,o.jsxs)("p",{className:"text-xs text-github-text mt-1",children:["user"===e.type?"You":"GREENHACKER Bot"," • ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]},t)),u&&(0,o.jsx)("div",{className:"mr-auto",children:(0,o.jsx)("div",{className:"bg-github-light rounded-lg p-3 max-w-[80%]",children:(0,o.jsxs)("div",{className:"flex space-x-1",children:[(0,o.jsx)("div",{className:"h-2 w-2 bg-neon-green rounded-full animate-bounce"}),(0,o.jsx)("div",{className:"h-2 w-2 bg-neon-green rounded-full animate-bounce",style:{animationDelay:"0.2s"}}),(0,o.jsx)("div",{className:"h-2 w-2 bg-neon-green rounded-full animate-bounce",style:{animationDelay:"0.4s"}})]})})}),(0,o.jsx)("div",{ref:p})]})}),(0,o.jsx)("form",{onSubmit:e=>{e.preventDefault(),d.trim()&&(f(d),c(""))},className:"p-3 border-t border-neon-green/30 bg-github-dark",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("span",{className:"text-neon-green font-mono mr-2",children:"$"}),(0,o.jsx)("input",{ref:h,type:"text",value:d,onChange:e=>c(e.target.value),className:"flex-grow bg-transparent border-none text-white font-mono focus:outline-none text-sm",placeholder:"Type a message or command..."}),(0,o.jsx)("button",{type:"submit",className:"text-neon-green hover:text-white transition-colors focus:outline-none",children:(0,o.jsx)(F.A,{size:16})})]})})]})})]})},_=new s.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1}}});function K({children:e}){let[t,n]=(0,r.useState)(!0),[s,a]=(0,r.useState)(!1);return(0,o.jsx)(m,{children:(0,o.jsx)(i.Ht,{client:_,children:(0,o.jsxs)(c,{children:[(0,o.jsx)(k,{}),(0,o.jsx)(T,{}),t&&(0,o.jsx)(S,{}),(0,o.jsx)(L,{}),!s&&(0,o.jsx)(R,{}),e,(0,o.jsx)(M,{})]})})})}},94431:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l,metadata:()=>a});var o=n(37413),s=n(7339),i=n.n(s);n(61135);var r=n(29519);let a={title:"GREENHACKER | Developer Portfolio",description:"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.",keywords:["developer","portfolio","React","Next.js","TypeScript","AI","machine learning"],authors:[{name:"GreenHacker"}],creator:"GreenHacker",publisher:"GreenHacker",icons:{icon:"/logo.jpg",shortcut:"/logo.jpg",apple:"/logo.jpg"},openGraph:{type:"website",locale:"en_US",url:"https://greenhacker.dev",title:"GREENHACKER | Developer Portfolio",description:"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.",siteName:"GreenHacker Portfolio"},twitter:{card:"summary_large_image",title:"GREENHACKER | Developer Portfolio",description:"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.",creator:"@greenhacker"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function l({children:e}){return(0,o.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,o.jsxs)("head",{children:[(0,o.jsx)("link",{rel:"manifest",href:"/site.webmanifest"}),(0,o.jsx)("meta",{name:"theme-color",content:"#0d1117"}),(0,o.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"})]}),(0,o.jsx)("body",{className:i().className,suppressHydrationWarning:!0,children:(0,o.jsx)(r.Providers,{children:e})})]})}}};