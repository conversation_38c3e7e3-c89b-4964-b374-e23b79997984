"use strict";exports.id=802,exports.ids=[802],exports.modules={98802:(e,r,t)=>{t.r(r),t.d(r,{default:()=>n});let n=(()=>{var e="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return function(r={}){r.ready=new Promise((e,r)=>{t=e,n=r});var t,n,o,a,s,i,u,l,d,c,p,h,f,m,v,y,g,w,E,b,_,$,k,P,C,T,D,F,A,S=Object.assign({},r),M="./this.program",x="";"undefined"!=typeof document&&document.currentScript&&(x=document.currentScript.src),e&&(x=e),x=0!==x.indexOf("blob:")?x.substr(0,x.replace(/[?#].*/,"").lastIndexOf("/")+1):"",o=e=>{var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},a=(e,r,t)=>{var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=()=>{if(200==n.status||0==n.status&&n.response)return void r(n.response);t()},n.onerror=t,n.send(null)};var j=r.print||console.log.bind(console),O=r.printErr||console.error.bind(console);Object.assign(r,S),S=null,r.arguments&&r.arguments,r.thisProgram&&(M=r.thisProgram),r.quit&&r.quit,r.wasmBinary&&(i=r.wasmBinary),r.noExitRuntime,"object"!=typeof WebAssembly&&Y("no native wasm support detected");var R=!1;function W(){var e=u.buffer;r.HEAP8=l=new Int8Array(e),r.HEAP16=c=new Int16Array(e),r.HEAPU8=d=new Uint8Array(e),r.HEAPU16=p=new Uint16Array(e),r.HEAP32=h=new Int32Array(e),r.HEAPU32=f=new Uint32Array(e),r.HEAPF32=m=new Float32Array(e),r.HEAPF64=v=new Float64Array(e)}var z=[],N=[],B=[],I=0,H=null,L=null;function U(e){I++,r.monitorRunDependencies&&r.monitorRunDependencies(I)}function V(e){if(I--,r.monitorRunDependencies&&r.monitorRunDependencies(I),0==I&&(null!==H&&(clearInterval(H),H=null),L)){var t=L;L=null,t()}}function Y(e){r.onAbort&&r.onAbort(e),O(e="Aborted("+e+")"),R=!0,e+=". Build with -sASSERTIONS for more info.";var t=new WebAssembly.RuntimeError(e);throw n(t),t}function q(e){return e.startsWith("data:application/octet-stream;base64,")}function X(e){if(e==y&&i)return new Uint8Array(i);if(s)return s(e);throw"both async and sync fetching of the wasm failed"}function G(e,r,t){return(!i&&"function"==typeof fetch?fetch(e,{credentials:"same-origin"}).then(r=>{if(!r.ok)throw"failed to load wasm binary file at '"+e+"'";return r.arrayBuffer()}).catch(()=>X(e)):Promise.resolve().then(()=>X(e))).then(e=>WebAssembly.instantiate(e,r)).then(e=>e).then(t,e=>{O(`failed to asynchronously prepare wasm: ${e}`),Y(e)})}q(y="process.wasm")||(T=y,y=r.locateFile?r.locateFile(T,x):x+T);var J=e=>{for(;e.length>0;)e.shift()(r)};function K(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){f[this.ptr+4>>2]=e},this.get_type=function(){return f[this.ptr+4>>2]},this.set_destructor=function(e){f[this.ptr+8>>2]=e},this.get_destructor=function(){return f[this.ptr+8>>2]},this.set_caught=function(e){e=+!!e,l[this.ptr+12|0]=e},this.get_caught=function(){return 0!=l[this.ptr+12|0]},this.set_rethrown=function(e){e=+!!e,l[this.ptr+13|0]=e},this.get_rethrown=function(){return 0!=l[this.ptr+13|0]},this.init=function(e,r){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(r)},this.set_adjusted_ptr=function(e){f[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return f[this.ptr+16>>2]},this.get_exception_ptr=function(){if(r2(this.get_type()))return f[this.excPtr>>2];var e=this.get_adjusted_ptr();return 0!==e?e:this.excPtr}}var Z=0,Q=0,ee={},er=e=>{for(;e.length;){var r=e.pop();e.pop()(r)}};function et(e){return this.fromWireType(h[e>>2])}var en={},eo={},ea={},es=e=>{throw new E(e)},ei=(e,r,t)=>{function n(r){var n=t(r);n.length!==e.length&&es("Mismatched type converter count");for(var o=0;o<e.length;++o)ec(e[o],n[o])}e.forEach(function(e){ea[e]=r});var o=Array(r.length),a=[],s=0;r.forEach((e,r)=>{eo.hasOwnProperty(e)?o[r]=eo[e]:(a.push(e),en.hasOwnProperty(e)||(en[e]=[]),en[e].push(()=>{o[r]=eo[e],++s===a.length&&n(o)}))}),0===a.length&&n(o)},eu={},el=e=>{for(var r="",t=e;d[t];)r+=b[d[t++]];return r},ed=e=>{throw new _(e)};function ec(e,r,t={}){if(!("argPackAdvance"in r))throw TypeError("registerType registeredInstance requires argPackAdvance");return function(e,r,t={}){var n=r.name;if(e||ed(`type "${n}" must have a positive integer typeid pointer`),eo.hasOwnProperty(e))if(t.ignoreDuplicateRegistrations)return;else ed(`Cannot register type '${n}' twice`);if(eo[e]=r,delete ea[e],en.hasOwnProperty(e)){var o=en[e];delete en[e],o.forEach(e=>e())}}(e,r,t)}var ep=e=>({count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType}),eh=e=>{ed(e.$$.ptrType.registeredClass.name+" instance already deleted")},ef=!1,em=e=>{},ev=e=>{e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)},ey=e=>{e.count.value-=1,0===e.count.value&&ev(e)},eg=(e,r,t)=>{if(r===t)return e;if(void 0===t.baseClass)return null;var n=eg(e,r,t.baseClass);return null===n?null:t.downcast(n)},ew={},eE=[],eb=()=>{for(;eE.length;){var e=eE.pop();e.$$.deleteScheduled=!1,e.delete()}},e_={},e$=(e,r)=>{for(void 0===r&&ed("ptr should not be undefined");e.baseClass;)r=e.upcast(r),e=e.baseClass;return r},ek=(e,r)=>e_[r=e$(e,r)],eP=(e,r)=>(r.ptrType&&r.ptr||es("makeClassHandle requires ptr and ptrType"),!!r.smartPtrType!=!!r.smartPtr&&es("Both smartPtrType and smartPtr must be specified"),r.count={value:1},eC(Object.create(e,{$$:{value:r}}))),eC=e=>"undefined"==typeof FinalizationRegistry?(eC=e=>e,e):(ef=new FinalizationRegistry(e=>{ey(e.$$)}),eC=e=>{var r=e.$$;return r.smartPtr&&ef.register(e,{$$:r},e),e},em=e=>ef.unregister(e),eC(e));function eT(){}var eD=e=>{if(void 0===e)return"_unknown";var r=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=48&&r<=57?`_${e}`:e};function eF(e,r){return({[e=eD(e)]:function(){return r.apply(this,arguments)}})[e]}var eA=(e,r,t)=>{if(void 0===e[r].overloadTable){var n=e[r];e[r]=function(){return e[r].overloadTable.hasOwnProperty(arguments.length)||ed(`Function '${t}' called with an invalid number of arguments (${arguments.length}) - expects one of (${e[r].overloadTable})!`),e[r].overloadTable[arguments.length].apply(this,arguments)},e[r].overloadTable=[],e[r].overloadTable[n.argCount]=n}},eS=(e,t,n)=>{r.hasOwnProperty(e)?((void 0===n||void 0!==r[e].overloadTable&&void 0!==r[e].overloadTable[n])&&ed(`Cannot register public name '${e}' twice`),eA(r,e,e),r.hasOwnProperty(n)&&ed(`Cannot register multiple overloads of a function with the same number of arguments (${n})!`),r[e].overloadTable[n]=t):(r[e]=t,void 0!==n&&(r[e].numArguments=n))};function eM(e,r,t,n,o,a,s,i){this.name=e,this.constructor=r,this.instancePrototype=t,this.rawDestructor=n,this.baseClass=o,this.getActualType=a,this.upcast=s,this.downcast=i,this.pureVirtualFunctions=[]}var ex=(e,r,t)=>{for(;r!==t;)r.upcast||ed(`Expected null or instance of ${t.name}, got an instance of ${r.name}`),e=r.upcast(e),r=r.baseClass;return e};function ej(e,r){if(null===r)return this.isReference&&ed(`null is not a valid ${this.name}`),0;r.$$||ed(`Cannot pass "${e3(r)}" as a ${this.name}`),r.$$.ptr||ed(`Cannot pass deleted object as a pointer of type ${this.name}`);var t=r.$$.ptrType.registeredClass;return ex(r.$$.ptr,t,this.registeredClass)}function eO(e,r){if(null===r)return(this.isReference&&ed(`null is not a valid ${this.name}`),this.isSmartPointer)?(t=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,t),t):0;r.$$||ed(`Cannot pass "${e3(r)}" as a ${this.name}`),r.$$.ptr||ed(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.isConst&&r.$$.ptrType.isConst&&ed(`Cannot convert argument of type ${r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name} to parameter type ${this.name}`);var t,n=r.$$.ptrType.registeredClass;if(t=ex(r.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===r.$$.smartPtr&&ed("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:r.$$.smartPtrType===this?t=r.$$.smartPtr:ed(`Cannot convert argument of type ${r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name} to parameter type ${this.name}`);break;case 1:t=r.$$.smartPtr;break;case 2:if(r.$$.smartPtrType===this)t=r.$$.smartPtr;else{var o=r.clone();t=this.rawShare(t,e0.toHandle(()=>o.delete())),null!==e&&e.push(this.rawDestructor,t)}break;default:ed("Unsupporting sharing policy")}return t}function eR(e,r){if(null===r)return this.isReference&&ed(`null is not a valid ${this.name}`),0;r.$$||ed(`Cannot pass "${e3(r)}" as a ${this.name}`),r.$$.ptr||ed(`Cannot pass deleted object as a pointer of type ${this.name}`),r.$$.ptrType.isConst&&ed(`Cannot convert argument of type ${r.$$.ptrType.name} to parameter type ${this.name}`);var t=r.$$.ptrType.registeredClass;return ex(r.$$.ptr,t,this.registeredClass)}function eW(e){return this.fromWireType(f[e>>2])}function ez(e,r,t,n,o,a,s,i,u,l,d){this.name=e,this.registeredClass=r,this.isReference=t,this.isConst=n,this.isSmartPointer=o,this.pointeeType=a,this.sharingPolicy=s,this.rawGetPointee=i,this.rawConstructor=u,this.rawShare=l,this.rawDestructor=d,o||void 0!==r.baseClass?this.toWireType=eO:(n?this.toWireType=ej:this.toWireType=eR,this.destructorFunction=null)}var eN=(e,t,n)=>{r.hasOwnProperty(e)||es("Replacing nonexistant public symbol"),void 0!==r[e].overloadTable&&void 0!==n?r[e].overloadTable[n]=t:(r[e]=t,r[e].argCount=n)},eB=(e,t,n)=>{var o=r["dynCall_"+e];return n&&n.length?o.apply(null,[t].concat(n)):o.call(null,t)},eI=[],eH=e=>{var r=eI[e];return r||(e>=eI.length&&(eI.length=e+1),eI[e]=r=k.get(e)),r},eL=(e,r,t)=>e.includes("j")?eB(e,r,t):eH(r).apply(null,t),eU=(e,r)=>{var t=[];return function(){return t.length=0,Object.assign(t,arguments),eL(e,r,t)}},eV=(e,r)=>{var t=(e=el(e)).includes("j")?eU(e,r):eH(r);return"function"!=typeof t&&ed(`unknown function pointer with signature ${e}: ${r}`),t},eY=e=>{var r=rZ(e),t=el(r);return rK(r),t},eq=(e,r)=>{var t=[],n={};throw r.forEach(function e(r){if(!n[r]&&!eo[r]){if(ea[r])return void ea[r].forEach(e);t.push(r),n[r]=!0}}),new P(`${e}: `+t.map(eY).join([", "]))},eX=(e,r)=>{for(var t=[],n=0;n<e;n++)t.push(f[r+4*n>>2]);return t};function eG(e,r,t,n,o,a){var s=r.length;s<2&&ed("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==t,u=!1,l=1;l<r.length;++l)if(null!==r[l]&&void 0===r[l].destructorFunction){u=!0;break}for(var d="void"!==r[0].name,c="",p="",l=0;l<s-2;++l)c+=(0!==l?", ":"")+"arg"+l,p+=(0!==l?", ":"")+"arg"+l+"Wired";var h=`
        return function ${eD(e)}(${c}) {
        if (arguments.length !== ${s-2}) {
          throwBindingError('function ${e} called with ' + arguments.length + ' arguments, expected ${s-2}');
        }`;u&&(h+="var destructors = [];\n");var f=u?"destructors":"null",m=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[ed,n,o,er,r[0],r[1]];i&&(h+="var thisWired = classParam.toWireType("+f+", this);\n");for(var l=0;l<s-2;++l)h+="var arg"+l+"Wired = argType"+l+".toWireType("+f+", arg"+l+"); // "+r[l+2].name+"\n",m.push("argType"+l),v.push(r[l+2]);if(i&&(p="thisWired"+(p.length>0?", ":"")+p),h+=(d||a?"var rv = ":"")+"invoker(fn"+(p.length>0?", ":"")+p+");\n",u)h+="runDestructors(destructors);\n";else for(var l=i?1:2;l<r.length;++l){var y=1===l?"thisWired":"arg"+(l-2)+"Wired";null!==r[l].destructorFunction&&(h+=y+"_dtor("+y+"); // "+r[l].name+"\n",m.push(y+"_dtor"),v.push(r[l].destructorFunction))}return d&&(h+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),h+="}\n",m.push(h),(function(e,r){if(!(e instanceof Function))throw TypeError(`new_ called with constructor type ${typeof e} which is not a function`);var t=eF(e.name||"unknownFunctionName",function(){});t.prototype=e.prototype;var n=new t,o=e.apply(n,r);return o instanceof Object?o:n})(Function,m).apply(null,v)}var eJ=(e,r,t)=>(e instanceof Object||ed(`${t} with invalid "this": ${e}`),e instanceof r.registeredClass.constructor||ed(`${t} incompatible with "this" of type ${e.constructor.name}`),e.$$.ptr||ed(`cannot call emscripten binding method ${t} on deleted object`),ex(e.$$.ptr,e.$$.ptrType.registeredClass,r.registeredClass));function eK(){this.allocated=[void 0],this.freelist=[]}var eZ=new eK,eQ=e=>{e>=eZ.reserved&&0==--eZ.get(e).refcount&&eZ.free(e)},e0={toValue:e=>(e||ed("Cannot use deleted val. handle = "+e),eZ.get(e).value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return eZ.allocate({refcount:1,value:e})}}},e1=(e,r,t)=>{switch(r){case 1:return t?function(e){return this.fromWireType(l[0|e])}:function(e){return this.fromWireType(d[0|e])};case 2:return t?function(e){return this.fromWireType(c[e>>1])}:function(e){return this.fromWireType(p[e>>1])};case 4:return t?function(e){return this.fromWireType(h[e>>2])}:function(e){return this.fromWireType(f[e>>2])};default:throw TypeError(`invalid integer width (${r}): ${e}`)}},e2=(e,r)=>{var t=eo[e];return void 0===t&&ed(r+" has unknown type "+eY(e)),t},e3=e=>{if(null===e)return"null";var r=typeof e;return"object"===r||"array"===r||"function"===r?e.toString():""+e},e4=(e,r)=>{switch(r){case 4:return function(e){return this.fromWireType(m[e>>2])};case 8:return function(e){return this.fromWireType(v[e>>3])};default:throw TypeError(`invalid float width (${r}): ${e}`)}},e6=(e,r,t)=>{switch(r){case 1:return t?e=>l[0|e]:e=>d[0|e];case 2:return t?e=>c[e>>1]:e=>p[e>>1];case 4:return t?e=>h[e>>2]:e=>f[e>>2];default:throw TypeError(`invalid integer width (${r}): ${e}`)}},e5=(e,r,t,n)=>{if(!(n>0))return 0;for(var o=t,a=t+n-1,s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&e.charCodeAt(++s)),i<=127){if(t>=a)break;r[t++]=i}else if(i<=2047){if(t+1>=a)break;r[t++]=192|i>>6,r[t++]=128|63&i}else if(i<=65535){if(t+2>=a)break;r[t++]=224|i>>12,r[t++]=128|i>>6&63,r[t++]=128|63&i}else{if(t+3>=a)break;r[t++]=240|i>>18,r[t++]=128|i>>12&63,r[t++]=128|i>>6&63,r[t++]=128|63&i}}return r[t]=0,t-o},e8=(e,r,t)=>e5(e,d,r,t),e7=e=>{for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n<=127?r++:n<=2047?r+=2:n>=55296&&n<=57343?(r+=4,++t):r+=3}return r},e9="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,re=(e,r,t)=>{for(var n=r+t,o=r;e[o]&&!(o>=n);)++o;if(o-r>16&&e.buffer&&e9)return e9.decode(e.subarray(r,o));for(var a="";r<o;){var s=e[r++];if(!(128&s)){a+=String.fromCharCode(s);continue}var i=63&e[r++];if((224&s)==192){a+=String.fromCharCode((31&s)<<6|i);continue}var u=63&e[r++];if((s=(240&s)==224?(15&s)<<12|i<<6|u:(7&s)<<18|i<<12|u<<6|63&e[r++])<65536)a+=String.fromCharCode(s);else{var l=s-65536;a+=String.fromCharCode(55296|l>>10,56320|1023&l)}}return a},rr=(e,r)=>e?re(d,e,r):"",rt="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,rn=(e,r)=>{for(var t=e,n=t>>1,o=n+r/2;!(n>=o)&&p[n];)++n;if((t=n<<1)-e>32&&rt)return rt.decode(d.subarray(e,t));for(var a="",s=0;!(s>=r/2);++s){var i=c[e+2*s>>1];if(0==i)break;a+=String.fromCharCode(i)}return a},ro=(e,r,t)=>{if(void 0===t&&(t=0x7fffffff),t<2)return 0;for(var n=r,o=(t-=2)<2*e.length?t/2:e.length,a=0;a<o;++a){var s=e.charCodeAt(a);c[r>>1]=s,r+=2}return c[r>>1]=0,r-n},ra=e=>2*e.length,rs=(e,r)=>{for(var t=0,n="";!(t>=r/4);){var o=h[e+4*t>>2];if(0==o)break;if(++t,o>=65536){var a=o-65536;n+=String.fromCharCode(55296|a>>10,56320|1023&a)}else n+=String.fromCharCode(o)}return n},ri=(e,r,t)=>{if(void 0===t&&(t=0x7fffffff),t<4)return 0;for(var n=r,o=n+t-4,a=0;a<e.length;++a){var s=e.charCodeAt(a);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&e.charCodeAt(++a)),h[r>>2]=s,(r+=4)+4>o)break}return h[r>>2]=0,r-n},ru=e=>{for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n>=55296&&n<=57343&&++t,r+=4}return r},rl=()=>0x80000000,rd=e=>{var r=(e-u.buffer.byteLength+65535)/65536;try{return u.grow(r),W(),1}catch(e){}},rc={},rp=()=>M||"./this.program",rh=()=>{if(!rh.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:rp()};for(var r in rc)void 0===rc[r]?delete e[r]:e[r]=rc[r];var t=[];for(var r in e)t.push(`${r}=${e[r]}`);rh.strings=t}return rh.strings},rf=(e,r)=>{for(var t=0;t<e.length;++t)l[0|r++]=e.charCodeAt(t);l[0|r]=0},rm={isAbs:e=>"/"===e.charAt(0),splitPath:e=>/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1),normalizeArray:(e,r)=>{for(var t=0,n=e.length-1;n>=0;n--){var o=e[n];"."===o?e.splice(n,1):".."===o?(e.splice(n,1),t++):t&&(e.splice(n,1),t--)}if(r)for(;t;t--)e.unshift("..");return e},normalize:e=>{var r=rm.isAbs(e),t="/"===e.substr(-1);return(e=rm.normalizeArray(e.split("/").filter(e=>!!e),!r).join("/"))||r||(e="."),e&&t&&(e+="/"),(r?"/":"")+e},dirname:e=>{var r=rm.splitPath(e),t=r[0],n=r[1];return t||n?(n&&(n=n.substr(0,n.length-1)),t+n):"."},basename:e=>{if("/"===e)return"/";var r=(e=(e=rm.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return -1===r?e:e.substr(r+1)},join:function(){var e=Array.prototype.slice.call(arguments);return rm.normalize(e.join("/"))},join2:(e,r)=>rm.normalize(e+"/"+r)},rv=()=>{if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return e=>crypto.getRandomValues(e);Y("initRandomDevice")},ry=e=>(ry=rv())(e),rg={resolve:function(){for(var e="",r=!1,t=arguments.length-1;t>=-1&&!r;t--){var n=t>=0?arguments[t]:rS.cwd();if("string"!=typeof n)throw TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,r=rm.isAbs(n)}return e=rm.normalizeArray(e.split("/").filter(e=>!!e),!r).join("/"),(r?"/":"")+e||"."},relative:(e,r)=>{function t(e){for(var r=0;r<e.length&&""===e[r];r++);for(var t=e.length-1;t>=0&&""===e[t];t--);return r>t?[]:e.slice(r,t-r+1)}e=rg.resolve(e).substr(1),r=rg.resolve(r).substr(1);for(var n=t(e.split("/")),o=t(r.split("/")),a=Math.min(n.length,o.length),s=a,i=0;i<a;i++)if(n[i]!==o[i]){s=i;break}for(var u=[],i=s;i<n.length;i++)u.push("..");return(u=u.concat(o.slice(s))).join("/")}},rw=[];function rE(e,r,t){var n=Array(t>0?t:e7(e)+1),o=e5(e,n,0,n.length);return r&&(n.length=o),n}var rb=()=>{if(!rw.length){var e=null;if("function"==typeof readline&&null!==(e=readline())&&(e+="\n"),!e)return null;rw=rE(e,!0)}return rw.shift()},r_={ttys:[],init(){},shutdown(){},register(e,r){r_.ttys[e]={input:[],output:[],ops:r},rS.registerDevice(e,r_.stream_ops)},stream_ops:{open(e){var r=r_.ttys[e.node.rdev];if(!r)throw new rS.ErrnoError(43);e.tty=r,e.seekable=!1},close(e){e.tty.ops.fsync(e.tty)},fsync(e){e.tty.ops.fsync(e.tty)},read(e,r,t,n,o){if(!e.tty||!e.tty.ops.get_char)throw new rS.ErrnoError(60);for(var a,s=0,i=0;i<n;i++){try{a=e.tty.ops.get_char(e.tty)}catch(e){throw new rS.ErrnoError(29)}if(void 0===a&&0===s)throw new rS.ErrnoError(6);if(null==a)break;s++,r[t+i]=a}return s&&(e.node.timestamp=Date.now()),s},write(e,r,t,n,o){if(!e.tty||!e.tty.ops.put_char)throw new rS.ErrnoError(60);try{for(var a=0;a<n;a++)e.tty.ops.put_char(e.tty,r[t+a])}catch(e){throw new rS.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),a}},default_tty_ops:{get_char:e=>rb(),put_char(e,r){null===r||10===r?(j(re(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},fsync(e){e.output&&e.output.length>0&&(j(re(e.output,0)),e.output=[])},ioctl_tcgets:e=>({c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}),ioctl_tcsets:(e,r,t)=>0,ioctl_tiocgwinsz:e=>[24,80]},default_tty1_ops:{put_char(e,r){null===r||10===r?(O(re(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},fsync(e){e.output&&e.output.length>0&&(O(re(e.output,0)),e.output=[])}}},r$=e=>{Y()},rk={ops_table:null,mount:e=>rk.createNode(null,"/",16895,0),createNode(e,r,t,n){if(rS.isBlkdev(t)||rS.isFIFO(t))throw new rS.ErrnoError(63);rk.ops_table||(rk.ops_table={dir:{node:{getattr:rk.node_ops.getattr,setattr:rk.node_ops.setattr,lookup:rk.node_ops.lookup,mknod:rk.node_ops.mknod,rename:rk.node_ops.rename,unlink:rk.node_ops.unlink,rmdir:rk.node_ops.rmdir,readdir:rk.node_ops.readdir,symlink:rk.node_ops.symlink},stream:{llseek:rk.stream_ops.llseek}},file:{node:{getattr:rk.node_ops.getattr,setattr:rk.node_ops.setattr},stream:{llseek:rk.stream_ops.llseek,read:rk.stream_ops.read,write:rk.stream_ops.write,allocate:rk.stream_ops.allocate,mmap:rk.stream_ops.mmap,msync:rk.stream_ops.msync}},link:{node:{getattr:rk.node_ops.getattr,setattr:rk.node_ops.setattr,readlink:rk.node_ops.readlink},stream:{}},chrdev:{node:{getattr:rk.node_ops.getattr,setattr:rk.node_ops.setattr},stream:rS.chrdev_stream_ops}});var o=rS.createNode(e,r,t,n);return rS.isDir(o.mode)?(o.node_ops=rk.ops_table.dir.node,o.stream_ops=rk.ops_table.dir.stream,o.contents={}):rS.isFile(o.mode)?(o.node_ops=rk.ops_table.file.node,o.stream_ops=rk.ops_table.file.stream,o.usedBytes=0,o.contents=null):rS.isLink(o.mode)?(o.node_ops=rk.ops_table.link.node,o.stream_ops=rk.ops_table.link.stream):rS.isChrdev(o.mode)&&(o.node_ops=rk.ops_table.chrdev.node,o.stream_ops=rk.ops_table.chrdev.stream),o.timestamp=Date.now(),e&&(e.contents[r]=o,e.timestamp=o.timestamp),o},getFileDataAsTypedArray:e=>e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0),expandFileStorage(e,r){var t=e.contents?e.contents.length:0;if(!(t>=r)){r=Math.max(r,t*(t<1048576?2:1.125)>>>0),0!=t&&(r=Math.max(r,256));var n=e.contents;e.contents=new Uint8Array(r),e.usedBytes>0&&e.contents.set(n.subarray(0,e.usedBytes),0)}},resizeFileStorage(e,r){if(e.usedBytes!=r)if(0==r)e.contents=null,e.usedBytes=0;else{var t=e.contents;e.contents=new Uint8Array(r),t&&e.contents.set(t.subarray(0,Math.min(r,e.usedBytes))),e.usedBytes=r}},node_ops:{getattr(e){var r={};return r.dev=rS.isChrdev(e.mode)?e.id:1,r.ino=e.id,r.mode=e.mode,r.nlink=1,r.uid=0,r.gid=0,r.rdev=e.rdev,rS.isDir(e.mode)?r.size=4096:rS.isFile(e.mode)?r.size=e.usedBytes:rS.isLink(e.mode)?r.size=e.link.length:r.size=0,r.atime=new Date(e.timestamp),r.mtime=new Date(e.timestamp),r.ctime=new Date(e.timestamp),r.blksize=4096,r.blocks=Math.ceil(r.size/r.blksize),r},setattr(e,r){void 0!==r.mode&&(e.mode=r.mode),void 0!==r.timestamp&&(e.timestamp=r.timestamp),void 0!==r.size&&rk.resizeFileStorage(e,r.size)},lookup(e,r){throw rS.genericErrors[44]},mknod:(e,r,t,n)=>rk.createNode(e,r,t,n),rename(e,r,t){if(rS.isDir(e.mode)){var n;try{n=rS.lookupNode(r,t)}catch(e){}if(n)for(var o in n.contents)throw new rS.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=t,r.contents[t]=e,r.timestamp=e.parent.timestamp,e.parent=r},unlink(e,r){delete e.contents[r],e.timestamp=Date.now()},rmdir(e,r){var t=rS.lookupNode(e,r);for(var n in t.contents)throw new rS.ErrnoError(55);delete e.contents[r],e.timestamp=Date.now()},readdir(e){var r=[".",".."];for(var t in e.contents)e.contents.hasOwnProperty(t)&&r.push(t);return r},symlink(e,r,t){var n=rk.createNode(e,r,41471,0);return n.link=t,n},readlink(e){if(!rS.isLink(e.mode))throw new rS.ErrnoError(28);return e.link}},stream_ops:{read(e,r,t,n,o){var a=e.node.contents;if(o>=e.node.usedBytes)return 0;var s=Math.min(e.node.usedBytes-o,n);if(s>8&&a.subarray)r.set(a.subarray(o,o+s),t);else for(var i=0;i<s;i++)r[t+i]=a[o+i];return s},write(e,r,t,n,o,a){if(r.buffer===l.buffer&&(a=!1),!n)return 0;var s=e.node;if(s.timestamp=Date.now(),r.subarray&&(!s.contents||s.contents.subarray)){if(a)return s.contents=r.subarray(t,t+n),s.usedBytes=n,n;else if(0===s.usedBytes&&0===o)return s.contents=r.slice(t,t+n),s.usedBytes=n,n;else if(o+n<=s.usedBytes)return s.contents.set(r.subarray(t,t+n),o),n}if(rk.expandFileStorage(s,o+n),s.contents.subarray&&r.subarray)s.contents.set(r.subarray(t,t+n),o);else for(var i=0;i<n;i++)s.contents[o+i]=r[t+i];return s.usedBytes=Math.max(s.usedBytes,o+n),n},llseek(e,r,t){var n=r;if(1===t?n+=e.position:2===t&&rS.isFile(e.node.mode)&&(n+=e.node.usedBytes),n<0)throw new rS.ErrnoError(28);return n},allocate(e,r,t){rk.expandFileStorage(e.node,r+t),e.node.usedBytes=Math.max(e.node.usedBytes,r+t)},mmap(e,r,t,n,o){if(!rS.isFile(e.node.mode))throw new rS.ErrnoError(43);var a,s,i=e.node.contents;if(2&o||i.buffer!==l.buffer){if((t>0||t+r<i.length)&&(i=i.subarray?i.subarray(t,t+r):Array.prototype.slice.call(i,t,t+r)),s=!0,!(a=r$(r)))throw new rS.ErrnoError(48);l.set(i,a)}else s=!1,a=i.byteOffset;return{ptr:a,allocated:s}},msync:(e,r,t,n,o)=>(rk.stream_ops.write(e,r,0,n,t,!1),0)}},rP=(e,r,t,n)=>{var o=n?"":`al ${e}`;a(e,t=>{var n;n=`Loading data file "${e}" failed (no arrayBuffer).`,t||Y(n),r(new Uint8Array(t)),o&&V(o)},r=>{if(t)t();else throw`Loading data file "${e}" failed.`}),o&&U(o)},rC=(e,r,t,n,o,a)=>rS.createDataFile(e,r,t,n,o,a),rT=r.preloadPlugins||[],rD=(e,r,t,n)=>{"undefined"!=typeof Browser&&Browser.init();var o=!1;return rT.forEach(a=>{!o&&a.canHandle(r)&&(a.handle(e,r,t,n),o=!0)}),o},rF=e=>{var r={r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090}[e];if(void 0===r)throw Error(`Unknown file open mode: ${e}`);return r},rA=(e,r)=>{var t=0;return e&&(t|=365),r&&(t|=146),t},rS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath(e,r={}){if(!(e=rg.resolve(e)))return{path:"",node:null};if((r=Object.assign({follow_mount:!0,recurse_count:0},r)).recurse_count>8)throw new rS.ErrnoError(32);for(var t=e.split("/").filter(e=>!!e),n=rS.root,o="/",a=0;a<t.length;a++){var s=a===t.length-1;if(s&&r.parent)break;if(n=rS.lookupNode(n,t[a]),o=rm.join2(o,t[a]),rS.isMountpoint(n)&&(!s||s&&r.follow_mount)&&(n=n.mounted.root),!s||r.follow)for(var i=0;rS.isLink(n.mode);){var u=rS.readlink(o);if(o=rg.resolve(rm.dirname(o),u),n=rS.lookupPath(o,{recurse_count:r.recurse_count+1}).node,i++>40)throw new rS.ErrnoError(32)}}return{path:o,node:n}},getPath(e){for(var r;;){if(rS.isRoot(e)){var t=e.mount.mountpoint;if(!r)return t;return"/"!==t[t.length-1]?`${t}/${r}`:t+r}r=r?`${e.name}/${r}`:e.name,e=e.parent}},hashName(e,r){for(var t=0,n=0;n<r.length;n++)t=(t<<5)-t+r.charCodeAt(n)|0;return(e+t>>>0)%rS.nameTable.length},hashAddNode(e){var r=rS.hashName(e.parent.id,e.name);e.name_next=rS.nameTable[r],rS.nameTable[r]=e},hashRemoveNode(e){var r=rS.hashName(e.parent.id,e.name);if(rS.nameTable[r]===e)rS.nameTable[r]=e.name_next;else for(var t=rS.nameTable[r];t;){if(t.name_next===e){t.name_next=e.name_next;break}t=t.name_next}},lookupNode(e,r){var t=rS.mayLookup(e);if(t)throw new rS.ErrnoError(t,e);for(var n=rS.hashName(e.id,r),o=rS.nameTable[n];o;o=o.name_next){var a=o.name;if(o.parent.id===e.id&&a===r)return o}return rS.lookup(e,r)},createNode(e,r,t,n){var o=new rS.FSNode(e,r,t,n);return rS.hashAddNode(o),o},destroyNode(e){rS.hashRemoveNode(e)},isRoot:e=>e===e.parent,isMountpoint:e=>!!e.mounted,isFile:e=>(61440&e)==32768,isDir:e=>(61440&e)==16384,isLink:e=>(61440&e)==40960,isChrdev:e=>(61440&e)==8192,isBlkdev:e=>(61440&e)==24576,isFIFO:e=>(61440&e)==4096,isSocket:e=>(49152&e)==49152,flagsToPermissionString(e){var r=["r","w","rw"][3&e];return 512&e&&(r+="w"),r},nodePermissions:(e,r)=>rS.ignorePermissions?0:r.includes("r")&&!(292&e.mode)||r.includes("w")&&!(146&e.mode)||r.includes("x")&&!(73&e.mode)?2:0,mayLookup(e){var r=rS.nodePermissions(e,"x");return r||2*!e.node_ops.lookup},mayCreate(e,r){try{return rS.lookupNode(e,r),20}catch(e){}return rS.nodePermissions(e,"wx")},mayDelete(e,r,t){try{n=rS.lookupNode(e,r)}catch(e){return e.errno}var n,o=rS.nodePermissions(e,"wx");if(o)return o;if(t){if(!rS.isDir(n.mode))return 54;if(rS.isRoot(n)||rS.getPath(n)===rS.cwd())return 10}else if(rS.isDir(n.mode))return 31;return 0},mayOpen:(e,r)=>e?rS.isLink(e.mode)?32:rS.isDir(e.mode)&&("r"!==rS.flagsToPermissionString(r)||512&r)?31:rS.nodePermissions(e,rS.flagsToPermissionString(r)):44,MAX_OPEN_FDS:4096,nextfd(){for(var e=0;e<=rS.MAX_OPEN_FDS;e++)if(!rS.streams[e])return e;throw new rS.ErrnoError(33)},getStreamChecked(e){var r=rS.getStream(e);if(!r)throw new rS.ErrnoError(8);return r},getStream:e=>rS.streams[e],createStream:(e,r=-1)=>(rS.FSStream||(rS.FSStream=function(){this.shared={}},rS.FSStream.prototype={},Object.defineProperties(rS.FSStream.prototype,{object:{get(){return this.node},set(e){this.node=e}},isRead:{get(){return(2097155&this.flags)!=1}},isWrite:{get(){return(2097155&this.flags)!=0}},isAppend:{get(){return 1024&this.flags}},flags:{get(){return this.shared.flags},set(e){this.shared.flags=e}},position:{get(){return this.shared.position},set(e){this.shared.position=e}}})),e=Object.assign(new rS.FSStream,e),-1==r&&(r=rS.nextfd()),e.fd=r,rS.streams[r]=e,e),closeStream(e){rS.streams[e]=null},chrdev_stream_ops:{open(e){var r=rS.getDevice(e.node.rdev);e.stream_ops=r.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek(){throw new rS.ErrnoError(70)}},major:e=>e>>8,minor:e=>255&e,makedev:(e,r)=>e<<8|r,registerDevice(e,r){rS.devices[e]={stream_ops:r}},getDevice:e=>rS.devices[e],getMounts(e){for(var r=[],t=[e];t.length;){var n=t.pop();r.push(n),t.push.apply(t,n.mounts)}return r},syncfs(e,r){"function"==typeof e&&(r=e,e=!1),rS.syncFSRequests++,rS.syncFSRequests>1&&O(`warning: ${rS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`);var t=rS.getMounts(rS.root.mount),n=0;function o(e){return rS.syncFSRequests--,r(e)}function a(e){if(e)return a.errored?void 0:(a.errored=!0,o(e));++n>=t.length&&o(null)}t.forEach(r=>{if(!r.type.syncfs)return a(null);r.type.syncfs(r,e,a)})},mount(e,r,t){var n,o="/"===t,a=!t;if(o&&rS.root)throw new rS.ErrnoError(10);if(!o&&!a){var s=rS.lookupPath(t,{follow_mount:!1});if(t=s.path,n=s.node,rS.isMountpoint(n))throw new rS.ErrnoError(10);if(!rS.isDir(n.mode))throw new rS.ErrnoError(54)}var i={type:e,opts:r,mountpoint:t,mounts:[]},u=e.mount(i);return u.mount=i,i.root=u,o?rS.root=u:n&&(n.mounted=i,n.mount&&n.mount.mounts.push(i)),u},unmount(e){var r=rS.lookupPath(e,{follow_mount:!1});if(!rS.isMountpoint(r.node))throw new rS.ErrnoError(28);var t=r.node,n=t.mounted,o=rS.getMounts(n);Object.keys(rS.nameTable).forEach(e=>{for(var r=rS.nameTable[e];r;){var t=r.name_next;o.includes(r.mount)&&rS.destroyNode(r),r=t}}),t.mounted=null;var a=t.mount.mounts.indexOf(n);t.mount.mounts.splice(a,1)},lookup:(e,r)=>e.node_ops.lookup(e,r),mknod(e,r,t){var n=rS.lookupPath(e,{parent:!0}).node,o=rm.basename(e);if(!o||"."===o||".."===o)throw new rS.ErrnoError(28);var a=rS.mayCreate(n,o);if(a)throw new rS.ErrnoError(a);if(!n.node_ops.mknod)throw new rS.ErrnoError(63);return n.node_ops.mknod(n,o,r,t)},create:(e,r)=>(r=(void 0!==r?r:438)&4095|32768,rS.mknod(e,r,0)),mkdir:(e,r)=>(r=(void 0!==r?r:511)&1023|16384,rS.mknod(e,r,0)),mkdirTree(e,r){for(var t=e.split("/"),n="",o=0;o<t.length;++o)if(t[o]){n+="/"+t[o];try{rS.mkdir(n,r)}catch(e){if(20!=e.errno)throw e}}},mkdev:(e,r,t)=>(void 0===t&&(t=r,r=438),r|=8192,rS.mknod(e,r,t)),symlink(e,r){if(!rg.resolve(e))throw new rS.ErrnoError(44);var t=rS.lookupPath(r,{parent:!0}).node;if(!t)throw new rS.ErrnoError(44);var n=rm.basename(r),o=rS.mayCreate(t,n);if(o)throw new rS.ErrnoError(o);if(!t.node_ops.symlink)throw new rS.ErrnoError(63);return t.node_ops.symlink(t,n,e)},rename(e,r){var t,n,o,a,s=rm.dirname(e),i=rm.dirname(r),u=rm.basename(e),l=rm.basename(r);if(n=rS.lookupPath(e,{parent:!0}).node,o=rS.lookupPath(r,{parent:!0}).node,!n||!o)throw new rS.ErrnoError(44);if(n.mount!==o.mount)throw new rS.ErrnoError(75);var d=rS.lookupNode(n,u),c=rg.relative(e,i);if("."!==c.charAt(0))throw new rS.ErrnoError(28);if("."!==(c=rg.relative(r,s)).charAt(0))throw new rS.ErrnoError(55);try{a=rS.lookupNode(o,l)}catch(e){}if(d!==a){var p=rS.isDir(d.mode),h=rS.mayDelete(n,u,p);if(h||(h=a?rS.mayDelete(o,l,p):rS.mayCreate(o,l)))throw new rS.ErrnoError(h);if(!n.node_ops.rename)throw new rS.ErrnoError(63);if(rS.isMountpoint(d)||a&&rS.isMountpoint(a))throw new rS.ErrnoError(10);if(o!==n&&(h=rS.nodePermissions(n,"w")))throw new rS.ErrnoError(h);rS.hashRemoveNode(d);try{n.node_ops.rename(d,o,l)}catch(e){throw e}finally{rS.hashAddNode(d)}}},rmdir(e){var r=rS.lookupPath(e,{parent:!0}).node,t=rm.basename(e),n=rS.lookupNode(r,t),o=rS.mayDelete(r,t,!0);if(o)throw new rS.ErrnoError(o);if(!r.node_ops.rmdir)throw new rS.ErrnoError(63);if(rS.isMountpoint(n))throw new rS.ErrnoError(10);r.node_ops.rmdir(r,t),rS.destroyNode(n)},readdir(e){var r=rS.lookupPath(e,{follow:!0}).node;if(!r.node_ops.readdir)throw new rS.ErrnoError(54);return r.node_ops.readdir(r)},unlink(e){var r=rS.lookupPath(e,{parent:!0}).node;if(!r)throw new rS.ErrnoError(44);var t=rm.basename(e),n=rS.lookupNode(r,t),o=rS.mayDelete(r,t,!1);if(o)throw new rS.ErrnoError(o);if(!r.node_ops.unlink)throw new rS.ErrnoError(63);if(rS.isMountpoint(n))throw new rS.ErrnoError(10);r.node_ops.unlink(r,t),rS.destroyNode(n)},readlink(e){var r=rS.lookupPath(e).node;if(!r)throw new rS.ErrnoError(44);if(!r.node_ops.readlink)throw new rS.ErrnoError(28);return rg.resolve(rS.getPath(r.parent),r.node_ops.readlink(r))},stat(e,r){var t=rS.lookupPath(e,{follow:!r}).node;if(!t)throw new rS.ErrnoError(44);if(!t.node_ops.getattr)throw new rS.ErrnoError(63);return t.node_ops.getattr(t)},lstat:e=>rS.stat(e,!0),chmod(e,r,t){var n;if(!(n="string"==typeof e?rS.lookupPath(e,{follow:!t}).node:e).node_ops.setattr)throw new rS.ErrnoError(63);n.node_ops.setattr(n,{mode:4095&r|-4096&n.mode,timestamp:Date.now()})},lchmod(e,r){rS.chmod(e,r,!0)},fchmod(e,r){var t=rS.getStreamChecked(e);rS.chmod(t.node,r)},chown(e,r,t,n){var o;if(!(o="string"==typeof e?rS.lookupPath(e,{follow:!n}).node:e).node_ops.setattr)throw new rS.ErrnoError(63);o.node_ops.setattr(o,{timestamp:Date.now()})},lchown(e,r,t){rS.chown(e,r,t,!0)},fchown(e,r,t){var n=rS.getStreamChecked(e);rS.chown(n.node,r,t)},truncate(e,r){if(r<0)throw new rS.ErrnoError(28);if("string"==typeof e){var t;t=rS.lookupPath(e,{follow:!0}).node}else t=e;if(!t.node_ops.setattr)throw new rS.ErrnoError(63);if(rS.isDir(t.mode))throw new rS.ErrnoError(31);if(!rS.isFile(t.mode))throw new rS.ErrnoError(28);var n=rS.nodePermissions(t,"w");if(n)throw new rS.ErrnoError(n);t.node_ops.setattr(t,{size:r,timestamp:Date.now()})},ftruncate(e,r){var t=rS.getStreamChecked(e);if((2097155&t.flags)==0)throw new rS.ErrnoError(28);rS.truncate(t.node,r)},utime(e,r,t){var n=rS.lookupPath(e,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(r,t)})},open(e,t,n){if(""===e)throw new rS.ErrnoError(44);if(t="string"==typeof t?rF(t):t,n=void 0===n?438:n,n=64&t?4095&n|32768:0,"object"==typeof e)o=e;else{e=rm.normalize(e);try{var o;o=rS.lookupPath(e,{follow:!(131072&t)}).node}catch(e){}}var a=!1;if(64&t)if(o){if(128&t)throw new rS.ErrnoError(20)}else o=rS.mknod(e,n,0),a=!0;if(!o)throw new rS.ErrnoError(44);if(rS.isChrdev(o.mode)&&(t&=-513),65536&t&&!rS.isDir(o.mode))throw new rS.ErrnoError(54);if(!a){var s=rS.mayOpen(o,t);if(s)throw new rS.ErrnoError(s)}512&t&&!a&&rS.truncate(o,0),t&=-131713;var i=rS.createStream({node:o,path:rS.getPath(o),flags:t,seekable:!0,position:0,stream_ops:o.stream_ops,ungotten:[],error:!1});return i.stream_ops.open&&i.stream_ops.open(i),r.logReadFiles&&!(1&t)&&(rS.readFiles||(rS.readFiles={}),e in rS.readFiles||(rS.readFiles[e]=1)),i},close(e){if(rS.isClosed(e))throw new rS.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{rS.closeStream(e.fd)}e.fd=null},isClosed:e=>null===e.fd,llseek(e,r,t){if(rS.isClosed(e))throw new rS.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new rS.ErrnoError(70);if(0!=t&&1!=t&&2!=t)throw new rS.ErrnoError(28);return e.position=e.stream_ops.llseek(e,r,t),e.ungotten=[],e.position},read(e,r,t,n,o){if(n<0||o<0)throw new rS.ErrnoError(28);if(rS.isClosed(e)||(2097155&e.flags)==1)throw new rS.ErrnoError(8);if(rS.isDir(e.node.mode))throw new rS.ErrnoError(31);if(!e.stream_ops.read)throw new rS.ErrnoError(28);var a=void 0!==o;if(a){if(!e.seekable)throw new rS.ErrnoError(70)}else o=e.position;var s=e.stream_ops.read(e,r,t,n,o);return a||(e.position+=s),s},write(e,r,t,n,o,a){if(n<0||o<0)throw new rS.ErrnoError(28);if(rS.isClosed(e)||(2097155&e.flags)==0)throw new rS.ErrnoError(8);if(rS.isDir(e.node.mode))throw new rS.ErrnoError(31);if(!e.stream_ops.write)throw new rS.ErrnoError(28);e.seekable&&1024&e.flags&&rS.llseek(e,0,2);var s=void 0!==o;if(s){if(!e.seekable)throw new rS.ErrnoError(70)}else o=e.position;var i=e.stream_ops.write(e,r,t,n,o,a);return s||(e.position+=i),i},allocate(e,r,t){if(rS.isClosed(e))throw new rS.ErrnoError(8);if(r<0||t<=0)throw new rS.ErrnoError(28);if((2097155&e.flags)==0)throw new rS.ErrnoError(8);if(!rS.isFile(e.node.mode)&&!rS.isDir(e.node.mode))throw new rS.ErrnoError(43);if(!e.stream_ops.allocate)throw new rS.ErrnoError(138);e.stream_ops.allocate(e,r,t)},mmap(e,r,t,n,o){if((2&n)!=0&&(2&o)==0&&(2097155&e.flags)!=2||(2097155&e.flags)==1)throw new rS.ErrnoError(2);if(!e.stream_ops.mmap)throw new rS.ErrnoError(43);return e.stream_ops.mmap(e,r,t,n,o)},msync:(e,r,t,n,o)=>e.stream_ops.msync?e.stream_ops.msync(e,r,t,n,o):0,munmap:e=>0,ioctl(e,r,t){if(!e.stream_ops.ioctl)throw new rS.ErrnoError(59);return e.stream_ops.ioctl(e,r,t)},readFile(e,r={}){if(r.flags=r.flags||0,r.encoding=r.encoding||"binary","utf8"!==r.encoding&&"binary"!==r.encoding)throw Error(`Invalid encoding type "${r.encoding}"`);var t,n=rS.open(e,r.flags),o=rS.stat(e).size,a=new Uint8Array(o);return rS.read(n,a,0,o,0),"utf8"===r.encoding?t=re(a,0):"binary"===r.encoding&&(t=a),rS.close(n),t},writeFile(e,r,t={}){t.flags=t.flags||577;var n=rS.open(e,t.flags,t.mode);if("string"==typeof r){var o=new Uint8Array(e7(r)+1),a=e5(r,o,0,o.length);rS.write(n,o,0,a,void 0,t.canOwn)}else if(ArrayBuffer.isView(r))rS.write(n,r,0,r.byteLength,void 0,t.canOwn);else throw Error("Unsupported data type");rS.close(n)},cwd:()=>rS.currentPath,chdir(e){var r=rS.lookupPath(e,{follow:!0});if(null===r.node)throw new rS.ErrnoError(44);if(!rS.isDir(r.node.mode))throw new rS.ErrnoError(54);var t=rS.nodePermissions(r.node,"x");if(t)throw new rS.ErrnoError(t);rS.currentPath=r.path},createDefaultDirectories(){rS.mkdir("/tmp"),rS.mkdir("/home"),rS.mkdir("/home/<USER>")},createDefaultDevices(){rS.mkdir("/dev"),rS.registerDevice(rS.makedev(1,3),{read:()=>0,write:(e,r,t,n,o)=>n}),rS.mkdev("/dev/null",rS.makedev(1,3)),r_.register(rS.makedev(5,0),r_.default_tty_ops),r_.register(rS.makedev(6,0),r_.default_tty1_ops),rS.mkdev("/dev/tty",rS.makedev(5,0)),rS.mkdev("/dev/tty1",rS.makedev(6,0));var e=new Uint8Array(1024),r=0,t=()=>(0===r&&(r=ry(e).byteLength),e[--r]);rS.createDevice("/dev","random",t),rS.createDevice("/dev","urandom",t),rS.mkdir("/dev/shm"),rS.mkdir("/dev/shm/tmp")},createSpecialDirectories(){rS.mkdir("/proc");var e=rS.mkdir("/proc/self");rS.mkdir("/proc/self/fd"),rS.mount({mount(){var r=rS.createNode(e,"fd",16895,73);return r.node_ops={lookup(e,r){var t=rS.getStreamChecked(+r),n={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>t.path}};return n.parent=n,n}},r}},{},"/proc/self/fd")},createStandardStreams(){r.stdin?rS.createDevice("/dev","stdin",r.stdin):rS.symlink("/dev/tty","/dev/stdin"),r.stdout?rS.createDevice("/dev","stdout",null,r.stdout):rS.symlink("/dev/tty","/dev/stdout"),r.stderr?rS.createDevice("/dev","stderr",null,r.stderr):rS.symlink("/dev/tty1","/dev/stderr"),rS.open("/dev/stdin",0),rS.open("/dev/stdout",1),rS.open("/dev/stderr",1)},ensureErrnoError(){rS.ErrnoError||(rS.ErrnoError=function(e,r){this.name="ErrnoError",this.node=r,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},rS.ErrnoError.prototype=Error(),rS.ErrnoError.prototype.constructor=rS.ErrnoError,[44].forEach(e=>{rS.genericErrors[e]=new rS.ErrnoError(e),rS.genericErrors[e].stack="<generic error, no stack>"}))},staticInit(){rS.ensureErrnoError(),rS.nameTable=Array(4096),rS.mount(rk,{},"/"),rS.createDefaultDirectories(),rS.createDefaultDevices(),rS.createSpecialDirectories(),rS.filesystems={MEMFS:rk}},init(e,t,n){rS.init.initialized=!0,rS.ensureErrnoError(),r.stdin=e||r.stdin,r.stdout=t||r.stdout,r.stderr=n||r.stderr,rS.createStandardStreams()},quit(){rS.init.initialized=!1;for(var e=0;e<rS.streams.length;e++){var r=rS.streams[e];r&&rS.close(r)}},findObject(e,r){var t=rS.analyzePath(e,r);return t.exists?t.object:null},analyzePath(e,r){try{var t=rS.lookupPath(e,{follow:!r});e=t.path}catch(e){}var n={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var t=rS.lookupPath(e,{parent:!0});n.parentExists=!0,n.parentPath=t.path,n.parentObject=t.node,n.name=rm.basename(e),t=rS.lookupPath(e,{follow:!r}),n.exists=!0,n.path=t.path,n.object=t.node,n.name=t.node.name,n.isRoot="/"===t.path}catch(e){n.error=e.errno}return n},createPath(e,r,t,n){e="string"==typeof e?e:rS.getPath(e);for(var o=r.split("/").reverse();o.length;){var a=o.pop();if(a){var s=rm.join2(e,a);try{rS.mkdir(s)}catch(e){}e=s}}return s},createFile(e,r,t,n,o){var a=rm.join2("string"==typeof e?e:rS.getPath(e),r),s=rA(n,o);return rS.create(a,s)},createDataFile(e,r,t,n,o,a){var s=r;e&&(e="string"==typeof e?e:rS.getPath(e),s=r?rm.join2(e,r):e);var i=rA(n,o),u=rS.create(s,i);if(t){if("string"==typeof t){for(var l=Array(t.length),d=0,c=t.length;d<c;++d)l[d]=t.charCodeAt(d);t=l}rS.chmod(u,146|i);var p=rS.open(u,577);rS.write(p,t,0,t.length,0,a),rS.close(p),rS.chmod(u,i)}return u},createDevice(e,r,t,n){var o=rm.join2("string"==typeof e?e:rS.getPath(e),r),a=rA(!!t,!!n);rS.createDevice.major||(rS.createDevice.major=64);var s=rS.makedev(rS.createDevice.major++,0);return rS.registerDevice(s,{open(e){e.seekable=!1},close(e){n&&n.buffer&&n.buffer.length&&n(10)},read(e,r,n,o,a){for(var s,i=0,u=0;u<o;u++){try{s=t()}catch(e){throw new rS.ErrnoError(29)}if(void 0===s&&0===i)throw new rS.ErrnoError(6);if(null==s)break;i++,r[n+u]=s}return i&&(e.node.timestamp=Date.now()),i},write(e,r,t,o,a){for(var s=0;s<o;s++)try{n(r[t+s])}catch(e){throw new rS.ErrnoError(29)}return o&&(e.node.timestamp=Date.now()),s}}),rS.mkdev(o,a,s)},forceLoadFile(e){if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(o)try{e.contents=rE(o(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new rS.ErrnoError(29)}else throw Error("Cannot load without read() or XMLHttpRequest.")},createLazyFile(e,r,t,n,o){function a(){this.lengthKnown=!1,this.chunks=[]}if(a.prototype.get=function(e){if(!(e>this.length-1)&&!(e<0)){var r=e%this.chunkSize,t=e/this.chunkSize|0;return this.getter(t)[r]}},a.prototype.setDataGetter=function(e){this.getter=e},a.prototype.cacheLength=function(){var e,r=new XMLHttpRequest;if(r.open("HEAD",t,!1),r.send(null),!(r.status>=200&&r.status<300||304===r.status))throw Error("Couldn't load "+t+". Status: "+r.status);var n=Number(r.getResponseHeader("Content-length")),o=(e=r.getResponseHeader("Accept-Ranges"))&&"bytes"===e,a=(e=r.getResponseHeader("Content-Encoding"))&&"gzip"===e,s=1048576;o||(s=n);var i=(e,r)=>{if(e>r)throw Error("invalid range ("+e+", "+r+") or no bytes requested!");if(r>n-1)throw Error("only "+n+" bytes available! programmer error!");var o=new XMLHttpRequest;if(o.open("GET",t,!1),n!==s&&o.setRequestHeader("Range","bytes="+e+"-"+r),o.responseType="arraybuffer",o.overrideMimeType&&o.overrideMimeType("text/plain; charset=x-user-defined"),o.send(null),!(o.status>=200&&o.status<300||304===o.status))throw Error("Couldn't load "+t+". Status: "+o.status);return void 0!==o.response?new Uint8Array(o.response||[]):rE(o.responseText||"",!0)},u=this;u.setDataGetter(e=>{var r=e*s,t=(e+1)*s-1;if(t=Math.min(t,n-1),void 0===u.chunks[e]&&(u.chunks[e]=i(r,t)),void 0===u.chunks[e])throw Error("doXHR failed!");return u.chunks[e]}),(a||!n)&&(s=n=1,s=n=this.getter(0).length,j("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=s,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){var s;throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc"}var s={isDevice:!1,url:t},i=rS.createFile(e,r,s,n,o);s.contents?i.contents=s.contents:s.url&&(i.contents=null,i.url=s.url),Object.defineProperties(i,{usedBytes:{get:function(){return this.contents.length}}});var u={};function d(e,r,t,n,o){var a=e.node.contents;if(o>=a.length)return 0;var s=Math.min(a.length-o,n);if(a.slice)for(var i=0;i<s;i++)r[t+i]=a[o+i];else for(var i=0;i<s;i++)r[t+i]=a.get(o+i);return s}return Object.keys(i.stream_ops).forEach(e=>{var r=i.stream_ops[e];u[e]=function(){return rS.forceLoadFile(i),r.apply(null,arguments)}}),u.read=(e,r,t,n,o)=>(rS.forceLoadFile(i),d(e,r,t,n,o)),u.mmap=(e,r,t,n,o)=>{rS.forceLoadFile(i);var a=r$(r);if(!a)throw new rS.ErrnoError(48);return d(e,l,a,r,t),{ptr:a,allocated:!0}},i.stream_ops=u,i}},rM={DEFAULT_POLLMASK:5,calculateAt(e,r,t){if(rm.isAbs(r))return r;if(-100===e)n=rS.cwd();else{var n;n=rM.getStreamFromFD(e).path}if(0==r.length){if(!t)throw new rS.ErrnoError(44);return n}return rm.join2(n,r)},doStat(e,r,t){try{var n=e(r)}catch(e){if(e&&e.node&&rm.normalize(r)!==rm.normalize(rS.getPath(e.node)))return -54;throw e}h[t>>2]=n.dev,h[t+4>>2]=n.mode,f[t+8>>2]=n.nlink,h[t+12>>2]=n.uid,h[t+16>>2]=n.gid,h[t+20>>2]=n.rdev,w=[n.size>>>0,+Math.abs(g=n.size)>=1?g>0?Math.floor(g/0x100000000)>>>0:~~Math.ceil((g-(~~g>>>0))/0x100000000)>>>0:0],h[t+24>>2]=w[0],h[t+28>>2]=w[1],h[t+32>>2]=4096,h[t+36>>2]=n.blocks;var o=n.atime.getTime(),a=n.mtime.getTime(),s=n.ctime.getTime();return w=[Math.floor(o/1e3)>>>0,+Math.abs(g=Math.floor(o/1e3))>=1?g>0?Math.floor(g/0x100000000)>>>0:~~Math.ceil((g-(~~g>>>0))/0x100000000)>>>0:0],h[t+40>>2]=w[0],h[t+44>>2]=w[1],f[t+48>>2]=o%1e3*1e3,w=[Math.floor(a/1e3)>>>0,+Math.abs(g=Math.floor(a/1e3))>=1?g>0?Math.floor(g/0x100000000)>>>0:~~Math.ceil((g-(~~g>>>0))/0x100000000)>>>0:0],h[t+56>>2]=w[0],h[t+60>>2]=w[1],f[t+64>>2]=a%1e3*1e3,w=[Math.floor(s/1e3)>>>0,+Math.abs(g=Math.floor(s/1e3))>=1?g>0?Math.floor(g/0x100000000)>>>0:~~Math.ceil((g-(~~g>>>0))/0x100000000)>>>0:0],h[t+72>>2]=w[0],h[t+76>>2]=w[1],f[t+80>>2]=s%1e3*1e3,w=[n.ino>>>0,+Math.abs(g=n.ino)>=1?g>0?Math.floor(g/0x100000000)>>>0:~~Math.ceil((g-(~~g>>>0))/0x100000000)>>>0:0],h[t+88>>2]=w[0],h[t+92>>2]=w[1],0},doMsync(e,r,t,n,o){if(!rS.isFile(r.node.mode))throw new rS.ErrnoError(43);if(2&n)return 0;var a=d.slice(e,e+t);rS.msync(r,a,o,t,n)},varargs:void 0,get(){var e=h[rM.varargs>>2];return rM.varargs+=4,e},getp:()=>rM.get(),getStr:e=>rr(e),getStreamFromFD:e=>rS.getStreamChecked(e)},rx=(e,r,t,n)=>{for(var o=0,a=0;a<t;a++){var s=f[r>>2],i=f[r+4>>2];r+=8;var u=rS.read(e,l,s,i,n);if(u<0)return -1;if(o+=u,u<i)break;void 0!==n&&(n+=u)}return o},rj=(e,r)=>r+2097152>>>0<4194305-!!e?(e>>>0)+0x100000000*r:NaN,rO=(e,r,t,n)=>{for(var o=0,a=0;a<t;a++){var s=f[r>>2],i=f[r+4>>2];r+=8;var u=rS.write(e,l,s,i,n);if(u<0)return -1;o+=u,void 0!==n&&(n+=u)}return o},rR=e=>e%4==0&&(e%100!=0||e%400==0),rW=(e,r)=>{for(var t=0,n=0;n<=r;t+=e[n++]);return t},rz=[31,29,31,30,31,30,31,31,30,31,30,31],rN=[31,28,31,30,31,30,31,31,30,31,30,31],rB=(e,r)=>{for(var t=new Date(e.getTime());r>0;){var n=rR(t.getFullYear()),o=t.getMonth(),a=(n?rz:rN)[o];if(r>a-t.getDate())r-=a-t.getDate()+1,t.setDate(1),o<11?t.setMonth(o+1):(t.setMonth(0),t.setFullYear(t.getFullYear()+1));else{t.setDate(t.getDate()+r);break}}return t},rI=(e,r)=>{l.set(e,r)},rH=(e,r,t,n)=>{var o=f[n+40>>2],a={tm_sec:h[n>>2],tm_min:h[n+4>>2],tm_hour:h[n+8>>2],tm_mday:h[n+12>>2],tm_mon:h[n+16>>2],tm_year:h[n+20>>2],tm_wday:h[n+24>>2],tm_yday:h[n+28>>2],tm_isdst:h[n+32>>2],tm_gmtoff:h[n+36>>2],tm_zone:o?rr(o):""},s=rr(t),i={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var u in i)s=s.replace(RegExp(u,"g"),i[u]);var l=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],d=["January","February","March","April","May","June","July","August","September","October","November","December"];function c(e,r,t){for(var n="number"==typeof e?e.toString():e||"";n.length<r;)n=t[0]+n;return n}function p(e,r){return c(e,r,"0")}function m(e,r){var t;function n(e){return e<0?-1:+(e>0)}return 0===(t=n(e.getFullYear()-r.getFullYear()))&&0===(t=n(e.getMonth()-r.getMonth()))&&(t=n(e.getDate()-r.getDate())),t}function v(e){switch(e.getDay()){case 0:return new Date(e.getFullYear()-1,11,29);case 1:return e;case 2:return new Date(e.getFullYear(),0,3);case 3:return new Date(e.getFullYear(),0,2);case 4:return new Date(e.getFullYear(),0,1);case 5:return new Date(e.getFullYear()-1,11,31);case 6:return new Date(e.getFullYear()-1,11,30)}}function y(e){var r=rB(new Date(e.tm_year+1900,0,1),e.tm_yday),t=new Date(r.getFullYear(),0,4),n=new Date(r.getFullYear()+1,0,4),o=v(t),a=v(n);return 0>=m(o,r)?0>=m(a,r)?r.getFullYear()+1:r.getFullYear():r.getFullYear()-1}var g={"%a":e=>l[e.tm_wday].substring(0,3),"%A":e=>l[e.tm_wday],"%b":e=>d[e.tm_mon].substring(0,3),"%B":e=>d[e.tm_mon],"%C":e=>p((e.tm_year+1900)/100|0,2),"%d":e=>p(e.tm_mday,2),"%e":e=>c(e.tm_mday,2," "),"%g":e=>y(e).toString().substring(2),"%G":e=>y(e),"%H":e=>p(e.tm_hour,2),"%I":e=>{var r=e.tm_hour;return 0==r?r=12:r>12&&(r-=12),p(r,2)},"%j":e=>p(e.tm_mday+rW(rR(e.tm_year+1900)?rz:rN,e.tm_mon-1),3),"%m":e=>p(e.tm_mon+1,2),"%M":e=>p(e.tm_min,2),"%n":()=>"\n","%p":e=>e.tm_hour>=0&&e.tm_hour<12?"AM":"PM","%S":e=>p(e.tm_sec,2),"%t":()=>"	","%u":e=>e.tm_wday||7,"%U":e=>p(Math.floor((e.tm_yday+7-e.tm_wday)/7),2),"%V":e=>{var r=Math.floor((e.tm_yday+7-(e.tm_wday+6)%7)/7);if((e.tm_wday+371-e.tm_yday-2)%7<=2&&r++,r){if(53==r){var t=(e.tm_wday+371-e.tm_yday)%7;4==t||3==t&&rR(e.tm_year)||(r=1)}}else{r=52;var n=(e.tm_wday+7-e.tm_yday-1)%7;(4==n||5==n&&rR(e.tm_year%400-1))&&r++}return p(r,2)},"%w":e=>e.tm_wday,"%W":e=>p(Math.floor((e.tm_yday+7-(e.tm_wday+6)%7)/7),2),"%y":e=>(e.tm_year+1900).toString().substring(2),"%Y":e=>e.tm_year+1900,"%z":e=>{var r=e.tm_gmtoff;return(r>=0?"+":"-")+String("0000"+(r=(r=Math.abs(r)/60)/60*100+r%60)).slice(-4)},"%Z":e=>e.tm_zone,"%%":()=>"%"};for(var u in s=s.replace(/%%/g,"\0\0"),g)s.includes(u)&&(s=s.replace(RegExp(u,"g"),g[u](a)));var w=rE(s=s.replace(/\0\0/g,"%"),!1);return w.length>r?0:(rI(w,e),w.length-1)},rL=e=>r["_"+e],rU=e=>{var r=e7(e)+1,t=r1(r);return e8(e,t,r),t};E=r.InternalError=class extends Error{constructor(e){super(e),this.name="InternalError"}};for(var rV=Array(256),rY=0;rY<256;++rY)rV[rY]=String.fromCharCode(rY);b=rV,_=r.BindingError=class extends Error{constructor(e){super(e),this.name="BindingError"}},Object.assign(eT.prototype,{isAliasOf(e){if(!(this instanceof eT)||!(e instanceof eT))return!1;var r=this.$$.ptrType.registeredClass,t=this.$$.ptr;e.$$=e.$$;for(var n=e.$$.ptrType.registeredClass,o=e.$$.ptr;r.baseClass;)t=r.upcast(t),r=r.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return r===n&&t===o},clone(){if(this.$$.ptr||eh(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e=eC(Object.create(Object.getPrototypeOf(this),{$$:{value:ep(this.$$)}}));return e.$$.count.value+=1,e.$$.deleteScheduled=!1,e},delete(){this.$$.ptr||eh(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&ed("Object already scheduled for deletion"),em(this),ey(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},isDeleted(){return!this.$$.ptr},deleteLater(){return this.$$.ptr||eh(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&ed("Object already scheduled for deletion"),eE.push(this),1===eE.length&&$&&$(eb),this.$$.deleteScheduled=!0,this}}),r.getInheritedInstanceCount=()=>Object.keys(e_).length,r.getLiveInheritedInstances=()=>{var e=[];for(var r in e_)e_.hasOwnProperty(r)&&e.push(e_[r]);return e},r.flushPendingDeletes=eb,r.setDelayFunction=e=>{$=e,eE.length&&$&&$(eb)},Object.assign(ez.prototype,{getPointee(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e},destructor(e){this.rawDestructor&&this.rawDestructor(e)},argPackAdvance:8,readValueFromPointer:eW,deleteObject(e){null!==e&&e.delete()},fromWireType:function(e){var r,t=this.getPointee(e);if(!t)return this.destructor(e),null;var n=ek(this.registeredClass,t);if(void 0!==n)if(0===n.$$.count.value)return n.$$.ptr=t,n.$$.smartPtr=e,n.clone();else{var o=n.clone();return this.destructor(e),o}function a(){return this.isSmartPointer?eP(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):eP(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var s=ew[this.registeredClass.getActualType(t)];if(!s)return a.call(this);r=this.isConst?s.constPointerType:s.pointerType;var i=eg(t,this.registeredClass,r.registeredClass);return null===i?a.call(this):this.isSmartPointer?eP(r.registeredClass.instancePrototype,{ptrType:r,ptr:i,smartPtrType:this,smartPtr:e}):eP(r.registeredClass.instancePrototype,{ptrType:r,ptr:i})}}),D=Error,(A=eF(F="UnboundTypeError",function(e){this.name=F,this.message=e;var r=Error(e).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))})).prototype=Object.create(D.prototype),A.prototype.constructor=A,A.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`},P=r.UnboundTypeError=A,Object.assign(eK.prototype,{get(e){return this.allocated[e]},has(e){return void 0!==this.allocated[e]},allocate(e){var r=this.freelist.pop()||this.allocated.length;return this.allocated[r]=e,r},free(e){this.allocated[e]=void 0,this.freelist.push(e)}}),eZ.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),eZ.reserved=eZ.allocated.length,r.count_emval_handles=()=>{for(var e=0,r=eZ.reserved;r<eZ.allocated.length;++r)void 0!==eZ.allocated[r]&&++e;return e};var rq=function(e,r,t,n){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=rS.nextInode++,this.name=r,this.mode=t,this.node_ops={},this.stream_ops={},this.rdev=n};Object.defineProperties(rq.prototype,{read:{get:function(){return(365&this.mode)==365},set:function(e){e?this.mode|=365:this.mode&=-366}},write:{get:function(){return(146&this.mode)==146},set:function(e){e?this.mode|=146:this.mode&=-147}},isFolder:{get:function(){return rS.isDir(this.mode)}},isDevice:{get:function(){return rS.isChrdev(this.mode)}}}),rS.FSNode=rq,rS.createPreloadedFile=(e,r,t,n,o,a,s,i,u,l)=>{var d=r?rg.resolve(rm.join2(e,r)):e,c=`cp ${d}`;function p(t){function p(t){l&&l(),i||rC(e,r,t,n,o,u),a&&a(),V(c)}rD(t,d,p,()=>{s&&s(),V(c)})||p(t)}U(c),"string"==typeof t?rP(t,e=>p(e),s):p(t)},rS.staticInit();var rX={d:(e,r,t)=>{throw new K(e).init(r,t),Z=e,Q++,Z},n:e=>{var r=ee[e];delete ee[e];var t=r.elements,n=t.length,o=t.map(e=>e.getterReturnType).concat(t.map(e=>e.setterArgumentType)),a=r.rawConstructor,s=r.rawDestructor;ei([e],o,function(e){return t.forEach((r,t)=>{var o=e[t],a=r.getter,s=r.getterContext,i=e[t+n],u=r.setter,l=r.setterContext;r.read=e=>o.fromWireType(a(s,e)),r.write=(e,r)=>{var t=[];u(l,e,i.toWireType(t,r)),er(t)}}),[{name:r.name,fromWireType:e=>{for(var r=Array(n),o=0;o<n;++o)r[o]=t[o].read(e);return s(e),r},toWireType:(e,o)=>{if(n!==o.length)throw TypeError(`Incorrect number of tuple elements for ${r.name}: expected=${n}, actual=${o.length}`);for(var i=a(),u=0;u<n;++u)t[u].write(i,o[u]);return null!==e&&e.push(s,i),i},argPackAdvance:8,readValueFromPointer:et,destructorFunction:s}]})},l:e=>{var r=eu[e];delete eu[e];var t=r.rawConstructor,n=r.rawDestructor,o=r.fields;ei([e],o.map(e=>e.getterReturnType).concat(o.map(e=>e.setterArgumentType)),e=>{var a={};return o.forEach((r,t)=>{var n=r.fieldName,s=e[t],i=r.getter,u=r.getterContext,l=e[t+o.length],d=r.setter,c=r.setterContext;a[n]={read:e=>s.fromWireType(i(u,e)),write:(e,r)=>{var t=[];d(c,e,l.toWireType(t,r)),er(t)}}}),[{name:r.name,fromWireType:e=>{var r={};for(var t in a)r[t]=a[t].read(e);return n(e),r},toWireType:(e,r)=>{for(var o in a)if(!(o in r))throw TypeError(`Missing field: "${o}"`);var s=t();for(o in a)a[o].write(s,r[o]);return null!==e&&e.push(n,s),s},argPackAdvance:8,readValueFromPointer:et,destructorFunction:n}]})},w:(e,r,t,n,o)=>{},G:(e,r,t,n)=>{ec(e,{name:r=el(r),fromWireType:function(e){return!!e},toWireType:function(e,r){return r?t:n},argPackAdvance:8,readValueFromPointer:function(e){return this.fromWireType(d[e])},destructorFunction:null})},h:(e,r,t,n,o,a,s,i,u,l,d,c,p)=>{d=el(d),a=eV(o,a),i&&(i=eV(s,i)),l&&(l=eV(u,l)),p=eV(c,p);var h=eD(d);eS(h,function(){eq(`Cannot construct ${d} due to unbound types`,[n])}),ei([e,r,t],n?[n]:[],function(r){r=r[0];var t,o=n?(t=r.registeredClass).instancePrototype:eT.prototype,s=eF(h,function(){if(Object.getPrototypeOf(this)!==u)throw new _("Use 'new' to construct "+d);if(void 0===c.constructor_body)throw new _(d+" has no accessible constructor");var e=c.constructor_body[arguments.length];if(void 0===e)throw new _(`Tried to invoke ctor of ${d} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(c.constructor_body).toString()}) parameters instead!`);return e.apply(this,arguments)}),u=Object.create(o,{constructor:{value:s}});s.prototype=u;var c=new eM(d,s,u,p,t,a,i,l);c.baseClass&&(void 0===c.baseClass.__derivedClasses&&(c.baseClass.__derivedClasses=[]),c.baseClass.__derivedClasses.push(c));var f=new ez(d,c,!0,!1,!1),m=new ez(d+"*",c,!1,!1,!1),v=new ez(d+" const*",c,!1,!0,!1);return ew[e]={pointerType:m,constPointerType:v},eN(h,s),[f,m,v]})},g:(e,r,t,n,o,a)=>{var s=eX(r,t);o=eV(n,o),ei([],[e],function(e){e=e[0];var t=`constructor ${e.name}`;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[r-1])throw new _(`Cannot register multiple constructors with identical number of parameters (${r-1}) for class '${e.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return e.registeredClass.constructor_body[r-1]=()=>{eq(`Cannot construct ${e.name} due to unbound types`,s)},ei([],s,n=>(n.splice(1,0,null),e.registeredClass.constructor_body[r-1]=eG(t,n,null,o,a),[])),[]})},c:(e,r,t,n,o,a,s,i,u)=>{var l=eX(t,n);r=el(r),a=eV(o,a),ei([],[e],function(e){e=e[0];var n=`${e.name}.${r}`;function o(){eq(`Cannot call ${n} due to unbound types`,l)}r.startsWith("@@")&&(r=Symbol[r.substring(2)]),i&&e.registeredClass.pureVirtualFunctions.push(r);var d=e.registeredClass.instancePrototype,c=d[r];return void 0===c||void 0===c.overloadTable&&c.className!==e.name&&c.argCount===t-2?(o.argCount=t-2,o.className=e.name,d[r]=o):(eA(d,r,n),d[r].overloadTable[t-2]=o),ei([],l,function(o){var i=eG(n,o,e,a,s,u);return void 0===d[r].overloadTable?(i.argCount=t-2,d[r]=i):d[r].overloadTable[t-2]=i,[]}),[]})},q:(e,r,t,n,o,a,s,i,u,l)=>{r=el(r),o=eV(n,o),ei([],[e],function(e){e=e[0];var n=`${e.name}.${r}`,d={get(){eq(`Cannot access ${n} due to unbound types`,[t,s])},enumerable:!0,configurable:!0};return u?d.set=()=>eq(`Cannot access ${n} due to unbound types`,[t,s]):d.set=e=>ed(n+" is a read-only property"),Object.defineProperty(e.registeredClass.instancePrototype,r,d),ei([],u?[t,s]:[t],function(t){var s=t[0],d={get(){var r=eJ(this,e,n+" getter");return s.fromWireType(o(a,r))},enumerable:!0};if(u){u=eV(i,u);var c=t[1];d.set=function(r){var t=eJ(this,e,n+" setter"),o=[];u(l,t,c.toWireType(o,r)),er(o)}}return Object.defineProperty(e.registeredClass.instancePrototype,r,d),[]}),[]})},F:(e,r)=>{ec(e,{name:r=el(r),fromWireType:e=>{var r=e0.toValue(e);return eQ(e),r},toWireType:(e,r)=>e0.toHandle(r),argPackAdvance:8,readValueFromPointer:et,destructorFunction:null})},p:(e,r,t,n)=>{function o(){}r=el(r),o.values={},ec(e,{name:r,constructor:o,fromWireType:function(e){return this.constructor.values[e]},toWireType:(e,r)=>r.value,argPackAdvance:8,readValueFromPointer:e1(r,t,n),destructorFunction:null}),eS(r,o)},i:(e,r,t)=>{var n=e2(e,"enum");r=el(r);var o=n.constructor,a=Object.create(n.constructor.prototype,{value:{value:t},constructor:{value:eF(`${n.name}_${r}`,function(){})}});o.values[t]=a,o[r]=a},t:(e,r,t)=>{ec(e,{name:r=el(r),fromWireType:e=>e,toWireType:(e,r)=>r,argPackAdvance:8,readValueFromPointer:e4(r,t),destructorFunction:null})},a:(e,r,t,n,o,a,s)=>{var i=eX(r,t);e=el(e),o=eV(n,o),eS(e,function(){eq(`Cannot call ${e} due to unbound types`,i)},r-1),ei([],i,function(t){var n=[t[0],null].concat(t.slice(1));return eN(e,eG(e,n,null,o,a,s),r-1),[]})},j:(e,r,t,n,o)=>{r=el(r),-1===o&&(o=0xffffffff);var a,s=e=>e;if(0===n){var i=32-8*t;s=e=>e<<i>>>i}var u=r.includes("unsigned"),l=(e,r)=>{};ec(e,{name:r,fromWireType:s,toWireType:u?function(e,r){return l(r,this.name),r>>>0}:function(e,r){return l(r,this.name),r},argPackAdvance:8,readValueFromPointer:e6(r,t,0!==n),destructorFunction:null})},e:(e,r,t)=>{var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(e){var r=f[e>>2],t=f[e+4>>2];return new n(l.buffer,t,r)}ec(e,{name:t=el(t),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},u:(e,r)=>{var t="std::string"===(r=el(r));ec(e,{name:r,fromWireType(e){var r,n=f[e>>2],o=e+4;if(t)for(var a=o,s=0;s<=n;++s){var i=o+s;if(s==n||0==d[i]){var u=i-a,l=rr(a,u);void 0===r?r=l:(r+="\0",r+=l),a=i+1}}else{for(var c=Array(n),s=0;s<n;++s)c[s]=String.fromCharCode(d[o+s]);r=c.join("")}return rK(e),r},toWireType(e,r){r instanceof ArrayBuffer&&(r=new Uint8Array(r));var n,o="string"==typeof r;o||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||ed("Cannot pass non-string to std::string"),n=t&&o?e7(r):r.length;var a=rJ(4+n+1),s=a+4;if(f[a>>2]=n,t&&o)e8(r,s,n+1);else if(o)for(var i=0;i<n;++i){var u=r.charCodeAt(i);u>255&&(rK(s),ed("String has UTF-16 code units that do not fit in 8 bits")),d[s+i]=u}else for(var i=0;i<n;++i)d[s+i]=r[i];return null!==e&&e.push(rK,a),a},argPackAdvance:8,readValueFromPointer:eW,destructorFunction(e){rK(e)}})},r:(e,r,t)=>{var n,o,a,s,i;t=el(t),2===r?(n=rn,o=ro,s=ra,a=()=>p,i=1):4===r&&(n=rs,o=ri,s=ru,a=()=>f,i=2),ec(e,{name:t,fromWireType:e=>{for(var t,o=f[e>>2],s=a(),u=e+4,l=0;l<=o;++l){var d=e+4+l*r;if(l==o||0==s[d>>i]){var c=d-u,p=n(u,c);void 0===t?t=p:(t+="\0",t+=p),u=d+r}}return rK(e),t},toWireType:(e,n)=>{"string"!=typeof n&&ed(`Cannot pass non-string to C++ string type ${t}`);var a=s(n),u=rJ(4+a+r);return f[u>>2]=a>>i,o(n,u+4,a+r),null!==e&&e.push(rK,u),u},argPackAdvance:8,readValueFromPointer:et,destructorFunction(e){rK(e)}})},o:(e,r,t,n,o,a)=>{ee[e]={name:el(r),rawConstructor:eV(t,n),rawDestructor:eV(o,a),elements:[]}},b:(e,r,t,n,o,a,s,i,u)=>{ee[e].elements.push({getterReturnType:r,getter:eV(t,n),getterContext:o,setterArgumentType:a,setter:eV(s,i),setterContext:u})},m:(e,r,t,n,o,a)=>{eu[e]={name:el(r),rawConstructor:eV(t,n),rawDestructor:eV(o,a),fields:[]}},f:(e,r,t,n,o,a,s,i,u,l)=>{eu[e].fields.push({fieldName:el(r),getterReturnType:t,getter:eV(n,o),getterContext:a,setterArgumentType:s,setter:eV(i,u),setterContext:l})},H:(e,r)=>{ec(e,{isVoid:!0,name:r=el(r),argPackAdvance:0,fromWireType:()=>void 0,toWireType:(e,r)=>void 0})},I:eQ,J:e=>{e>4&&(eZ.get(e).refcount+=1)},k:(e,r)=>{var t=(e=e2(e,"_emval_take_value")).readValueFromPointer(r);return e0.toHandle(t)},s:()=>{Y("")},E:(e,r,t)=>d.copyWithin(e,r,r+t),y:e=>{var r=d.length;e>>>=0;var t=rl();if(e>t)return!1;for(var n=(e,r)=>e+(r-e%r)%r,o=1;o<=4;o*=2){var a=r*(1+.2/o);if(a=Math.min(a,e+0x6000000),rd(Math.min(t,n(Math.max(e,a),65536))))return!0}return!1},z:(e,r)=>{var t=0;return rh().forEach((n,o)=>{var a=r+t;f[e+4*o>>2]=a,rf(n,a),t+=n.length+1}),0},A:(e,r)=>{var t=rh();f[e>>2]=t.length;var n=0;return t.forEach(e=>n+=e.length+1),f[r>>2]=n,0},B:function(e){try{var r=rM.getStreamFromFD(e);return rS.close(r),0}catch(e){if(void 0===rS||"ErrnoError"!==e.name)throw e;return e.errno}},D:function(e,r,t,n){try{var o=rM.getStreamFromFD(e),a=rx(o,r,t);return f[n>>2]=a,0}catch(e){if(void 0===rS||"ErrnoError"!==e.name)throw e;return e.errno}},v:function(e,r,t,n,o){var a=rj(r,t);try{if(isNaN(a))return 61;var s=rM.getStreamFromFD(e);return rS.llseek(s,a,n),w=[s.position>>>0,(g=s.position,+Math.abs(g)>=1?g>0?Math.floor(g/0x100000000)>>>0:~~Math.ceil((g-(~~g>>>0))/0x100000000)>>>0:0)],h[o>>2]=w[0],h[o+4>>2]=w[1],s.getdents&&0===a&&0===n&&(s.getdents=null),0}catch(e){if(void 0===rS||"ErrnoError"!==e.name)throw e;return e.errno}},C:function(e,r,t,n){try{var o=rM.getStreamFromFD(e),a=rO(o,r,t);return f[n>>2]=a,0}catch(e){if(void 0===rS||"ErrnoError"!==e.name)throw e;return e.errno}},x:(e,r,t,n,o)=>rH(e,r,t,n)},rG=function(){var e,t,o,a={a:rX};function s(e,r){var t;return u=(rG=e.exports).K,W(),k=rG.O,t=rG.L,N.unshift(t),V("wasm-instantiate"),rG}if(U("wasm-instantiate"),r.instantiateWasm)try{return r.instantiateWasm(a,s)}catch(e){O(`Module.instantiateWasm callback failed with error: ${e}`),n(e)}return(e=i,t=y,o=function(e){s(e.instance)},!e&&"function"==typeof WebAssembly.instantiateStreaming&&!q(t)&&"function"==typeof fetch?fetch(t,{credentials:"same-origin"}).then(e=>WebAssembly.instantiateStreaming(e,a).then(o,function(e){return O(`wasm streaming compile failed: ${e}`),O("falling back to ArrayBuffer instantiation"),G(t,a,o)})):G(t,a,o)).catch(n),{}}(),rJ=r._malloc=e=>(rJ=r._malloc=rG.M)(e),rK=r._free=e=>(rK=r._free=rG.N)(e),rZ=e=>(rZ=rG.P)(e),rQ=(r.__embind_initialize_bindings=()=>(r.__embind_initialize_bindings=rG.Q)(),()=>(rQ=rG.R)()),r0=e=>(r0=rG.S)(e),r1=e=>(r1=rG.T)(e),r2=e=>(r2=rG.U)(e);function r3(){if(!(I>0)){if(r.preRun)for("function"==typeof r.preRun&&(r.preRun=[r.preRun]);r.preRun.length;){var e;e=r.preRun.shift(),z.unshift(e)}J(z),I>0||(r.setStatus?(r.setStatus("Running..."),setTimeout(function(){setTimeout(function(){r.setStatus("")},1),n()},1)):n())}function n(){if(!C&&(C=!0,r.calledRun=!0,!R)){if(r.noFSInit||rS.init.initialized||rS.init(),rS.ignorePermissions=!1,r_.init(),J(N),t(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),r.postRun)for("function"==typeof r.postRun&&(r.postRun=[r.postRun]);r.postRun.length;){var e;e=r.postRun.shift(),B.unshift(e)}J(B)}}}if(r.dynCall_jiji=(e,t,n,o,a)=>(r.dynCall_jiji=rG.V)(e,t,n,o,a),r.dynCall_viijii=(e,t,n,o,a,s,i)=>(r.dynCall_viijii=rG.W)(e,t,n,o,a,s,i),r.dynCall_iiiiij=(e,t,n,o,a,s,i)=>(r.dynCall_iiiiij=rG.X)(e,t,n,o,a,s,i),r.dynCall_iiiiijj=(e,t,n,o,a,s,i,u,l)=>(r.dynCall_iiiiijj=rG.Y)(e,t,n,o,a,s,i,u,l),r.dynCall_iiiiiijj=(e,t,n,o,a,s,i,u,l,d)=>(r.dynCall_iiiiiijj=rG.Z)(e,t,n,o,a,s,i,u,l,d),r.ccall=(e,r,t,n,o)=>{var a,s={string:e=>{var r=0;return null!=e&&0!==e&&(r=rU(e)),r},array:e=>{var r=r1(e.length);return rI(e,r),r}},i=rL(e),u=[],l=0;if(n)for(var d=0;d<n.length;d++){var c=s[t[d]];c?(0===l&&(l=rQ()),u[d]=c(n[d])):u[d]=n[d]}var p=i.apply(null,u);return a=p,0!==l&&r0(l),p="string"===r?rr(a):"boolean"===r?!!a:a},L=function e(){C||r3(),C||(L=e)},r.preInit)for("function"==typeof r.preInit&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return r3(),r.ready}})()}};