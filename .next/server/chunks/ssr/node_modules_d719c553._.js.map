{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/lodash.debounce/index.js"], "sourcesContent": ["/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the `TypeError` message for \"Functions\" methods. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        result = wait - timeSinceLastCall;\n\n    return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = debounce;\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,6DAA6D,GAC7D,IAAI,kBAAkB;AAEtB,uDAAuD,GACvD,IAAI,MAAM,IAAI;AAEd,yCAAyC,GACzC,IAAI,YAAY;AAEhB,mDAAmD,GACnD,IAAI,SAAS;AAEb,yDAAyD,GACzD,IAAI,aAAa;AAEjB,yCAAyC,GACzC,IAAI,aAAa;AAEjB,wCAAwC,GACxC,IAAI,YAAY;AAEhB,+DAA+D,GAC/D,IAAI,eAAe;AAEnB,gDAAgD,GAChD,IAAI,aAAa,OAAO,UAAU,YAAY,UAAU,OAAO,MAAM,KAAK,UAAU;AAEpF,iCAAiC,GACjC,IAAI,WAAW,OAAO,QAAQ,YAAY,QAAQ,KAAK,MAAM,KAAK,UAAU;AAE5E,8CAA8C,GAC9C,IAAI,OAAO,cAAc,YAAY,SAAS;AAE9C,yCAAyC,GACzC,IAAI,cAAc,OAAO,SAAS;AAElC;;;;CAIC,GACD,IAAI,iBAAiB,YAAY,QAAQ;AAEzC,sFAAsF,GACtF,IAAI,YAAY,KAAK,GAAG,EACpB,YAAY,KAAK,GAAG;AAExB;;;;;;;;;;;;;;;CAeC,GACD,IAAI,MAAM;IACR,OAAO,KAAK,IAAI,CAAC,GAAG;AACtB;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqDC,GACD,SAAS,SAAS,IAAI,EAAE,IAAI,EAAE,OAAO;IACnC,IAAI,UACA,UACA,SACA,QACA,SACA,cACA,iBAAiB,GACjB,UAAU,OACV,SAAS,OACT,WAAW;IAEf,IAAI,OAAO,QAAQ,YAAY;QAC7B,MAAM,IAAI,UAAU;IACtB;IACA,OAAO,SAAS,SAAS;IACzB,IAAI,SAAS,UAAU;QACrB,UAAU,CAAC,CAAC,QAAQ,OAAO;QAC3B,SAAS,aAAa;QACtB,UAAU,SAAS,UAAU,SAAS,QAAQ,OAAO,KAAK,GAAG,QAAQ;QACrE,WAAW,cAAc,UAAU,CAAC,CAAC,QAAQ,QAAQ,GAAG;IAC1D;IAEA,SAAS,WAAW,IAAI;QACtB,IAAI,OAAO,UACP,UAAU;QAEd,WAAW,WAAW;QACtB,iBAAiB;QACjB,SAAS,KAAK,KAAK,CAAC,SAAS;QAC7B,OAAO;IACT;IAEA,SAAS,YAAY,IAAI;QACvB,6BAA6B;QAC7B,iBAAiB;QACjB,yCAAyC;QACzC,UAAU,WAAW,cAAc;QACnC,2BAA2B;QAC3B,OAAO,UAAU,WAAW,QAAQ;IACtC;IAEA,SAAS,cAAc,IAAI;QACzB,IAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO,gBAC7B,SAAS,OAAO;QAEpB,OAAO,SAAS,UAAU,QAAQ,UAAU,uBAAuB;IACrE;IAEA,SAAS,aAAa,IAAI;QACxB,IAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO;QAEjC,uEAAuE;QACvE,uEAAuE;QACvE,6DAA6D;QAC7D,OAAQ,iBAAiB,aAAc,qBAAqB,QACzD,oBAAoB,KAAO,UAAU,uBAAuB;IACjE;IAEA,SAAS;QACP,IAAI,OAAO;QACX,IAAI,aAAa,OAAO;YACtB,OAAO,aAAa;QACtB;QACA,qBAAqB;QACrB,UAAU,WAAW,cAAc,cAAc;IACnD;IAEA,SAAS,aAAa,IAAI;QACxB,UAAU;QAEV,gEAAgE;QAChE,2BAA2B;QAC3B,IAAI,YAAY,UAAU;YACxB,OAAO,WAAW;QACpB;QACA,WAAW,WAAW;QACtB,OAAO;IACT;IAEA,SAAS;QACP,IAAI,YAAY,WAAW;YACzB,aAAa;QACf;QACA,iBAAiB;QACjB,WAAW,eAAe,WAAW,UAAU;IACjD;IAEA,SAAS;QACP,OAAO,YAAY,YAAY,SAAS,aAAa;IACvD;IAEA,SAAS;QACP,IAAI,OAAO,OACP,aAAa,aAAa;QAE9B,WAAW;QACX,WAAW,IAAI;QACf,eAAe;QAEf,IAAI,YAAY;YACd,IAAI,YAAY,WAAW;gBACzB,OAAO,YAAY;YACrB;YACA,IAAI,QAAQ;gBACV,sCAAsC;gBACtC,UAAU,WAAW,cAAc;gBACnC,OAAO,WAAW;YACpB;QACF;QACA,IAAI,YAAY,WAAW;YACzB,UAAU,WAAW,cAAc;QACrC;QACA,OAAO;IACT;IACA,UAAU,MAAM,GAAG;IACnB,UAAU,KAAK,GAAG;IAClB,OAAO;AACT;AAEA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,OAAO,OAAO;IAClB,OAAO,CAAC,CAAC,SAAS,CAAC,QAAQ,YAAY,QAAQ,UAAU;AAC3D;AAEA;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,SAAS,aAAa,KAAK;IACzB,OAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AACpC;AAEA;;;;;;;;;;;;;;;;CAgBC,GACD,SAAS,SAAS,KAAK;IACrB,OAAO,OAAO,SAAS,YACpB,aAAa,UAAU,eAAe,IAAI,CAAC,UAAU;AAC1D;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO;IACT;IACA,IAAI,SAAS,QAAQ;QACnB,OAAO;IACT;IACA,IAAI,SAAS,QAAQ;QACnB,IAAI,QAAQ,OAAO,MAAM,OAAO,IAAI,aAAa,MAAM,OAAO,KAAK;QACnE,QAAQ,SAAS,SAAU,QAAQ,KAAM;IAC3C;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,UAAU,IAAI,QAAQ,CAAC;IAChC;IACA,QAAQ,MAAM,OAAO,CAAC,QAAQ;IAC9B,IAAI,WAAW,WAAW,IAAI,CAAC;IAC/B,OAAO,AAAC,YAAY,UAAU,IAAI,CAAC,SAC/B,aAAa,MAAM,KAAK,CAAC,IAAI,WAAW,IAAI,KAC3C,WAAW,IAAI,CAAC,SAAS,MAAM,CAAC;AACvC;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "file": "index.mjs", "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/react-merge-refs/src/index.tsx"], "sourcesContent": ["import type * as React from \"react\";\n\nexport function mergeRefs<T = any>(\n  refs: Array<React.MutableRefObject<T> | React.LegacyRef<T> | undefined | null>\n): React.RefCallback<T> {\n  return (value) => {\n    refs.forEach((ref) => {\n      if (typeof ref === \"function\") {\n        ref(value);\n      } else if (ref != null) {\n        (ref as React.MutableRefObject<T | null>).current = value;\n      }\n    });\n  };\n}\n"], "names": ["mergeRefs", "refs", "value", "ref"], "mappings": ";;;AAEgB,SAAAA,EACdC,CAAAA,CACsB;IACtB,QAAQC,GAAU;QAChBD,EAAK,OAAA,EAASE,GAAQ;YAChB,OAAOA,KAAQ,aACjBA,EAAID,CAAK,IACAC,KAAO,QAAA,CACfA,EAAyC,OAAA,GAAUD,CAAAA;QAExD,CAAC;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40splinetool/react-spline/dist/ParentSize.js"], "sourcesContent": ["\"use client\";\nimport { jsx as F } from \"react/jsx-runtime\";\nimport b from \"lodash.debounce\";\nimport { forwardRef as g, useRef as m, useState as j, useMemo as k, useEffect as B } from \"react\";\nimport { mergeRefs as E } from \"react-merge-refs\";\nconst I = [], K = { width: \"100%\", height: \"100%\" }, M = g(function({\n  className: w,\n  children: p,\n  debounceTime: i = 300,\n  ignoreDimensions: s = I,\n  parentSizeStyles: z,\n  enableDebounceLeadingCall: u = !0,\n  resizeObserverPolyfill: f,\n  ...R\n}, S) {\n  const o = m(null), d = m(0), [v, y] = j({\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0\n  }), n = k(() => {\n    const a = Array.isArray(s) ? s : [s];\n    return b(\n      (e) => {\n        y((r) => Object.keys(r).filter(\n          (t) => r[t] !== e[t]\n        ).every(\n          (t) => a.includes(t)\n        ) ? r : e);\n      },\n      i,\n      { leading: u }\n    );\n  }, [i, u, s]);\n  return B(() => {\n    const a = f || window.ResizeObserver, e = new a((r) => {\n      r.forEach((c) => {\n        const { left: h, top: l, width: t, height: A } = (c == null ? void 0 : c.contentRect) ?? {};\n        d.current = window.requestAnimationFrame(() => {\n          n({ width: t, height: A, top: l, left: h });\n        });\n      });\n    });\n    return o.current && e.observe(o.current), () => {\n      window.cancelAnimationFrame(d.current), e.disconnect(), n.cancel();\n    };\n  }, [n, f]), /* @__PURE__ */ F(\n    \"div\",\n    {\n      style: { ...K, ...z },\n      ref: E([S, o]),\n      className: w,\n      ...R,\n      children: p({\n        ...v,\n        ref: o.current,\n        resize: n\n      })\n    }\n  );\n});\nexport {\n  M as default\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AAJA;;;;;AAKA,MAAM,IAAI,EAAE,EAAE,IAAI;IAAE,OAAO;IAAQ,QAAQ;AAAO,GAAG,kBAAI,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE,SAAS,EAClE,WAAW,CAAC,EACZ,UAAU,CAAC,EACX,cAAc,IAAI,GAAG,EACrB,kBAAkB,IAAI,CAAC,EACvB,kBAAkB,CAAC,EACnB,2BAA2B,IAAI,CAAC,CAAC,EACjC,wBAAwB,CAAC,EACzB,GAAG,GACJ,EAAE,CAAC;IACF,MAAM,IAAI,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,OAAO,IAAI,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE;QACtC,OAAO;QACP,QAAQ;QACR,KAAK;QACL,MAAM;IACR,IAAI,IAAI,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE;QACR,MAAM,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI;YAAC;SAAE;QACpC,OAAO,CAAA,GAAA,2IAAA,CAAA,UAAC,AAAD,EACL,CAAC;YACC,EAAE,CAAC,IAAM,OAAO,IAAI,CAAC,GAAG,MAAM,CAC5B,CAAC,IAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EACpB,KAAK,CACL,CAAC,IAAM,EAAE,QAAQ,CAAC,MAChB,IAAI;QACV,GACA,GACA;YAAE,SAAS;QAAE;IAEjB,GAAG;QAAC;QAAG;QAAG;KAAE;IACZ,OAAO,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE;QACP,MAAM,IAAI,KAAK,OAAO,cAAc,EAAE,IAAI,IAAI,EAAE,CAAC;YAC/C,EAAE,OAAO,CAAC,CAAC;gBACT,MAAM,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ,CAAC,EAAE,GAAG,CAAC,KAAK,OAAO,KAAK,IAAI,EAAE,WAAW,KAAK,CAAC;gBAC1F,EAAE,OAAO,GAAG,OAAO,qBAAqB,CAAC;oBACvC,EAAE;wBAAE,OAAO;wBAAG,QAAQ;wBAAG,KAAK;wBAAG,MAAM;oBAAE;gBAC3C;YACF;QACF;QACA,OAAO,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,EAAE,OAAO,GAAG;YACxC,OAAO,oBAAoB,CAAC,EAAE,OAAO,GAAG,EAAE,UAAU,IAAI,EAAE,MAAM;QAClE;IACF,GAAG;QAAC;QAAG;KAAE,GAAG,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAC,AAAD,EAC1B,OACA;QACE,OAAO;YAAE,GAAG,CAAC;YAAE,GAAG,CAAC;QAAC;QACpB,KAAK,CAAA,GAAA,wJAAA,CAAA,YAAC,AAAD,EAAE;YAAC;YAAG;SAAE;QACb,WAAW;QACX,GAAG,CAAC;QACJ,UAAU,EAAE;YACV,GAAG,CAAC;YACJ,KAAK,EAAE,OAAO;YACd,QAAQ;QACV;IACF;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40splinetool/react-spline/dist/react-spline.js"], "sourcesContent": ["\"use client\";\nimport { jsx as f, jsxs as j, Fragment as z } from \"react/jsx-runtime\";\nimport { forwardRef as D, useRef as U, useState as l, useEffect as F } from \"react\";\nimport { Application as H } from \"@splinetool/runtime\";\nimport I from \"./ParentSize.js\";\nconst C = D(\n  ({\n    scene: s,\n    style: u,\n    onSplineMouseDown: p,\n    onSplineMouseUp: b,\n    onSplineMouseHover: d,\n    onSplineKeyDown: v,\n    onSplineKeyUp: w,\n    onSplineStart: y,\n    onSplineLookAt: h,\n    onSplineFollow: S,\n    onSplineScroll: k,\n    onLoad: r,\n    renderOnDemand: E = !0,\n    children: g,\n    ...x\n  }, A) => {\n    const o = U(null), [c, a] = l(!0), [i, R] = l();\n    if (i)\n      throw i;\n    return F(() => {\n      a(!0);\n      let e;\n      const m = [\n        {\n          name: \"mouseDown\",\n          cb: p\n        },\n        {\n          name: \"mouseUp\",\n          cb: b\n        },\n        {\n          name: \"mouseHover\",\n          cb: d\n        },\n        {\n          name: \"keyDown\",\n          cb: v\n        },\n        {\n          name: \"keyUp\",\n          cb: w\n        },\n        {\n          name: \"start\",\n          cb: y\n        },\n        {\n          name: \"lookAt\",\n          cb: h\n        },\n        {\n          name: \"follow\",\n          cb: S\n        },\n        {\n          name: \"scroll\",\n          cb: k\n        }\n      ];\n      if (o.current) {\n        e = new H(o.current, { renderOnDemand: E });\n        async function t() {\n          await e.load(s);\n          for (let n of m)\n            n.cb && e.addEventListener(n.name, n.cb);\n          a(!1), r == null || r(e);\n        }\n        t().catch((n) => {\n          R(n);\n        });\n      }\n      return () => {\n        for (let t of m)\n          t.cb && e.removeEventListener(t.name, t.cb);\n        e.dispose();\n      };\n    }, [s]), /* @__PURE__ */ f(\n      I,\n      {\n        ref: A,\n        parentSizeStyles: { overflow: \"hidden\", ...u },\n        debounceTime: 50,\n        ...x,\n        children: () => /* @__PURE__ */ j(z, { children: [\n          c && g,\n          /* @__PURE__ */ f(\n            \"canvas\",\n            {\n              ref: o,\n              style: {\n                display: c ? \"none\" : \"block\"\n              }\n            }\n          )\n        ] })\n      }\n    );\n  }\n);\nexport {\n  C as default\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AAJA;;;;;AAKA,MAAM,kBAAI,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EACR,CAAC,EACC,OAAO,CAAC,EACR,OAAO,CAAC,EACR,mBAAmB,CAAC,EACpB,iBAAiB,CAAC,EAClB,oBAAoB,CAAC,EACrB,iBAAiB,CAAC,EAClB,eAAe,CAAC,EAChB,eAAe,CAAC,EAChB,gBAAgB,CAAC,EACjB,gBAAgB,CAAC,EACjB,gBAAgB,CAAC,EACjB,QAAQ,CAAC,EACT,gBAAgB,IAAI,CAAC,CAAC,EACtB,UAAU,CAAC,EACX,GAAG,GACJ,EAAE;IACD,MAAM,IAAI,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD;IAC5C,IAAI,GACF,MAAM;IACR,OAAO,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE;QACP,EAAE,CAAC;QACH,IAAI;QACJ,MAAM,IAAI;YACR;gBACE,MAAM;gBACN,IAAI;YACN;YACA;gBACE,MAAM;gBACN,IAAI;YACN;YACA;gBACE,MAAM;gBACN,IAAI;YACN;YACA;gBACE,MAAM;gBACN,IAAI;YACN;YACA;gBACE,MAAM;gBACN,IAAI;YACN;YACA;gBACE,MAAM;gBACN,IAAI;YACN;YACA;gBACE,MAAM;gBACN,IAAI;YACN;YACA;gBACE,MAAM;gBACN,IAAI;YACN;YACA;gBACE,MAAM;gBACN,IAAI;YACN;SACD;QACD,IAAI,EAAE,OAAO,EAAE;YACb,IAAI,IAAI,2JAAA,CAAA,cAAC,CAAC,EAAE,OAAO,EAAE;gBAAE,gBAAgB;YAAE;YACzC,eAAe;gBACb,MAAM,EAAE,IAAI,CAAC;gBACb,KAAK,IAAI,KAAK,EACZ,EAAE,EAAE,IAAI,EAAE,gBAAgB,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;gBACzC,EAAE,CAAC,IAAI,KAAK,QAAQ,EAAE;YACxB;YACA,IAAI,KAAK,CAAC,CAAC;gBACT,EAAE;YACJ;QACF;QACA,OAAO;YACL,KAAK,IAAI,KAAK,EACZ,EAAE,EAAE,IAAI,EAAE,mBAAmB,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;YAC5C,EAAE,OAAO;QACX;IACF,GAAG;QAAC;KAAE,GAAG,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAC,AAAD,EACvB,qKAAA,CAAA,UAAC,EACD;QACE,KAAK;QACL,kBAAkB;YAAE,UAAU;YAAU,GAAG,CAAC;QAAC;QAC7C,cAAc;QACd,GAAG,CAAC;QACJ,UAAU,IAAM,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAC,AAAD,EAAE,uNAAA,CAAA,WAAC,EAAE;gBAAE,UAAU;oBAC/C,KAAK;oBACL,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAC,AAAD,EACd,UACA;wBACE,KAAK;wBACL,OAAO;4BACL,SAAS,IAAI,SAAS;wBACxB;oBACF;iBAEH;YAAC;IACJ;AAEJ", "ignoreList": [0], "debugId": null}}]}