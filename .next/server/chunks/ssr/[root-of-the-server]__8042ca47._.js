module.exports = {

"[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_59fa4ecd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_react-reconciler_4b3d0196._.js",
  "server/chunks/ssr/node_modules_three_build_three_core_a75ea919.js",
  "server/chunks/ssr/node_modules_three_build_three_module_bc28cc46.js",
  "server/chunks/ssr/node_modules_three_build_three_module_c9a5c6ae.js",
  "server/chunks/ssr/node_modules_@react-three_fiber_dist_976af2d0._.js",
  "server/chunks/ssr/node_modules_4c0c6338._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/components/3d/InteractiveThreeScene.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_three_build_three_core_a75ea919.js",
  "server/chunks/ssr/node_modules_three_build_three_module_bc28cc46.js",
  "server/chunks/ssr/node_modules_react-reconciler_4b3d0196._.js",
  "server/chunks/ssr/node_modules_5bf33382._.js",
  "server/chunks/ssr/src_components_3d_InteractiveThreeScene_tsx_77ab1dd8._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/3d/InteractiveThreeScene.tsx [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@splinetool/react-spline/dist/react-spline.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@splinetool_runtime_build_338bbfa6._.js",
  "server/chunks/ssr/node_modules_@splinetool_runtime_build_runtime_782a1a29.js",
  "server/chunks/ssr/node_modules_d719c553._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@splinetool/react-spline/dist/react-spline.js [app-ssr] (ecmascript)");
    });
});
}}),

};