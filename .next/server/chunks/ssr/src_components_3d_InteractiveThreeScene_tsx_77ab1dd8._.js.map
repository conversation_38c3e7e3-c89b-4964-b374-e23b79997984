{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/3d/InteractiveThreeScene.tsx"], "sourcesContent": ["\nimport { useRef, useState, useEffect } from 'react';\nimport { useFrame, useThree } from '@react-three/fiber';\nimport * as THREE from 'three';\n\ninterface InteractiveThreeSceneProps {\n  color?: string;\n  count?: number;\n  size?: number;\n  mouseInfluence?: number;\n}\n\nconst InteractiveThreeScene = ({\n  color = \"#3fb950\",\n  count = 2000,\n  size = 0.06,\n  mouseInfluence = 0.05,\n}: InteractiveThreeSceneProps) => {\n  // Create a reference to the points object\n  const pointsRef = useRef<THREE.Points>(null!);\n\n  // Get the mouse and viewport from Three\n  const { mouse, viewport } = useThree();\n\n  // Store the original particle positions\n  const [particlePositions, setParticlePositions] = useState<Float32Array | null>(null);\n\n\n\n  // Create particle positions\n  useEffect(() => {\n    const positions = new Float32Array(count * 3);\n\n    for (let i = 0; i < count; i++) {\n      // Create particles in a more cloud-like formation to match the nebula image\n      const radius = Math.random() * 5 + 0.5;\n      const phi = Math.acos((Math.random() * 2) - 1);\n      const theta = Math.random() * Math.PI * 2;\n\n      positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);     // x\n      positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta); // y\n      positions[i * 3 + 2] = radius * Math.cos(phi) * 0.5;            // z - flatter on z-axis\n    }\n\n    setParticlePositions(positions);\n  }, [count]);\n\n  // Animation frame loop\n  useFrame((state) => {\n    if (!pointsRef.current || !particlePositions) return;\n\n    // Rotate the particle system\n    pointsRef.current.rotation.y += 0.0008;\n\n    // Apply mouse influence to camera\n    state.camera.position.x = THREE.MathUtils.lerp(\n      state.camera.position.x,\n      mouse.x * mouseInfluence,\n      0.05\n    );\n    state.camera.position.y = THREE.MathUtils.lerp(\n      state.camera.position.y,\n      mouse.y * mouseInfluence,\n      0.05\n    );\n\n    // Look at center\n    state.camera.lookAt(0, 0, 0);\n\n    // Update uniforms\n    if (pointsRef.current.material instanceof THREE.ShaderMaterial) {\n      pointsRef.current.material.uniforms.time.value = state.clock.getElapsedTime();\n      pointsRef.current.material.uniforms.mousePosition.value.set(\n        mouse.x * viewport.width / 2,\n        mouse.y * viewport.height / 2\n      );\n    }\n  });\n\n  if (!particlePositions) return null;\n\n  // Create geometry and material using Three.js directly\n  const geometry = new THREE.BufferGeometry();\n  geometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));\n\n  const material = new THREE.PointsMaterial({\n    color: color,\n    size: size,\n    transparent: true,\n    opacity: 0.8,\n    sizeAttenuation: true,\n  });\n\n  return (\n    /* @ts-ignore */\n    <points ref={pointsRef} geometry={geometry} material={material} />\n  );\n};\n\nexport default InteractiveThreeScene;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;;;;;AASA,MAAM,wBAAwB,CAAC,EAC7B,QAAQ,SAAS,EACjB,QAAQ,IAAI,EACZ,OAAO,IAAI,EACX,iBAAiB,IAAI,EACM;IAC3B,0CAA0C;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB;IAEvC,wCAAwC;IACxC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IAEnC,wCAAwC;IACxC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAIhF,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,IAAI,aAAa,QAAQ;QAE3C,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,4EAA4E;YAC5E,MAAM,SAAS,KAAK,MAAM,KAAK,IAAI;YACnC,MAAM,MAAM,KAAK,IAAI,CAAC,AAAC,KAAK,MAAM,KAAK,IAAK;YAC5C,MAAM,QAAQ,KAAK,MAAM,KAAK,KAAK,EAAE,GAAG;YAExC,SAAS,CAAC,IAAI,EAAE,GAAG,SAAS,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,QAAY,IAAI;YACrE,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,SAAS,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,QAAQ,IAAI;YACrE,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,SAAS,KAAK,GAAG,CAAC,OAAO,KAAgB,wBAAwB;QAC1F;QAEA,qBAAqB;IACvB,GAAG;QAAC;KAAM;IAEV,uBAAuB;IACvB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,mBAAmB;QAE9C,6BAA6B;QAC7B,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI;QAEhC,kCAAkC;QAClC,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,+IAAA,CAAA,YAAe,CAAC,IAAI,CAC5C,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,EACvB,MAAM,CAAC,GAAG,gBACV;QAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,+IAAA,CAAA,YAAe,CAAC,IAAI,CAC5C,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,EACvB,MAAM,CAAC,GAAG,gBACV;QAGF,iBAAiB;QACjB,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG;QAE1B,kBAAkB;QAClB,IAAI,UAAU,OAAO,CAAC,QAAQ,YAAY,+IAAA,CAAA,iBAAoB,EAAE;YAC9D,UAAU,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK,CAAC,cAAc;YAC3E,UAAU,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CACzD,MAAM,CAAC,GAAG,SAAS,KAAK,GAAG,GAC3B,MAAM,CAAC,GAAG,SAAS,MAAM,GAAG;QAEhC;IACF;IAEA,IAAI,CAAC,mBAAmB,OAAO;IAE/B,uDAAuD;IACvD,MAAM,WAAW,IAAI,+IAAA,CAAA,iBAAoB;IACzC,SAAS,YAAY,CAAC,YAAY,IAAI,+IAAA,CAAA,kBAAqB,CAAC,mBAAmB;IAE/E,MAAM,WAAW,IAAI,+IAAA,CAAA,iBAAoB,CAAC;QACxC,OAAO;QACP,MAAM;QACN,aAAa;QACb,SAAS;QACT,iBAAiB;IACnB;IAEA,OACE,cAAc,iBACd,8OAAC;QAAO,KAAK;QAAW,UAAU;QAAU,UAAU;;;;;;AAE1D;uCAEe", "debugId": null}}]}